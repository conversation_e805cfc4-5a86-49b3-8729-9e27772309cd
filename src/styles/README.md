# Styles Directory

This directory contains global styles, themes, and styling utilities using Tailwind CSS.

## Purpose

- Global styles and CSS reset
- Theme configuration
- Custom Tailwind utilities
- Global CSS variables
- Shared animations

## Structure

```
styles/
├── globals.css        # Global styles and Tailwind imports
└── theme/            # Theme configuration
    ├── colors.ts     # Color palette
    ├── typography.ts # Typography styles
    └── spacing.ts    # Spacing scale
```

## Best Practices

1. Use Tailwind CSS for consistent styling
2. Define custom utilities in globals.css
3. Keep theme configuration organized
4. Use CSS variables for dynamic values
5. Follow design system guidelines
6. Maintain responsive design principles
7. Document custom utilities

## Example Global Styles

```css
/* globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    /* Add more custom properties */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 0 0% 100%;
  }
}

@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
```

## Theme Configuration

```typescript
// theme/colors.ts
export const colors = {
  primary: {
    50: '#f0f9ff',
    500: '#0ea5e9',
    900: '#0c4a6e',
  },
  // ... more colors
};
```
