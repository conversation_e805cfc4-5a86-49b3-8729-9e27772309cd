@tailwind base;
@tailwind components;
@tailwind utilities;

body
{
  margin: 0;
  background-color: #F6F7F8;
}
@layer base {
  :root {
    --background: #ffffff;  /* White */
    --foreground: #2d2d2d;  /* HSL(0, 0%, 3.9%) */
    --card: #ffffff;       /* HSL(0, 0%, 100%) */
    --card-foreground: #2d2d2d;  /* HSL(0, 0%, 3.9%) */
    --popover: #ffffff;    /* HSL(0, 0%, 100%) */
    --popover-foreground: #2d2d2d;  /* HSL(0, 0%, 3.9%) */
    --primary: #2e2e2e;    /* HSL(0, 0%, 9%) */
    --primary-foreground: #f1f1f1;  /* HSL(0, 0%, 98%) */
    --secondary: #f4f4f4;  /* HSL(0, 0%, 96.1%) */
    --secondary-foreground: #2d2d2d;  /* HSL(0, 0%, 9%) */
    --muted: #f4f4f4;      /* HSL(0, 0%, 96.1%) */
    --muted-foreground: #7a7a7a;  /* HSL(0, 0%, 45.1%) */
    --accent: #f4f4f4;     /* HSL(0, 0%, 96.1%) */
    --accent-foreground: #2d2d2d;  /* HSL(0, 0%, 9%) */
    --destructive: #ff4c4c;  /* HSL(0, 84.2%, 60.2%) */
    --destructive-foreground: #f1f1f1;  /* HSL(0, 0%, 98%) */
    --border: #d1d1d1;     /* HSL(0, 0%, 89.8%) */
    --input: #d1d1d1;      /* HSL(0, 0%, 89.8%) */
    --ring: #2d2d2d;       /* HSL(0, 0%, 3.9%) */
    --chart-1: #e1566a;    /* HSL(12, 76%, 61%) */
    --chart-2: #82bf6b;    /* HSL(173, 58%, 39%) */
    --chart-3: #8e5e2b;    /* HSL(197, 37%, 24%) */
    --chart-4: #6e9e4f;    /* HSL(43, 74%, 66%) */
    --chart-5: #8c6d3c;    /* HSL(27, 87%, 67%) */
    --chart-6: #D67309;
    --radius: 0.5rem;



    --interface-text-inverse: #EEEEF0;
    --Brand-Support-1-700: linear-gradient(90deg, #65B6FF 0%, #3798FA 100%);
    --Brand-Support-1-pure: #1570EF;
    --Interface-Text-white: #FFF;
    --Brand-Neutral-900 : #42536D;
    --Brand-Support-2-300 : #FACE4F;
    --Interface-Text-lighter: #C9D0DB;
    --text-green-color: #39D145;
    --Interface-Text-subtitle : #7F8488;
    --login-border: #575E5F;
    --Brand-Primary-pure: #418E4C;
    --login-card-border: #293448;
    --login-card-bg: rgba(0, 0, 0, 0.20);
    --login-card-shadow: rgba(0, 0, 0, 0.25);
    --login-Microsoft-border: #4A5051;
    --Interface-Text-title: #212325;
    --Interface-Stroke-default: #C9D3DB;
    --close-color: #D42600;
    --Interface-Stroke-soft: #E5E7EB;
    --interface-surface-component: #FFFFFF;
    --Interface-Text-default: #3C4146;
    --Interface-Surface-component-muted: #F6F7F8;
    --Clear-All: #8B8B8B;
    --interface-stroke-default-new:#BECDE3;
    --yellow-bg: #FFD916;
    --Brand-Neutral-50: #F5F6F8;
    --Brand-Neutral-100: #ECEFF3;
    --Interface-Stroke-hard: #BBC1C7;
    --Brand-Neutral-700: #6480AB;
    --Brand-Neutral-800:#4F6484;
    --Brand-Neutral-pure: #768FB5;
    --Brand-Support-1-400: #5CB6FE;
    --Brand-Support-1-300: #90D1FF;
    --Brand-Primary-500: #4C9B57;
    --black-color:#000;
    --dark-green-color: #38DF0C;
    --Brand-Primary-7001: #00953A;
    --Brand-Primary-100: #D6FFE6 !important;
    --Brand-Support-2-pure: #D67309;

    --Brand-Support-1-50: #EFF8FF;
    --Interface-Surface-page-muted:#E5E7EB;
    --Brand-Neutral-500:#91A5C3;
    --Brand-Primary-300:#A1D4A7;
    --Brand-Support-2-50: #FFFBEB;
    --Brand-Primary-50: #F3FAF3;
    --Brand-Neutral-950: #263040;
    --overlay-bg:#11131A;
    --Brand-Support-1-500: #3696FB;
    --sky-blue-light: #65B6FF;
    --sky-blue-dark: #3798FA;
    --Button-Base-Default---1:linear-gradient(180deg, var(#FEFEFE) 0%, var(#F6F8FA) 100%);

     /* nilesh */
    --Brand-Primary-100: #E4F4E6;
    --Brand-Primary-700: #306538;
    --Brand-Primary-600:#418E4C;
    --border-white: #FFF;
    --Interface-Text-primary:#1570EF;
    --Interface-feedback-neutral-700: #D67309;
    --Brand-Support-2-800: #903D10;
    --Brand-Support-2-100: #FDF2C8;
    --Brand-Support-2-400: #F8B720;
    --Interface-Surface-hc-inverse: #1F2A37;
    --light-gray-text: #8B8B8B;
    --Brand-Highlight-300: #ACD69F;
    --Interface-feedback-error-400: #FFB5A5;
    --Interface-feedback-error-100: #FFF3F0;
    --Interface-feedback-error-700: #D42600;
    --Brand-Support-2500:#F2980E;
    /* nilesh */

    /* neha */
    --Brand-Highlight-100: #E8F4E4;
    --Brand-Primary-300New: #71FFA8;
    --Brand-Highlight-800: #315229;
    --Brand-Primary-800 : #2A5130;
    --Brand-Support-1-7001 : #1861DD;
    --button-base-default-hover-2 : #F6F8FA;
    --Brand-Primary-50new : #EDFFF3;
    --Brand-Primary-purenew : #00953A;
    --Brand-Highlight-pure : #72B35F;
    --Brand-Highlight-600 : #488138;
    --Brand-Highlight-500:#5D9D4A;
    --Brand-Neutral-200: #DBE1EA;
    --Brand-Neutral-400: #A9B9D0;
    /* neha */


    /* Poonam */

    --Brand-Highlight-50:#F5FAF3;
    --Brand-Highlight-400:#72B35F;
     --Brand-Primary-600 : #00BF46;
    /* Poonam */
  }

  .dark {

    --background: 0 0% 3.9%;

    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;

    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;

    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;

    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;

    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;

    --input: 0 0% 14.9%;

    --ring: 0 0% 83.1%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%
  }
}
/* Global font size */

.font12 {
  @apply text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw];
}
.font14 {
  @apply text-[13px] lg:text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw];
}
.font16 {
  @apply text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw];
}
.font18 {
  @apply text-[15px] lg:text-[15px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw];
}
.font20 {
  @apply text-[16px] lg:text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw];
}

/* Global font size End */

@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

@font-face {
    font-family: 'cloudquark';
    src:  url('/fonts/cloudquark.eot?dum542');
    src:  url('/fonts/cloudquark.eot?dum542#iefix') format('embedded-opentype'),
      url('/fonts/cloudquark.ttf?dum542') format('truetype'),
      url('/fonts/cloudquark.woff?dum542') format('woff'),
      url('/fonts/cloudquark.svg?dum542#cloudquark') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
  }

  [class^="cloud-"], [class*=" cloud-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'cloudquark' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .cloud-foldericon:before {
  content: "\e9c9";
}
  .cloud-productview:before {
  content: "\e9c8";
}
.cloud-fillinfo:before {
  content: "\e9bb";
}
.cloud-fillnotepad:before {
  content: "\e9be";
}
.cloud-user1:before {
  content: "\e9bf";
}
.cloud-edit2:before {
  content: "\e9c0";
}
.cloud-information:before {
  content: "\e9c2";
}
.cloud-notepad:before {
  content: "\e9c3";
}
.cloud-pending:before {
  content: "\e9c4";
}
.cloud-plus-square:before {
  content: "\e9c5";
}
.cloud-receipt1:before {
  content: "\e9c6";
}
.cloud-refresh1:before {
  content: "\e9c7";
}
.cloud-trashlighticon:before {
  content: "\e9b7";
}
.cloud-doc-forward:before {
  content: "\e9bc";
}
.cloud-mail-tracking:before {
  content: "\e9bd";
}
.cloud-back-square:before {
  content: "\e9b5";
}
.cloud-doc-text:before {
  content: "\e9b6";
}
.cloud-link2:before {
  content: "\e9b8";
}
.cloud-refresh-square-2:before {
  content: "\e9b9";
}
.cloud-repeate-circle:before {
  content: "\e9ba";
}
.cloud-trash2:before {
  content: "\e9b3";
}
.cloud-save-2:before {
  content: "\e9b4";
}
.cloud-receipt-disscount:before {
  content: "\e9b1";
}
.cloud-minus:before {
  content: "\e9b2";
}
  .cloud-dangerlight:before {
  content: "\e9b0";
}
  .cloud-new:before {
  content: "\e9ad";
}
  .cloud-medalstar:before {
  content: "\e9a6";
}
.cloud-refreshcircle:before {
  content: "\e9a7";
}
.cloud-repeat:before {
  content: "\e9a8";
}
.cloud-saveminus:before {
  content: "\e9a9";
}
.cloud-saveremove:before {
  content: "\e9aa";
}
.cloud-slash:before {
  content: "\e9ab";
}
.cloud-task:before {
  content: "\e9ac";
}
.cloud-user:before {
  content: "\e9ae";
}
.cloud-export:before {
  content: "\e9af";
}
.cloud-calendar:before {
  content: "\e99b";
}
.cloud-copy:before {
  content: "\e99c";
}
.cloud-doc1:before {
  content: "\e99d";
}
.cloud-doubleuser:before {
  content: "\e99e";
}
.cloud-files:before {
  content: "\e99f";
}
.cloud-files1:before {
  content: "\e9a0";
}
.cloud-fillstar:before {
  content: "\e9a1";
}
.cloud-flag:before {
  content: "\e9a2";
}
.cloud-permission:before {
  content: "\e9a3";
}
.cloud-permission2:before {
  content: "\e9a4";
}
.cloud-savedoc:before {
  content: "\e9a5";
}

  .cloud-audio-square:before {
  content: "\e90f";
}
.cloud-coin:before {
  content: "\e910";
}
.cloud-company-profile:before {
  content: "\e911";
}
.cloud-copy-success1:before {
  content: "\e912";
}
.cloud-discount-fillshap:before {
  content: "\e913";
}
.cloud-discount-shap:before {
  content: "\e914";
}
.cloud-gallery:before {
  content: "\e915";
}
.cloud-grid1:before {
  content: "\e91c";
}
.cloud-link:before {
  content: "\e91d";
}
.cloud-note-remove:before {
  content: "\e992";
}
.cloud-promotion-tag:before {
  content: "\e993";
}
.cloud-receipt-text:before {
  content: "\e994";
}
.cloud-row-vertical:before {
  content: "\e995";
}
.cloud-save:before {
  content: "\e996";
}
.cloud-ticketDiscount:before {
  content: "\e997";
}
.cloud-timer:before {
  content: "\e998";
}
.cloud-verify1:before {
  content: "\e999";
}
.cloud-video-play:before {
  content: "\e99a";
}
.cloud-edit:before {
  content: "\e90e";
}
.cloud-card1:before {
  content: "\e90c";
}
.cloud-applyfilters:before {
  content: "\e901";
}
.cloud-brush2:before {
  content: "\e902";
}
.cloud-canlendar:before {
  content: "\e903";
}
.cloud-columns:before {
  content: "\e904";
}
.cloud-convertsshap:before {
  content: "\e905";
}
.cloud-dots:before {
  content: "\e906";
}
.cloud-rightarrowfill:before {
  content: "\e907";
}
.cloud-settingicon:before {
  content: "\e908";
}
.cloud-document-filter:before {
  content: "\e900";
}
.cloud-seeallicon:before {
  content: "\e925";
}
.cloud-brifcase:before {
  content: "\e916";
}
.cloud-baghappy:before {
  content: "\e917";
}
.cloud-statusup:before {
  content: "\e918";
}
.cloud-cardtick:before {
  content: "\e919";
}
.cloud-clipboard:before {
  content: "\e91a";
}
.cloud-dislike:before {
  content: "\e91b";
}
.cloud-home2:before {
  content: "\e91e";
}
.cloud-linksquare:before {
  content: "\e91f";
}
.cloud-moresquare:before {
  content: "\e920";
}
.cloud-newbellicon:before {
  content: "\e921";
}
.cloud-passkey:before {
  content: "\e922";
}
.cloud-personalcard:before {
  content: "\e923";
}
.cloud-receiptedit:before {
  content: "\e924";
}
.cloud-shop:before {
  content: "\e926";
}
.cloud-smstracking:before {
  content: "\e927";
}
.cloud-subcircle:before {
  content: "\e928";
}
.cloud-usersquare:before {
  content: "\e929";
}
.cloud-arrowup:before {
  content: "\e96a";
}
.cloud-filtericon:before {
  content: "\e964";
}
.cloud-arrow-up:before {
  content: "\e930";
}
.cloud-arrow-left1:before {
  content: "\e958";
}
.cloud-brush1:before {
  content: "\e959";
}
.cloud-card11:before {
  content: "\e95a";
}
.cloud-card-edit1:before {
  content: "\e95b";
}
.cloud-cards1:before {
  content: "\e95c";
}
.cloud-clock11:before {
  content: "\e95d";
}
.cloud-document-download1:before {
  content: "\e95e";
}
.cloud-filter:before {
  content: "\e960";
}
.cloud-key-square:before {
  content: "\e961";
}
.cloud-lamp-on:before {
  content: "\e962";
}
.cloud-logout1:before {
  content: "\e963";
}
.cloud-readeye1:before {
  content: "\e966";
}
.cloud-unreadeye1:before {
  content: "\e968";
}
.cloud-verify:before {
  content: "\e969";
}
.cloud-clock:before {
  content: "\e94e";
}
.cloud-download:before {
  content: "\e952";
}
.cloud-dropdownarrow:before {
  content: "\e953";
}
.cloud-fillcircletick:before {
  content: "\e954";
}
.cloud-notification:before {
  content: "\e955";
}
.cloud-settings1:before {
  content: "\e956";
}
.cloud-threedot:before {
  content: "\e957";
}
.cloud-videosquare:before {
  content: "\e94d";
}
.cloud-tickcircle:before {
  content: "\e947";
}
.cloud-lock:before {
  content: "\e941";
}
.cloud-playbutton:before {
  content: "\e940";
}
.cloud-downarrowcicle:before {
  content: "\e93f";
}
.cloud-trash:before {
  content: "\e93e";
}
.cloud-filluparrow:before {
  content: "\e936";
}
.cloud-fillarrowdown:before {
  content: "\e937";
}
.cloud-folder:before {
  content: "\e938";
}
.cloud-fillcroscircle:before {
  content: "\e935";
}
.cloud-info2:before {
  content: "\e92f";
}
.cloud-rightarrow1:before {
  content: "\e92a";
}
.cloud-Add:before {
  content: "\e92b";
}
.cloud-addprofile:before {
  content: "\e92c";
}
.cloud-application:before {
  content: "\e92d";
}
.cloud-arrowleft:before {
  content: "\e92e";
}
.cloud-back:before {
  content: "\e931";
}
.cloud-bellicon:before {
  content: "\e932";
}
.cloud-book:before {
  content: "\e933";
}
.cloud-brokenshield:before {
  content: "\e934";
}
.cloud-bulb:before {
  content: "\e939";
}
.cloud-calling:before {
  content: "\e93a";
}
.cloud-card:before {
  content: "\e90d";
}
.cloud-cart:before {
  content: "\e93c";
}
.cloud-chnagemode:before {
  content: "\e93d";
}
.cloud-circlearrow:before {
  content: "\e942";
}
.cloud-circletick:before {
  content: "\e943";
}
.cloud-closecircle:before {
  content: "\e944";
}
.cloud-copy-success:before {
  content: "\e945";
}
.cloud-crown:before {
  content: "\e946";
}
.cloud-doc:before {
  content: "\e948";
}
.cloud-eye:before {
  content: "\e949";
}
.cloud-favriote:before {
  content: "\e94a";
}
.cloud-fileupload:before {
  content: "\e94b";
}
.cloud-filledcart:before {
  content: "\e94c";
}
.cloud-filledcrown:before {
  content: "\e94f";
}
.cloud-filledhome:before {
  content: "\e950";
}
.cloud-filledmsg:before {
  content: "\e951";
}
.cloud-filledpeople:before {
  content: "\e95f";
}
.cloud-filledshopping:before {
  content: "\e965";
}
.cloud-filledtrend:before {
  content: "\e967";
}
.cloud-fillleftarrowcircle:before {
  content: "\e96b";
}
.cloud-financialprofit:before {
  content: "\e96c";
}
.cloud-gear:before {
  content: "\e96d";
}
.cloud-gps:before {
  content: "\e96e";
}
.cloud-grid:before {
  content: "\e96f";
}
.cloud-group:before {
  content: "\e970";
}
.cloud-handshake:before {
  content: "\e971";
}
.cloud-handstars:before {
  content: "\e972";
}
.cloud-hexauser:before {
  content: "\e973";
}
.cloud-home:before {
  content: "\e974";
}
.cloud-info:before {
  content: "\e975";
}
.cloud-infrasturctrueservices:before {
  content: "\e976";
}
.cloud-investment:before {
  content: "\e977";
}
.cloud-leftcirclearrow:before {
  content: "\e978";
}
.cloud-location:before {
  content: "\e979";
}
.cloud-messagebox:before {
  content: "\e97a";
}
.cloud-mind:before {
  content: "\e97b";
}
.cloud-mode:before {
  content: "\e97c";
}
.cloud-modeselect:before {
  content: "\e97d";
}
.cloud-msguser:before {
  content: "\e97e";
}
.cloud-next:before {
  content: "\e97f";
}
.cloud-portfolio:before {
  content: "\e980";
}
.cloud-previous:before {
  content: "\e981";
}
.cloud-pricing:before {
  content: "\e982";
}
.cloud-productivity:before {
  content: "\e983";
}
.cloud-profile:before {
  content: "\e984";
}
.cloud-puzzle:before {
  content: "\e985";
}
.cloud-receipt:before {
  content: "\e986";
}
.cloud-refresh:before {
  content: "\e987";
}
.cloud-refresh2:before {
  content: "\e988";
}
.cloud-logout:before {
  content: "\e989";
}
.cloud-search:before {
  content: "\e98a";
}
.cloud-searchbar:before {
  content: "\e98b";
}
.cloud-security:before {
  content: "\e98c";
}
.cloud-send:before {
  content: "\e98d";
}
.cloud-settings:before {
  content: "\e98e";
}
.cloud-shield-tickk:before {
  content: "\e98f";
}
.cloud-shopping:before {
  content: "\e990";
}
.cloud-sms:before {
  content: "\e991";
}
.cloud-software:before {
  content: "\ea44";
}
.cloud-technology:before {
  content: "\ea45";
}
.cloud-textalign:before {
  content: "\ea46";
}
.cloud-tick:before {
  content: "\ea47";
}
.cloud-trend:before {
  content: "\ea48";
}
.cloud-world:before {
  content: "\e90b";
}
.cloud-notification-bing:before {
  content: "\e90a";
}
.cloud-danger:before {
  content: "\e909";
}


  .contentbg{
    border: 1px solid #0C212E;
    background: rgba(0, 0, 0, 0.20);
    box-shadow: 4px 8px 40px 0px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(8px);

  }

  .login.contentbg input{
    background: transparent !important;
    border: 1px solid #575E5F;
  }
  .loginbtn{
    border: 1px solid #3696FB;
    background: linear-gradient(90deg, #65B6FF 0%, #3798FA 100%);
  }
  .loginbtnnew{
    border: 1px solid #418E4C;
    background: #418E4C;
  }
  .cancelbtn{
    border: 1px solid  #E5E7EB;
    background: linear-gradient(180deg, #FEFEFE) 0%, #F6F8FA 100%;
  }
  .cancelbtn:hover{
    border: 1px solid  #E5E7EB;
    background: linear-gradient(180deg, #E5E7EB) 0%, #E5E7EB 100%;
  }
  .acceptbtn{
    border: 1px solid  #3B662F;
    background: #5D9D4A;
    color: #FFF;
  }
  .applybtn{
    border: 1px solid  #067532;
    background: #00953A
  }
  .applybtn:hover{
    border: 1px solid  #067532;
    background: #067532
  }
  .custtable td {
    vertical-align: middle;
  }

    .custtable td {
      vertical-align: middle;
    }

    .custtable tr td {
      /* background: var(--Interface-Surface-component-muted); */
      border-radius: 8px;
    }
    /* .custtable tr th {
      background: #FFF;
    } */
    .custtable table {
      border-collapse: separate;
      border-spacing: 0 10px;
      border: none;
    }
    .custtable .border {
      border-width: 0px;
    }
    .custtableborder .border {
      border-color: transparent !important;
    }
    .custtableborder thead tr th{
      height: 5px !important;
    }
    .custtableborder thead tr{
      background-color: var(--interface-surface-component);
    }
     .custborder {
      border: 1px solid #FFD916 !important;
      border-width: 1px !important;
     }
    .seobtnborder{
      border: 1px solid #72B35F !important;
      border-width: 1px !important;
    }
    .sapbtnborder{
      border: 1px solid #1570EF !important;
      border-width: 1px !important;
    }

     .custinputborder {
      border: 1px solid var(--Interface-Stroke-hard) !important;
      border-width: 1px !important;
     }

    .custtable th,
    .custtable td {
      border: none;
    }


  .blueBtn {
    border: 1px solid var(--Brand-Support-1-500);
    background: linear-gradient(90deg, var(--sky-blue-light) 0%, var(--sky-blue-dark) 100%);
    color: var(--Interface-Text-white);
  }

.cardShadow {
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);
  }


  .cardShadow2 {

    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);
  }

  .framShadow {
    background: #D8E6DE;
    box-shadow: 0px 12px 16px -4px rgba(215, 233, 223, 0.50);
  }

  .join-us-bg {
    background-image: url('/Join-Us.svg');
    background-size: cover;
    background-position: center;
  }

.bannervideo video {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover
}

.bannervideo:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100%;
  width: 100%;
  min-height: 100%;
  opacity: 0.8;
  background: no-repeat url("../../public/images/BG.png");
  background-size: cover;
}
.custom_header
{
  background: #fff;

/* Shadows/shadow-2xl */
box-shadow: 0px 25px 50px -12px rgba(0, 0, 0, 0.25);
}
.custom_header2
{
  background: linear-gradient(90deg, #264763 0%, #2C384A 100%);

/* Shadows/shadow-2xl */
box-shadow: 0px 25px 50px -12px rgba(0, 0, 0, 0.25);
}
.tagline:before
{
  content: "";
  position: absolute;
  left: 28vw;
  top: 0.8vw;
 width: 190px;
  height: 100%;
  min-height: 100%;
  background: no-repeat url("../../public/images/tagline-left.png");
}
.tagline:after
{
  content: "";
  position: absolute;
  right: 28vw;
  top: 0.8vw;
 width: 190px;
  height: 100%;
  min-height: 100%;
  background: no-repeat url("../../public/images/tagline-right.png");
}

.tagline:after
{
  content: "";
  position: absolute;
  right: 28vw;
  top: 0.8vw;
 width: 190px;
  height: 100%;
  min-height: 100%;
  background: no-repeat url("../../public/images/tagline-right.png");
}
/* .hideclose svg{ display: none;} */
  .hideclose button.right-4.top-4 svg { display: none; }

.blueBtn {
border: 1px solid var(--Brand-Support-1-500);
background: linear-gradient(90deg, var(--sky-blue-light) 0%, var(--sky-blue-dark) 100%);
color: var(--Interface-Text-white);
}




.join-us-bg {
background-image: url('/Join-Us.svg');
background-size: cover;
background-position: center;
}






/* left menu */
.left-menu-h { height: calc(100vh); transition: width 0.4s; }
.left-menu-h.active { width: 320px; }
.logo { display: none; }
.left-menu-h:hover .logo { display: block; }
.left-menu-h:hover .logo_icon { display: none; }
.left-menu-h:hover .lock_icon { display: none; }
ul.left-menu { padding-top: 5px; }
ul.left-menu li { position: relative; }
ul.left-menu li a{ color: #363A44; font-size: 14px; padding: 0.730vw 0; display: block; }
ul.left-menu li.p-l-15 a{ padding-left: 15px; }

.left-menu-h ul.left-menu>li:hover>a{ background-color: #F3FAF3; border-radius: 8px; }
.left-menu-h ul.left-menu li a span  { min-width: 180px; display: inline-flex; white-space: nowrap;}
[dir='rtl'] .left-menu-h ul.left-menu li a span  { min-width: 50px; display: inline-flex; white-space: nowrap;}
[dir='rtl'] .bottom_menu li a span  { display: flex !important; justify-content: end;} 
.left-menu-h ul.left-menu li a span, .left-menu-h ul.left-menu li a span i { font-size: 0; padding-left: 52px; }

.left-menu-h:hover ul.left-menu li a span, .left-menu-h:hover ul.left-menu li a span i { font-size: 15px; }
.sub-menu { position: fixed; top: 24px; left: 280px; z-index: 99999; display: none; background-color: #F0F4F5; min-width: 234px; height: calc(100vh - 47px); padding: 24px 16px; overflow-y: auto; }
.left-menu li:hover .sub-menu { display: block; }
.left-menu-h .sub-menu li a { padding-left: 15px; }
.left-menu-h ul.left-menu .sub-menu li a:hover{ background-color: white; border-radius: 8px; }

ul.left-menu li ul.sub-menu li a{ padding:0; }
ul.left-menu li.active a { color: #1570EF; }
ul.left-menu li:not(:first-child) { display: block; } /* margin-top: 12px; */
.left-menu-h:hover .sub-menu li:not(:first-child) { margin-top: 0px; }
ul.left-menu li:before { content: ""; position: absolute; left: 16px; top: 50%; transform: translateY(-50%); width: 24px; height: 24px; pointer-events: none;}
.divide { border-top: 1px solid #eeeeee; }
ul.left-menu li.lock:before {  background: no-repeat url("/images/left-menu-icons/lock.svg"); }
ul.left-menu li.ico-menu:before {  background: no-repeat url("/images/left-menu-icons/homenew.svg"); }
ul.left-menu li.ico-business:before {  background: no-repeat url("/images/left-menu-icons/people.svg"); }
ul.left-menu li.ico-sales:before {  background: no-repeat url("/images/left-menu-icons/sales_icon.svg"); }
ul.left-menu li.ico-customer:before {  background: no-repeat url("/images/left-menu-icons/customer_icon.svg"); }
ul.left-menu li.ico-lic:before {  background: no-repeat url("/images/left-menu-icons/lic_icon.svg"); }
ul.left-menu li.ico-billing:before {  background: no-repeat url("/images/left-menu-icons/billing_icon.svg"); }
ul.left-menu li.ico-analytics:before {  background: no-repeat url("/images/left-menu-icons/analytics_icon.svg"); }
ul.left-menu li.ico-security:before {  background: no-repeat url("/images/left-menu-icons/security-user.svg"); }




ul.left-menu li.ico-detailed:before {  background: no-repeat url("/images/left-menu-icons/message.svg"); }
ul.left-menu li.ico-analysis:before {  background: no-repeat url("/images/left-menu-icons/analysis_icon.svg"); }

ul.left-menu li.active.ico-menu:before { background: no-repeat url("/images/left-menu-icons/active_homenew.svg"); }
ul.left-menu li.active.ico-business:before {  background: no-repeat url("/images/left-menu-icons/active_people.svg"); }
ul.left-menu li.active.ico-sales:before {  background: no-repeat url("/images/left-menu-icons/sales_icon_active.svg"); }
ul.left-menu li.active.ico-sales:before {  background: no-repeat url("/images/left-menu-icons/customer_icon_active.svg"); }
ul.left-menu li.active.ico-sales:before {  background: no-repeat url("/images/left-menu-icons/lic_icon_active.svg"); }
ul.left-menu li.active.ico-sales:before {  background: no-repeat url("/images/left-menu-icons/billing_icon_active.svg"); }
ul.left-menu li.active.ico-sales:before {  background: no-repeat url("/images/left-menu-icons/analytics_icon_active.svg"); }
ul.left-menu li.active.ico-security:before {  background: no-repeat url("/images/left-menu-icons/security-userblue.svg"); }


ul.left-menu li.active.ico-detailed:before {  background: no-repeat url("/images/left-menu-icons/active_message.svg"); }
ul.left-menu li.active.ico-analysis:before {  background: no-repeat url("/images/left-menu-icons/active_analysis_icon.svg"); }
ul.left-menu li.active.settings:before {  background: no-repeat url("/images/left-menu-icons/active_settings.svg"); }
ul.left-menu li.ico-folderopen:before {  background: no-repeat url("/images/left-menu-icons/folder-open.svg"); }
ul.left-menu li.ico-folderclose:before {  background: no-repeat url("/images/left-menu-icons/folder-2.svg"); }
ul.left-menu li.ico-chart:before {  background: no-repeat url("/images/left-menu-icons/status-up.svg"); }
ul.left-menu li.ico-report:before {  background: no-repeat url("/images/left-menu-icons/document-text.svg"); }
ul.left-menu li.ico-filter:before {  background: no-repeat url("/images/left-menu-icons/document-filter.svg"); }
ul.left-menu li.settings:before {  background: no-repeat url("/images/left-menu-icons/settings.svg"); }
ul.left-menu li.sitetour:before {  background: no-repeat url("/images/left-menu-icons/lampon.svg"); }
ul.left-menu li.darkMode:before {  background: no-repeat url("/images/left-menu-icons/darkMode.svg"); }
ul.left-menu li.support:before {  background: no-repeat url("/images/left-menu-icons/support-tickets.svg"); }
ul.left-menu li a.supportBg { background-color: #3C4146; color: #FFF; border-radius: 8px; }
ul.left-menu li a.supportBg span { color: #FFF; }
.left-menu-h ul.left-menu.hoverNone>li:hover>a.supportBg { background-color: #3C4146; }
.left-menu-h.active .simplebar-track.simplebar-vertical .simplebar-scrollbar:before, .left-menu-h.active .simplebar-track.simplebar-horizontal .simplebar-scrollbar:before { width: 2px; }
.left-menu-h .simplebar-track.simplebar-vertical .simplebar-scrollbar:before, .left-menu-h .simplebar-track.simplebar-horizontal .simplebar-scrollbar:before { width: 0; }
.profile img { min-width: 48px; }
.left-menu-h ul.left-menu.hoverNone li.userProfile span { padding-left: 10px; }
.left-menu-h .userprofileIcon { display: none; }
.left-menu-h:hover .userprofileIcon { display: block; }
.left-menu-h ul.left-menu.hoverNone li.userProfile .userPic img  { min-width: 45px; height: 45px; border-radius: 100%; }
.shadow1 { box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.03), 0px 4px 6px -1px rgba(0, 0, 0, 0.02); }
/* left menu End*/

.theme-cust{ display: flex !important; align-items: center; margin-left: 0px;padding-left: 10px !important;}
 ul.left-menu li.theme-cust:before{ display: none;}

.left-menu-h ul.left-menu li a.theme-cust span{ padding-left: 0px !important; min-width: 50px;}
ul.left-menu li .theme-cust a{ padding-top: 0; padding-bottom: 0;}
ul.left-menu li a{ color: #363A44; }
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}
.custbadge{
  background-color: #418E4C;
}
.textbg:after
{
  content: "";
  position: absolute;
  left: 0;
  top: -4px;
  width: 100%;
  height: 100%;
  min-height: 100%;
  background: #317a91;
  border-radius: 6px;
  z-index: -1;
  transform: rotate(-1.5deg);
}
.custom_shadow
{
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16) inset;
}
.accordian-styles .border-b{
  border-bottom: none;
}
.accordian-styles .border-b button:hover{
 text-decoration: none;
}

.PhoneInput button,
.PhoneInput input
{
  height: 34px;
  border:1px solid #d1d1d1;
  padding: 6px 12px 6px 12px;
  border-radius: 0;

}
  .leftbar_shadow
  {
    box-shadow: 2px 0px 6px 0px rgba(0, 0, 0, 0.12);
  }



/* nilesh dashboard */
.greenBtn {
  border: 1px solid var(--Brand-Primary-500);
  background: linear-gradient(90deg, var(--Brand-Primary-500) 0%, var(--Brand-Primary-600) 100%);
  color: var(--Interface-Text-white);
}
.greenBtnnew {
  border: 1px solid linear-gradient(90deg, var(--Brand-Primary-500) 0%, var(--Brand-Primary-600) 100%);
  background: linear-gradient(85deg, #59BC49 16.23%, #019049 92.47%);
  color: var(--Interface-Text-white);
}
.greenBtnnew:hover {
  background-color: #02753b;
}
.newGreenBtn {
  border: 1px solid var(--Brand-Primary-800);
  background: var(--Brand-Primary-pure);
}
.dashboard_bg {
  background-image: url('/images/dashboard_bg.png');
  background-repeat: no-repeat;
  min-height: 252px;
}

.videoBg {
  background: linear-gradient(99deg, rgba(19, 62, 89, 0.65) 1.64%, rgba(5, 7, 8, 0.98) 96.39%);
}

.arrowShadow {
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);
}
.openCardShadow {
  box-shadow: 0px 20px 18px -41px rgba(0, 0, 0, 0.10), 0px 10px 10px 0px rgba(0, 0, 0, 0.04);
}

.prevIcon svg {
  display: none;
}
.prevIcon::before {
    position: absolute;
    font-family: 'cloudquark';
    content: "\e981";
    font-size: 16px;
    color: var(--Interface-Text-subtitle);
}
.nextIcon svg {
  display: none;
}
.nextIcon::before {
    position: absolute;
    font-family: 'cloudquark';
    content: "\e97f";
    font-size: 16px;
    color: var(--Interface-Text-subtitle);
}



.profile_silder .prevIcon svg {
  display: none;
}
.profile_silder .prevIcon::before {
    position: absolute;
    font-family: "Roboto", sans-serif;
    content: "Prev";
    font-size: 14px;
    color: var(--Interface-Text-subtitle);
}
.profile_silder .nextIcon svg {
  display: none;
}
.profile_silder .nextIcon::before {
    position: absolute;
    font-family: "Roboto", sans-serif;
    content: "Next";
    font-size: 14px;
   color: var(--Brand-Support-1-pure );
}


.introBanner video {
  height: 298px;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover
}
.carosal_bg {
  background-image: url('/images/carosal_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 300px;
  width: 100%;
  height: 100%;
  min-height: 252px;
}
.progress .bg-primary {
  background-color: #1570EF;
}

/* order steps start */

.customSteps li {
    height: 60px;
    margin-right: 5px;
    display: flex;
    position: relative;
    background: #fff;
    fill: drop-shadow(4px 0px 10px rgba(0, 0, 0, 0.10));
    color: var(--Interface-Text-default);
    font-size: 12px;
}

.customSteps .content {
    display: flex;
    align-items: center;
    padding: 5px 0px 5px 8px;
}

.customSteps li::after {
    content: "";
    display: inline-block;
    background: transparent;
    width: 0;
    height: 0;
    border-top: 30px solid transparent;
    border-bottom: 30px solid transparent;
    right: -14px;
    position: absolute;
    z-index: 1;
}

.customSteps li.current {
    background: linear-gradient(90deg, var(--Brand-Neutral-500, #91A5C3) 0%, var(--Brand-Neutral-600, #768FB5) 100%);
    color: var(--Interface-Text-white);
}
.customSteps li.current::before {
    background: #91A5C3;
}
.customSteps li.current::after {
    border-left: 15px solid #768FB5;
}

/* ------------ */
.customSteps li.next-step {
    background: #FFF;
    color: var(--Interface-Text-default);
}
.customSteps li.next-step::before {
    background: #FFF;
}
.customSteps li.next-step::after {
    border-left: 15px solid #FFF;
}

.customSteps li.prev-step {
    background: #FFF;
    color: var(--Brand-Highlight-600);
}
.customSteps li.prev-step::before {
    background: #FFF;
}
.customSteps li.prev-step::after {
    border-left: 15px solid #FFF;
}
/* ------------ */
/* .customSteps li:first-child::before {
    content: "" !important;
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 25px solid #768FB5;
    border-left: 15px solid #F6F7F8;
    border-bottom: 25px solid #768FB5;
} */
.customSteps li::before {
    content: "";
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 30px solid transparent;
    border-left: 15px solid #F6F7F8;
    border-bottom: 30px solid transparent;
}

.customSteps li::before {
    background: #fff;
}

.customSteps li::after {
    border-left: 25px solid #fff;
}
/* order steps end */

@media (min-width:1900px) {
  .introBanner video {
    height: 17.448vw;
  }

  .carosal_bg {
    background-size: 100% 17.604vw;
  }
  .customSteps li {
    height: 3.125vw;
  }
}


.cancelbtn{ border: 1px solid  #E5E7EB; background: linear-gradient(180deg, #FEFEFE) 0%, #F6F8FA 100%; }
  .ticketcard{
    background: var(--Interface-Surface-component, #FFF);
    padding: 10px 24px 10px 20px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);
  }
.accordion-container h3 button{ padding-top: 0; padding-bottom: 0;}
.accordion-container h3 button:hover{text-decoration: none; }
.tbl-shadow{ box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10); }
.accordion-container h3 button svg{ display: none;}
/* nilesh dashboard */
.arrowcustom svg.lucide-chevron-down{
  margin-left: 6px;
}
.cust-switch .data-\[state\=unchecked\]\:bg-input[data-state="unchecked"],
.cust-switch .data-\[state\=checked\]\:bg-primary[data-state="checked"]{
  background: #1570EF !important;
  width: 40px;
  height: 20px;
}
.cust-switch .data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"]{
    width: 16px;
  height: 16px;
}

.documenttype{
  position: relative;
  height: 24px;
  width: 24px;
}
.documenttype.audio-ic::before{
  position: absolute;
  height: 24px;
  width: 24px;
  content: "";
  background-image: url('/images/audio-square.svg');
  background-repeat: no-repeat;
}

.knowlegde-icons{
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  /* height: 20px;
  width: 20px; */
}
.grid-ic::before{
  position: absolute;
  /* top:8px;left:8px; */
  height: 20px;
  width: 20px;
  content: "";
  background: no-repeat url('/images/category.svg');
}
.grid-ic-active::before{
  position: absolute;
  height: 20px;
  width: 20px;
  content: "";
  background: url('/images/category-active.svg') no-repeat;
}
.table-ic::before{
  position: absolute;
  height: 20px;
  width: 20px;
  content: "";
  background: url('/images/table.svg') no-repeat;
}
.table-ic-active::before{
  position: absolute;
  height: 20px;
  width: 20px;
  content: "";
  background: url('/images/table-active.svg') no-repeat;
}
.row-ic::before{
  position: absolute;
  height: 20px;
  width: 20px;
  content: "";
  background: url('/images/row-vertical.svg') no-repeat;
}
.row-ic-active::before{
  position: absolute;
  height: 20px;
  width: 20px;
  content: "";
  background: url('/images/row-vertical-active.svg') no-repeat;
}
.sticky-shadow-left::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.09), transparent);
  z-index: 20;
}

.submittbtn{
  border-radius: 0px;
  border: 1px solid #067532;
  background: #00953A;
}

.custtable td {
      vertical-align: middle;
    }

    .custtable tr td {
      /* background: var(--Interface-Surface-component-muted); */
      border-radius: 2px;
    }
    /* .custtable tr th {
      background: #FFF;
    } */
    .custtable table {
      border-collapse: separate;
      border-spacing: 0 10px;
      border: none;
    }
    /* .custtable1 .border {
      border-width: 1px;
    } */
     .custtabletd tbody tr td {
      padding: 10px;
      background: #F6F7F8;
     }
     .custtabletd thead tr th {
      background: #FFF;
      height: 0.1rem !important;
     }
     .customtabletd thead tr th {
      background: var(--Interface-Surface-page-muted);
      color: var(--Interface-Text-title);
     }

    .custtable th,
    .custtable td {
      border: none;
    }

        .custprevious svg {
          display: none;
        }

        .custprevious::before {
          position: absolute;
          font-family: 'cloudquark';
          content: "\e981";
          font-size: 16px;
          color: var(--Interface-Text-subtitle);
        }
        .custprevious:hover::before {
          position: absolute;
          font-family: 'cloudquark';
          content: "\e981";
          font-size: 16px;
          color: var(--Brand-Support-1-pure);
        }


        .custnext svg {
          display: none;
        }

        .custnext::before {
          position: absolute;
          font-family: 'cloudquark';
          content: "\e97f";
          font-size: 16px;
          color: var(--Interface-Text-subtitle);
        }
         .custnext:hover::before {
          position: absolute;
          font-family: 'cloudquark';
          content: "\e97f";
          font-size: 16px;
          color: var(--Brand-Support-1-pure);
        }
    .custcheck.data-\[state\=checked\]\:bg-\[\#1570EF\][data-state="checked"]{
      background: #00953A;
      border: 1px solid #00953A;
    }

    .custcheck.border{
      border: 1px solid #BBC1C7;
      background: #fff;
    }

    .custrad button svg.lucide.lucide-circle{
      color: #00953A;
      fill: #00953A
    }

    .signinbtn{
      background:linear-gradient(180deg, var(--Button-Base-Default---1, #FEFEFE) 0%, var(--button-base-default-hover-2, #F6F8FA) 100%);
    }
    .applybtnbg{
      background:linear-gradient(180deg, var(--Button-Base-Default---1, #FEFEFE) 0%, var(--button-base-default-hover-2, #F6F8FA) 100%);
    }
    .applybtnbg:hover{
      background:#ECEFF3;
    }

  .cart-shadow{
    box-shadow: 0px 20px 25px -5px rgba(0, 0, 0, 0.10), 0px 10px 10px 0px rgba(0, 0, 0, 0.04);
  }
  .tooltip-shadow{
    box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 4px -2px rgba(0, 0, 0, 0.05);
  }

.graygradientbtn {
  background: linear-gradient(180deg, var(--Button-Base-Default---1, #FEFEFE) 0%, var(--button-base-default-hover-2, #F6F8FA) 100%);
}
.graygradientbtn:hover 
{
  background: var(--Brand-Neutral-100) !important;
}

.hidearrow svg{
  display: none;
}
.bgcolor{
   background: linear-gradient(180deg, var(--Button-Base-Default---1, #FEFEFE) 0%, var(--button-base-default-hover-2, #F6F8FA) 100%);
}
.bgcolor2{
   background: linear-gradient(0deg, rgba(255, 255, 255, 0.60) 0%, rgba(255, 255, 255, 0.60) 100%), var(--Brand-Primary-pure, #00953A);
}

.custcheckbox .data-\[state\=checked\]\:bg-\[\#1570EF\][data-state="checked"]
 {
    --tw-bg-opacity: 1;
    background-color: var(--Brand-Primary-pure);
}
.custcheckbox .data-\[state\=checked\]\:border-\[\#1570EF\][data-state="checked"]
 {
    --tw-border-opacity: 1;
    border-color: var(--Brand-Primary-pure);
}
.custrediogroup .fill-\[\#1570EF\] {
    fill: var(--Brand-Primary-pure);
}
.custrediogroup .text-\[\#1570EF\] {
    --tw-text-opacity: 1;
    color: var(--Brand-Primary-pure);
}
.remarkbtn{
background: linear-gradient(180deg, var(--Button-Base-Default---1, #FEFEFE) 0%, var(--button-base-default-hover-2, #F6F8FA) 100%);
}
.remarkbtn:hover{
  background: #dbe1e7;
}
.remarkbtn:focus{
  background: #dbe1e7;
}


.custom-hovercard-left {
  left: -147px !important;
  position: fixed !important;
  top: 0px !important; /* Optional - adjust based on your needs */
}
