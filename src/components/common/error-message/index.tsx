'use client';

import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

interface PieChartProps {
  errorCode: number;
  errorMessage: string;
  shortMessage?: string;
}
export default function ErrorMessage({ errorCode, errorMessage, shortMessage }: PieChartProps) {
  return (
    <div className="h-screen flex flex-col justify-center items-center bg-gray-50 px-4 text-center">
      <Image
        src="/images/svg/logo.svg"
        width={400}
        height={80}
        alt="logo"
        className="w-[400px] 3xl:w-[20.833vw] mb-5 lg:mb-8 3xl:mb-[1.667vw]"
      />
      <h1 className="text-[120px] 3xl:text-[6.25vw] font-extrabold text-gray-800 leading-none">
        {errorCode}
      </h1>
      <p className="text-[28px] md:text-[32px] 3xl:text-[1.667vw] font-semibold text-gray-700 mb-4 3xl:mb-[0.833vw]">
        {errorMessage}
      </p>
      <p className="text-[16px] md:text-[18px] 3xl:text-[0.938vw] text-gray-500 max-w-[400px] 3xl:max-w-[20.833vw] mb-6 3xl:mb-[1.25vw]">
        {shortMessage}
      </p>
      <Link
        href="/"
        className="inline-block px-6 3xl:px-[1.25vw] py-3 3xl:py-[0.625vw] bg-[#067532] text-white rounded-lg 3xl:rounded-[0.417vw] text-[16px] 3xl:text-[0.833vw] font-medium hover:bg-[#00953A] transition"
      >
        Go to Homepage
      </Link>
    </div>
  );
}
