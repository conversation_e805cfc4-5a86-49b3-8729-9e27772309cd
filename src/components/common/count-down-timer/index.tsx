'use client';

import React, { useEffect, useRef, useState } from 'react';

type CountdownTimerProps = {
  initialSeconds?: number;
  isActive: boolean; // Controls whether timer runs
  onComplete?: () => void;
  resetKey?: string | number; // When this value changes, timer resets
};

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  initialSeconds = 60,
  isActive,
  onComplete,
  resetKey,
}) => {
  const [timeLeft, setTimeLeft] = useState(initialSeconds);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Reset timer when resetKey changes
  useEffect(() => {
    setTimeLeft(initialSeconds);
    if (timerRef.current) clearInterval(timerRef.current);
  }, [resetKey, initialSeconds]);

  useEffect(() => {
    if (!isActive) {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      return;
    }

    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          timerRef.current = null;
          onComplete?.();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isActive, onComplete]);

  const minutes = String(Math.floor(timeLeft / 60)).padStart(2, '0');
  const seconds = String(timeLeft % 60).padStart(2, '0');

  return (
    <span>
      {minutes}:{seconds}
    </span>
  );
};

export default CountdownTimer;
