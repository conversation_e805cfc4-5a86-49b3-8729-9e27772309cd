'use client';

import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
} from '@redington-gulf-fze/cloudquarks-component-library';

const dropdownItems = [
  'Starts with',
  'Contains',
  'Not contains',
  'Ends with',
  'Equals',
  'Not equals',
  'No Filter',
];

type ColumnHeaderFilterProps = {
  placeholder: string;
  onChange: (value: string) => void;
};

export function ColumnHeaderFilter({ placeholder, onChange }: ColumnHeaderFilterProps) {
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  const handleItemClick = (item: string) => {
    setSelectedItem(item);
  };

  return (
    <div className="flex items-center gap-[10px] xl:gap-[10px] 3xl:gap-[0.573vw]">
      <Input
        placeholder={placeholder}
        className="my-2 w-full h-9 rounded-[2px] border border-InterfaceStrokedefault text-[14px] xl:text-[14px] 3xl:text-[0.729vw] placeholder:font-normal"
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
      />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <i className="cloud-filtericon text-InterfaceTextsubtitle cursor-pointer" />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="bg-background w-[120px] xl:w-[120px] 3xl:w-[6.25vw] mt-[10px] rounded-[4px] xl:rounded-[4px] 3xl:rounded-[0.230vw]"
        >
          {dropdownItems.map((item) => (
            <DropdownMenuItem
              key={item}
              className={`p-0 ${item === 'No Filter' ? 'border-t border-BrandNeutral100' : ''}`}
              onClick={() => handleItemClick(item)}
            >
              <div
                className={`w-full h-full px-3 py-2 rounded flex items-center cursor-pointer ${
                  selectedItem === item ? 'bg-blue-50' : 'hover:bg-BrandNeutral100'
                }`}
              >
                {item}
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
