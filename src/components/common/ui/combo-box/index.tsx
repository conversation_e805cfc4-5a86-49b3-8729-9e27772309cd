'use client';

import * as React from 'react';
import { Check } from 'lucide-react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { cn } from '@/lib/utils/util';

export type Select2Option = {
  value: string;
  label: string;
};

type ComboboxProps = {
  options: (Select2Option | Select2Group)[];
  value: string | string[]; // Can be string or array
  onChange: (value: string | string[]) => void;
  error?: string;
  placeholder?: string;
  disabled?: boolean;
  multiSelect?: boolean; // Enable multi-select
  classNames?: {
    wrapper?: string;
    trigger?: string;
    popoverContent?: string;
    input?: string;
    groupLabel?: string;
    item?: string;
    itemSelectedIcon?: string;
  };
  styles?: {
    wrapper?: React.CSSProperties;
    trigger?: React.CSSProperties;
    popoverContent?: React.CSSProperties;
    input?: React.CSSProperties;
    groupLabel?: React.CSSProperties;
    item?: React.CSSProperties;
    itemSelectedIcon?: React.CSSProperties;
  };
  className?: string; // Additional className for the outermost div
};

export type Select2Group = {
  label: string;
  options: Select2Option[];
};

export function Select2({
  options,
  value,
  onChange,
  error,
  placeholder = 'Select an option...',
  disabled = false,
  multiSelect = false,
  classNames,
  styles,
  className = '',
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);

  const selectedValues = Array.isArray(value) ? value : [value];

  const handleSelect = (selectedValue: string) => {
    if (disabled) return;

    if (multiSelect) {
      const newValue = selectedValues.includes(selectedValue)
        ? selectedValues.filter((val) => val !== selectedValue) // remove
        : [...selectedValues, selectedValue]; // add
      onChange(newValue);
    } else {
      onChange(selectedValue);
      setOpen(false);
    }
  };

  const flatOptions = options.flatMap((opt) => ('options' in opt ? opt.options : [opt]));

  const displayLabel = () => {
    if (!value || (Array.isArray(value) && value.length === 0)) return placeholder;
    if (!multiSelect)
      return flatOptions.find((option) => option.value === value)?.label || placeholder;
    return flatOptions
      .filter((option) => selectedValues.includes(option.value))
      .map((o) => o.label)
      .join(', ');
  };

  return (
    <div className={cn('w-full', classNames?.wrapper)} style={styles?.wrapper}>
      <Popover open={open} onOpenChange={(v: boolean) => !disabled && setOpen(v)}>
        <PopoverTrigger asChild>
          <Button
            variant="dropdownbtn"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className={cn(
              'w-full !font-normal !py-[.37rem] text-blackcolor border',
              error ? 'border-red-500' : 'border-InterfaceStrokehard',
              classNames?.trigger
            )}
            style={styles?.trigger}
          >
            <span
              className={cn(
                !value || (Array.isArray(value) && value.length === 0)
                  ? 'text-InterfaceTextsubtitle'
                  : 'text-blackcolor',
                className
              )}
            >
              {displayLabel()}
            </span>
            {/* <ChevronsUpDown className="ml-auto opacity-50" /> */}
            <i className="cloud-dropdownarrow ml-auto opacity-50 text-InterfaceTexttitle text-[6px] xl:text-[6px] 3xl:text-[0.36vw] font-medium" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn('w-[var(--radix-popover-trigger-width)] p-0', classNames?.popoverContent)}
          style={styles?.popoverContent}
        >
          <Command>
            <CommandInput
              placeholder="Search..."
              className={cn('h-9', classNames?.input)}
              style={styles?.input}
            />
            <CommandList>
              <CommandEmpty>No option found.</CommandEmpty>
              {options.map((groupOrOption, index) => {
                if ('options' in groupOrOption) {
                  return (
                    <CommandGroup key={index}>
                      <div
                        className={cn(
                          'px-2 py-2 text-sm border-b border-gray-300 font-semibold text-gray-400',
                          classNames?.groupLabel
                        )}
                        style={styles?.groupLabel}
                      >
                        {groupOrOption.label}
                      </div>
                      {groupOrOption.options.map((option) => {
                        const isSelected = selectedValues.includes(option.value);
                        return (
                          <CommandItem
                            key={option.value}
                            disabled={disabled}
                            value={option.value}
                            onSelect={() => handleSelect(option.value)}
                            className={cn(classNames?.item)}
                            style={styles?.item}
                          >
                            {option.label}
                            <Check
                              className={cn(
                                'ml-auto',
                                isSelected ? 'opacity-100' : 'opacity-0',
                                classNames?.itemSelectedIcon
                              )}
                              style={styles?.itemSelectedIcon}
                            />
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                  );
                } else {
                  const isSelected = selectedValues.includes(groupOrOption.value);
                  return (
                    <CommandGroup key={index}>
                      <CommandItem
                        key={groupOrOption.value}
                        disabled={disabled}
                        value={groupOrOption.value}
                        onSelect={() => handleSelect(groupOrOption.value)}
                        className={cn(classNames?.item)}
                        style={styles?.item}
                      >
                        {groupOrOption.label}
                        <Check
                          className={cn(
                            'ml-auto',
                            isSelected ? 'opacity-100' : 'opacity-0',
                            classNames?.itemSelectedIcon
                          )}
                          style={styles?.itemSelectedIcon}
                        />
                      </CommandItem>
                    </CommandGroup>
                  );
                }
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
