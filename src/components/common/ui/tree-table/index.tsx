'use client';

import * as React from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getExpandedRowModel,
  ColumnFiltersState,
  SortingState,
  flexRender,
  ColumnDef,
  ExpandedState,
} from '@tanstack/react-table';

import clsx from 'clsx';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Checkbox,
} from '@redington-gulf-fze/cloudquarks-component-library';

type DataTableProps<TData extends { subRows?: TData[] }, TValue> = {
  data: TData[];
  columns: ColumnDef<TData, TValue>[];
  withCheckbox?: boolean;
};

export function TreeTable<TData extends { subRows?: TData[] }, TValue>({
  data,
  columns,
  withCheckbox = false,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [expanded, setExpanded] = React.useState<ExpandedState>({});

  // Optional Checkbox Column
  const checkboxColumn: ColumnDef<TData> = {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(val) => table.toggleAllPageRowsSelected(!!val)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        disabled={!row.getCanSelect()}
        onCheckedChange={(val) => row.toggleSelected(!!val)}
        aria-label="Select row"
        className="mr-[16px] xl:mr-[16px] 2xl:mr-[16px] 3xl:mr-[0.833vw]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 45,
  };

  // Expand Toggle Column
  const expandColumn: ColumnDef<TData> = {
    id: 'expander',
    header: () => null,
    cell: ({ row }) =>
      row.getCanExpand() ? (
        <button
          onClick={row.getToggleExpandedHandler()}
          style={{ paddingLeft: `${row.depth * 16}px` }}
          className="cursor-pointer"
          aria-label={row.getIsExpanded() ? 'Collapse' : 'Expand'}
        >
          <i
            className={clsx(
              'text-[16px] inline-block transition-transform duration-300 ease-in-out',
              'cloud-downarrowcicle',
              row.getIsExpanded() ? 'rotate-0' : '-rotate-90'
            )}
          ></i>
        </button>
      ) : (
        <div style={{ paddingLeft: `${row.depth * 16}px`, width: '1em' }} />
      ),
    enableSorting: false,
    enableHiding: false,
    size: 40,
  };

  const allColumns = withCheckbox
    ? [checkboxColumn, expandColumn, ...columns]
    : [expandColumn, ...columns];

  const table = useReactTable({
    data,
    columns: allColumns,
    state: { sorting, columnFilters, expanded },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onExpandedChange: setExpanded,
    getSubRows: (row) => row.subRows ?? [],
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    enableRowSelection: withCheckbox,
  });

  return (
    <div className="w-full overflow-x-auto">
      <div className="border">
        <Table>
          <TableHeader className="bg-InterfaceStrokesoft">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    style={{ width: header.getSize() }}
                    className={clsx(
                      'px-[12px]',
                      header.column.id === 'select' && 'sticky left-0 z-10 bg-InterfaceStrokesoft'
                    )}
                  >
                    {!header.isPlaceholder &&
                      flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id} className="hover:bg-BrandNeutral100 transition-colors">
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className={clsx(
                      'px-[12px] text-InterfaceTextdefault',
                      cell.column.id === 'select' && 'sticky left-0 z-10 bg-white'
                    )}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
