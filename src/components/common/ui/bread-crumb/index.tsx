'use client';
import {
  B<PERSON><PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';

type BreadcrumbItem = { label: string; href: string };

const BreadcrumbComponent = ({ navItems }: { navItems: BreadcrumbItem[] }) => {
  return (
    <Breadcrumb>
      <BreadcrumbList>
        {navItems?.map((item, index: number) => (
          <React.Fragment key={index}>
            <BreadcrumbItem>
              {index != navItems?.length - 1 && (
                <BreadcrumbLink
                  className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] text-BrandSupport1pure font-[500] "
                  href={item?.href}
                >
                  {item?.label}
                </BreadcrumbLink>
              )}

              {index == navItems?.length - 1 && (
                <BreadcrumbPage className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextsubtitle ">
                  {item?.label}
                </BreadcrumbPage>
              )}
            </BreadcrumbItem>
            {!(index == navItems?.length - 1) && (
              <BreadcrumbSeparator className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] text-BrandSupport1pure font-[500] " />
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadcrumbComponent;
