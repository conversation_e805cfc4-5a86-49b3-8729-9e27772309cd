'use client';

import * as React from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  ColumnFiltersState,
  SortingState,
  flexRender,
  ColumnDef,
} from '@tanstack/react-table';
import clsx from 'clsx';

import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '@redington-gulf-fze/cloudquarks-component-library';

import { Checkbox } from '@redington-gulf-fze/cloudquarks-component-library';

type DataTableProps<TData, TValue> = {
  data: TData[];
  withCheckbox?: boolean;
  columns: ColumnDef<TData, TValue>[];
};

export function DataTable<TData, TValue>({
  data,
  columns,
  withCheckbox = false,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  const checkboxColumn: ColumnDef<TData> = {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="mr-[20px] border-[#b6b6b6] bg-background"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        disabled={!row.getCanSelect()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="border-[#b6b6b6]  bg-background"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 45,
    minSize: 45,
    maxSize: 45,
  };

  const allColumns = withCheckbox ? [checkboxColumn, ...columns] : columns;

  const table = useReactTable({
    data,
    columns: allColumns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableRowSelection: true,
  });

  return (
    <div className="w-full overflow-x-auto">
      <div className="border">
        <Table>
          <TableHeader className="bg-InterfaceStrokesoft">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    style={{ width: header.getSize() }}
                    className={clsx(
                      'relative px-[12px] xl:px-[12px] 3xl:[0.625vw]',
                      header.column.id === 'select' && 'sticky left-0 z-10 bg-InterfaceStrokesoft',
                      header.column.id === 'action' &&
                        'sticky right-0 bg-InterfaceStrokesoft z-10  '
                    )}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                    {header.column.getCanResize() && (
                      <div
                        onMouseDown={header.getResizeHandler()}
                        onTouchStart={header.getResizeHandler()}
                        className="absolute right-0 top-0 h-full w-1 bg-transparent "
                        style={{ touchAction: 'none' }}
                      />
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                className={`group hover:bg-BrandNeutral100 transition-colors duration-150`}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className={clsx(
                      'text-InterfaceTextdefault px-[12px] xl:px-[12px] 3xl:[0.625vw]  ',
                      cell.column.id === 'select' &&
                        'sticky left-0 bg-white z-10 group-hover:bg-BrandNeutral100',
                      cell.column.id === 'action' &&
                        'sticky right-0 bg-white z-10 group-hover:bg-BrandNeutral100'
                    )}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
