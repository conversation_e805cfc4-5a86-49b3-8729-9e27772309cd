'use client';

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export function TablePagination() {
  const t = useTranslations();
  return (
    <div className="flex items-center justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 3xl:py-[0.333vw] w-full border-b border-y-[2px] border-t-none rounded-bl-1 rounded-br-1 shadow-sm shadow-InterfaceStrokesoft">
      <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
        Page{' '}
        <span className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
          2
        </span>
        -
        <span className="  text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
          10
        </span>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex-nowrap text-InterfaceTextdefault">
            {t('itemPerPage')}:
          </span>
          <Select>
            <SelectTrigger className="h-8 w-16 px-2 py-1 rounded-[2px] border border-InterfaceStrokesoft placeholder:text-InterfaceTextdefault">
              <SelectValue placeholder="10 " className=" placeholder:text-InterfaceTextdefault" />
            </SelectTrigger>
            <SelectContent className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault bg-white z-50 cursor-pointer rounded-[2px] ">
              <SelectItem value="10" className="  ">
                10
              </SelectItem>
              <SelectItem value="20" className="">
                20
              </SelectItem>
              <SelectItem value="50" className="">
                50
              </SelectItem>
              <SelectItem value="100" className="">
                100
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <div>
                  <i className="cloud-previous text-[12px] xl:text-[12px] 3xl:text-[0.629vw] text-InterfaceTextdefault"></i>
                </div>
              </PaginationItem>

              <PaginationItem className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                <PaginationLink href="#" size="sm">
                  1
                </PaginationLink>
              </PaginationItem>
              <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                <PaginationLink href="#" size="sm" className="text-BrandSupport1pure">
                  2
                </PaginationLink>
              </PaginationItem>
              <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                <PaginationLink href="#" size="sm">
                  3
                </PaginationLink>
              </PaginationItem>
              <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                <PaginationEllipsis />
              </PaginationItem>
              <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                <PaginationLink href="#" size="sm">
                  99
                </PaginationLink>
              </PaginationItem>
              <div>
                <i className="cloud-next  text-[12px] xl:text-[12px] 3xl:text-[0.629vw] text-InterfaceTextdefault"></i>
              </div>
              <PaginationItem></PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>
    </div>
  );
}
