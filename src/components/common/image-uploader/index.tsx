'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';

interface ImageUploaderProps {
  onImageUpload: (file: File) => void;
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];

export const ImageUploader: React.FC<ImageUploaderProps> = ({ onImageUpload }) => {
  const t = useTranslations();
  const [error, setError] = useState<string | null>(null);
  const [showBorder, setShowBorder] = useState<boolean>(false);

  const handleFile = (file: File) => {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      setError('Only JPG, JPEG, and PNG files are allowed');
      return;
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      setError('File size must be less than 5MB');
      return;
    }

    setError(null);
    onImageUpload(file);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) handleFile(file);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault(); // ✅ Prevent default file open
    setShowBorder(false);
    const file = e.dataTransfer.files?.[0];
    if (file) handleFile(file);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault(); // ✅ Required to allow drop
    setShowBorder(true);
  };

  return (
    <div
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      className={`${showBorder && 'border border-dashed'} text-start p-5 border-gray-400 rounded cursor-pointer bg-white`}
    >
      <input
        type="file"
        accept=".jpg,.jpeg,.png"
        onChange={handleFileChange}
        className="hidden"
        id="fileInput"
      />
      <label htmlFor="fileInput" className="cursor-pointer">
        <div className="text-blue-500 font-semibold text-xs">{t('clickToUploadOrDragAndDrop')}</div>
        <div className="text-gray-400 font-semibold text-xs">JPG, JPEG or PNG (Max 5MB)</div>
        {error && <p className="text-red-500 font-semibold text-xs mt-2">{error}</p>}
      </label>
    </div>
  );
};
