import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { removeFilterKey, clearFilters } from '@/store/slices/filters/filter-slice';
import { format } from 'date-fns';

interface FilterConfig {
  key: string;
  label: string;
  isDate?: boolean;
}

interface Props {
  moduleKey: string;
  filterConfig: FilterConfig[];
}

export default function AppliedFilters({ moduleKey, filterConfig }: Props) {
  const dispatch = useDispatch();
  const moduleState = useSelector((state: RootState) => state.filters[moduleKey]);
  const filters = moduleState?.filters || {};

  const activeFilters = filterConfig
    .filter(({ key }) => {
      const value = filters[key];
      if (value === undefined || value === null) return false;
      if (typeof value === 'object' && 'value' in value && !value.value) return false;
      return true;
    })
    .map(({ key, label, isDate }) => {
      const value = filters[key];

      let displayValue: string = '';

      if (isDate && value) {
        displayValue = format(new Date(value as Date), 'dd-MM-yyyy');
      } else if (typeof value === 'object' && value !== null && 'label' in value) {
        displayValue = (value as { label: string }).label;
      } else {
        displayValue = String(value);
      }

      return { key, label, displayValue };
    });

  if (activeFilters.length === 0) return null;

  return (
    <div className="overflow-x-auto whitespace-nowrap flex items-center justify-start px-[14px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] gap-2 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
      {activeFilters.map(({ key, label, displayValue }) => (
        <div
          key={key}
          className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]"
        >
          <span className="text-InterfaceTextsubtitle">{label} :</span> {displayValue}
          <i
            onClick={() => dispatch(removeFilterKey({ moduleKey: moduleKey, key }))}
            className="cloud-closecircle text-ClearAll cursor-pointer"
          ></i>
        </div>
      ))}

      <div
        onClick={() => dispatch(clearFilters(moduleKey))}
        className="text-ClearAll py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] cursor-pointer"
      >
        <i className="cloud-closecircle text-ClearAll"></i> Clear All
      </div>
    </div>
  );
}
