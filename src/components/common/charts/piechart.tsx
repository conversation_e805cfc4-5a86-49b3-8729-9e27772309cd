'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { EChartsOption, PieSeriesOption } from 'echarts';

interface PieChartProps {
  legends?: EChartsOption['legend'];
  name?: string;
  radius?: PieSeriesOption['radius'];
  center?: PieSeriesOption['center'];
  itemstyle?: PieSeriesOption['itemStyle'];
  rosetype?: PieSeriesOption['roseType'];
  labelline?: PieSeriesOption['labelLine'];
  label?: PieSeriesOption['label'];
  data?: PieSeriesOption['data'];
  title?: EChartsOption['title'];
  graphic?: EChartsOption['graphic'];
  tooltip?: EChartsOption['tooltip'];
}

export default function Piechart({
  legends,
  name,
  radius,
  center,
  itemstyle,
  rosetype,
  labelline,
  label,
  data,
  title,
  graphic,
  tooltip,
}: PieChartProps) {
  const option: EChartsOption = {
    title: title,
    legend: legends,
    tooltip: tooltip,
    series: [
      {
        name,
        type: 'pie',
        radius,
        center,
        roseType: rosetype,
        itemStyle: itemstyle,
        labelLine: labelline,
        label,
        data,
      },
    ],
    graphic,
  };

  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
