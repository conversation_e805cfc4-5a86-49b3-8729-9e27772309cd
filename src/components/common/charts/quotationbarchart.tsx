'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { EChartsOption } from 'echarts';
import { useTranslations } from 'next-intl';
export default function QuotationBarchart() {
  const t = useTranslations();
  const option: EChartsOption = {
    grid: {
      top: 15,
      right: 0,
      bottom: 0,
      left: 14,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [
        'new',
        'approved',
        'rejected',
        'accepted',
        'declined',
        'canceled',
        'in-review',
        'expired',
        'redeemed',
      ].map((item) => item),
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisLabel: {
        color: '#7F8488',
        fontSize: 9,
        rotate: 30, // 👈 this helps avoid overlaps
        interval: 0, // 👈 ensures no labels are skipped
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      name: t('countOfQuotes'),
      nameLocation: 'middle',
      nameTextStyle: {
        fontSize: 8,
        color: '#7F8488',
      },
      nameGap: 30,
      min: 0,
      max: 100,
      interval: 25,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E5E7EB',
          type: 'dashed',
        },
      },
      axisLabel: {
        color: '#7F8488',
        fontSize: 9,
      },
    },
    series: [
      {
        data: [
          { value: 75, itemStyle: { color: '#3379CC' } },
          { value: 60, itemStyle: { color: '#00953A' } },
          { value: 45, itemStyle: { color: '#D42600' } },
          { value: 60, itemStyle: { color: '#72B35F' } },
          { value: 25, itemStyle: { color: '#FFB5A5' } },
          { value: 50, itemStyle: { color: '#768FB5' } },
          { value: 75, itemStyle: { color: '#FACE4F' } },
          { value: 57, itemStyle: { color: '#A9B9D0' } },
          { value: 50, itemStyle: { color: '#90D1FF' } },
        ],
        type: 'bar',
      },
    ],
  };

  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
