'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { useTranslations } from 'next-intl';

export default function Barchart() {
  const t = useTranslations();

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '10%',
      right: '0%',
      bottom: '3%',
      top: '25%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['0-30 Days', '31-60 Days', '61-90 Days', '>91 Days'],
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    yAxis: [
      {
        name: 'Count Of Subscriptions',
        nameLocation: 'center',
        nameGap: 20,
        nameTextStyle: {
          fontSize: 9, // <-- Font size
          color: '#7F8488', // Optional: font color
          padding: [0, 0, 10, 0], // Optional: spacing from axis
        },
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    series: [
      {
        name: t('direct'),
        type: 'bar',
        barWidth: '70%',
        data: [80, 65, 48, 65],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#1570EF' }, // Top color
            { offset: 1, color: '#5CB6FE' }, // Bottom color
          ]),
          borderRadius: [2, 2, 0, 0],
        },
      },
    ],
  };
  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
