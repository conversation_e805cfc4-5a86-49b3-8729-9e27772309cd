'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { useTranslations } from 'next-intl';

export default function DealsRedeemedBarchart() {
  const t = useTranslations();
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '5%',
      right: '0%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['Customer 1', 'Customer 2', 'Customer 3', 'Customer 4'],
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 8,
          interval: 0,
        },
      },
    ],
    yAxis: [
      {
        name: `${t('overdue')} (USD , k)`,
        nameLocation: 'center',
        nameGap: 14,
        nameTextStyle: {
          fontSize: 8, // <-- Font size
          color: '#7F8488', // Optional: font color
          padding: [0, 0, 15, 0], // Optional: spacing from axis
        },
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    series: [
      {
        name: 'Direct',
        type: 'bar',
        barWidth: '60%',
        data: [80, 65, 48, 65, 75],
        itemStyle: {
          color: '#F2980E',
          borderRadius: [3, 3, 0, 0],
        },
      },
    ],
  };
  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
