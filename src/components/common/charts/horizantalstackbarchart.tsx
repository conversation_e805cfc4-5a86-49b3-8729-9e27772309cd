'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
import {
  EChartsOption,
  BarSeriesOption,
  XAXisComponentOption,
  YAXisComponentOption,
  GridComponentOption,
} from 'echarts';

interface HorizantalStackBarChartProps {
  data1: number[];
  color11: string; // Gradient start
  color12: string; // Gradient end
  data2: number[];
  color2: string;
  grid?: GridComponentOption;
  xaxislabel?: XAXisComponentOption['axisLabel'];
  xaxisline?: XAXisComponentOption['axisLine'];
  xaxisspliteline?: XAXisComponentOption['splitLine'];
  yaxislabel?: YAXisComponentOption['axisLabel'];
  yaxisline?: YAXisComponentOption['axisLine'];
  yaxistick?: YAXisComponentOption['axisTick'];
}

export default function Horizantalstackbarchart({
  data1,
  color11,
  color12,
  data2,
  color2,
  grid,
  xaxislabel,
  xaxisline,
  xaxisspliteline,
  yaxisline,
  yaxislabel,
  yaxistick,
}: HorizantalStackBarChartProps) {
  const series: BarSeriesOption[] = [
    {
      data: data1,
      type: 'bar',
      stack: 'a',
      itemStyle: {
        borderRadius: [0, 0, 0, 0],
      },
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: color11 },
        { offset: 1, color: color12 },
      ]),
      name: 'a',
    },
    {
      data: data2,
      type: 'bar',
      stack: 'a',
      itemStyle: {
        borderRadius: [0, 4, 4, 0],
      },
      color: color2,
      name: 'b',
    },
  ];

  const option: EChartsOption = {
    grid: grid,
    xAxis: {
      type: 'value',
      axisLabel: xaxislabel,
      axisLine: xaxisline,
      splitLine: xaxisspliteline,
    },
    yAxis: {
      type: 'category',
      data: ['Mon'],
      axisLine: yaxisline,
      axisLabel: yaxislabel,
      axisTick: yaxistick,
    },
    series: series,
  };

  return (
    <ReactEcharts echarts={echarts} option={option} style={{ width: '100%', height: '100%' }} />
  );
}
