import React from 'react';
import ReactEcharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';

interface HorizontalBarChartProps {
  legend?: EChartsOption['legend'];
  grid?: EChartsOption['grid'];
  min?: EChartsOption['min'];
  max?: EChartsOption['max'];
  xAxisSplitLine?: EChartsOption['xAxisSplitLine'];
  yAxisdata2?: EChartsOption['yAxisdata2'];
  yAxisdata?: EChartsOption['yAxisdata'];
  xAxisLabel?: EChartsOption['xAxisLabel'];
  yAxisLabel2?: EChartsOption['yAxisLabel2'];
  yAxisLine?: EChartsOption['yAxisLine'];
  yAxisLine2?: EChartsOption['yAxisLine2'];
  yAxisLabel?: EChartsOption['yAxisLabel'];
  yAxisTick?: EChartsOption['yAxisTick'];
  itemStyle?: EChartsOption['itemStyle'];
  showBackground?: EChartsOption['showBackground'];
  barWidth?: EChartsOption['barWidth'];
  backgroundStyle?: EChartsOption['backgroundStyle'];
  name?: string;
  label?: EChartsOption['label'];
  data?: EChartsOption['data'];
}

export default function HorizontalBarChart({
  legend,
  grid,
  min = 0,
  max = 100,
  xAxisSplitLine,
  yAxisdata2,
  data,
  xAxisLabel,
  yAxisLabel2,
  yAxisdata,
  yAxisLine,
  yAxisLine2,
  yAxisLabel,
  yAxisTick,
  label,
  itemStyle,
  name,
  showBackground,
  barWidth,
  backgroundStyle,
}: HorizontalBarChartProps) {
  const barchart = {
    legend: legend,
    grid: grid,
    xAxis: {
      type: 'value',
      min: min,
      max: max,
      axisPointer: {
        type: 'shadow',
        min: 0,
        max: 100,
      },
      axisLabel: xAxisLabel,
      splitLine: xAxisSplitLine,
    },
    yAxis: [
      {
        type: 'category',
        axisLabel: yAxisLabel,
        axisTick: yAxisTick,
        axisLine: yAxisLine,
        data: yAxisdata,
      },
      {
        type: 'category',
        axisLabel: yAxisLabel2,
        axisTick: {
          show: false,
        },
        axisLine: yAxisLine2,

        data: yAxisdata2,
      },
    ],
    series: [
      {
        name: name,
        barWidth: barWidth,
        showBackground: showBackground,
        backgroundStyle: backgroundStyle,
        type: 'bar',
        label: label,
        itemStyle: itemStyle,
        data: data,
      },
    ],
  };

  return <ReactEcharts option={barchart} style={{ width: '100%', height: '100%' }} />;
}
