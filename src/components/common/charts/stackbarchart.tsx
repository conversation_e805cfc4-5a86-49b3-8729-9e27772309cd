'use client';
import React from 'react';
import * as echarts from 'echarts';
import ReactEcharts from 'echarts-for-react';
import type { EChartsOption } from 'echarts';

type StackbarchartProps = {
  grid?: {
    left?: number | string;
    right?: number | string;
    top?: number | string;
    bottom?: number | string;
  };
  legends?: {
    show?: boolean;
    left?: string | number;
    bottom?: string | number;
    itemWidth?: number;
    itemHeight?: number;
    textStyle?: {
      fontSize?: number;
      color?: string;
    };
  };
  data?: string[]; // x-axis categories
  xaxistick?: {
    show?: boolean;
  };
  xaxisline?: {
    show?: boolean;
    lineStyle?: {
      color?: string;
    };
  };
  xaxislabel?: {
    color?: string;
    fontSize?: number;
  };
  yaxisname?: string;
  yaxislocation?: 'start' | 'middle' | 'end';
  yaxisnamegap?: number;
  min?: number;
  max?: number;
  yaxislabel?: {
    color?: string;
    fontSize?: number;
  };
  yaxissplitline?: {
    show?: boolean;
    lineStyle?: {
      color?: string;
      type?: 'solid' | 'dashed' | 'dotted';
    };
  };
  data1?: number[];
  data2?: number[];
  color1?: string;
  color2?: string;
  color3?: string;
  color4?: string;
  stackname1?: string;
  stackname2?: string;
  yaxisnametextstyle?: {
    fontSize?: number;
    color?: string;
    fontWeight?: 'normal';
  };
};

export default function Stackbarchart({
  grid,
  legends,
  data,
  xaxistick,
  xaxisline,
  xaxislabel,
  yaxisname,
  yaxislocation,
  yaxisnamegap,
  min,
  max,
  yaxislabel,
  yaxissplitline,
  data1,
  data2,
  color1,
  color2,
  color3,
  color4,
  stackname1,
  stackname2,
  yaxisnametextstyle,
}: StackbarchartProps) {
  const series: EChartsOption['series'] = [
    {
      data: data1 ?? [],
      type: 'bar',
      name: stackname1 ?? '',
      stack: 'a',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: color1 ?? '#1570EF' },
          { offset: 1, color: color2 ?? '#3696FB' },
        ]),
      },
    },
    {
      data: data2 ?? [],
      type: 'bar',
      name: stackname2 ?? '',
      stack: 'a',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: color3 ?? '#F8B720' },
          { offset: 1, color: color4 ?? '#FACE4F' },
        ]),
        borderRadius: [3, 3, 0, 0],
      },
    },
  ];

  const option: EChartsOption = {
    grid: grid,
    legend: legends,
    xAxis: {
      type: 'category',
      data: data ?? [],
      axisTick: xaxistick,
      axisLine: xaxisline,
      axisLabel: xaxislabel,
    },
    yAxis: {
      type: 'value',
      name: yaxisname,
      nameLocation: yaxislocation,
      nameGap: yaxisnamegap,
      min: min,
      max: max,
      axisLabel: yaxislabel,
      splitLine: yaxissplitline,
      nameTextStyle: yaxisnametextstyle,
    },
    series: series,
  };

  return (
    <ReactEcharts echarts={echarts} option={option} style={{ width: '100%', height: '100%' }} />
  );
}
