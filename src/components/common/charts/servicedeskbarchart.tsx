'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
interface ServiceDeskBarchartProps {
  xaxisdata: string[];
  yaxisdata: string[];
  color1: string;
}
export default function ServiceDeskBarchart({
  xaxisdata,
  yaxisdata,
  color1,
}: ServiceDeskBarchartProps) {
  const option = {
    // tooltip: {
    //   trigger: 'axis',
    //   axisPointer: {
    //     type: 'shadow',
    //   },
    // },
    legend: {
      orient: 'horizontal',
      left: 'left',
      bottom: -4,
      icon: 'rect',
      itemWidth: 8,
      itemHeight: 2,
      textStyle: {
        color: '#7F8488',
        fontSize: 10,
        fontWeight: 500,
      },
      data: [
        {
          name: 'Ticket',
          icon: 'rect',
        },
      ],
    },

    grid: {
      left: '12%',
      right: '0%',
      bottom: '22%',
      top: '4%',
      containLabel: true,
    },
    xAxis: [
      {
        name: 'Categories',
        nameLocation: 'center',
        nameGap: 22,
        nameTextStyle: {
          fontSize: 8,
          color: '#7F8488',
          padding: [0, 0, 10, 0],
        },
        type: 'category',
        data: xaxisdata,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 8,
          interval: 0,
        },
      },
    ],
    yAxis: [
      {
        name: 'Count Of Subscriptions',
        nameLocation: 'center',
        nameGap: 20,
        nameTextStyle: {
          fontSize: 8,
          color: '#7F8488',
          padding: [0, 0, 10, 0],
        },
        type: 'value',
        min: 0,
        max: 800,
        interval: 200,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 9,
        },
      },
    ],
    series: [
      {
        name: 'Ticket',
        type: 'bar',
        barWidth: '70%',
        data: yaxisdata,
        itemStyle: {
          color: color1,
          borderRadius: [2, 2, 0, 0],
        },
        label: {
          show: true,
          position: 'insideTop',
          color: '#FFFFFF',
          fontSize: 10,
        },
      },
    ],
  };
  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
