'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';

export default function Barchart2() {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '3%',
      top: '5%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['Brand 1', 'Brand 2', 'Brand 3', 'Brand 4', 'Brand 5'],
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    yAxis: [
      {
        name: 'Count Of Contracts',
        nameLocation: 'center',
        nameGap: 20,
        nameTextStyle: {
          fontSize: 9, // <-- Font size
          color: '#7F8488', // Optional: font color
          padding: [0, 0, 10, 0], // Optional: spacing from axis
        },
        type: 'value',
        min: 0,
        max: 800,
        interval: 200,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    series: [
      {
        name: 'Direct',
        type: 'bar',
        barWidth: '70%',
        data: [650, 520, 400, 520, 100],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#73609B' }, // Top color
            { offset: 1, color: '#9380BB' }, // Bottom color
          ]),
          borderRadius: [2, 2, 0, 0],
        },
      },
    ],
  };
  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
