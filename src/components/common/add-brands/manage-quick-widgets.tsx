'use client';
import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

export function AddBrands() {
  const providers = [
    'Oracle',
    'Microsoft',
    'AWS',
    'Google',
    'Oracles',
    'Brand 0',
    'Brand One',
    'Brand Two',
    'Brand 3',
    'Brand 4',
    'Brand 5',
    'Brand 6',
    'Brand Seven',
    'Brand 8',
    'Brand Nine',
    'Brand 10',
    'Brand 11',
    'Brand Twelve',
    'Brand 13',
    'Brand 14',
  ];
  const [activeStates, setActiveStates] = useState<Record<string, boolean>>({});
  const handleClick = (label: string) => {
    setActiveStates((prev) => ({
      ...prev,
      [label]: !prev[label],
    }));
  };
  const t = useTranslations();

  const [popoverOpen, setPopoverOpen] = useState(false);
  const handleUpdate = () => {
    setPopoverOpen(false);
  };
  const handleCancel = () => {
    setPopoverOpen(false);
  };
  const handleReset = () => {
    const resetStates = Object.fromEntries(
      Object.keys(activeStates).map((provider) => [provider, false])
    );
    setActiveStates(resetStates);
  };
  return (
    <>
      <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            id="addBrands"
            className=" relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:font-[500] text-[#3C4146] bg-transparent border border-[#E5E7EB] rounded-none text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          >
            <i className="cloud-Add text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
            {t('addBrands')}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="absolute top-0 left-[-52px] xl:left-[-55px] 2xl:left-[-63px] 3xl:left-[-68px] p-0  w-[460px] xl:w-[475px] 2xl:w-[540px] 3xl:w-[27.91vw] rounded-none bg-[#FFF]">
          <div>
            <div className="border-b border-[#E5E7EB] flex justify-between items-center px-[18px] xl:px-[18px] 2xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw]">
              <h4 className="text-[#212325] text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-medium leading-none">
                {t('manageQuickWidgets')}
              </h4>
            </div>
            <div className="p-[18px] xl:p-[18px] 2xl:p-[20px] 3xl:p-[1.042vw]">
              <div className="flex flex-col gap-[10px] 3xl:gap-[0.525vw]">
                <div className="flex flex-wrap items-center gap-[10px] 3xl:gap-[0.525vw]">
                  {providers.map((provider) => {
                    const isActive = activeStates[provider];

                    return (
                      <Button
                        key={provider}
                        onClick={() => handleClick(provider)}
                        className={`${
                          isActive
                            ? 'bg-[#1F2A37] border border-[#4A4C4E] text-white'
                            : 'text-InterfaceTextdefault border border-InterfaceStrokesoft bg-InterfaceSurfacecomponent'
                        }  px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.525vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-[4px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]`}
                      >
                        <i
                          className={`${
                            isActive ? 'cloud-fillcircletick' : 'cloud-circletick'
                          } ${isActive ? 'text-white' : 'text-[#3C4146]'} text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]`}
                        />
                        {isActive ? 'Category' : provider}
                      </Button>
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="flex justify-end items-center gap-[8px] 3xl:gap-[0.417vw] px-[18px] xl:px-[18px] 2xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw] border-t border-[#BECDE3]">
              <Button
                onClick={handleCancel}
                className="text-[#3C4146] bg-transparent border border-[#E5E7EB] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"
              >
                <i className="cloud-circletick text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('cancel')}
              </Button>
              <Button
                onClick={handleReset}
                className="text-[#3C4146] bg-transparent border border-[#E5E7EB] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"
              >
                <i className="cloud-brush1 text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('reset')}
              </Button>
              <Button
                onClick={handleUpdate}
                className="border border-[#65B6FF] text-[#FFF] bg-[linear-gradient(90deg,_#65B6FF_0%,_#3798FA_100%)] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"
              >
                <i className="cloud-fillcircletick text-[#fff] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('update')}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
