'use client';

import React, { useEffect, useState } from 'react';

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { regionService } from '@/lib/services/api';
import { isApiResponseError } from '@/lib/utils/util';
import { logger } from '@/lib/utils/logger';
import { NETWORK_STATUS } from '@/lib/enums';
import { localStorageService } from '@/lib/services/storage';
import { RegionType } from '@/types';

export const RegionSelector: React.FC = () => {
  const [regionList, setRegionList] = useState<RegionType[]>([]);
  const [selectedRegion, setSelectedRegion] = useState<string | undefined>(undefined);

  useEffect(() => {
    getRegions();
  }, []);

  const getRegions = async () => {
    try {
      const response = await regionService.getRegions();
      if (response?.status == NETWORK_STATUS.SUCCESS) {
        const regions = response?.data as RegionType[];
        setRegionList(regions);

        const savedRegion: string | null = await localStorageService.get('region');

        if (savedRegion && regions.find((r) => r.id === savedRegion)) {
          setSelectedRegion(savedRegion);
        } else if (regions.length > 0) {
          setSelectedRegion(regions[0].id);
          await localStorageService.set('region', regions[0].id);
        }
      }
    } catch (error) {
      if (isApiResponseError(error)) {
        logger.error('Error:', error?.data?.message);
      }
    }
  };

  const handleRegionChange = async (value: string) => {
    setSelectedRegion(value);
    await localStorageService.set('region', value);
  };

  return (
    <>
      {regionList?.length > 0 && (
        <Select value={selectedRegion} onValueChange={handleRegionChange} defaultValue="india">
          <SelectTrigger className="bg-transparent text-interfacetextinverse placeholder-text-sm  rounded-none border-BrandNeutral text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw]">
            <i className="cloud-world mr-[5px]"></i>
            <SelectValue placeholder="Country" className="" />
          </SelectTrigger>
          <SelectContent className="rounded-none bg-overlaybg border-none">
            <SelectGroup className="text-interfacetextinverse  text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
              {regionList?.map((region: RegionType) => (
                <SelectItem key={region?.id} value={region?.id}>
                  {region?.name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      )}
    </>
  );
};
