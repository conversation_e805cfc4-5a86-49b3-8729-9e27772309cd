'use client';

import React from 'react';
import { Robot<PERSON> } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

// Type for Step
interface Step {
  id: number;
  title: string;
  currentStatus?: boolean;
  currentActiveStatus?: boolean;
  icon?: string;
}

// Props for the Page component
interface PageProps {
  currentStepId: number;
  curret_status?: string;
}

// Helper function to truncate text
const truncateText = (text: string | undefined, maxLength: number): string => {
  return text && text.length > maxLength ? text.slice(0, maxLength) + '...' : text || '';
};

export default function OrderStatus({ currentStepId }: PageProps) {
  const t = useTranslations();
  const steps: Step[] = [
    { id: 1, title: t('manageCart'), icon: 'cloud-cart' },
    { id: 2, title: t('checkOut'), icon: 'cloud-shopping' },
    { id: 3, title: t('orderReview'), icon: 'cloud-receipt1' },
    { id: 4, title: t('orderPlacement'), icon: 'cloud-receipt-text' },
  ];

  const updatedSteps: Step[] = steps.map((step) => {
    if (step.id === currentStepId) {
      // Mark the exact step as current
      return {
        ...step,
        currentStatus: true,
        currentActiveStatus: true,
      };
    }
    if (step.id < currentStepId) {
      // Mark the previous steps as active
      return { ...step, currentActiveStatus: true };
    }
    return step;
  });

  return (
    <div className={roboto.className}>
      <div className="flex items-center flex-wrap gap-[12px]">
        {/* Step Progress Section */}
        <div className="customSteps relative w-full">
          <ul className="flex items-center justify-between">
            {updatedSteps.map((step, index) => {
              const isCurrectStepBlack = currentStepId - 1;
              const className =
                isCurrectStepBlack == index
                  ? 'current'
                  : isCurrectStepBlack > index
                    ? 'prev-step'
                    : 'next-step';

              return (
                <li key={step.id} className={`w-full ${className}`}>
                  <div className="content gap-[16px] 3xl:gap-[0.833vw]">
                    <div>
                      {isCurrectStepBlack > index ? (
                        <i className="cloud-fillcircletick text-BrandHighlightpure text-[18px] 3xl:text-[0.938vw]"></i>
                      ) : (
                        <i className={`${step.icon} text-[18px] 3xl:text-[0.938vw]`}></i>
                      )}
                    </div>
                    <div>
                      <div
                        className={`${isCurrectStepBlack == index ? '' : isCurrectStepBlack > index ? 'text-InterfaceTextsubtitle' : 'text-InterfaceTextsubtitle'} font12 font-normal leading-[140%]`}
                      >
                        Step {step.id}
                      </div>
                      <div className="font16 font-medium leading-[140%]">
                        {truncateText(step.title, 15)}
                      </div>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
}
