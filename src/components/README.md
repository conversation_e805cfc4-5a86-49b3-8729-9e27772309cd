# Components Directory

This directory contains reusable UI components that can be shared across the application.

## Purpose

- Shared UI components
- Layout components
- Form elements
- UI patterns

## Structure

```
components/
├── common/             # Common UI components
│   ├── Button/
│   ├── Input/
│   └── Card/
├── forms/              # Form-related components
│   ├── TextField/
│   └── Select/
├── layout/             # Layout components
│   ├── Header/
│   └── Footer/
└── ui/                # Complex UI components
    ├── Modal/
    └── DataTable/
```

## Best Practices

1. Create a directory for each component with its related files:
   ```
   Button/
   ├── Button.tsx
   ├── Button.test.tsx
   └── Button.module.css
   ```
2. Use TypeScript interfaces for props
3. Implement proper prop validation
4. Write unit tests for components
5. Use CSS modules or Tailwind for styling
6. Keep components focused and single-responsibility
7. Document component usage with comments or Storybook
