'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import React from 'react';

import 'simplebar-react/dist/simplebar.min.css';

import {
  Input,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';

import { SaleContact } from '@/components/layout/point-of-contact';
import { Settingpopup } from '@/components/layout/settings-popup';
import { Profileoverlay } from '@/components/layout/profile-overlay';
import { HelpSupport } from '@/components/layout/help-support';
import OnBoardingPopup from '@/modules/partner-registration/components/onboarding-popup';
import { AddBrands } from '@/components/common/add-brands/manage-quick-widgets';
import { NotificationPopup } from '../notification';
import { TourStep } from '@/types/components/index';
import StartTour from '../sitetour-popup/index';
import { SiteTour } from '../sitetour/index';
import { ComapnyPopover } from '../comapny-popover';
import { useTranslations } from 'next-intl';

import { getDirection } from '@/lib/utils/util';
import { useLocale } from 'next-intl';

const steps: TourStep[] = [
  {
    id: 'step1',
    title: 'Onboarding',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'Onboarding',
    position: 'left',
  },
  {
    id: 'step2',
    title: 'Help and Support',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'helpandsupport',
    position: 'left',
  },
  {
    id: 'step3',
    title: 'Explore all Products',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'marketPlace',
    position: 'right',
  },
  {
    id: 'step4',
    title: 'Add Brands',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'addBrands',
    position: 'right',
  },
];

export default function Header() {
  const pathname = usePathname();
  const locale = useLocale();
  const direction = getDirection(locale); // 'ltr' or 'rtl'
  const isRTL = direction === 'rtl';

  const [startTour, setStartTour] = React.useState(false);
  const [showTour, setShowTour] = React.useState(false);
  const t = useTranslations();

  return (
    <header dir={direction}>
      <div
        className={`grid grid-cols-12 gap-[14px] 3xl:gap-[0.729vw] bg-white py-[16px] 3xl:py-[0.833vw] ${
          pathname === '/becomeavendor' ||
          pathname === '/account/partner-registration' ||
          pathname === '/order/feedback'
            ? ''
            : isRTL
              ? 'pr-[110px] xl:pr-[110px] 3xl:pr-[5.729vw] pl-4'
              : 'pl-[110px] xl:pl-[110px] 3xl:pl-[5.729vw] pr-4'
        }`}
      >
        {/* Left */}
        <div className="col-span-12 xl:col-span-7 2xl:col-span-7 3xl:col-span-7">
          {pathname === '/becomeavendor' || pathname === '/account/partner-registration' ? (
            <div className={`${isRTL ? 'pr-[32px]' : 'pl-[32px]'} 3xl:pl-[1.67vw]`}>
              <Image
                src="/images/Cloudquarkslogogreen.png"
                alt="cloudquarks_logo"
                width={213}
                height={54}
                className="w-[170px] h-[40px]"
              />
            </div>
          ) : (
            <div className="flex items-center gap-[14px] 3xl:gap-[0.417vw]">
              {pathname === '/order/feedback' ? (
                <div className="bg-[#FFF] pl-[28px] 3xl:pl-[1.67vw] ">
                  <Image
                    src="/images/svg/redinton_logo.svg"
                    alt="cloudquarks_logo"
                    layout=""
                    height={54}
                    width={30}
                    className="w-[30px] h-[54px] 3xl:w-[1.563vw] 3xl:h-[2.813vw]"
                  />
                </div>
              ) : null}
              <div className={`flex w-full items-stretch ${isRTL ? '' : ''}`}>
                <Link
                  href="/marketplace"
                  className="px-[14px] 3xl:px-[0.833vw] py-[8px] 3xl:py-[0.313vw] flex items-center justify-center gap-2 cursor-pointer bg-[linear-gradient(85deg,_#59BC49_16.23%,_#019049_92.47%)] text-white text-[16px] 3xl:text-[0.833vw] font-[500]"
                >
                  <i className="cloud-book 3xl:text-[0.833vw]"></i>
                  {t('marketplace')}
                </Link>
                <div className="relative">
                  <Input
                    dir={direction}
                    type="text"
                    placeholder="Marketplace Search"
                    className={`w-[400px] xl:w-[350px] 2xl:w-[425px] 3xl:w-[22.79vw] ${
                      isRTL
                        ? 'pr-[40px] xl:pr-[40px] 2xl:pr-[42px] 3xl:pr-[2.34vw]'
                        : 'pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw]'
                    }   py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:font-[300] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokehard focus:outline-none focus:ring-0 focus:border-InterfaceStrokehard h-full`}
                  />
                  <i
                    className={`cloud-search absolute text-[#828A91] top-[14px] 3xl:top-[0.729vw] ${
                      isRTL ? 'right-[12px]' : 'left-[12px]'
                    } text-[16px]`}
                  ></i>
                </div>
                <div className={`${isRTL ? 'mr-2' : 'ml-2'}`}>
                  <Link
                    href="/quick-order"
                    className="px-[14px] 3xl:px-[0.833vw] py-[8px] 3xl:py-[0.417vw] flex items-center justify-center gap-2 cursor-pointer bg-[linear-gradient(83deg,_#475AFF_36.4%,_#9748FF_84.06%)] text-white text-[16px] 3xl:text-[0.833vw] font-[500] border border-[#1B4EB2]"
                  >
                    <i className="cloud-shopping 3xl:text-[0.833vw]"></i>
                    {t('quickOrder')}
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right */}
        <div className="col-span-12 xl:col-span-5 2xl:col-span-5 3xl:col-span-5">
          <div
            className={`flex items-center justify-start xl:justify-start 2xl:justify-evenly 3xl:justify-end  gap-2 ${isRTL ? '' : ''}`}
          >
            <ComapnyPopover />
            <Settingpopup />

            <div className="countrydropdown arrowcustom flex items-center hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:font-[500]">
              <i className="cloud-gps text-[18px] text-center"></i>
              <Select defaultValue="mea">
                <SelectTrigger className="bg-transparent font-[500] rounded-none border-none text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw]">
                  <SelectValue placeholder="Country" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="india">{t('india')}</SelectItem>
                    <SelectItem value="mea">{t('mea')}</SelectItem>
                    <SelectItem value="turkey">{t('turkey')}</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <NotificationPopup />

            <Link
              href="/open-carts"
              className="p-[8px] 3xl:p-[0.417vw] hover:bg-[#e6e8e9] flex items-center justify-center text-[#7F8488]"
              title="Cart"
            >
              <i className="cloud-cart text-[24px]"></i>
            </Link>

            <Profileoverlay />
          </div>
        </div>
      </div>

      {/* Brands & Sales Row */}
      {pathname === '/becomeavendor' ||
      pathname === '/account/partner-registration' ||
      pathname.startsWith('/order') ? null : (
        <div
          className={`grid grid-cols-12 gap-[14px] 3xl:gap-[0.729vw] bg-white py-[4px] 3xl:py-[0.21vw] ${
            isRTL ? '' : 'xl:pl-[110px] 3xl:pl-[5.729vw]'
          } pl-[32px] 3xl:pl-[1.67vw]  border-t border-[#E5E7EB]`}
        >
          <div className="col-span-12 xl:col-span-7">
            <div
              className={`flex items-center gap-[8px] ${isRTL ? ' pr-[110px] xl:pr-[110px] 3xl:pr-[5.729vw] justify-start' : ''}`}
            >
              <div className="text-[12px] font-[400] text-[#7F8488]">{t('quickBrands')}:</div>
              <div className={`flex items-center ${isRTL ? 'justify-end' : ''}`}>
                {['Favourites', 'Oracle', 'Dell', 'Azure', 'Google', 'HP'].map((brand) => (
                  <div
                    key={brand}
                    className="cursor-pointer px-[10px] py-[6px] text-[12px] font-[500] text-[#3C4146] hover:bg-[#e6e8e9] hover:text-[#1570EF]"
                  >
                    {brand}
                  </div>
                ))}
              </div>
              <AddBrands />
            </div>
          </div>
          <div className="col-span-12 xl:col-span-5">
            <div
              className={`flex items-center justify-end ${isRTL ? '' : 'flex items-center justify-start'}`}
            >
              <SaleContact />
              <HelpSupport />
              <OnBoardingPopup />
            </div>
          </div>
        </div>
      )}

      {/* Tour */}
      <StartTour
        open={startTour}
        onClose={() => setStartTour(false)}
        onStart={() => {
          setShowTour(true);
          setStartTour(false);
        }}
      />
      {showTour && <SiteTour steps={steps} onClose={() => setShowTour(false)} />}
    </header>
  );
}
