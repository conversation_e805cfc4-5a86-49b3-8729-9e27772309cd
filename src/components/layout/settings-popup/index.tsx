'use client';

import React from 'react';
import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Label } from '@redington-gulf-fze/cloudquarks-component-library';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { localStorageService, clientCookiesService } from '@/lib/services/storage';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export function Settingpopup() {
  const [open, setOpen] = useState(false);
  const [selectedLang, setSelectedLang] = useState<'en' | 'ar'>('en');
  // const t = useTranslations('Settings');
  const th = useTranslations();
  const router = useRouter();
  useEffect(() => {
    (async () => {
      const storedLocale = await localStorageService.get<string>('locale');
      setSelectedLang(storedLocale === 'ar' ? 'ar' : 'en');
    })();
  }, []);

  const handleLangChange = async (lang: 'en' | 'ar') => {
    setSelectedLang(lang);
    await localStorageService.set('locale', lang);
    clientCookiesService.set('locale', lang, { path: '/' });
    router.refresh();
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          {/* tr:pl-[12px] ltr:xl:pl-[12px] ltr:2xl:pl-[14px] ltr:3xl:pl-[0.729vw] */}
          <div
            className="countrydropdown arrowcustom flex items-center l rtl:pr-[12px] rtl:xl:pr-[12px] rtl:2xl:pr-[14px] rtl:3xl:pr-[0.729vw] hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:font-[500]"
            onClick={() => setOpen(true)}
          >
            <i className="cloud-world xl:text-[0.938vw] text-[20px] text-center"></i>
            <Select value={selectedLang} onValueChange={handleLangChange}>
              <SelectTrigger
                className="bg-transparent placeholder-text-sm font-[500] rounded-none border-none text-[12px] md:text-[12px] xl:text-[12px] 
              2xl:text-[12px] 3xl:text-[0.729vw]"
              >
                <SelectValue placeholder="Country" />{' '}
                {selectedLang === 'ar' ? th('languageArabic') : th('languageEnglish')}
              </SelectTrigger>
            </Select>
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 mr-[290px] w-[350px] xl:w-[350px] 2xl:w-[395px] 3xl:w-[420px] rounded-none bg-[#FFF]">
          <div>
            <div className="border-b border-[#C9D3DB] flex justify-between items-center px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[22px] 3xl:py-[1.25vw]">
              <h4 className="text-[#212325] text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-medium leading-none">
                {th('languageCurrencySettings')}
              </h4>
              <i
                className="cloud-closecircle text-closecolor text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"
                onClick={() => setOpen(false)}
              ></i>
            </div>
            <div className="p-[28px] xl:p-[28px] 2xl:p-[30px] 3xl:p-[1.67vw] flex flex-col gap-[24px] 3xl:gap-[1.25vw]">
              <div className="flex flex-col gap-[10px] 3xl:gap-[0.525vw]">
                <div className="text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500]">
                  {th('selectTheLanguageYouPreferForBrowsingThePlatform')}
                </div>
                <div>
                  <RadioGroup
                    value={selectedLang}
                    onValueChange={handleLangChange}
                    className="flex flex-col gap-[0px]"
                  >
                    <div className="flex items-center space-x-2 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.36vw]">
                      <RadioGroupItem value="en" id="r1" />
                      <Label>{th('languageEnglish')}</Label>
                    </div>
                    <div className="flex items-center space-x-2 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.36vw]">
                      <RadioGroupItem value="ar" id="r2" />
                      <Label>{th('languageArabic')}</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
              <div className="flex flex-col gap-[10px] 3xl:gap-[0.525vw]">
                <div className="text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500]">
                  {th('selectTheCurrencyWith')}
                </div>
                <div className="col-span-4">
                  <Select defaultValue="india">
                    <SelectTrigger className="bg-[#FFF] text-[#3C4146] placeholder-text-sm  rounded-none border-[#BBC1C7] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectValue placeholder="Country" className="" />
                    </SelectTrigger>
                    <SelectContent className="rounded-none bg-[#FFF] border-none">
                      <SelectGroup className="text-[#3C4146] text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                        <SelectItem value="india">
                          AED - United Arab Emirates Dirham (Default)
                        </SelectItem>
                        <SelectItem value="mea">United Arab Emirates Dirham</SelectItem>
                        <SelectItem value="tukry">United Arab Emirates Dirham</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[22px] 3xl:py-[1.25vw] flex justify-end border-t border-[#E5E7EB]">
              <Button
                type="submit"
                className="bg-BrandPrimarypure rounded-none text-[#FFFFFF]  px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <Image
                  src="/images/save.svg"
                  width={18}
                  height={18}
                  className="w-[18px] h-[18px]"
                  alt="save"
                />
                {th('saveChanges')}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
