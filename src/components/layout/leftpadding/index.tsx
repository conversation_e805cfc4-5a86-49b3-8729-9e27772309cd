'use client';
import { getDirection } from '@/lib/utils/util';
import { useLocale } from 'next-intl';
import { usePathname } from 'next/navigation';
import React from 'react';

export default function LeftPading({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const locale = useLocale();
  const direction = getDirection(locale);
  return (
    <div
      className={`
        ${
          direction === 'rtl'
            ? 'pr-[80px] xl:pr-[80px] 2xl:pr-[80px] 3xl:pr-[4.167vw] pb-6 xl:pb-[1.25vw]'
            : pathname === '/order/feedback'
              ? ''
              : 'pl-[80px] xl:pl-[80px] 2xl:pl-[80px] 3xl:pl-[4.167vw]'
        }`}
    >
      {children}
    </div>
  );
}
