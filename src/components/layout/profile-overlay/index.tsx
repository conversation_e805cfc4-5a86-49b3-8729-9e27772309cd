'use client';

import {
  <PERSON><PERSON>,
  <PERSON>overContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { useQuery } from '@tanstack/react-query';
import { userService } from '@/lib/services/api/user';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

export function Profileoverlay() {
  const router = useRouter();
  const t = useTranslations();
  const { data: userDetails } = useQuery({
    queryKey: ['user-details'],
    queryFn: () => userService.getUserDetails(),
  });

  const handleLogout = () => {
    userService.deleteSession();
    router.replace('/login');
  };

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <div className="flex items-center gap-2 cursor-pointer ml-[10px] 3xl:ml-[0.525vw]">
            <div className="flex items-center gap-3 3xl:gap-[0.625vw]">
              <div className="flex flex-col gap-[6px] 3xl:gap-[0.36vw]">
                <div className="text-[#2C363F] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] font-[600] block leading-none">
                  {userDetails?.data?.[0]?.first_name} {userDetails?.data?.[0]?.last_name}
                </div>
                <div className="text-[#828A91] text-end text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-[400] block">
                  {userDetails?.data?.[0]?.designation?.name}
                </div>
              </div>
              <div className="userPic ml-2">
                <Image
                  src="/images/left-menu-icons/profile.svg"
                  width={48}
                  height={48}
                  className="h-[30px] xl:h-[35px] 2xl:h-[40px] 3xl:h-[2.083vw]
                                 w-[30px] xl:w-[35px] 2xl:w-[40px] 3xl:w-[2.083vw]"
                  alt="profile"
                />
              </div>
            </div>
            <div>
              <i className="cloud-downarrowcicle text-[#828A91] 3xl:text-[1vw] text-[18px]"></i>
            </div>
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-[230px] xl:w-[230px] 2xl:w-[235px] 3xl:w-[250px] rounded-none bg-[#FFF] mr-[20px] xl:mr-[25px] 3xl:mr-[1.302vw]">
          <div>
            <div className="border-b border-[#C9D3DB] flex justify-between items-center px-[24px] xl:px-[24px] 2xl:px-[26px] 3xl:px-[1.46vw] py-[18px] xl:py-[18px] 2xl:py-[20px] 3xl:py-[1.15vw]">
              <h4 className="text-[#212325] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-none">
                {t('profile')}
              </h4>
              <i className="cloud-closecircle text-closecolor text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
            </div>
            <div className="hover:bg-[#e6e8e9] px-[24px] xl:px-[24px] 2xl:px-[26px] 3xl:px-[1.46vw] flex flex-col gap-[24px] 3xl:gap-[1.25vw]">
              <div
                className="cursor-pointer text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
                onClick={() => router.push('/account')}
              >
                <i className="cloud-hexauser text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] mr-[12px] 3xl:mr-[0.625vw]"></i>
                {t('myAccount')}
              </div>
            </div>
            <div className="hover:bg-[#e6e8e9] px-[24px] xl:px-[24px] 2xl:px-[26px] 3xl:px-[1.46vw] flex flex-col gap-[24px] 3xl:gap-[1.25vw]">
              <div className="cursor-pointer text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]">
                <i className="cloud-passkey text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] mr-[12px] 3xl:mr-[0.625vw]"></i>
                {t('changePassword')}
              </div>
            </div>
            <div className="hover:bg-[#e6e8e9] px-[24px] xl:px-[24px] 2xl:px-[26px] 3xl:px-[1.46vw] flex flex-col gap-[24px] 3xl:gap-[1.25vw]">
              <div
                className="cursor-pointer text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
                onClick={() => router.push('/settings/language-currency')}
              >
                <i className="cloud-settings1 text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] mr-[12px] 3xl:mr-[0.625vw]"></i>
                {t('settings')}
              </div>
            </div>

            <div className="border-t border-[#E5E7EB] hover:bg-[#e6e8e9] px-[24px] xl:px-[24px] 2xl:px-[26px] 3xl:px-[1.46vw] flex flex-col gap-[24px] 3xl:gap-[1.25vw]">
              <div
                onClick={handleLogout}
                className="cursor-pointer text-[#D42600] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-hexauser text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] mr-[12px] 3xl:mr-[0.625vw]"></i>
                {t('signOut')}
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
