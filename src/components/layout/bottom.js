import Image from 'next/image';
import Link from 'next/link';
import 'simplebar-react/dist/simplebar.min.css';

export default function Top1({ ...pageProps }) {
  return (
    <header className="">
      <div className="bg-white border-b border-[#E5E7EB] flex flex-wrap items-center justify-between py-[12px] xl:py-[14px] 2xl:py-[18px] 3xl:py-[0.938vw] pr-4 xl:pr-[1.667vw] pl-[118px] xl:pl-[128px] dark:bg-[#191A1E]">
        <div className="">
          <div className="text-[#24262D] xl:text-[1.250vw] text-xl font-semibold leading-7 dark:text-[#fff]">
            {pageProps.pageTitle}
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-[12px] 3xl:gap-[0.625vw]">
          <Link href={'/'} className="p-[8px] 3xl:p-[0.417vw] relative" title="Cart">
            <i className="cloud-bellicon xl:text-[1.25vw] text-[24px]"></i>
          </Link>
          <Link href={'/'} className="p-[8px] 3xl:p-[0.417vw]" title="Cart">
            <i className="cloud-cart xl:text-[1.25vw] text-[24px]"></i>
          </Link>
          <div className="flex items-center gap-5 3xl:gap-[1.042vw] cursor-pointer">
            <div className="flex items-center gap-3 3xl:gap-[0.625vw]">
              <div className="flex flex-col gap-[6px] 3xl:gap-[0.36vw]">
                <div className="text-[#2C363F] text-[16px] xl:text-[0.833vw] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] block leading-none">
                  Jese Leos
                </div>
                <div className="text-[#828A91] text-end text-[14px] xl:text-[0.729vw] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] block">
                  Admin
                </div>
              </div>
              <div className="userPic ml-2">
                <Image
                  src="/assets/images/left-menu-icons/profile.svg"
                  width={48}
                  height={48}
                  className="h-[30px] xl:h-[35px] 2xl:h-[48px] 3xl:h-[2.5vw] w-[30px] xl:w-[35px] 2xl:w-[48px] 3xl:w-[2.5vw]"
                  alt="profile"
                />
              </div>
            </div>
            <div>
              <i className="cloud-previous xl:text-[1.25vw] text-[24px]"></i>
              {/* <i className="cloud-downarrowcicle xl:text-[1.25vw] text-[24px]"></i> */}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
