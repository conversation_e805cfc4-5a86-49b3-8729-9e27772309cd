'use client';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
import { useTranslations } from 'next-intl';

export default function Footer() {
  const t = useTranslations();
  const pathname = usePathname();
  return (
    <div
      className={`${pathname === '/home' ? 'block' : 'hidden'} bg-background border-t border-t-InterfaceStrokedefault px-[10px] xl:px-[20px] 3xl:px-[1.042vw] pb-[35px] 3xl:pb-[2.083vw]`}
    >
      <div className="grid grid-cols-1 md:grid-cols-4 xl:grid-cols-4 gap-[20px] md:gap-[50px] xl:gap-[80px] 3xl:gap-[8.438vw] py-[20px] md:py-[40px] 3xl:py-[2.083vw] px-[10px] md:px-[80px] xl:px-[100px] 3xl:px-[8vw]">
        <div>
          <div className="text-blackcolor text-[16px] md:text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-[600] mb-[16px] 3xl:mb-[0.833vw]">
            {t('getSupported')}
          </div>
          <div className="space-y-[8px] 3xl:scale-y-[0.417vw]">
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('knowledgeBase')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('serviceDesk')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('faq')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('trainingCertification')}
            </Link>
          </div>
        </div>
        <div>
          <div className="text-blackcolor text-[16px] md:text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-[600] mb-[16px] 3xl:mb-[0.833vw]">
            {t('quickLinks')}
          </div>
          <div className="space-y-[8px] 3xl:scale-y-[0.417vw]">
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('quickOrder')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('accountManagement')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('analytics')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('miniShop')}
            </Link>
          </div>
        </div>
        <div>
          <div className="text-blackcolor text-[16px] md:text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-[600] mb-[16px] 3xl:mb-[0.833vw]">
            {t('getToKnowUs')}
          </div>
          <div className="space-y-[8px] 3xl:scale-y-[0.417vw]">
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('aboutCompany')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('privacyPolicy')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('termsOfUse')}
            </Link>
            <Link
              href={''}
              className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('termsOfSale')}
            </Link>
          </div>
        </div>
        <div>
          <div className="text-blackcolor text-[16px] md:text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-[600] mb-[16px] 3xl:mb-[0.833vw]">
            {t('contact')}
          </div>
          <div className="space-y-[8px] 3xl:scale-y-[0.417vw]">
            <div className="flex items-start gap-[9px] 3xl:gap-[0.469vw]">
              <Image
                width={16}
                height={16}
                src="/images/location.svg"
                className="mt-[6px]"
                alt="Location"
              />
              <Link
                href={''}
                className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
              >
                {t('contactAddress')}
              </Link>
            </div>

            <div className="flex items-start gap-[9px] 3xl:gap-[0.469vw]">
              <Image width={16} height={16} src="/images/sms.svg" alt="Email" />
              <Link
                href={'mailto:<EMAIL>'}
                className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
              >
                <EMAIL>
              </Link>
            </div>

            <div className="flex items-start gap-[9px] 3xl:gap-[0.469vw]">
              <Image width={16} height={16} src="/images/call-calling.svg" alt="Phone" />
              <Link
                href={'tel:+97145161504'}
                className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
              >
                +971 4 516 1504
              </Link>
            </div>

            <div className="flex items-center gap-[24px] 3xl:gap-[1.25vw]">
              <Link
                href={''}
                className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
              >
                <Image
                  className="w-[20px] 3xl:w-[1.042vw] h-[20px] 3xl:h-[1.042vw]"
                  width={20}
                  height={20}
                  src="/images/facebook.svg"
                  alt="facebook"
                />
              </Link>
              <Link
                href={''}
                className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
              >
                <Image
                  className="w-[20px] 3xl:w-[1.042vw] h-[20px] 3xl:h-[1.042vw]"
                  width={20}
                  height={20}
                  src="/images/twitter.svg"
                  alt="twitter"
                />
              </Link>
              <Link
                href={''}
                className="block text-InterfaceTextdefault text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
              >
                <Image
                  className="w-[20px] 3xl:w-[1.042vw] h-[20px] 3xl:h-[1.042vw]"
                  width={20}
                  height={20}
                  src="/images/linkedin1.svg"
                  alt="linkedin"
                />
              </Link>
            </div>
          </div>
        </div>
      </div>
      <div className="border-t border-InterfaceStrokesoft pt-[24px] 3xl:pt-[1.25vw] text-center">
        <p className="text-InterfaceTextdefault text-[16px] md:text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-[400] ">
          {t('copyright')} | {t('allRightsReserved')}
        </p>
      </div>
    </div>
  );
}
