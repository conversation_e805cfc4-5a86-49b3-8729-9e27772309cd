import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Card, CardContent } from '@redington-gulf-fze/cloudquarks-component-library';
import { Button, Progress } from '@redington-gulf-fze/cloudquarks-component-library';
import { TourStep } from '@/types/components/index';
import Image from 'next/image';

interface Props {
  steps: TourStep[];
  onClose: () => void;
}

export const SiteTour: React.FC<Props> = ({ steps, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [style, setStyle] = useState({});
  const [highlightRect, setHighlightRect] = useState<DOMRect | null>(null);

  const step = steps[currentStep];

  const positionCard = () => {
    const el = document.getElementById(step.targetId);
    if (!el) return;

    const rect = el.getBoundingClientRect();
    setHighlightRect(rect);

    const padding = 8;
    let top = 0;
    let left = 0;

    switch (step.position || 'bottom') {
      case 'top':
        top = rect.top + window.scrollY - 130;
        left = rect.left + window.scrollX + rect.width / 2;
        break;
      case 'bottom':
        top = rect.bottom + window.scrollY + padding;
        left = rect.left + window.scrollX + rect.width / 2;
        break;
      case 'left':
        top = rect.top + window.scrollY + rect.height / 2;
        left = rect.left + window.scrollX - 300;
        break;
      case 'right':
        top = rect.top + window.scrollY + rect.height / 2 + 2; // ⬇ Drop card slightly
        left = rect.right + window.scrollX + 270; // ➡ Push card further right
        break;
    }

    setStyle({ top, left });
  };

  useEffect(() => {
    positionCard();
    window.addEventListener('resize', positionCard);
    return () => window.removeEventListener('resize', positionCard);
  }, [step]);

  const next = () => (currentStep < steps.length - 1 ? setCurrentStep(currentStep + 1) : onClose());
  const prev = () => (currentStep > 0 ? setCurrentStep(currentStep - 1) : null);
  const progress = ((currentStep + 1) / steps.length) * 100;

  if (!highlightRect) return null;

  return createPortal(
    <>
      {/* Overlay for highlight */}
      <div className="fixed inset-0 z-[-10] pointer-events-none " />

      {/* Highlight Box */}
      {/* {highlightRect && (
  <div
    className="absolute z-50 pointer-events-none rounded-[4px]"
    style={{
      top: highlightRect.top + window.scrollY - 6,
      left: highlightRect.left + window.scrollX - 6,
      width: highlightRect.width + 12,
      height: highlightRect.height + 12,
      boxShadow: "0 0 20px rgba(22, 43, 85, 0.6), 0 0 0 2000px rgba(0,0,0,0.3)", // glow + focus ring
      border: "2px solid #162B55",
      background: "rgba(255, 255, 255, 0.03)", // subtle light
    //   backdropFilter: "blur(4px)", // blur behind
      transition: "all 0.3s ease",
    }}
  />
  
)} */}
      {highlightRect && (
        <div
          className="absolute z-50 pointer-events-none rounded-[4px]"
          style={{
            top: highlightRect.top + window.scrollY - 6,
            left: highlightRect.left + window.scrollX - 6,
            width: highlightRect.width + 12,
            height: highlightRect.height + 12,
            background: 'transparent',
            border: '2px solid #162B55',
            boxShadow: `
                0 2px 6px rgba(22, 43, 85, 0.1),    /* ↓ reduced blue tint */
                0 0 0 2000px rgba(0,0,0,0.3)        /* background dim */
            `,
            transition: 'all 0.3s ease',
          }}
        />
      )}

      {/* Tooltip Card with arrow */}
      <div className="absolute z-50 transform -translate-x-1/2" style={style}>
        {/* Arrow */}
        <div className="relative">
          {step.position === 'top' && (
            <div className="absolute left-[20px] bottom-0 transform translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white z-10" />
          )}

          {step.position === 'bottom' && (
            <div className="absolute left-[20px] top-0 transform -translate-y-full w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white z-10" />
          )}

          {step.position === 'left' && (
            <div className="absolute right-0 top-[20px] transform translate-x-full w-0 h-0 border-t-8 border-b-8 border-l-8 border-t-transparent border-b-transparent border-l-white z-10" />
          )}

          {step.position === 'right' && (
            <div className="absolute left-0 top-[20px] transform -translate-x-full w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-white z-10" />
          )}

          <Card className="bg-[#fff] w-[480px] 3xl:w-[25vw] shadow-lg rounded-none">
            <CardContent className="cardContent p-0">
              <div className=" px-[24px] py-[16px] gap-[4px] 3xl:gap-[0.208vw]">
                <div onClick={onClose}>
                  <i className="flex justify-end items-right cloud-closecircle text-[18px] text-InterfaceTextsubtitle" />
                </div>
                <div className=" text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.938vw] text-InterfaceTexttitle font-[600] leading-[140%]">
                  {step.title}
                </div>
                <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-[400] leading-[140%]">
                  {step.description}
                </div>
              </div>

              <div className="flex justify-between items-center px-4 py-4">
                {/* Left: Skip Link */}
                <div
                  onClick={onClose}
                  className="cursor-pointer font-[400] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.625vw]"
                >
                  Skip to Main content &raquo;
                </div>

                {/* Right: Prev + Next Buttons */}
                <div className="flex gap-4">
                  <Button
                    onClick={prev}
                    disabled={currentStep === 0}
                    type="button"
                    className="py-[10px] xl:py-[10px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                    variant="outline"
                  >
                    <i className="cloud-next rotate-180 text-[10px]"></i>&nbsp;prev
                  </Button>

                  <Button
                    onClick={next}
                    type="button"
                    className="py-[10px] xl:py-[10px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#067532] bg-[#00953A] text-[#fff] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                  >
                    {currentStep === steps.length - 1 ? (
                      <span>Finish</span>
                    ) : (
                      <div>
                        Next &nbsp;<i className="cloud-next text-[10px]"></i>
                      </div>
                    )}
                  </Button>
                </div>
              </div>
              <Progress value={progress} className=" progress w-[100%] h-[4px] " />
              <div className=" flex justify-between items-center font-[500] text-[14px] text-InterfaceTextdefault p-4">
                {currentStep + 1}/{steps.length}
                <Image
                  src="/images/left-menu-icons/logo.svg"
                  width={100}
                  height={80}
                  alt="logo icon"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>,
    document.body
  );
};
