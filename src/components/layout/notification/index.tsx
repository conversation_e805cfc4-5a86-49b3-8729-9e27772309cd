'use client';
import {
  <PERSON><PERSON>,
  <PERSON>over,
  <PERSON>overContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

export function NotificationPopup() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const t = useTranslations();
  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative" onClick={() => setOpen(true)}>
            <div
              className="cursor-pointer p-[8px] 3xl:p-[0.417vw]  hover:bg-[#e6e8e9] flex items-center justify-center"
              title="Notification"
            >
              <i className="cloud-newbellicon xl:text-[1.25vw] text-[24px] text-[#7F8488]"></i>
            </div>
            <div className="absolute top-[-3px] xl:top-[-3px] 2xl:top-[-4px] 3xl:top-[-0.3vw] right-0 rounded-full px-[4px] py-[2px] text-[8px] xl:text-[9px] 2xl:text-[10px] 3xl:text-[0.57vw] font-[400] text-InterfaceTextwhite bg-BrandPrimarypure custbadge">
              12
            </div>
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 mr-[200px] w-[410px] xl:w-[410px] 2xl:w-[410px] 3xl:w-[21.354vw] rounded-none bg-background">
          <div>
            <div className="border-b border-InterfaceStrokedefault flex justify-between items-center px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[22px] 3xl:py-[1.25vw]">
              <h4 className="text-[#212325] text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] font-medium leading-none">
                {t('notifications')}
              </h4>
              <i
                className="cloud-closecircle text-closecolor text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"
                onClick={() => setOpen(false)}
              ></i>
            </div>

            <div className="px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[20px] 3xl:py-[1.042vw]">
              <div className="border-b border-b-InterfaceSurfacepagemuted pb-[16px] 3xl:pb-[0.833vw]">
                <div className="flex flex-col gap-[10px] 3xl:gap-[0.525vw]">
                  <div className="text-BrandSupport1pure text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400]">
                    {t('newNotifications')} (2)
                  </div>
                </div>

                <div className="space-y-[24px] 3xl:space-y-[1.25vw] mt-[16px] 3xl:mt-[0.833vw]">
                  <div className="flex items-start gap-[12px] 3xl:gap-[0.625vw]">
                    <Image
                      src={'/images/user1.png'}
                      width={32}
                      height={32}
                      className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]"
                      alt=""
                    />
                    <div>
                      <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        <span className="text-InterfaceTexttitle font-semibold">Will Smith</span>{' '}
                        ordered four items, with a total order value of{' '}
                        <span className="text-InterfaceTexttitle font-semibold">$1,210</span>
                      </div>
                      <div className="">
                        <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] flex items-center gap-[6px] 3xl:gap-[0.313vw]">
                          3 Minutes ago{' '}
                          <div className="h-1 w-1 bg-InterfaceTextsubtitle rounded-full"></div>{' '}
                          Order Placement
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-[12px] 3xl:gap-[0.625vw]">
                    <Image
                      src={'/images/user2.png'}
                      width={32}
                      height={32}
                      className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]"
                      alt=""
                    />
                    <div>
                      <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        <span className="text-InterfaceTexttitle font-semibold">
                          Cameron Williamson
                        </span>{' '}
                        delivered the order
                      </div>
                      <div className="">
                        <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] flex items-center gap-[6px] 3xl:gap-[0.313vw]">
                          1 hour ago{' '}
                          <div className="h-1 w-1 bg-InterfaceTextsubtitle rounded-full"></div>{' '}
                          Order Delivery
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-[16px] 3xl:pt-[0.833vw]">
                <div className="flex flex-col gap-[10px] 3xl:gap-[0.525vw]">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400]">
                    {t('olderNotifications')} (99+)
                  </div>
                </div>

                <div className="space-y-[24px] 3xl:space-y-[1.25vw] mt-[16px] 3xl:mt-[0.833vw]">
                  <div className="flex items-start gap-[12px] 3xl:gap-[0.625vw]">
                    <Image
                      src={'/images/user3.png'}
                      width={32}
                      height={32}
                      className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]"
                      alt=""
                    />
                    <div>
                      <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        <span className="text-InterfaceTexttitle font-semibold">Albert Flores</span>{' '}
                        delivered the order
                      </div>
                      <div className="">
                        <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] flex items-center gap-[6px] 3xl:gap-[0.313vw]">
                          1 day ago{' '}
                          <div className="h-1 w-1 bg-InterfaceTextsubtitle rounded-full"></div>{' '}
                          Order Delivery
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-[12px] 3xl:gap-[0.625vw]">
                    <Image
                      src={'/images/user2.png'}
                      width={32}
                      height={32}
                      className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]"
                      alt=""
                    />
                    <div>
                      <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        <span className="text-InterfaceTexttitle font-semibold">Guy Hawkins</span>{' '}
                        delivered the order
                      </div>
                      <div className="">
                        <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] flex items-center gap-[6px] 3xl:gap-[0.313vw]">
                          3:08 PM, 01 Apr 2025{' '}
                          <div className="h-1 w-1 bg-InterfaceTextsubtitle rounded-full"></div>{' '}
                          Order Delivery
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[22px] 3xl:py-[1.25vw] flex justify-end border-t border-[#E5E7EB]">
              <Button
                onClick={() => router.push('/settings/alerts/notifications')}
                type="submit"
                className="bg-BrandPrimarypure rounded-none text-[#FFFFFF]  px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-eye"></i>
                {t('seeAllNotifications')}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
