import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
} from '@redington-gulf-fze/cloudquarks-component-library';

interface startTourProps {
  open: boolean;
  onClose: () => void;
  onStart: () => void;
}
export default function startTourPopup({ open, onClose, onStart }: startTourProps) {
  // const [open, setOpen] = React.useState<Boolean>(true);
  return (
    <div>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:rounded-none sm:max-w-[425px]">
          {/* <DialogClose asChild>
            <button
              aria-label="Close"
              className="absolute"
            >
              <i className='cloud-closecircle '/>
            </button>
          </DialogClose> */}
          <DialogHeader>
            <DialogTitle className="text-InterfaceTexttitle font-[600] text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] leading-[140%]">
              Take a Tour
            </DialogTitle>
            <DialogDescription className="text-[#3C4146] font-[400] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] ">
              Onboarding refers to the process of integrating a new employee into an organization.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 px-[24px] xl:px-[24px] 2xl:px-[24px] 3xl:px-[1.25vw] ">
            <center>
              <Button className="greenBtn rounded-none border-[#067532]" onClick={onStart}>
                Get Started
              </Button>
            </center>
          </div>
          <center>
            <div
              onClick={onClose}
              className="p-4 cursor-pointer flex justify-center items-center font-[400] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.625vw]"
            >
              Skip to Main content &raquo;
            </div>
          </center>
          {/* <DialogFooter>

          </DialogFooter> */}
        </DialogContent>
      </Dialog>
    </div>
  );
}
