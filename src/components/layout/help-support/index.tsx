'use client';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Select,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

import Link from 'next/link';

export function HelpSupport() {
  const t = useTranslations();
  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <div className="countrydropdown flex items-center  hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:font-[500]">
            <Select defaultValue="en">
              <SelectTrigger
                id="helpandsupport"
                className="bg-transparent placeholder-text-sm font-[500] rounded-none border-none text-[#3C4146] hover:bg-[#e6e8e9] hover:text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
              >
                <SelectValue placeholder="Country" />{' '}
                <i className="cloud-messagebox text-center text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] mr-[8px] 3xl:mr-[0.417vw]"></i>{' '}
                {t('helpSupport')}
              </SelectTrigger>
            </Select>
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-[140px] xl:w-[140px] 2xl:w-[155px] 3xl:w-[180px] rounded-none bg-[#FFF]">
          <div className="flex flex-col">
            <Link href="/help/knowledge-hub">
              <div className="hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:underline px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.833vw] font-[400]">
                {t('knowledgeHub')}
              </div>
            </Link>
            <Link href="/help/service-desk">
              <div className="hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:underline px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.833vw] font-[400]">
                {t('serviceDesk')}
              </div>
            </Link>
            <Link href="/help/faq">
              <div className="hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:underline px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.833vw] font-[400]">
                {t('faq')}
              </div>
            </Link>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
