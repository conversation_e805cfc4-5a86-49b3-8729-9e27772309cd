'use client';
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import SimpleBar from 'simplebar-react';
import 'simplebar-react/dist/simplebar.min.css';
import { cn, getDirection } from '@/lib/utils/util';
import { <PERSON>o } from 'next/font/google';
import { usePathname } from 'next/navigation';
import StartTour from '../sitetour-popup/index';
import { SiteTour } from '../sitetour/index';
import { TourStep } from '@/types/components/index';
import { useLocale } from 'next-intl';

import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

const steps: TourStep[] = [
  {
    id: 'step1',
    title: 'Onboarding',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'Onboarding',
    position: 'left',
  },
  {
    id: 'step2',
    title: 'Help and Support',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'helpandsupport',
    position: 'left',
  },
  {
    id: 'step3',
    title: 'Explore all Products',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'marketPlace',
    position: 'right',
  },
  {
    id: 'step4',
    title: 'Add Brands',
    description:
      'Onboarding refers to the process of integrating a new employee into an organization.',
    targetId: 'addBrands',
    position: 'right',
  },
];

export default function SideBar() {
  const pathname = usePathname();
  const locale = useLocale();
  const direction = getDirection(locale);
  const [startTour, setStartTour] = React.useState(false);
  const [showTour, setShowTour] = React.useState(false);

  const t = useTranslations();

  const navItems = [
    {
      label: t('home'),
      href: '/home',
      icon: `ico-menu`,
    },
    {
      label: t('accountManagement'),
      href: '/account',
      icon: `ico-business`,
    },
    {
      label: t('sales'),
      href: '/sales/discount-coupons',
      icon: `ico-sales`,
    },
    {
      label: t('customerManagement'),
      href: '/customer-management/customers',
      icon: `ico-customer`,
    },
    {
      label: t('subscriptionLicenses'),
      href: '/subscription-licenses/subscriptions',
      icon: `ico-lic`,
    },
    {
      label: t('billing'),
      href: '/billing/invoices',
      icon: `ico-billing`,
    },
    {
      label: t('analytics'),
      href: '/analytics',
      icon: `ico-analytics`,
    },
    {
      label: t('settings'),
      href: '/settings/language-currency',
      icon: `settings`,
    },
    {
      label: t('helpCenter'),
      href: '/help/knowledge-hub',
      icon: `ico-detailed`,
    },
    {
      label: t('becomeVendor'),
      href: '#',
      icon: `ico-security`,
    },
  ];

  return (
    <>
      {!pathname?.includes('onboarding') && !pathname?.includes('partner-registration') && (
        <div
          dir={direction}
          className={cn(
            roboto.className,
            `fixed top-0 z-20 max-md:h-full left-menu-mobile-H leftbar_shadow ${pathname === '/order/feedback' ? 'hidden' : ''}`,
            direction === 'rtl' ? 'right-0' : 'left-0'
          )}
        >
          <div className="group w-[80px] 3xl:w-[4.167vw] hover:w-[288px] hover:3xl:w-[15vw] bg-[#ECEFF3] left-menu-h border-r border-[#E5E7EB] overflow-hidden max-md:h-full left-menu-mobile">
            <div className="relative h-screen flex flex-col">
              {/* Logo Icon */}
              <div className="logo_icon flex justify-center py-[22px]">
                <Image
                  src="/images/left-menu-icons/logo-icon.svg"
                  width={30}
                  height={54}
                  alt="logo icon"
                />
              </div>

              {/* Full Logo */}
              <div className="logo flex mb-1 min-w-[200px] xl:min-w-[10vw] py-[22px] justify-center">
                <Link href="/">
                  <Image
                    src="/images/left-menu-icons/logo.svg"
                    width={250}
                    height={80}
                    alt="logo"
                  />
                </Link>
              </div>

              {/* Divider with icon */}
              <div>
                <div className="lock_icon relative mb-4">
                  <div className="w-full h-[1px] border-b-2 border-[#E5E7EB]"></div>
                  <div
                    className={cn(
                      'absolute -top-4 w-8 h-8 rounded-full flex items-center justify-center rotate-180',
                      direction === 'rtl'
                        ? 'right-1/2 translate-x-1/2'
                        : 'left-1/2 -translate-x-1/2'
                    )}
                  >
                    <i className="cloud-leftcirclearrow text-[24px] text-InterfaceTextsubtitle"></i>
                  </div>
                </div>

                {/* Mini Logo with Arrow */}
                <div
                  className={cn(
                    'logo relative min-w-[150px] mb-4 border-b-2 border-[#E5E7EB]',
                    direction === 'rtl' ? 'text-left pl-4' : 'text-right pr-4'
                  )}
                >
                  <Link href="">
                    <div
                      className={cn(
                        'w-8 h-8 rounded-full flex items-center justify-center absolute -bottom-4',
                        direction === 'rtl' ? 'left-0' : 'right-0'
                      )}
                    >
                      <i className="cloud-fillleftarrowcircle text-InterfaceTextsubtitle text-[24px]"></i>
                    </div>
                  </Link>
                </div>
              </div>

              {/* Sidebar Menu */}
              <div className="flex-grow overflow-y-auto">
                <SimpleBar
                  style={{ maxHeight: '100%' }}
                  className="h-[200px] lg:h-[220px] xl:h-[340px] 2xl:h-[480px] 3xl:h-[31.85vw]"
                >
                  <ul className="left-menu hoverNone rtl:flex-row-reverse">
                    {navItems.map((item, index) => {
                      const isActive =
                        pathname.includes(item.href) ||
                        (item.label === 'Settings' && pathname.includes('setting')) ||
                        (item.label === 'Help & Support' && pathname.includes('help'));

                      return (
                        <div
                          key={index}
                          className={cn(
                            'flex flex-col-1 rtl:flex-row-reverse gap-[16px] hover:bg-[#F6F7F8] text-[#3C4146] left-menu h-full',
                            isActive
                              ? 'bg-[#BDE2FF] font-[500] text-[16px] p-0 mx-[10px] my-[5px]'
                              : 'text-InterfaceTextdefault font-normal mx-[10px] my-[5px]'
                          )}
                        >
                          <li className={`${item.icon} ${isActive ? 'active' : ''}`}>
                            <Link href={item.href}>
                              <span className="text-[12px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                                {item.label}
                              </span>
                            </Link>
                          </li>
                        </div>
                      );
                    })}
                  </ul>
                </SimpleBar>
              </div>

              {/* Footer with SiteTour and Dark Mode */}
              <div className="px-2 pb-2">
                <ul className="left-menu hoverNone bottom_menu ">
                  <li className="sitetour">
                    <Link href="#" onClick={() => setStartTour(true)}>
                      <span>Site Tour</span>
                    </Link>
                  </li>
                  <li className="darkMode mt-2">
                    <Link href="#">
                      <span>Dark Theme</span>
                    </Link>
                  </li>
                </ul>
              </div>

              {/* Profile Footer */}
              <div className="px-6 py-4 border-t border-[#C9D3DB]">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
                    <Image
                      src="/images/left-menu-icons/profile.svg"
                      width={40}
                      height={40}
                      alt="profile"
                    />
                  </div>

                  <div className="max-w-0 group-hover:max-w-[200px] overflow-hidden transition-all duration-300 ease-in-out">
                    <span className="text-[#3C4146] text-[16px] font-[600] block whitespace-nowrap">
                      Jese Leos
                    </span>
                    <span className="text-[#7F8488] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.625vw] font-[400] block whitespace-nowrap">
                      Joined in August {new Date().getFullYear()}
                    </span>
                  </div>

                  <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-auto">
                    <Image
                      src="/images/left-menu-icons/more_square_dark.svg"
                      width={20}
                      height={20}
                      alt="more options"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Site Tour Popups */}
          <StartTour
            open={startTour}
            onClose={() => setStartTour(false)}
            onStart={() => {
              setShowTour(true);
              setStartTour(false);
            }}
          />

          {showTour && <SiteTour steps={steps} onClose={() => setShowTour(false)} />}
        </div>
      )}
    </>
  );
}
