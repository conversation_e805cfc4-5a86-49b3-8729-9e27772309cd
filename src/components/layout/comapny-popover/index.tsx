'use client';

import {
  <PERSON><PERSON>,
  <PERSON>over,
  <PERSON>overContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Label } from '@redington-gulf-fze/cloudquarks-component-library';
import React, { useState } from 'react';

import Image from 'next/image';
import { useTranslations } from 'next-intl';

export function ComapnyPopover() {
  const [open, setOpen] = useState(false);
  const t = useTranslations();
  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div
            className="countrydropdown arrowcustom bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokesoft flex items-center ltr:pl-[12px] ltr:xl:pl-[12px] ltr:2xl:pl-[14px] ltr:3xl:pl-[0.729vw] rtl:pr-[12px] rtl:xl:pr-[12px] rtl:2xl:pr-[14px] rtl:3xl:pr-[0.729vw]"
            onClick={() => setOpen(true)}
          >
            <i className="cloud-company-profile xl:text-[0.938vw] text-[20px] text-center"></i>
            <Select defaultValue="en">
              <SelectTrigger className="bg-transparent placeholder-text-sm font-[500] rounded-none border-none text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw]">
                <SelectValue placeholder="Country" /> Hexalytics{' '}
              </SelectTrigger>
            </Select>
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 mr-[290px] w-[350px] xl:w-[350px] 2xl:w-[395px] 3xl:w-[420px] rounded-none bg-[#FFF]">
          <div>
            <div className="border-b border-[#C9D3DB] flex justify-between items-center px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[22px] 3xl:py-[1.25vw]">
              <h4 className="text-InterfaceTexttitle text-[16px] xl:text-[20px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[700] leading-none">
                {t('manageCompanyProfile')}
              </h4>
              <i
                className="cloud-closecircle text-closecolor text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"
                onClick={() => setOpen(false)}
              ></i>
            </div>
            <div className="p-[28px] xl:p-[28px] 2xl:p-[30px] 3xl:p-[1.67vw] flex flex-col gap-[24px] 3xl:gap-[1.25vw]">
              <div>
                <div className="flex justify-between items-center pb-[8px] xl:pb-[8px] 2xl:pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
                  <p
                    className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 
                3xl:text-[0.833vw] font-[600]"
                  >
                    UAE
                  </p>

                  <Image src="/images/flags-sample.svg" width={34} height={24} alt="flags" />
                </div>
                <div className="mt-[10px]">
                  <RadioGroup defaultValue="en" className="flex flex-col gap-[0px]">
                    <div
                      className="flex items-center justify-between space-x-2 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.36vw] 
                    "
                    >
                      <div className="font-[500] space-x-2">
                        <RadioGroupItem value="c1" id="r1" />
                        <Label className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                          Hexalytics LLC - Dubai
                        </Label>
                      </div>
                      <div>
                        {' '}
                        <p className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                          1001001002
                        </p>
                      </div>
                    </div>
                    <div
                      className="flex items-center justify-between space-x-2 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.36vw] 
                 "
                    >
                      <div className="font-[400] space-x-2">
                        <RadioGroupItem value="c2" id="r2" />
                        <Label className="   text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                          Hexalytics - Abu Dhabi
                        </Label>
                      </div>
                      <div>
                        {' '}
                        <p className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                          1001001001
                        </p>
                      </div>
                    </div>
                    <div
                      className="flex items-center justify-between space-x-2 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.36vw] 
                  "
                    >
                      <div className="font-[400] space-x-2">
                        <RadioGroupItem value="c3" id="r3" />
                        <Label className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                          Alpha Systems - Dubai
                        </Label>
                      </div>
                      <div>
                        {' '}
                        <p className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                          1001001444
                        </p>
                      </div>
                    </div>
                  </RadioGroup>
                </div>
                <div className="mt-[16px] xl:mt-[32px] 2xl:mt-[32px] 3xl:mt-[1.667vw]">
                  <div className="flex justify-between items-center pb-[8px] xl:pb-[8px] 2xl:pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
                    <p
                      className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 
                3xl:text-[0.833vw] font-[600]"
                    >
                      KSA
                    </p>

                    <Image src="/images/flags-sample1.svg" width={34} height={24} alt="flags" />
                  </div>

                  <div className="mt-[10px]">
                    <RadioGroup defaultValue="en" className="flex flex-col gap-[0px]">
                      <div
                        className="flex items-center justify-between space-x-2 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.36vw] 
                    "
                      >
                        <div className="font-[500] space-x-2">
                          <RadioGroupItem value="c1" id="r1" />
                          <Label className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                            Innive Inc
                          </Label>
                        </div>
                        <div>
                          {' '}
                          <p className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                            1001001002
                          </p>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[22px] 3xl:py-[1.25vw] flex justify-end border-t border-[#E5E7EB]">
              <Button
                type="submit"
                className="bg-BrandPrimarypure rounded-none text-[#FFFFFF]  px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <Image
                  src="/images/save.svg"
                  width={18}
                  height={18}
                  className="w-[18px] h-[18px]"
                  alt="save"
                />
                {t('apply')}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
