import { Button, SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

type User = {
  category: string;
  name: string;
  email: string;
  contact: string;
  status?: 'inactive';
};
const data = [
  {
    category: 'AWS ',
    name: '<PERSON>',
    email: '<EMAIL> ',
    contact: '+971 555-0112 ',
  },
  {
    category: 'Azure Plan ',
    name: '<PERSON>',
    email: '<EMAIL> ',
    contact: '+971 555-0103 ',
  },
  {
    category: ' Azure RI',
    name: '<PERSON>',
    email: ' <EMAIL>',
    contact: '+971 555-0125 ',
  },
  {
    category: ' Commercial Market Place',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL> ',
    contact: '+971 555-0128 ',
  },
  {
    category: ' CSP',
    name: '<PERSON> <PERSON>',
    email: ' <EMAIL>',
    contact: '+971 555-0115 ',
  },
  {
    category: ' CSP NCE',
    name: 'Jenny Wilson',
    email: '<EMAIL> ',
    contact: ' +971 555-0117',
  },
  {
    category: 'Software Perpetual ',
    name: 'Guy Hawkins',
    email: '<EMAIL> ',
    contact: '+971 555-0109 ',
  },
  {
    category: ' Software Subscription',
    name: 'Albert Flores',
    email: '<EMAIL> ',
    contact: '+971 555-0107 ',
  },
  {
    category: 'Cisco ',
    name: 'Annette Black',
    email: ' <EMAIL>',
    contact: ' +971 555-0129',
  },
  {
    category: ' eScan',
    name: 'Dianne Russell',
    email: '<EMAIL> ',
    contact: ' +971 555-0117',
  },
  {
    category: ' Google Workspace Commitment',
    name: 'Arlene McCoy',
    email: '<EMAIL> ',
    contact: ' +971 555-0117',
  },
  {
    category: ' Google Workspace Flexible',
    name: 'Eleanor Pena',
    email: '<EMAIL> ',
    contact: ' +************',
  },
];
export function SaleContact() {
  const t = useTranslations();
  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'category',
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: () => (
        <div className="text-[14px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
          {t('brandCategory')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ">
          {row.getValue('category')}
        </div>
      ),
    },
    {
      accessorKey: 'name',
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: () => (
        <div className="text-[14px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
          {t('salesContactName')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('name')}
        </div>
      ),
    },
    {
      accessorKey: 'email',
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: () => (
        <div className="text-[14px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
          {t('emailLabel')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="cursor-pointer text-BrandSupport1pure text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('email')}
        </div>
      ),
    },
    {
      accessorKey: 'contact',
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: () => (
        <div className="text-[14px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
          {t('contactNumber')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('contact')}
        </div>
      ),
    },
  ];
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button className="px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:font-[500] text-[#3C4146] bg-transparent rounded-none text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          <i className="cloud-msguser text-center text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
          {t('pointOfContact')}
        </Button>
      </SheetTrigger>
      <SheetContent
        className={`${roboto.className} hideclose sm:max-w-[660px] xl:max-w-[640px] 2xl:max-w-[720px] 3xl:max-w-[40.76vw] p-[0px] h-full`}
        side={'right'}
      >
        <SheetHeader className="border-b border-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              {t('redingtonSalesContacts')}
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="overflow-y-auto h-full bg-InterfaceSurfacecomponentmuted px-[20px] lg:px-[20px] xl:px-[22px] 3xl:px-[1.25vw] pb-[20px] lg:pb-[20px] xl:pb-[22px] 3xl:pb-[1.25vw]">
          <div className="bg-InterfaceSurfacecomponentmuted">
            <div className="py-[20px] xl:py-[20px] 3xl:py-[1.042vw] mb-[100px] xl:mb-[5.208vw]">
              <div className="bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100">
                <div>
                  <DataTable data={data} columns={notificationColumns} withCheckbox={false} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
