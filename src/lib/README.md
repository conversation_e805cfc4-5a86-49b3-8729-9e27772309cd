# Lib Directory

This directory contains core utilities, configurations, and shared functionality used throughout the application.

## Purpose

Central location for:

- Application configurations
- Utility functions
- Business logic helpers
- API services
- Custom hooks
- Type definitions
- Constants and enums

## Structure

```
lib/
├── config/             # Application configurations
│   ├── env.ts
│   └── react-query.ts
├── constants/          # Constants and enums
│   ├── routes.ts
│   └── api.ts
├── helpers/           # Business logic helpers
│   ├── auth/
│   └── forms/
├── hooks/             # Custom React hooks
│   ├── auth/
│   └── common/
├── services/          # API services
│   └── api/
├── types/             # Shared TypeScript types
│   ├── auth.ts
│   └── common.ts
└── utils/             # Pure utility functions
    ├── string.ts
    ├── date.ts
    └── validation.ts
```

## Directory Descriptions

### config/

Configuration files for the application, including environment variables, external services, and library configurations.

### constants/

Application-wide constants, enums, and static values. These should be immutable and used across the application.

### helpers/

Domain-specific helper functions that may contain business logic, side effects, or combine multiple utilities.

### hooks/

Custom React hooks that can be reused across components. These should be well-documented and properly typed.

### services/

API services and external integrations. This includes API clients, service configurations, and API-specific logic.

### types/

Shared TypeScript types and interfaces that are used across multiple modules.

### utils/

Pure utility functions that are stateless, have no side effects, and can be used across different features.

## Best Practices

1. Keep utils pure and stateless
2. Document complex helper functions
3. Use TypeScript for better type safety
4. Write unit tests for utils and helpers
5. Keep configurations environment-aware
6. Use proper error handling in services
7. Follow single responsibility principle
