import { ApiResponse } from '@/types/core/api';
import { SendOtpResponse, VerifyOtpResponse } from '@/types/otp';
import { apiService } from '../axios';

class OtpService {
  async sendMobileOtp(payload: unknown): Promise<ApiResponse<SendOtpResponse>> {
    return apiService.post<SendOtpResponse>('/signup/mobile-send', payload);
  }

  async resendMobileOtp(payload: unknown): Promise<ApiResponse<SendOtpResponse>> {
    return apiService.post<SendOtpResponse>('/signup/mobile-resend', payload);
  }

  async sendEmailOtp(payload: unknown): Promise<ApiResponse<SendOtpResponse>> {
    return apiService.post<SendOtpResponse>('/signup/email-send', payload);
  }

  async resendEmailOtp(payload: unknown): Promise<ApiResponse<SendOtpResponse>> {
    return apiService.post<SendOtpResponse>('/signup/email-resend', payload);
  }

  async verifyEmailOtp(payload: unknown): Promise<ApiResponse<VerifyOtpResponse>> {
    return apiService.put<VerifyOtpResponse>('/signup/email-verify', payload);
  }

  async verifyMobileOtp(payload: unknown): Promise<ApiResponse<VerifyOtpResponse>> {
    return apiService.put<VerifyOtpResponse>('/signup/mobile-verify', payload);
  }
}

// Export a static instance
export const otpService = new OtpService();
