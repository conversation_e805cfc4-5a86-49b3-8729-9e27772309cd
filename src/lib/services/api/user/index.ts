import { ApiResponse } from '@/types/core/api';
import { DesignationType, TokenResponseType, UserDetailsType } from '@/types/user';
import { apiService } from '../axios';
import { serverCookiesService } from '../../storage';

class UserService {
  async registerDirectCustomerUser<T>(payload: unknown): Promise<ApiResponse<T>> {
    return apiService.post<T>('/signup?source=Direct Customer', payload);
  }

  async signInGuestUser(payload: unknown): Promise<ApiResponse<TokenResponseType>> {
    return apiService.post<TokenResponseType>('/auth/guest_user/emailpass', payload);
  }

  async createSession<T>(token: string): Promise<ApiResponse<T>> {
    return apiService.post<T>(
      '/auth/session',
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
  }

  async deleteSession<T>(): Promise<ApiResponse<T>> {
    const response = apiService.delete<T>('/auth/session');
    await serverCookiesService.flush();
    return response;
  }

  async forgotPassword<T>(payload: unknown): Promise<ApiResponse<T>> {
    return apiService.post<T>('/forgot-password', payload);
  }

  async getDesignations(): Promise<ApiResponse<DesignationType[]>> {
    return apiService.get<DesignationType[]>('/designation');
  }

  async getActors<T>(): Promise<ApiResponse<T>> {
    return apiService.get<T>('/actor');
  }

  async getUserDetails(): Promise<ApiResponse<UserDetailsType[]>> {
    return apiService.get<UserDetailsType[]>('/user');
  }

  async changePassword(payload: {
    oldPassword: string;
    newPassword: string;
  }): Promise<ApiResponse<unknown>> {
    return apiService.post<unknown>('/user/change-password', payload);
  }

  async updateUserDetails(payload: {
    first_name: string;
    last_name: string;
    designation: string;
    mobile_number: string;
    email: string;
    company_name: string;
    country_code: string;
    region_id?: string;
  }): Promise<ApiResponse<unknown>> {
    return apiService.put<unknown>('/user', payload);
  }
}

// Export a static instance
export const userService = new UserService();
