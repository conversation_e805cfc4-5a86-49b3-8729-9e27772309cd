import { ApiResponse } from '@/types/core/api';
import { apiService } from '../axios';
import { Brand, BrandCategoryGroup, Vendor } from '@/types/brand';
import { localStorageService } from '../../storage';
import { LOCALSTORAGE_KEYS } from '@/lib/enums';

class BrandService {
  async getVendors(): Promise<ApiResponse<Vendor[]>> {
    const regionId = await localStorageService.get(LOCALSTORAGE_KEYS.REGION);
    return apiService.get<Vendor[]>(`/partner-registration/vendor?region_id=${regionId}`);
  }

  async getBrands(vendorId: string): Promise<ApiResponse<Brand[]>> {
    const regionId = await localStorageService.get(LOCALSTORAGE_KEYS.REGION);
    return apiService.get<Brand[]>(
      `/partner-registration/brand?region_id=${regionId}&vendor_id=${vendorId}`
    );
  }

  async getBrandCategories(brandIds: string[]): Promise<ApiResponse<BrandCategoryGroup[]>> {
    const regionId = await localStorageService.get(LOCALSTORAGE_KEYS.REGION);
    const payload = {
      brand_id: brandIds,
    };
    return apiService.post<BrandCategoryGroup[]>(
      `/partner-registration/brand-category?region_id=${regionId}`,
      payload
    );
  }
}

// Export a static instance
export const brandService = new BrandService();
