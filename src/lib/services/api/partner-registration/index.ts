import { ApiResponse } from '@/types/core/api';
import { apiService } from '../axios';
import { VerifyGstNumberResponse } from '@/types/partner-registration';
import { QuestionnaireResponse } from '@/types/partner-registration/questionnaire';
import { localStorageService } from '../../storage';
import { LOCALSTORAGE_KEYS } from '@/lib/enums';

// Types for the submission payload
export interface PartnerRegistrationPayload {
  company_info: {
    gst_number: string;
    pan_number: string;
    cin_number?: string;
    company_name: string;
    address: {
      street1: string;
      street2?: string;
      country_business: string;
      state: string;
      city: string;
      postal_code: string;
      legal_status: string;
    };
    contact_info: {
      mobile_number: string;
      country_code: string;
    };
    website?: string;
  };
  additional_info: {
    number_of_offices_in_region?: string;
    other_countries_with_offices?: string[];
    number_of_warehouses_in_region?: number;
    number_of_employees?: number;
    number_of_sales_staff?: number;
    number_of_technical_staff?: number;
    twitter_account?: string;
    facebook_account?: string;
    linkedin_account?: string;
    instagram_account?: string;
  };
  business_info: {
    director_owner: {
      first_name: string;
      last_name: string;
      email: string;
      contact_info: {
        mobile_number: string;
        country_code: string;
      };
    };
    sales?: {
      first_name: string;
      last_name: string;
      email: string;
      contact_info: {
        mobile_number: string;
        country_code: string;
      };
    };
    accounts_operations?: {
      first_name: string;
      last_name: string;
      email: string;
      contact_info: {
        mobile_number: string;
        country_code: string;
      };
    };
    authorized_signatories: Array<{
      first_name: string;
      last_name: string;
      email: string;
      contact_info: {
        mobile_number: string;
        country_code: string;
      };
    }>;
  };
  documents: {
    pan_card_url?: string;
    gst_certificate_url?: string;
    tax_exemption_url?: string;
  };
  profile_info: Array<{
    question_id: string;
    answer_id: string;
  }>;
  user_brand_preferences: Array<{
    vendor_id: string;
    brand_id: string;
    brand_category_id: string;
  }>;
}

export interface PartnerRegistrationResponse {
  message: string;
  // registration_id: string;
  // status: string;
}

class PartnerRegistrationService {
  async verifyGstNumber(gstNo: string): Promise<ApiResponse<VerifyGstNumberResponse>> {
    return apiService.get<VerifyGstNumberResponse>(
      `/partner-registration/gst/verify?gstNo=${gstNo}`
    );
  }

  async getProfileQuestionnaire(): Promise<ApiResponse<QuestionnaireResponse>> {
    // Get region from localStorage
    const regionId = await localStorageService.get(LOCALSTORAGE_KEYS.REGION);

    return apiService.get<QuestionnaireResponse>(
      `/partner-registration/profile?region_id=${regionId}`
    );
  }

  async submitPartnerRegistration(
    payload: PartnerRegistrationPayload
  ): Promise<ApiResponse<PartnerRegistrationResponse>> {
    return apiService.post<PartnerRegistrationResponse>('/partner-registration', payload);
  }
}

export const partnerRegistrationService = new PartnerRegistrationService();
