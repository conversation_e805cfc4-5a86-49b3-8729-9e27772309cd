import { axiosInstance } from '@/lib/config/axios-client';
import { logger } from '@/lib/utils/logger';
import axios, { AxiosError, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types/core/api';

// Define a type for the error response data
interface ErrorResponseData {
  message?: string;
}

// Centralized error handling function
const handleApiError = (error: unknown, requestType: string): never => {
  logger.error(`${requestType} request failed`, error);
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;
    const status = axiosError.response?.status ?? 500;
    const data = axiosError.response?.data;

    // Extract error message with proper type checking
    const errorResponseData = axiosError.response?.data as ErrorResponseData | undefined;
    const errorMessage = errorResponseData?.message || axiosError.message || 'Something went wrong';

    throw {
      message: errorMessage,
      status,
      data,
    };
  }
  throw error;
};

// Process the API response to a standardized format
const processResponse = <T>(response: AxiosResponse): ApiResponse<T> => {
  return {
    data: response?.data?.data ?? response?.data,
    message: response?.data?.message,
    status: response.status,
  };
};

class AxiosApiService {
  // GET Request
  async get<T>(endpoint: string, options: object = {}): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.get(endpoint, options);
      return processResponse<T>(response);
    } catch (error) {
      return handleApiError(error, 'GET');
    }
  }

  // POST Request
  async post<T>(endpoint: string, payload: unknown, options: object = {}): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.post(endpoint, payload, options);
      return processResponse<T>(response);
    } catch (error) {
      return handleApiError(error, 'POST');
    }
  }

  // PUT Request
  async put<T>(endpoint: string, payload: unknown, options: object = {}): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.put(endpoint, payload, options);
      return processResponse<T>(response);
    } catch (error) {
      return handleApiError(error, 'PUT');
    }
  }

  // DELETE Request
  async delete<T>(endpoint: string, options: object = {}): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.delete(endpoint, options);
      return processResponse<T>(response);
    } catch (error) {
      return handleApiError(error, 'DELETE');
    }
  }
}

// Export a static instance for all services to use
export const apiService = new AxiosApiService();
