import { LocalStorageService } from './local-storage-service';
import { SessionStorageService } from './session-storage-service';
import * as serverCookieFunctions from './server-cookie-service';
export * from './local-storage-service';
export * from './session-storage-service';
export * from './client-cookie-service';
export * from './server-cookie-service';

// Create service instances
export const localStorageService = new LocalStorageService();
export const sessionStorageService = new SessionStorageService();
export const serverCookiesService = {
  get: serverCookieFunctions.get,
  set: serverCookieFunctions.set,
  delete: serverCookieFunctions.remove,
  flush: serverCookieFunctions.flush,
};
