import { encrypt, decrypt, encrypt<PERSON><PERSON>, remember<PERSON>ey, get<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils/encryption';
import { logger } from '@/lib/utils/logger';

export class SessionStorageService {
  private isAvailable(): boolean {
    try {
      const testKey = '__test__';
      sessionStorage.setItem(testKey, testKey);
      sessionStorage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      if (!this.isAvailable()) return null;

      const encryptedKey = encryptKey(key);
      const encryptedValue = sessionStorage.getItem(encryptedKey);

      if (!encryptedValue) return null;

      const decryptedValue = await decrypt(encryptedValue);
      return JSON.parse(decryptedValue) as T;
    } catch (e) {
      logger.error('SessionStorageService: Error getting item', e);
      return null;
    }
  }

  async set<T>(key: string, value: T): Promise<void> {
    try {
      if (!this.isAvailable()) return;

      const encryptedKey = encryptKey(key);
      const encryptedValue = await encrypt(JSON.stringify(value));

      sessionStorage.setItem(encryptedKey, encryptedValue);
      rememberKey(key, encryptedKey);
    } catch (e) {
      logger.error('SessionStorageService: Error setting item', e);
    }
  }

  delete(key: string): void {
    try {
      if (!this.isAvailable()) return;

      const encryptedKey = encryptKey(key);
      sessionStorage.removeItem(encryptedKey);
    } catch (e) {
      logger.error('SessionStorageService: Error deleting item', e);
    }
  }

  flush(): void {
    try {
      if (!this.isAvailable()) return;
      sessionStorage.clear();
    } catch (e) {
      logger.error('SessionStorageService: Error flushing storage', e);
    }
  }

  // Helper method to get all keys (original, not encrypted)
  getAllKeys(): string[] {
    try {
      if (!this.isAvailable()) return [];

      const keys: string[] = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const encryptedKey = sessionStorage.key(i);
        if (encryptedKey) {
          const originalKey = getOriginalKey(encryptedKey);
          if (originalKey) {
            keys.push(originalKey);
          }
        }
      }
      return keys;
    } catch (e) {
      logger.error('SessionStorageService: Error getting all keys', e);
      return [];
    }
  }
}
