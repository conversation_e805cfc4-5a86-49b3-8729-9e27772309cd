'use server';

import { cookies } from 'next/headers';
import { logger } from '@/lib/utils/logger';

interface CookieOptions {
  expires?: Date | number;
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  httpOnly?: boolean;
  maxAge?: number;
}

const defaultOptions: CookieOptions = {
  path: '/',
  sameSite: 'strict',
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
};

export async function get<T>(key: string): Promise<T | null> {
  try {
    const cookieStore = await cookies();
    const value = cookieStore.get(key)?.value;
    if (!value || value === 'undefined') return null;
    return JSON.parse(value) as T;
  } catch (e) {
    logger.error('ServerCookieService: Error getting cookie', e);
    return null;
  }
}

export async function set<T>(key: string, value: T, options: CookieOptions = {}): Promise<void> {
  try {
    const mergedOptions = { ...defaultOptions, ...options };
    const stringValue = JSON.stringify(value);

    // Convert expires to maxAge if provided
    if (mergedOptions.expires && !mergedOptions.maxAge) {
      if (typeof mergedOptions.expires === 'number') {
        mergedOptions.maxAge = mergedOptions.expires * 24 * 60 * 60;
      } else {
        mergedOptions.maxAge = Math.floor((mergedOptions.expires.getTime() - Date.now()) / 1000);
      }
    }

    const cookieStore = await cookies();
    cookieStore.set(key, stringValue, {
      path: mergedOptions.path,
      domain: mergedOptions.domain,
      secure: mergedOptions.secure,
      sameSite: mergedOptions.sameSite,
      httpOnly: mergedOptions.httpOnly,
      maxAge: mergedOptions.maxAge,
    });
  } catch (e) {
    logger.error('ServerCookieService: Error setting cookie', e);
  }
}

export async function remove(key: string): Promise<void> {
  try {
    const cookieStore = await cookies();
    cookieStore.delete(key);
  } catch (e) {
    logger.error('ServerCookieService: Error deleting cookie', e);
  }
}

export async function flush(): Promise<void> {
  try {
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    for (const cookie of allCookies) {
      await remove(cookie.name);
    }
  } catch (e) {
    logger.error('ServerCookieService: Error flushing cookies', e);
  }
}
