import { encrypt, decrypt, encrypt<PERSON><PERSON>, remember<PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils/encryption';
import { logger } from '@/lib/utils/logger';

export class LocalStorageService {
  private isAvailable(): boolean {
    try {
      const testKey = '__test__';
      localStorage.setItem(testKey, testKey);
      localStorage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      if (!this.isAvailable()) return null;

      const encryptedKey = encryptKey(key);
      const encryptedValue = localStorage.getItem(encryptedKey);

      if (!encryptedValue) return null;

      const decryptedValue = await decrypt(encryptedValue);
      return JSON.parse(decryptedValue) as T;
    } catch (e) {
      logger.error('LocalStorageService: Error getting item', e);
      return null;
    }
  }

  async set<T>(key: string, value: T): Promise<void> {
    try {
      if (!this.isAvailable()) return;

      const encryptedKey = encryptKey(key);
      const encryptedValue = await encrypt(JSON.stringify(value));

      localStorage.setItem(encryptedKey, encryptedValue);
      rememberKey(key, encryptedKey);
    } catch (e) {
      logger.error('LocalStorageService: Error setting item', e);
    }
  }

  delete(key: string): void {
    try {
      if (!this.isAvailable()) return;

      const encryptedKey = encryptKey(key);
      localStorage.removeItem(encryptedKey);
    } catch (e) {
      logger.error('LocalStorageService: Error deleting item', e);
    }
  }

  flush(): void {
    try {
      if (!this.isAvailable()) return;
      localStorage.clear();
    } catch (e) {
      logger.error('LocalStorageService: Error flushing storage', e);
    }
  }

  getAllKeys(): string[] {
    try {
      if (!this.isAvailable()) return [];

      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const encryptedKey = localStorage.key(i);
        if (encryptedKey) {
          const originalKey = getOriginalKey(encryptedKey);
          if (originalKey) {
            keys.push(originalKey);
          }
        }
      }
      return keys;
    } catch (e) {
      logger.error('LocalStorageService: Error getting all keys', e);
      return [];
    }
  }
}
