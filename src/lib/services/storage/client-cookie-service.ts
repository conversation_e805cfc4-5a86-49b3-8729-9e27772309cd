'use client';

import { logger } from '@/lib/utils/logger';

interface CookieOptions {
  expires?: Date | number;
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  httpOnly?: boolean;
}

export class ClientCookieService {
  private defaultOptions: CookieOptions = {
    path: '/',
    sameSite: 'strict',
    secure: process.env.NODE_ENV === 'production',
  };

  get<T>(key: string): T | null {
    try {
      const value = document.cookie
        .split('; ')
        .find((row) => row.startsWith(`${key}=`))
        ?.split('=')[1];

      if (!value || value === 'undefined') return null;
      return JSON.parse(decodeURIComponent(value)) as T;
    } catch (e) {
      logger.error('ClientCookieService: Error getting cookie', e);
      return null;
    }
  }

  set<T>(key: string, value: T, options: CookieOptions = {}): void {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const encodedValue = encodeURIComponent(JSON.stringify(value));
      let cookie = `${key}=${encodedValue}`;

      if (mergedOptions.expires) {
        if (typeof mergedOptions.expires === 'number') {
          const date = new Date();
          date.setTime(date.getTime() + mergedOptions.expires * 24 * 60 * 60 * 1000);
          cookie += `; expires=${date.toUTCString()}`;
        } else {
          cookie += `; expires=${mergedOptions.expires.toUTCString()}`;
        }
      }

      if (mergedOptions.path) cookie += `; path=${mergedOptions.path}`;
      if (mergedOptions.domain) cookie += `; domain=${mergedOptions.domain}`;
      if (mergedOptions.secure) cookie += '; secure';
      if (mergedOptions.sameSite) cookie += `; samesite=${mergedOptions.sameSite}`;

      document.cookie = cookie;
    } catch (e) {
      logger.error('ClientCookieService: Error setting cookie', e);
    }
  }

  delete(key: string): void {
    try {
      document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    } catch (e) {
      logger.error('ClientCookieService: Error deleting cookie', e);
    }
  }

  flush(): void {
    try {
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const key = cookie.split('=')[0].trim();
        this.delete(key);
      }
    } catch (e) {
      logger.error('ClientCookieService: Error flushing cookies', e);
    }
  }
}

export const clientCookiesService = new ClientCookieService();
