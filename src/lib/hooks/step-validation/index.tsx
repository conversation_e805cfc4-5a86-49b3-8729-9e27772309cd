'use client';
import { logger } from '@/lib/utils/logger';
import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';

export interface ValidationContextType {
  setStepValid: (step: number, isValid: boolean) => void;
  isStepValid: (step: number) => boolean;
  triggerValidation: (step: number) => void;
  validationTriggers: Record<number, number>;
  setStepError: (step: number, error: string | null) => void;
  getStepError: (step: number) => string | null;
  clearAllErrors: () => void;
}

const ValidationContext = createContext<ValidationContextType | null>(null);

export const useValidation = () => {
  const context = useContext(ValidationContext);
  if (!context) {
    throw new Error('useValidation must be used within ValidationProvider');
  }
  return context;
};

export const ValidationProvider = ({ children }: { children: React.ReactNode }) => {
  const [stepValidation, setStepValidation] = useState<Record<number, boolean>>({});
  const [validationTriggers, setValidationTriggers] = useState<Record<number, number>>({});
  const [stepErrors, setStepErrors] = useState<Record<number, string | null>>({});

  const setStepValid = useCallback((step: number, isValid: boolean) => {
    setStepValidation((prev) => ({ ...prev, [step]: isValid }));
    if (isValid) {
      setStepErrors((prev) => ({ ...prev, [step]: null }));
    }
  }, []);

  const isStepValid = useCallback(
    (step: number) => {
      logger.log('stepValidation', stepValidation);
      return stepValidation[step] ?? false;
    },
    [stepValidation]
  );

  const triggerValidation = useCallback((step: number) => {
    setValidationTriggers((prev) => ({ ...prev, [step]: (prev[step] || 0) + 1 }));
  }, []);

  const setStepError = useCallback((step: number, error: string | null) => {
    setStepErrors((prev) => ({ ...prev, [step]: error }));
  }, []);

  const getStepError = useCallback(
    (step: number) => {
      return stepErrors[step] || null;
    },
    [stepErrors]
  );

  const clearAllErrors = useCallback(() => {
    setStepErrors({});
  }, []);

  const value: ValidationContextType = useMemo(
    () => ({
      setStepValid,
      isStepValid,
      triggerValidation,
      validationTriggers,
      setStepError,
      getStepError,
      clearAllErrors,
    }),
    [
      setStepValid,
      isStepValid,
      triggerValidation,
      validationTriggers,
      setStepError,
      getStepError,
      clearAllErrors,
    ]
  );

  return <ValidationContext.Provider value={value}>{children}</ValidationContext.Provider>;
};
