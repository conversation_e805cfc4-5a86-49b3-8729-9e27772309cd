import { logger } from '@/lib/utils/logger';
import { env } from '@/lib/config/env';
// Function to convert ArrayBuffer to hex string
const ab2hex = (buffer: ArrayBuffer) => {
  return Array.from(new Uint8Array(buffer))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
};

// Function to convert hex string to ArrayBuffer
const hex2ab = (hex: string) => {
  const buffer = new ArrayBuffer(hex.length / 2);
  const view = new Uint8Array(buffer);
  for (let i = 0; i < hex.length; i += 2) {
    view[i / 2] = parseInt(hex.substr(i, 2), 16);
  }
  return buffer;
};

// Generate a key from the encryption key
async function getKey(): Promise<CryptoKey> {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(env.NEXT_PUBLIC_STORAGE_ENCRYPTION_KEY);

  return await crypto.subtle.importKey('raw', keyData, { name: 'AES-GCM' }, false, [
    'encrypt',
    'decrypt',
  ]);
}

interface EncryptedData {
  encrypted: string;
  iv: string;
}

export async function encrypt(text: string): Promise<string> {
  try {
    const key = await getKey();
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encoder = new TextEncoder();
    const data = encoder.encode(text);

    const encrypted = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv.buffer,
      },
      key,
      data
    );

    const encryptedData: EncryptedData = {
      encrypted: ab2hex(encrypted),
      iv: ab2hex(iv.buffer),
    };

    return JSON.stringify(encryptedData);
  } catch (error) {
    logger.error('Encryption error:', error);
    return text; // Return original text if encryption fails
  }
}

export async function decrypt(encryptedText: string): Promise<string> {
  try {
    const encryptedData: EncryptedData = JSON.parse(encryptedText);
    const key = await getKey();

    const decrypted = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: hex2ab(encryptedData.iv),
      },
      key,
      hex2ab(encryptedData.encrypted)
    );

    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  } catch (error) {
    logger.error('Decryption error:', error);
    return encryptedText; // Return original text if decryption fails
  }
}

// Function to encrypt storage keys
export function encryptKey(key: string): string {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(key + env.NEXT_PUBLIC_STORAGE_ENCRYPTION_KEY);
    const hashArray = Array.from(new Uint8Array(data));
    const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
    return hashHex.slice(0, 32); // Return first 32 chars for shorter keys
  } catch (error) {
    logger.error('Key encryption error:', error);
    return key; // Return original key if encryption fails
  }
}

// Store the key mappings in memory for reverse lookup
const keyMappings = new Map<string, string>();

// Function to remember original key mapping
export function rememberKey(originalKey: string, encryptedKey: string) {
  keyMappings.set(encryptedKey, originalKey);
}

// Function to get original key from encrypted key
export function getOriginalKey(encryptedKey: string): string | undefined {
  return keyMappings.get(encryptedKey);
}
