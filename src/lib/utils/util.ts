import { ApiResponseError } from '@/types';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
  const result = { ...obj };
  for (const key of keys) {
    delete result[key];
  }
  return result;
}

export function isApiResponseError(error: unknown): error is ApiResponseError {
  return typeof error === 'object' && error !== null && 'message' in error;
}

/**
 * Returns the text direction for the given locale.
 * @param locale - A string like 'en', 'ar', etc.
 * @returns 'rtl' or 'ltr'
 */
export function getDirection(locale: string): 'rtl' | 'ltr' {
  const RTL_LANGUAGES = ['ar'];

  if (RTL_LANGUAGES.includes(locale.toLowerCase())) {
    return 'rtl';
  }

  return 'ltr';
}
