import { logger } from '@/lib/utils/logger';
import { env } from './env';

interface CDNManifest {
  files: {
    [key: string]: string;
  };
  version: string;
}

let manifestCache: CDNManifest | null = null;

/**
 * Fetches and caches the CDN manifest
 */
async function getManifest(): Promise<CDNManifest> {
  if (manifestCache) {
    return manifestCache;
  }

  try {
    const manifestUrl = `${env.NEXT_PUBLIC_CDN_URL}/manifest.json`;
    const response = await fetch(manifestUrl, {
      next: {
        revalidate: 3600, // Revalidate manifest every hour
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch manifest');
    }

    manifestCache = await response.json();
    return manifestCache!;
  } catch (error) {
    logger.error('Error fetching CDN manifest:', error);
    throw error;
  }
}

/**
 * Gets the URL for a CSS file from the CDN
 * @param cssPath - The path to the CSS file relative to the css directory (e.g., 'styles.css', 'components/button.css')
 * @param options - Optional configuration
 * @returns The complete URL to the CSS file
 */
export async function getCDNStylesheet(
  cssPath: string,
  options: {
    /** Whether to force no-cache version in development */
    forceDev?: boolean;
  } = {}
): Promise<string> {
  const { forceDev = true } = options;

  try {
    logger.info('Getting CDN stylesheet', {
      env: env.NEXT_PUBLIC_NODE_ENV,
      cssPath,
    });

    if (env.NEXT_PUBLIC_NODE_ENV === 'development') {
      // In development, use the local server directly with cache busting if enabled
      const cacheBuster = forceDev ? `?v=${Date.now()}` : '';
      return `${env.NEXT_PUBLIC_CDN_URL}/css/${cssPath}${cacheBuster}`;
    }

    // In production, use the manifest to get the hashed filename
    const manifest = await getManifest();
    const hashedPath = manifest.files[cssPath];

    if (!hashedPath) {
      logger.error('CSS file not found in manifest:', cssPath);
      throw new Error(`CSS file "${cssPath}" not found in manifest`);
    }

    return `${env.NEXT_PUBLIC_CDN_URL}/css/${hashedPath}`;
  } catch (error) {
    logger.error('Error loading CDN stylesheet:', {
      error,
      cssPath,
    });

    // Fallback to non-hashed version
    const cacheBuster = forceDev ? `?v=${Date.now()}` : '';
    return `${env.NEXT_PUBLIC_CDN_URL}/css/${cssPath}${cacheBuster}`;
  }
}

/**
 * Gets multiple CSS files from the CDN
 * @param cssPaths - Array of CSS file paths
 * @returns Array of complete URLs to the CSS files
 */
export async function getCDNStylesheets(
  cssPaths: string[],
  options?: Parameters<typeof getCDNStylesheet>[1]
): Promise<string[]> {
  return await Promise.all(cssPaths.map((path) => getCDNStylesheet(path, options)));
}
