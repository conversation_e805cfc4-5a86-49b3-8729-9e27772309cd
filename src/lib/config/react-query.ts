import { QueryClient } from '@tanstack/react-query';
import { logger } from '@/lib/utils/logger';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes
      refetchOnWindowFocus: false,
      retry: 1,
    },
    mutations: {
      retry: 1,
      onError: (error) => {
        logger.error('Mutation Error:', error);
      },
    },
  },
});

// Optional: Add global query keys for better organization
export const queryKeys = {
  auth: {
    profile: ['profile'] as const,
    session: ['session'] as const,
    user: ['auth', 'user'],
  },
  users: {
    all: ['users'] as const,
    detail: (id: string) => ['users', id] as const,
  },
  // Add more query keys as needed
} as const;
