import { z } from 'zod';

const envSchema = z.object({
  NEXT_PUBLIC_API_URL: z.string().url(),
  NEXT_PUBLIC_SENTRY_DSN: z.string().optional(),
  NEXT_PUBLIC_STORAGE_ENCRYPTION_KEY: z
    .string()
    .min(32)
    .default('default-secure-key-min-32-chars-long!!'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().default('3000'),
  NEXT_PUBLIC_NODE_ENV: z.enum(['development', 'production']).default('development'),
  NEXT_PUBLIC_CDN_URL: z.string().url().default('http://localhost:8080'),
  // Add more environment variables as needed
});

export type Env = z.infer<typeof envSchema>;

export function validateEnv(): Env {
  try {
    return envSchema.parse({
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
      NEXT_PUBLIC_STORAGE_ENCRYPTION_KEY: process.env.NEXT_PUBLIC_STORAGE_ENCRYPTION_KEY,
      NODE_ENV: process.env.NODE_ENV,
      PORT: process.env.PORT,
      NEXT_PUBLIC_NODE_ENV: process.env.NEXT_PUBLIC_NODE_ENV,
      NEXT_PUBLIC_CDN_URL: process.env.NEXT_PUBLIC_CDN_URL,
      // Add more environment variables as needed
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.issues.map((issue) => issue.path.join('.'));
      throw new Error(
        `❌ Invalid environment variables: ${missingVars.join(', ')}. Please check your .env file`
      );
    }
    throw error;
  }
}

export const env = validateEnv();
