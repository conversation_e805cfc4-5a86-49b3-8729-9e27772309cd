import axios from 'axios';
import { env } from '@/lib/config/env';
import { serverCookiesService } from '../services/storage';

export const axiosInstance = axios.create({
  baseURL: env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Response
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const status = error.response?.status;
    const requestUrl = error.config?.url || '';

    // Ignore 401/403 errors for /auth/emailpass route
    if ((status === 401 || status === 403) && !requestUrl.includes('/emailpass')) {
      if (typeof window !== 'undefined') {
        await serverCookiesService.flush();
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);
