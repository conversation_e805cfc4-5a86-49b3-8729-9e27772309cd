export interface QuestionnaireAnswer {
  answer: string;
  id: string;
}

export interface AnswerType {
  type:
    | 'input'
    | 'single choice drop down'
    | 'single choice'
    | 'multiple choice'
    | 'multiple choice dropdown';
  id: string;
}

export interface Question {
  question: string;
  id: string;
  answer_type_id: string;
  answers: QuestionnaireAnswer[];
  answer_type: AnswerType;
}

export interface QuestionnaireResponse {
  questions: Question[];
}

export interface ProfileAnswerData {
  question_id: string;
  answer_id: string;
}

export interface ProfileInfoSubmission {
  profile_info: ProfileAnswerData[];
}

export interface ProfileInfoSubmissionResponse {
  success: boolean;
  message?: string;
  data?: {
    id: string;
    status: string;
  };
}

export type QuestionAnswerValue = string | string[]; // For different input types
