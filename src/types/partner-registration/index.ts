export interface VerifyGstNumberResponse {
  taxpayerInfo: {
    stjCd: string;
    lgnm: string;
    stj: string;
    dty: string;
    adadr: unknown[];
    cxdt: string;
    gstin: string;
    nba: string[];
    rgdt: string;
    ctb: string;
    pradr: {
      addr: {
        bnm: string;
        loc: string;
        st: string;
        bno: string;
        stcd: string;
        dst: string;
        city: string;
        flno: string;
        lt: string;
        pncd: string;
        lg: string;
      };
      ntr: string;
    };
    tradeNam: string;
    sts: string;
    ctjCd: string;
    ctj: string;
    panNo: string;
  };
  compliance: {
    filingFrequency: string | null;
  };
  filing: unknown[];
  error: boolean;
  message: string;
}
