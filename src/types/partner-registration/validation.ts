// Validation interface types for partner registration components
export interface ValidationState {
  isValid: boolean;
  errors: Record<string, string>;
  hasErrors: boolean;
}

export interface ValidationRef {
  validateForm: () => Promise<ValidationState> | ValidationState;
  triggerValidation?: () => void;
}

export interface BaseComponentProps {
  onValidationRef?: (ref: ValidationRef) => void;
  validationState?: ValidationState;
}

// Component-specific props
export type CompanyInformationProps = BaseComponentProps;
export type BusinessInformationProps = BaseComponentProps;
export type DocumentInformationProps = BaseComponentProps;
export type PartnerProfileProps = BaseComponentProps;
