/**
 * User-related type definitions
 */
export interface UserType {
  source?: 'Partner' | 'Direct Customer';
  id: string;
  first_name: string;
  last_name: string;
  designation: string;
  mobile_number: string;
  email: string;
  company_name: string;
  country_code: string;
  has_sales_reference: boolean;
  redington_reference?: string;
  profile_pic_url?: string;
  is_active?: boolean;
  metadata?: Record<string, unknown>;
  region_id: string;
  country_id: string;
}

export interface TokenResponseType {
  token: string;
}

export interface DesignationType {
  id: string;
  name: string;
  key: string;
}

export interface UserDetailsType {
  id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  mobile_number: string;
  company_name: string;
  profile_pic_url: string | null;
  designation: {
    id: string;
    name: string;
  };
  iso_2: string;
  persona: string;
}

export interface ActorType {
  id: string;
  actor: string;
  metadata: object | null;
  created_at: string;
  updated_at: string;
  deleted_at: null;
}
