# Types Directory

This directory contains global TypeScript type definitions that are shared across the application.

## Purpose

- Global type definitions
- Type augmentation
- Shared interfaces
- Type utilities
- External package type declarations

## Structure

```
types/
├── api.ts            # API-related types
├── common.ts         # Shared utility types
├── env.ts           # Environment variable types
└── next.ts          # Next.js specific types
```

## Best Practices

1. Keep types focused and well-documented
2. Use proper naming conventions
3. Leverage TypeScript utility types
4. Export types through index files
5. Use type inference when possible
6. Document complex types
7. Follow TypeScript best practices

## Example Types

```typescript
// api.ts
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

// common.ts
export type Nullable<T> = T | null;

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// env.ts
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NEXT_PUBLIC_API_URL: string;
      NODE_ENV: 'development' | 'production' | 'test';
    }
  }
}
```

## Type Organization

1. Group related types together
2. Use descriptive names
3. Keep interfaces and types separate
4. Use proper exports
5. Document complex types
6. Use generics when appropriate
7. Follow naming conventions
