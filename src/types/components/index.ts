/**
 * Common component prop types
 */
export interface PageProps {
  params: { token: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

export interface VerificationSheetProps {
  open: boolean;
  onClose: (data: OtpComponentResponse) => void;
  input: string;
}

export interface QuickOrderSheetProps {
  open: boolean;
  onClose: () => void;
}
export interface ProductDetailSheetProps {
  open: boolean;
  onClose: () => void;
}
export interface ActivitylogSheetProps {
  open: boolean;
  onClose: () => void;
}
export interface CommanSheetProps {
  open: boolean;
  onClose: () => void;
}

export interface CancelOuoteSheetProps {
  open: boolean;
  onClose: () => void;
}
export interface PartnerRemarkSheetProps {
  open: boolean;
  onClose: () => void;
}

export interface TermsSheetProps {
  open: boolean;
  onClose: (data: unknown) => void;
}

export interface SheetProps {
  open: boolean;
  onClose: (data: unknown) => void;
}

export interface OnboardNewCompanyProps {
  open: boolean;
  onClose: (data: unknown) => void;
}

export interface ImageUploaderProps {
  onImageUpload: (file: File) => void;
}

export interface OtpComponentResponse {
  data: boolean | null;
  state: boolean;
}

export type NavItem = {
  label: string;
  href: string;
};

export type SidebarProps = {
  title: string;
  navItems: NavItem[];
};

export interface TourStep {
  id: string;
  title: string;
  description: string;
  targetId: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
}
