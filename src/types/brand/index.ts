export interface Vendor {
  id: string;
  vendor_name: string;
}

export interface Brand {
  id: string;
  vendor_id: string;
  brand_name: string;
}

export interface BrandCategory {
  id: string;
  vendor_id: string;
  category_name: string;
}

export interface Brand {
  id: string;
  name: string;
}

export interface BrandCategoryItem {
  id: string;
  name: string;
}

export interface BrandCategoryGroup {
  brand: Brand;
  brand_category: BrandCategoryItem[];
}
