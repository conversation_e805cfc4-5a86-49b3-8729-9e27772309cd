# Store Directory

This directory contains Redux store configuration and state management logic using Redux Toolkit.

## Purpose

- Global state management
- Redux store configuration
- Redux slices
- State selectors
- Action creators

## Structure

```
store/
├── index.ts           # Store configuration and exports
└── slices/            # Redux slices
    ├── authSlice.ts   # Authentication state
    ├── uiSlice.ts     # UI state
    └── userSlice.ts   # User state
```

## Best Practices

1. Use Redux Toolkit's createSlice for reducing boilerplate
2. Keep slices focused and domain-specific
3. Use TypeScript for type-safe actions and state
4. Export selectors alongside slices
5. Use proper state normalization
6. Implement proper error handling
7. Use middleware for side effects

## Example Slice Structure

```typescript
// authSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  loading: false,
};

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // ... reducers
  },
});
```

## Using the Store

1. Access state with useAppSelector:

```typescript
const user = useAppSelector((state) => state.auth.user);
```

2. Dispatch actions with useAppDispatch:

```typescript
const dispatch = useAppDispatch();
dispatch(authSlice.actions.setUser(user));
```

3. Create selectors for complex state:

```typescript
export const selectUserPermissions = (state: RootState) => state.auth.user?.permissions ?? [];
```
