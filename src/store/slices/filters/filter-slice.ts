import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface FilterOption {
  value: string;
  label: string;
}

export type FilterValue = string | number | boolean | Date | null | undefined | FilterOption;

export interface FilterPayload {
  moduleKey: string;
  filters: Record<string, FilterValue>;
}

export interface FiltersState {
  [moduleKey: string]: {
    filters: Record<string, FilterValue>;
    applied: boolean;
  };
}

const initialState: FiltersState = {};

const filterSlice = createSlice({
  name: 'filters',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<FilterPayload>) => {
      const { moduleKey, filters } = action.payload;
      state[moduleKey] = {
        filters: { ...state[moduleKey]?.filters, ...filters },
        applied: true,
      };
    },
    removeFilterKey: (state, action: PayloadAction<{ moduleKey: string; key: string }>) => {
      const { moduleKey, key } = action.payload;
      if (state[moduleKey]) {
        delete state[moduleKey].filters[key];
        state[moduleKey].applied = true;
      }
    },
    clearFilters: (state, action: PayloadAction<string>) => {
      const moduleKey = action.payload;
      if (state[moduleKey]) {
        state[moduleKey] = { filters: {}, applied: false };
      }
    },
  },
});

export const { setFilters, removeFilterKey, clearFilters } = filterSlice.actions;
export default filterSlice.reducer;
