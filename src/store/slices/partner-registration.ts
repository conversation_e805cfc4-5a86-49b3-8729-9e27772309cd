import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Company Information Types
export interface CompanyInformationState {
  gstNumber: string;
  panNumber: string;
  isSezCompany: boolean;
  cinNumber: string;
  companyName: string;
  website: string;
  address: {
    street1: string;
    street2: string;
    countryBusiness: string;
    state: string;
    city: string;
    postalCode: string;
    legalStatus: string;
  };
  additionalInfo: {
    numberOfOfficesInRegion: string;
    otherCountriesWithOffices: string[];
    numberOfWareHousesInRegion: number | string;
    numberOfEmployees: number | string;
    numberOfSalesStaff: number | string;
    numberOfTechnicalStaff: number | string;
    twitterAccount: string;
    facebookAccount: string;
    linkedinAccount: string;
    instagramAccount: string;
  };
  contactInfo: {
    mobileNumber: string;
    countryCode: string;
  };
}

// Business Information Types
export interface KeyContact {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
}

export interface OptionalKeyContact {
  firstName: string | undefined;
  lastName: string | undefined;
  email: string | undefined;
  mobileNumber: string | undefined;
}

export interface BusinessInformationState {
  directorDetails: KeyContact;
  salesDetails: OptionalKeyContact;
  accountsDetails: OptionalKeyContact;
  authorizedSignatory: KeyContact[];
}

// Document Information Types
export interface DocumentFile {
  id: string;
  name: string;
  url: string; // Store S3 URL instead of File object
  uploadedAt: string | null;
  status: 'pending' | 'uploaded' | 'verified' | 'rejected';
  size: number; // File size in bytes
}

export interface DocumentInformationState {
  passportDocuments: DocumentFile[]; // Max 4 files
  auditFinancials: DocumentFile[]; // Max 1 file
  taxExemptionDocument: DocumentFile[]; // Max 1 file
  taxExemptionExpiryDate: string; // Expiry date for tax exemption document
}

// Profile Information Types
export interface ProfileAnswerData {
  question_id: string;
  answer_id: string;
}

export interface ProfileInformationState {
  profile_info: ProfileAnswerData[];
  acceptTermsCondition: boolean;
}

// Main Partner Registration State
export interface PartnerRegistrationState {
  currentStep: number;
  completedSteps: number[];
  isFormValid: boolean;
  companyInformation: CompanyInformationState;
  businessInformation: BusinessInformationState;
  documentInformation: DocumentInformationState;
  profileInformation: ProfileInformationState;
  userBrandPreferences: BrandPreference[];
  lastSavedAt: string | null;
  isDirty: boolean;
}

interface LabeledItem {
  label: string;
  value: string;
}

interface BrandPreference {
  vendor: LabeledItem;
  brand: LabeledItem;
  brandCategory: LabeledItem;
}

// Initial state
const initialCompanyInformation: CompanyInformationState = {
  gstNumber: '',
  panNumber: '',
  isSezCompany: false,
  cinNumber: '',
  companyName: '',
  website: '',
  address: {
    street1: '',
    street2: '',
    countryBusiness: '',
    state: '',
    city: '',
    postalCode: '',
    legalStatus: '',
  },
  additionalInfo: {
    numberOfOfficesInRegion: '',
    otherCountriesWithOffices: [],
    numberOfWareHousesInRegion: '',
    numberOfEmployees: '',
    numberOfSalesStaff: '',
    numberOfTechnicalStaff: '',
    twitterAccount: '',
    facebookAccount: '',
    linkedinAccount: '',
    instagramAccount: '',
  },
  contactInfo: {
    mobileNumber: '',
    countryCode: '',
  },
};

const initialBusinessInformation: BusinessInformationState = {
  directorDetails: {
    email: '',
    mobileNumber: '',
    firstName: '',
    lastName: '',
  },
  salesDetails: {
    firstName: undefined,
    lastName: undefined,
    email: undefined,
    mobileNumber: undefined,
  },
  accountsDetails: {
    firstName: undefined,
    lastName: undefined,
    email: undefined,
    mobileNumber: undefined,
  },
  authorizedSignatory: [
    {
      email: '',
      firstName: '',
      lastName: '',
      mobileNumber: '',
    },
  ],
};

const initialDocumentInformation: DocumentInformationState = {
  passportDocuments: [],
  auditFinancials: [],
  taxExemptionDocument: [],
  taxExemptionExpiryDate: '',
};

const initialProfileInformation: ProfileInformationState = {
  profile_info: [],
  acceptTermsCondition: false,
};

const initialState: PartnerRegistrationState = {
  currentStep: 0,
  completedSteps: [],
  isFormValid: false,
  companyInformation: initialCompanyInformation,
  businessInformation: initialBusinessInformation,
  documentInformation: initialDocumentInformation,
  profileInformation: initialProfileInformation,
  userBrandPreferences: [],
  lastSavedAt: null,
  isDirty: false,
};

export const partnerRegistrationSlice = createSlice({
  name: 'partnerRegistration',
  initialState,
  reducers: {
    // Navigation actions
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },

    markStepCompleted: (state, action: PayloadAction<number>) => {
      if (!state.completedSteps.includes(action.payload)) {
        state.completedSteps.push(action.payload);
      }
    },

    markStepIncomplete: (state, action: PayloadAction<number>) => {
      state.completedSteps = state.completedSteps.filter((step) => step !== action.payload);
    },

    // Company Information actions
    updateCompanyInformation: (state, action: PayloadAction<Partial<CompanyInformationState>>) => {
      state.companyInformation = { ...state.companyInformation, ...action.payload };
      state.isDirty = true;
    },

    updateAddress: (state, action: PayloadAction<Partial<CompanyInformationState['address']>>) => {
      state.companyInformation.address = {
        ...state.companyInformation.address,
        ...action.payload,
      };
      state.isDirty = true;
    },

    updateAdditionalInfo: (
      state,
      action: PayloadAction<Partial<CompanyInformationState['additionalInfo']>>
    ) => {
      state.companyInformation.additionalInfo = {
        ...state.companyInformation.additionalInfo,
        ...action.payload,
      };
      state.isDirty = true;
    },

    updateContactInfo: (
      state,
      action: PayloadAction<Partial<CompanyInformationState['contactInfo']>>
    ) => {
      state.companyInformation.contactInfo = {
        ...state.companyInformation.contactInfo,
        ...action.payload,
      };
      state.isDirty = true;
    },

    // Business Information actions
    updateBusinessInformation: (
      state,
      action: PayloadAction<Partial<BusinessInformationState>>
    ) => {
      state.businessInformation = { ...state.businessInformation, ...action.payload };
      state.isDirty = true;
    },

    addBrandPreference: (state, action: PayloadAction<BrandPreference>) => {
      const newItem = action.payload;

      const exists = state.userBrandPreferences.some(
        (item) =>
          item.vendor.value === newItem.vendor.value &&
          item.brand.value === newItem.brand.value &&
          item.brandCategory.value === newItem.brandCategory.value
      );

      if (!exists) {
        state.userBrandPreferences.push(newItem);
      }
    },

    removeBrandPreference: (state, action: PayloadAction<BrandPreference>) => {
      const toRemove = action.payload;

      state.userBrandPreferences = state.userBrandPreferences.filter(
        (item) =>
          !(
            item.vendor.value === toRemove.vendor.value &&
            item.brand.value === toRemove.brand.value &&
            item.brandCategory.value === toRemove.brandCategory.value
          )
      );
    },

    addAuthorizedSignatory: (state, action: PayloadAction<KeyContact>) => {
      if (state.businessInformation.authorizedSignatory.length < 3) {
        state.businessInformation.authorizedSignatory.push(action.payload);
        state.isDirty = true;
      }
    },

    removeAuthorizedSignatory: (state, action: PayloadAction<number>) => {
      state.businessInformation.authorizedSignatory =
        state.businessInformation.authorizedSignatory.filter(
          (contact, index) => index !== action.payload
        );
      state.isDirty = true;
    },

    // Document Information actions
    updateDocumentInformation: (
      state,
      action: PayloadAction<Partial<DocumentInformationState>>
    ) => {
      state.documentInformation = { ...state.documentInformation, ...action.payload };
      state.isDirty = true;
    },

    addPassportDocument: (state, action: PayloadAction<Omit<DocumentFile, 'id'>>) => {
      if (state.documentInformation.passportDocuments.length < 4) {
        const newDocument: DocumentFile = {
          ...action.payload,
          id: Date.now().toString(),
        };
        state.documentInformation.passportDocuments.push(newDocument);
        state.isDirty = true;
      }
    },

    addAuditDocument: (state, action: PayloadAction<Omit<DocumentFile, 'id'>>) => {
      // Replace if exists (max 1 file)
      const newDocument: DocumentFile = {
        ...action.payload,
        id: Date.now().toString(),
      };
      state.documentInformation.auditFinancials = [newDocument];
      state.isDirty = true;
    },

    addTaxExemptionDocument: (state, action: PayloadAction<Omit<DocumentFile, 'id'>>) => {
      // Replace if exists (max 1 file)
      const newDocument: DocumentFile = {
        ...action.payload,
        id: Date.now().toString(),
      };
      state.documentInformation.taxExemptionDocument = [newDocument];
      state.isDirty = true;
    },

    removePassportDocument: (state, action: PayloadAction<string>) => {
      state.documentInformation.passportDocuments =
        state.documentInformation.passportDocuments.filter((doc) => doc.id !== action.payload);
      state.isDirty = true;
    },

    removeAuditDocument: (state) => {
      state.documentInformation.auditFinancials = [];
      state.isDirty = true;
    },

    removeTaxExemptionDocument: (state) => {
      state.documentInformation.taxExemptionDocument = [];
      state.isDirty = true;
    },

    updateTaxExemptionExpiryDate: (state, action: PayloadAction<string>) => {
      state.documentInformation.taxExemptionExpiryDate = action.payload;
      state.isDirty = true;
    },

    // Profile Information actions
    updateProfileInformation: (state, action: PayloadAction<Partial<ProfileInformationState>>) => {
      state.profileInformation = { ...state.profileInformation, ...action.payload };
      state.isDirty = true;
    },

    // General actions
    saveFormData: (state) => {
      state.lastSavedAt = new Date().toISOString();
      state.isDirty = false;
    },

    loadFormData: (state, action: PayloadAction<Partial<PartnerRegistrationState>>) => {
      const loadedData = action.payload;

      // Merge loaded data with current state, preserving structure
      if (loadedData.companyInformation) {
        state.companyInformation = {
          ...state.companyInformation,
          ...loadedData.companyInformation,
        };
      }
      if (loadedData.businessInformation) {
        state.businessInformation = {
          ...state.businessInformation,
          ...loadedData.businessInformation,
        };
      }
      if (loadedData.documentInformation) {
        state.documentInformation = {
          ...state.documentInformation,
          ...loadedData.documentInformation,
        };
      }
      if (loadedData.profileInformation) {
        state.profileInformation = {
          ...state.profileInformation,
          ...loadedData.profileInformation,
        };
      }
      if (loadedData.currentStep !== undefined) {
        state.currentStep = loadedData.currentStep;
      }
      if (loadedData.completedSteps) {
        state.completedSteps = loadedData.completedSteps;
      }
      if (loadedData.isFormValid !== undefined) {
        state.isFormValid = loadedData.isFormValid;
      }

      state.isDirty = false;
      state.lastSavedAt = loadedData.lastSavedAt || null;
    },

    resetForm: (state) => {
      // Reset to initial state
      Object.assign(state, initialState);
    },

    setFormValid: (state, action: PayloadAction<boolean>) => {
      state.isFormValid = action.payload;
    },

    // // Verification actions
    // setEmailVerified: (state, action: PayloadAction<boolean>) => {
    //   state.companyInformation.isEmailVerified = action.payload;
    //   state.isDirty = true;
    // },

    // setMobileVerified: (state, action: PayloadAction<boolean>) => {
    //   state.companyInformation.isMobileVerified = action.payload;
    //   state.isDirty = true;
    // },
  },
});

export const {
  setCurrentStep,
  markStepCompleted,
  markStepIncomplete,
  updateCompanyInformation,
  updateAddress,
  updateContactInfo,
  addBrandPreference,
  removeBrandPreference,
  // copyCommunicationAddress,
  updateAdditionalInfo,
  updateBusinessInformation,
  addAuthorizedSignatory,
  removeAuthorizedSignatory,
  updateDocumentInformation,
  addPassportDocument,
  addAuditDocument,
  addTaxExemptionDocument,
  removePassportDocument,
  removeAuditDocument,
  removeTaxExemptionDocument,
  updateTaxExemptionExpiryDate,
  updateProfileInformation,
  saveFormData,
  loadFormData,
  resetForm,
  setFormValid,
  // setEmailVerified,
  // setMobileVerified,
} = partnerRegistrationSlice.actions;

export default partnerRegistrationSlice.reducer;
