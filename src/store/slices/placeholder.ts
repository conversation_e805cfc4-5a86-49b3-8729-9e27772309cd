import { createSlice } from '@reduxjs/toolkit';

interface PlaceholderState {
  initialized: boolean;
}

const initialState: PlaceholderState = {
  initialized: false,
};

export const placeholderSlice = createSlice({
  name: 'placeholder',
  initialState,
  reducers: {
    setInitialized: (state) => {
      state.initialized = true;
    },
  },
});

export const { setInitialized } = placeholderSlice.actions;
export default placeholderSlice.reducer;
