import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Company Information Types
export interface CompanyInformationState {
  redingtonId: string;
  gstNumber: string;
  companyName: string;
  website: string;
  address: {
    street1: string;
    street2: string;
    countryBusiness: string;
    state: string;
    city: string;
    postalCode: string;
    // legalStatus: string;
  };
}

// Business Information Types
export interface KeyContact {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
}

export interface UserDetails {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
}

export interface OptionalKeyContact {
  firstName: string | undefined;
  lastName: string | undefined;
  email: string | undefined;
  mobileNumber: string | undefined;
}

export interface BusinessInformationState {
  userDetails: UserDetails;
  directorDetails: KeyContact;
  salesDetails: OptionalKeyContact;
  accountsDetails: OptionalKeyContact;
  authorizedSignatory: KeyContact[];
}

// Main Partner Registration State
export interface partnerOnboardingState {
  currentStep: number;
  completedSteps: number[];
  isFormValid: boolean;
  companyInformation: CompanyInformationState;
  businessInformation: BusinessInformationState;
  userBrandPreferences: BrandPreference[];
  lastSavedAt: string | null;
  isDirty: boolean;
}

interface LabeledItem {
  label: string;
  value: string;
}

interface BrandPreference {
  vendor: LabeledItem;
  brand: LabeledItem;
  brandCategory: LabeledItem;
}

// Initial state
const initialCompanyInformation: CompanyInformationState = {
  redingtonId: '',
  gstNumber: '',
  companyName: '',
  website: '',
  address: {
    street1: '',
    street2: '',
    countryBusiness: '',
    state: '',
    city: '',
    postalCode: '',
    // legalStatus: '',
  },
};

const initialBusinessInformation: BusinessInformationState = {
  userDetails: {
    email: '',
    mobileNumber: '',
    firstName: '',
    lastName: '',
  },
  directorDetails: {
    email: '',
    mobileNumber: '',
    firstName: '',
    lastName: '',
  },
  salesDetails: {
    firstName: undefined,
    lastName: undefined,
    email: undefined,
    mobileNumber: undefined,
  },
  accountsDetails: {
    firstName: undefined,
    lastName: undefined,
    email: undefined,
    mobileNumber: undefined,
  },
  authorizedSignatory: [
    {
      email: '',
      firstName: '',
      lastName: '',
      mobileNumber: '',
    },
  ],
};

const initialState: partnerOnboardingState = {
  currentStep: 0,
  completedSteps: [],
  isFormValid: false,
  companyInformation: initialCompanyInformation,
  businessInformation: initialBusinessInformation,
  userBrandPreferences: [],
  lastSavedAt: null,
  isDirty: false,
};

export const partnerOnboardingSlice = createSlice({
  name: 'partnerOnboarding',
  initialState,
  reducers: {
    // Navigation actions
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },

    markStepCompleted: (state, action: PayloadAction<number>) => {
      if (!state.completedSteps.includes(action.payload)) {
        state.completedSteps.push(action.payload);
      }
    },

    markStepIncomplete: (state, action: PayloadAction<number>) => {
      state.completedSteps = state.completedSteps.filter((step) => step !== action.payload);
    },

    // Company Information actions
    updateCompanyInformation: (state, action: PayloadAction<Partial<CompanyInformationState>>) => {
      state.companyInformation = { ...state.companyInformation, ...action.payload };
      state.isDirty = true;
    },

    updateAddress: (state, action: PayloadAction<Partial<CompanyInformationState['address']>>) => {
      state.companyInformation.address = {
        ...state.companyInformation.address,
        ...action.payload,
      };
      state.isDirty = true;
    },

    // Business Information actions
    updateBusinessInformation: (
      state,
      action: PayloadAction<Partial<BusinessInformationState>>
    ) => {
      state.businessInformation = { ...state.businessInformation, ...action.payload };
      state.isDirty = true;
    },

    addBrandPreference: (state, action: PayloadAction<BrandPreference>) => {
      const newItem = action.payload;
      const exists = state.userBrandPreferences.some(
        (item) =>
          item.vendor.value === newItem.vendor.value &&
          item.brand.value === newItem.brand.value &&
          item.brandCategory.value === newItem.brandCategory.value
      );

      if (!exists) {
        state.userBrandPreferences.push(newItem);
      }
    },

    removeBrandPreference: (state, action: PayloadAction<BrandPreference>) => {
      const toRemove = action.payload;

      state.userBrandPreferences = state.userBrandPreferences.filter(
        (item) =>
          !(
            item.vendor.value === toRemove.vendor.value &&
            item.brand.value === toRemove.brand.value &&
            item.brandCategory.value === toRemove.brandCategory.value
          )
      );
    },

    addAuthorizedSignatory: (state, action: PayloadAction<KeyContact>) => {
      if (state.businessInformation.authorizedSignatory.length < 3) {
        state.businessInformation.authorizedSignatory.push(action.payload);
        state.isDirty = true;
      }
    },

    removeAuthorizedSignatory: (state, action: PayloadAction<number>) => {
      state.businessInformation.authorizedSignatory =
        state.businessInformation.authorizedSignatory.filter(
          (contact, index) => index !== action.payload
        );
      state.isDirty = true;
    },

    // General actions
    saveFormData: (state) => {
      state.lastSavedAt = new Date().toISOString();
      state.isDirty = false;
    },

    loadFormData: (state, action: PayloadAction<Partial<partnerOnboardingState>>) => {
      const loadedData = action.payload;

      // Merge loaded data with current state, preserving structure
      if (loadedData.companyInformation) {
        state.companyInformation = {
          ...state.companyInformation,
          ...loadedData.companyInformation,
        };
      }
      if (loadedData.businessInformation) {
        state.businessInformation = {
          ...state.businessInformation,
          ...loadedData.businessInformation,
        };
      }
      if (loadedData.currentStep !== undefined) {
        state.currentStep = loadedData.currentStep;
      }
      if (loadedData.completedSteps) {
        state.completedSteps = loadedData.completedSteps;
      }
      if (loadedData.isFormValid !== undefined) {
        state.isFormValid = loadedData.isFormValid;
      }

      state.isDirty = false;
      state.lastSavedAt = loadedData.lastSavedAt || null;
    },

    resetForm: (state) => {
      // Reset to initial state
      Object.assign(state, initialState);
    },

    setFormValid: (state, action: PayloadAction<boolean>) => {
      state.isFormValid = action.payload;
    },
  },
});

export const {
  setCurrentStep,
  markStepCompleted,
  markStepIncomplete,
  updateCompanyInformation,
  updateAddress,
  addBrandPreference,
  removeBrandPreference,
  updateBusinessInformation,
  addAuthorizedSignatory,
  removeAuthorizedSignatory,
  saveFormData,
  loadFormData,
  resetForm,
  setFormValid,
} = partnerOnboardingSlice.actions;

export default partnerOnboardingSlice.reducer;
