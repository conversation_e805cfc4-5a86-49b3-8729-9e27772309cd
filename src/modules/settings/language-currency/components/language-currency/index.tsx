'use client';
import {
  Button,
  Checkbox,
  Label,
  RadioGroup,
  RadioGroupItem,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { cn } from '@/lib/utils/util';
import Image from 'next/image';
import React from 'react';
import { useTranslations } from 'next-intl';
import { Controller, useForm } from 'react-hook-form';
import { Select2 } from '@/components/common/ui/combo-box';
import { GenerateReportFormData } from '@/modules/billing/reports/components/generate-new-report';

function Languagecurrency() {
  const { control } = useForm<GenerateReportFormData>({
    mode: 'onTouched',
    defaultValues: {
      endCustomer: '',
      brandCategory: '',
      reportType: '',
      reportTitle: '',
    },
  });
  const t = useTranslations();
  return (
    <>
      <div className="">
        <h1 className="text-[#19212A] text-[20px] 3xl:text-[1.042vw] font-semibold leading-[140%] mb-[16px] 3xl:mb-[0.833vw]">
          {t('Currency & Language')}
        </h1>
        <div className="flex gap-[4px] 3xl:gap-[0.104vw] ">
          <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none">
            <div className="flex items-center">
              <Image src="/images/save2.svg" width={20} height={20} alt="saveimg" className="" />
            </div>
            <div className="text-[#3C4146] hover:text-[#1570EF] font-normal leading-[140%] text-[14px] 3xl:text-[0.729vw] ">
              {t('saveChanges')}
            </div>
          </Button>

          <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none">
            <div className="flex items-center">
              <i className="cloud-refresh text-[#1570EF]"></i>
            </div>
            <div className="text-[#3C4146] hover:text-[#1570EF] font-normal leading-[140%] text-[14px] 3xl:text-[0.729vw] flex items-center">
              {t('resetToDefaultSettings')}
            </div>
          </Button>
        </div>
        <div className="flex items-center space-x-2 mt-[20px] 3xl:mt-[1.042vw]">
          <Checkbox
            id="terms"
            className="bg-InterfaceTextlighter border border-InterfaceTextlighter h-[13px] xl:h-[13px] 3xl:h-[0.677vw] w-[13px] xl:w-[13px] 3xl:w-[0.677vw]"
          />
          <label
            htmlFor="terms"
            className="text-[#7F8488] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]"
          >
            {t('languageSettings')}
          </label>
        </div>
        <div className="my-[12px] 3xl:my-[0.625vw] text-InterfaceTextdefault text-[16px] 3xl:text-[0.833vw] font-medium leading-[140%]">
          {t('selectThelanguage')}
        </div>
        <RadioGroup defaultValue="comfortable">
          <div className="flex items-center space-x-2 custrediogroup">
            <RadioGroupItem value="default" id="r1" />
            <Label htmlFor="r1" className="font-medium">
              English - EN
            </Label>
          </div>
          <div className="flex items-center space-x-2 custrediogroup">
            <RadioGroupItem value="comfortable" id="r2" />
            <Label htmlFor="r2" className="font-normal text-InterfaceTextdefault">
              Arabic - AR - عربي
            </Label>
          </div>
        </RadioGroup>
        <div className="mt-[40px]">
          <div className="flex items-center space-x-2 mt-[20px] 3xl:mt-[1.042vw]">
            <Checkbox
              id="terms"
              className="bg-InterfaceTextlighter border border-InterfaceTextlighter h-[13px] xl:h-[13px] 3xl:h-[0.677vw] w-[13px] xl:w-[13px] 3xl:w-[0.677vw]"
            />
            <label
              htmlFor="terms"
              className="text-[#7F8488] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]"
            >
              {t('currencySettings')}
            </label>
          </div>
          <div className="my-[12px] 3xl:my-[0.625vw] text-InterfaceTextdefault text-[16px] 3xl:text-[0.833vw] font-medium leading-[140%]">
            {t('selectTheCurrencyWith')}:
          </div>
          <div className="col-span-4 w-[50%]">
            <div className="flex flex-col gap-1.5">
              <Controller
                name="endCustomer"
                control={control}
                render={({ field }) => (
                  <Select2
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={'AED - United Arab Emirates Dirham (Default)'}
                    options={[
                      { value: 'all', label: 'AED - United Arab Emirates Dirham (Default)' },
                      { value: 'customer1', label: 'BHD-Bahraini Dinar' },
                      { value: 'customer2', label: 'CA$ CAD - Canadian Dollar' },
                      { value: 'customer3', label: 'EGP - Egyptian Pound' },
                      { value: 'customer4', label: '€ - EUR - Euro' },
                      { value: 'customer5', label: '£ - GBP - British Pound' },
                      { value: 'customer6', label: '₹ - INR - Indian Rupee' },
                      { value: 'customer7', label: 'KWD - Kuwaiti Dinar' },
                    ]}
                    classNames={{
                      trigger: cn(
                        'placeholder:text-InterfaceTextsubtitle text-InterfaceTextdefault border border-InterfaceStrokehard rounded-none  text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-[#fff] px-[11px] xl:px-[11px] 2xl:px-[11px] 3xl:px-[0.573vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]'
                      ),
                      item: 'text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400 ',
                    }}
                    styles={{
                      popoverContent: {
                        border: 'none',
                        backgroundColor: '#fff',
                        padding: -10,
                      },
                    }}
                  />
                )}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
export default Languagecurrency;
