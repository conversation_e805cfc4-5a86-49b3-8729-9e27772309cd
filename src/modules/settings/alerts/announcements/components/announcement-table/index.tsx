import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Announcementview } from '../announcementview';
import { useTranslations } from 'next-intl';

export default function Announcementtable() {
  const t = useTranslations();
  const data = [
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
    {
      notificationdate: '03/03/2025',
      notificationtype: 'Order Placement',
      details: 'Order Placed On : 03/03/2025',
    },
  ];
  type User = {
    notificationdate: string;
    notificationtype: string;
    details: string;
  };

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'notificationdate',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('notificationDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'notificationtype',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('announcementsType')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'details',
      enableSorting: true,
      size: 1000,
      minSize: 1000,
      maxSize: 1000,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('details')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },

    {
      accessorKey: 'action',
      header: () => (
        <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-fit z-20 bg-interfacesurfacecomponent cursor-pointer p-0">
                <div className="">
                  <Announcementview />
                  <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                    <i className="cloud-circletick text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
                    {t('markAsUnread2')}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 100,
      minSize: 100,
      maxSize: 100,
    },
  ];
  return (
    <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
      <div className="flex justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
        <div className="flex items-center gap-2 ">
          <div className="text-InterfaceTexttitle text-[18px] font-semibold">
            {t('allAnnouncements')}{' '}
            <span className="font-normal text-BrandSupport1pure text-[14px] 3xl:text-[0.729vw] leading-[140%]">
              (2 New)
            </span>
          </div>
          <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
            {t('showingRecords')}{' '}
          </div>
        </div>
        <div className="  flex gap-[24px] xl:gap-[24px] 3xl:gap-[1.25vw] items-center">
          <div className="relative">
            <Input
              type="text"
              placeholder={t('search')}
              className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
            />
            <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
          </div>
          <div className="flex items-center gap-2 cursor-pointer">
            <i className="cloud-filtericon"></i>
            {t('filter')}
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <DataTable data={data} columns={notificationColumns} withCheckbox={true} />
      </div>
      <TablePagination />
    </div>
  );
}
