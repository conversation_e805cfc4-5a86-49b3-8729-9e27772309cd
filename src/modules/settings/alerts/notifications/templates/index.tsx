'use client';
import React from 'react';
import Notifications from '../components';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import { useTranslations } from 'next-intl';
export default function NotificationsTemplate() {
  const t = useTranslations();
  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('settings'),
      href: '/settings/language-currency',
    },
    {
      label: t('alerts'),
      href: '',
    },
    {
      label: t('notifications'),
      href: '/settings/alerts/notifications',
    },
  ];

  const SideBarNavItems = [
    {
      label: t('notifications'),
      href: '/settings/alerts/notifications',
    },
    {
      label: t('announcements'),
      href: '/settings/alerts/announcements',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex">
        <SubSidebar title={t('alerts')} navItems={SideBarNavItems} />
        <Notifications />
      </div>
    </>
  );
}
