'use client';

interface AlertsTableProps {
  activeTab: 'notifications' | 'announcements';
}

export default function AlertsTable({ activeTab }: AlertsTableProps) {
  return (
    <div className="space-y-4">
      <div className="text-center p-8">
        <h3 className="text-lg font-medium">
          {activeTab === 'notifications' ? 'Notifications' : 'Announcements'} Table
        </h3>
        <p className="text-gray-500 mt-2">
          Table implementation will be completed with proper data table component
        </p>
      </div>
    </div>
  );
}
