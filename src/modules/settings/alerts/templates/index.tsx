'use client';
import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  B<PERSON>crumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@redington-gulf-fze/cloudquarks-component-library';
import NotificationsTemplate from '../notifications/templates';

export default function AlertsTemplate() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <div>
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink
                  className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] text-BrandSupport1pure font-[500] "
                  href="/"
                >
                  Home
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] text-BrandSupport1pure font-[500] " />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextsubtitle ">
                  Alerts
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      <div>
        <NotificationsTemplate />
      </div>
    </>
  );
}
