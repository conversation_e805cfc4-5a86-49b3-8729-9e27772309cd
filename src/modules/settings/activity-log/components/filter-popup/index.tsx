'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Sheet,
  Sheet<PERSON>lose,
  <PERSON>et<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types/components';
import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import { useTranslations } from 'next-intl';

export default function ApplyFilterPopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();
  const [date, setDate] = React.useState<Date>();
  const activityOptions: Select2Option[] = [
    { label: 'All', value: 'All' },
    { label: 'Active', value: 'Active' },
    { label: 'Inactive', value: 'Inactive' },
  ];
  const userOptions: Select2Option[] = [
    { label: 'All', value: 'All' },
    { label: 'Admin', value: 'Admin' },
    { label: 'Partner', value: 'Partner' },
  ];

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-2  h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="relative flex items-center">
            <i className="cloud-filtericon text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto lg:h-[400px] xl:h-[500px] 3xl:h-[32.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('activityDateRange')}
              </Label>
              <div className="grid grid-cols-2 gap-[10px] xl:gap-[10px] 3xl:gap-[0.521vw]">
                <div className="col-span-1">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-[12px] lg:text-[10px] md:text-[10px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                          !date && 'text-muted-foreground'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle text-[12px] lg:text-[10px] md:text-[10px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw">
                            {t('selectFromDate')}
                          </span>
                        )}
                        <i className="cloud-canlendar"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto bg-[#FFF] p-0" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="col-span-1">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-[12px] lg:text-[10px] md:text-[10px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] justify-between border border-InterfaceStrokehard text-left font-normal rounded-none',
                          !date && 'text-muted-foreground'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle text-[12px] lg:text-[10px] md:text-[10px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw">
                            {t('selectEndDate')}
                          </span>
                        )}
                        <i className="cloud-canlendar"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto bg-[#FFF] p-0" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            <div className="relative grid gap-1.5">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('activityType')}
              </label>
              <div className="">
                <Select2
                  options={activityOptions}
                  value={''}
                  onChange={() => {}}
                  placeholder="All"
                />
              </div>
            </div>
            <div className="relative grid gap-1.5">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('user')}
              </label>
              <div className="">
                <Select2 options={userOptions} value={''} onChange={() => {}} placeholder="All" />
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
              <Button
                size="sm"
                onClick={onClose}
                className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                  {t('cancel')}
                </div>
              </Button>
              <Button
                size="sm"
                onClick={onClose}
                className="flex items-center gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-filter text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                  {t('applyFilter')}
                </div>
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
