import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import ApplyFilterPopup from '../filter-popup';
import LogViewPopup from '../view-popup';
import { useTranslations } from 'next-intl';
export default function Logtable() {
  const t = useTranslations();
  const data = [
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Order Placed',
      desc: 'Order ID : #12345',
      user: '<PERSON>',
      ipaddress: '*************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Logged In',
      desc: 'Order ID : #12346',
      user: '<PERSON>',
      ipaddress: '**************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Logged Out',
      desc: 'Order ID : #12346',
      user: '<PERSON> <PERSON>',
      ipaddress: '**************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Order Placed',
      desc: 'Order ID : #12345',
      user: 'Robert Fox',
      ipaddress: '*************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Logged In',
      desc: 'Order ID : #12346',
      user: 'Ralph Edwards',
      ipaddress: '**************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Logged Out',
      desc: 'Order ID : #12346',
      user: 'Brooklyn Simmons',
      ipaddress: '**************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Order Placed',
      desc: 'Order ID : #12345',
      user: 'Robert Fox',
      ipaddress: '*************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Logged In',
      desc: 'Order ID : #12346',
      user: 'Ralph Edwards',
      ipaddress: '**************',
    },
    {
      timestamp: '20/05/2025, 11:00 hrs',
      activitytype: 'Logged Out',
      desc: 'Order ID : #12346',
      user: 'Brooklyn Simmons',
      ipaddress: '**************',
    },
  ];

  type User = {
    timestamp: string;
    activitytype: string;
    desc: string;
    user: string;
    ipaddress: string;
  };

  const logColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'timestamp',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center  gap-[80px] xl:gap-[80px] 3xl:gap-[4.167vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('timeStamp')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'activitytype',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[80px] xl:gap-[80px] 3xl:gap-[4.167vw] text-left  font-medium justify-between"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('activityType')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'desc',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[80px] xl:gap-[80px] 3xl:gap-[4.167vw] text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('description')}/{t('details')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'user',
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[80px] xl:gap-[80px] 3xl:gap-[4.167vw] text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('user')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      size: 200,
      minSize: 200,
      maxSize: 200,
    },
    {
      accessorKey: 'ipaddress',
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[80px] xl:gap-[80px] 3xl:gap-[4.167vw] text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('ipAddress')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      size: 200,
      minSize: 200,
      maxSize: 200,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-[150px] xl:w-[150px] 3xl:w-[7.813vw] z-20 bg-interfacesurfacecomponent cursor-pointer p-0">
                <div className="">
                  <LogViewPopup open={false} onClose={() => {}} />
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 100,
      minSize: 100,
      maxSize: 100,
    },
  ];

  return (
    <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
      <div className="flex flex-wrap gap-[10px] justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
        <div className="flex flex-wrap items-center gap-2 ">
          <div className="text-InterfaceTexttitle text-[16px] md:text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
            {t('listOfRecords')}
          </div>
          <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[11px] md:text-[11px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%]  mt-[4px] 3xl:mt-[0.208vw]">
            {t('showingRecords')}{' '}
          </div>
        </div>
        <div className=" flex-wrap flex gap-[24px] lg:gap-[6px] md:gap-[6px] xl:gap-[6px] 2xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
          <div className="relative">
            <Input
              type="text"
              placeholder={t('search')}
              className="w-[300px] lg:w-[250px] md:w-[250px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
            />
            <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
          </div>
          {/* <div className="flex items-center gap-2 cursor-pointer">
            <i className="cloud-filtericon"></i>Filter
          </div> */}
          <ApplyFilterPopup open={false} onClose={() => {}} />
        </div>
      </div>
      <div className="flex items-center justify-start px-[14px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] gap-2 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
        <div className="text-InterfaceTextsubtitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
          {t('fromDate')} : <span className="text-InterfaceTextdefault">01-01-2025</span>
          <i className="cloud-closecircle text-ClearAll"></i>
        </div>
        <div className="text-InterfaceTextsubtitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
          {t('toDate')}: <span className="text-InterfaceTextdefault">31-05-2025</span>
          <i className="cloud-closecircle text-ClearAll"></i>
        </div>
        <div className="text-InterfaceTextsubtitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
          {t('activityType')} : <span className="text-InterfaceTextdefault">All</span>
          <i className="cloud-closecircle text-ClearAll"></i>
        </div>
        <div className="text-ClearAll  py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
          <i className="cloud-closecircle text-ClearAll"></i>
          {t('clearAll')}
        </div>
      </div>
      <div className="overflow-x-auto">
        <DataTable data={data} columns={logColumns} />
      </div>
      <TablePagination />
    </div>
  );
}
