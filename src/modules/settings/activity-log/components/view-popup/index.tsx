'use client';
import { TermsSheetProps } from '@/types';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Sheet,
  Sheet<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
// import Image from 'next/image';
import { useTranslations } from 'next-intl';
export default function LogViewPopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="hover:bg-BrandNeutral100 w-full">
        <div className="px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center w-full hover:bg-BrandNeutral100">
          <i className="cloud-readeye1 text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
          {t('view')}
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between items-center">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%] flex gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw] items-center">
              {t('view')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid grid-cols-2 gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw]">
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('fullTimestamp')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  20/05/2025, 11:00 hrs
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('activity')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Order Placed
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('eventSpecificDataFields')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Order ID : #12345
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('performedBy(User, System, API)')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Robert Fox
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('ipAddress')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  *************
                </div>
              </div>
            </div>
          </div>
        </div>

        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
              <Link href="/#">
                <Button
                  size="sm"
                  onClick={onClose}
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </Button>
              </Link>
              <Link href="/#">
                <Button
                  size="sm"
                  onClick={onClose}
                  className="flex items-center gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-filter text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    {t('applyFilter')}
                  </div>
                </Button>
              </Link>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
