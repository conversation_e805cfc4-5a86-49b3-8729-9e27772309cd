'use client';
import React from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import Logtable from '../components/log-table';
import { useTranslations } from 'next-intl';

export default function ActivityLogTemplate() {
  const t = useTranslations();
  return (
    <>
      <div className="w-full">
        <div className="relative h-full px-[32px] py-[20px] 3xl:px-[1.667vw] 3xl:py-[1.042vw] bg-[#F6F7F8]">
          <div className="">
            <div className="flex items-center pb-[10px] xl:pb-[10px] 3xl:pb-[0.525vw]">
              <div className="text-interfacetexttitle2 text-[20px] xl:text-[20px] 3xl:text-[1.042vw] font-semibold">
                {t('activityLog')}
              </div>
            </div>
            <div className="flex gap-[2px] mb-[20px] xl:mb-[20px] 3xl:mb-[1.042vw]">
              <Link href="/settings/language-currency">
                <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
                  <i className="cloud-download text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  {t('download')}
                </Button>
              </Link>
            </div>
            <div className="bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
              <Logtable />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
