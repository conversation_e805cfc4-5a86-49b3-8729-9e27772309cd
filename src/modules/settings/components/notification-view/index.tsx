'use client';

import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function Notification() {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <div className="px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-readeye text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
          View
        </div>
      </SheetTrigger>
      <SheetContent
        className={`${roboto.className} sm:max-w-[460px] xl:max-w-[450px] 2xl:max-w-[570px] 3xl:max-w-[29.69vw] p-[0px] flex flex-col h-full`}
        side={'right'}
      >
        <div className="flex-1 overflow-y-auto mb-[30px] xl:mb-0 mt-[40px] xl:mt-[40px] 2xl:mt-[55px] 3xl:mt-[6.29vw] mx-[20px] xl:mx-[40px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[20px] xl:gap-[20px] 2xl:gap-[40px] 3xl:gap-[2.08vw]">
            <div>
              {/* <i className="cloud-notification text-BrandSupport1pure text-[50px] xl:text-[40px] 2xl:text-[60px] 3xl:text-[2.6vw] font-[400]"></i> */}
              <i className="cloud-bellicon text-BrandSupport1pure text-[50px] xl:text-[40px] 2xl:text-[60px] 3xl:text-[2.6vw] font-[400]"></i>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw]">
              <div className="flex flex-col justify-center items-center">
                <div className="text-InterfaceTexttitle text-[20px] xl:text-[20px] 2xl:text-[34px] 3xl:text-[1.875vw] font-[400] leading-[140%] text-center">
                  Order Placement
                </div>
                <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw]font-[400]">
                  <i className="cloud-info text-InterfaceTextsubtitle mr-2"></i>
                  Apr 14, 2025, 2:34 PM
                </div>
              </div>
              <div className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.839vw] font-[400] text-center">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                exercitation ullamco laboris nisi ut aliquip
              </div>
            </div>

            <div className=" bg-white border border-InterfaceStrokedefault rounded shadow">
              <table className="w-[385px] xl:w-[350px] 2xl:w-[400px] 3xl:w-[20.83vw] text-sm text-left text-gray-700">
                <thead>
                  <tr>
                    <th className="bg-InterfaceSurfacecomponentmuted text-gray-900 px-[16px] xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.525vw] text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.839vw] border-b border-InterfaceStrokesoft">
                      Order Details
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="px-[16px] xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.525vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] text-InterfaceTextdefault">
                      Order Placed on
                    </td>
                    <td className="pr-4 py-2 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      03/03/2025
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="px-[16px] xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.525vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] text-InterfaceTextdefault">
                      Order Placed by
                    </td>
                    <td className="pr-4 py-2 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      Will Smith
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="px-[16px] xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.525vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] text-InterfaceTextdefault">
                      Order#
                    </td>
                    <td className="pr-4 py-2 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      11223344
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="px-[16px] xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.525vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] text-InterfaceTextdefault">
                      Type
                    </td>
                    <td className="pr-4 py-2 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      Order Placement
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="px-[16px] xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.525vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] text-InterfaceTextdefault">
                      Total Items
                    </td>
                    <td className="pr-4 py-2 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      4
                    </td>
                  </tr>
                  <tr>
                    <td className="px-[16px] xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.525vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] text-InterfaceTextdefault">
                      Total Order Value
                    </td>
                    <td className="pr-4 py-2 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      5,000.00 USD
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="text-center">
              <SheetClose className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw]  text-InterfaceTextdefault bg-[linear-gradient(180deg,_#FEFEF_0%,_#F6F8FA_100%)] border border-InterfaceStrokesoft rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[12px] md:py-[12px] xl:py-[12px] 3xl:py-[0.625vw] font-[400] inline-flex items-center gap-2">
                <i className="cloud-back"></i>
                Back to Notification
              </SheetClose>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
