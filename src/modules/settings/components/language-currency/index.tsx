'use client';
import {
  <PERSON>ton,
  Checkbox,
  Label,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import React from 'react';
import { useTranslations } from 'next-intl';

function Languagecurrency() {
  const t = useTranslations();
  return (
    <>
      <div className="">
        <h1 className="text-[#19212A] text-[20px] 3xl:text-[1.042vw] font-semibold leading-[140%] mb-[16px] 3xl:mb-[0.833vw]">
          {t('languageCurrency')}
        </h1>
        <div className="flex gap-[4px] 3xl:gap-[0.104vw] ">
          <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none">
            <div className="flex items-center">
              <Image src="/images/save2.svg" width={20} height={20} alt="saveimg" className="" />
            </div>
            <div className="text-[#3C4146] font-normal leading-[140%] text-[14px] 3xl:text-[0.729vw]">
              {t('saveChanges')}
            </div>
          </Button>

          <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none">
            <div className="flex items-center">
              <i className="cloud-refresh text-[#1570EF]"></i>
            </div>
            <div className="text-[#3C4146] font-normal leading-[140%] text-[14px] 3xl:text-[0.729vw] flex items-center">
              {t('resetToDefaultSettings')}
            </div>
          </Button>
        </div>
        <div className="flex items-center space-x-2 mt-[20px] 3xl:mt-[1.042vw]">
          <Checkbox id="terms" className="" />
          <label
            htmlFor="terms"
            className="text-[#7F8488] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
          >
            {t('languageSettings')}
          </label>
        </div>
        <div className="my-[12px] 3xl:my-[0.625vw] text-InterfaceTextdefault text-[16px] 3xl:text-[0.833vw] font-medium leading-[140%]">
          {t('selectThelanguage')}
        </div>
        <RadioGroup defaultValue="comfortable">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="default" id="r1" />
            <Label htmlFor="r1" className="font-medium">
              English - EN (Default)
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="comfortable" id="r2" />
            <Label htmlFor="r2" className="font-normal">
              عربي - Arabic - AR
            </Label>
          </div>
        </RadioGroup>
        <div className="mt-[40px]">
          <div className="flex items-center space-x-2 mt-[20px] 3xl:mt-[1.042vw]">
            <Checkbox id="terms" />
            <label
              htmlFor="terms"
              className="text-[#7F8488] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
            >
              {t('currencySettings')}
            </label>
          </div>
          <div className="my-[12px] 3xl:my-[0.625vw] text-InterfaceTextdefault text-[16px] 3xl:text-[0.833vw] font-medium leading-[140%]">
            {t('selectTheCurrencyWith')}:
          </div>
          <div className="col-span-4">
            <Select defaultValue="india">
              <SelectTrigger className="bg-[#FFF] text-[#3C4146] placeholder-text-sm  rounded-none border-[#BBC1C7] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                <SelectValue placeholder="Country" className="" />
              </SelectTrigger>
              <SelectContent className="rounded-none bg-[#FFF] border-none">
                <SelectGroup className="text-[#3C4146] text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  <SelectItem value="india">AED - United Arab Emirates Dirham (Default)</SelectItem>
                  <SelectItem value="mea">BHD-Bahraini Dinar</SelectItem>
                  <SelectItem value="tukry">CA$ CAD - Canadian Dollar </SelectItem>
                  <SelectItem value="tukry">EGP - Egyptian Pound </SelectItem>
                  <SelectItem value="tukry">€ - EUR - Euro </SelectItem>
                  <SelectItem value="tukry">£ - GBP - British Pound </SelectItem>
                  <SelectItem value="tukry">₹ - INR - Indian Rupee</SelectItem>
                  <SelectItem value="tukry">KWD - Kuwaiti Dinar </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </>
  );
}
export default Languagecurrency;
