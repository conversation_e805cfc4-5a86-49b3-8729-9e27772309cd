'use client';

import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import Image from 'next/image';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function Announcement() {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <div className="px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-readeye text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
          View
        </div>
      </SheetTrigger>
      <SheetContent
        className={`${roboto.className} sm:max-w-[500px] xl:max-w-[520px] 2xl:max-w-[620px] 3xl:max-w-[37.76vw] p-[0px] flex flex-col h-full`}
        side={'right'}
      >
        <SheetHeader className="border-b border-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              Announcement
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto px-[20px] lg:px-[20px] xl:px-[22px] 3xl:px-[1.25vw] pb-[20px] lg:pb-[20px] xl:pb-[22px] 3xl:pb-[1.25vw]">
          <div>
            <div className="relative">
              <Image
                src="/images/announcement_image.svg"
                alt="announcement_image"
                width={40}
                height={40}
                className="w-full h-[219px] xl:h-[220px] 2xl:h-[280px] 3xl:h-[14.58vw] object-cover"
              />
              <div className="absolute top-3 left-3 right-3 text-interfacesurfacecomponent">
                <div className="flex justify-between items-start">
                  <div className="text-interfacesurfacecomponent text-[16px] xl:text-[16px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[500]">
                    Microsoft AI ignites telecom innovation <br />
                    and growth
                  </div>
                  <div className="px-[6px] 3xl:px-[0.36vw] py-[2px] 3xl:py-[0.1vw] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault bg-interfacesurfacecomponent">
                    General
                  </div>
                </div>
                <div className="mt-[6px] 3xl:mt-[0.36vw] text-InterfaceTextlighter text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw]font-[400]">
                  <i className="cloud-info text-InterfaceTextlighter mr-2"></i>
                  Apr 14, 2025, 2:34 PM
                </div>
              </div>
            </div>
            <p className="mt-[10px] xl:mt-[10px] 2xl:mt-[12px] 3xl:mt-[0.625vw] text-InterfaceTextsubtitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              The telecommunications industry is experiencing significant AI advancements, emerging
              as the leading adopter of generative and agentic AI to drive automation,
              personalization, and data-driven decisions. According to a recent IDC white paper,
              telecom and media companies are seeing nearly four times the return on investment
              (ROI) on every dollar invested in AI. Additionally, by 2027, almost 90% of telecom
              providers are expected to use generative AI to improve customer experiences, up from
              62% today.{' '}
            </p>{' '}
            <br />
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              96% of our tier-1 telecom customers are already adopting Microsoft AI solutions. Our
              ecosystem of customers and partners are harnessing the power of AI to reimagine
              customer experiences, modernize networks, automate business operations, and drive
              growth.
            </p>
            <div className="my-[10px] 3xl:my-[0.525vw]">
              <Image
                src="/images/add_image.svg"
                alt="add_image"
                width={40}
                height={40}
                className="w-full h-[148px] xl:h-[148px] 2xl:h-[158px] 3xl:h-[8.23vw]"
              />
            </div>
            <p className="mt-[10px] xl:mt-[10px] 2xl:mt-[12px] 3xl:mt-[0.625vw] text-InterfaceTextsubtitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Ahead of Mobile World Congress 2025 (MWC), we’re sharing new capabilities and customer
              momentum that show how telecoms are adopting the Microsoft Cloud and AI capabilities
              to support their AI journey and empower the next generation of telecom solutions. We
              invite you to join us next week at MWC to learn more about our new announcements and
              see firsthand how Microsoft AI is transforming the telecom industry. Experience live
              demos, attend insightful sessions, and meet our experts to learn how you can drive
              innovation and growth with Microsoft AI technologies.
            </p>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
