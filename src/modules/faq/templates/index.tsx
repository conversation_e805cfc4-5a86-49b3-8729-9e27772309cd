'use client';
import * as React from 'react';
import { Input } from '@redington-gulf-fze/cloudquarks-component-library';
import { Search } from 'lucide-react';
import {
  Button,
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function FaqTemplate() {
  const t = useTranslations();

  return (
    <div className="w-full p-[20px] pr-[20px] xl:pr-[30px] 2xl:pr-[32px] 3xl:pr-[1.823vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] pl-[38px]">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle mt-[16px] xl:mt-[18px] 3xl:mt-[1.042vw]">
          {t('frequentlyAskedQuestionsFaqs')}
        </div>
      </div>
      <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <Button className="ticketcard gap-2 items-center text-InterfaceTextdefault flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
          <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
          {t('downloadAsPdf')}
        </Button>
        <div className="grid grid-cols-10">
          <div className="col-span-3 border bg-white border-InterfaceStrokesoft p-[16px] xl:p-[18px] 3xl:p-[1.042vw]">
            <div className="mb-[22px] xl:mb-[24px] 3xl:mb-[1.458vw]">
              <div className="text-text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
                {t('mostViewed')}
              </div>
            </div>
            <div className="space-y-[8px] text-[14px] xl:text-[16px] 3xl:text-[0.833vw]">
              <div className="bg-BrandNeutral p-[14px] xl:p-[16px] 3xl:p-[0.833vw] font-semibold text-InterfaceTextwhite text-[16px] xl:text-[12px] 3xl:text-[0.833vw]">
                {t('whatIsCQ2')}
              </div>
              <div className="bg-InterfaceSurfacecomponentmuted p-[14px] xl:p-[16px] 3xl:p-[0.833vw] font-semibold text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw]">
                {t('whatIsNewInCQ2')}
              </div>
              <div className="bg-InterfaceSurfacecomponentmuted p-[14px] xl:p-[16px] 3xl:p-[0.833vw] font-semibold text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw]">
                {t('isCQ1Replaced')}
              </div>
              <div className="bg-InterfaceSurfacecomponentmuted p-[14px] xl:p-[16px] 3xl:p-[0.833vw] font-semibold text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw]">
                {t('needNewLogin')}
              </div>
              <div className="bg-InterfaceSurfacecomponentmuted p-[14px] xl:p-[16px] 3xl:p-[0.833vw] font-semibold text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw]">
                {t('existingTransactions')}
              </div>
            </div>
          </div>
          <div className="col-span-7 px-[16px] xl:px-[18px] 3xl:px-[1.042vw] space-y-[8px]">
            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] flex justify-between items-center">
              <div className="font-semibold text-InterfaceTexttitle">{t('listOfQuestions')}</div>
              <div>
                <div className="relative w-[300px] xl:w-[400px] 3xl:w-[23.438vw] bg-white border border-InterfaceStrokedefault">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4 " />
                  <Input
                    type="text"
                    placeholder={t('search')}
                    className="pl-[35px] xl:pl-[35px] 2xl:pl-[35px] 3xl:pl-[1.5vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[14px] xl:text-[16px] 3xl:text-[0.833vw]"
                  />
                </div>
              </div>
            </div>
            <div className="accordion-container ">
              <Accordion type="single" collapsible className="w-full">
                <div className="space-y-[8px] xl:space-y-[10px] 3xl:space-y-[0.521vw]">
                  <AccordionItem
                    value="item-1"
                    className="bg-interfacesurfacecomponent border border-InterfaceStrokesoft p-[16px] xl:p-[18] 3xl:p-[1.042vw]"
                  >
                    <AccordionTrigger className="group flex items-center justify-between text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium">
                      {t('whatIsNewInCQ2')}
                      <i className="cloud-fillarrowdown text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                    </AccordionTrigger>
                    <AccordionContent>{t('noData')}</AccordionContent>
                  </AccordionItem>
                  <AccordionItem
                    value="item-2"
                    className="bg-interfacesurfacecomponent border border-InterfaceStrokesoft p-[16px] xl:p-[18] 3xl:p-[1.042vw]"
                  >
                    <AccordionTrigger className="group flex items-center justify-between text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium">
                      {t('whatIsNewInCQ2')}
                      <i className="cloud-fillarrowdown text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                    </AccordionTrigger>
                    <AccordionContent>{t('noData')}</AccordionContent>
                  </AccordionItem>
                  <AccordionItem
                    value="item-3"
                    className="bg-interfacesurfacecomponent border border-InterfaceStrokesoft p-[16px] xl:p-[18] 3xl:p-[1.042vw]"
                  >
                    <AccordionTrigger className="group flex items-center justify-between text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium">
                      {t('isCQ1Replaced')}
                      <i className="cloud-fillarrowdown text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                    </AccordionTrigger>
                    <AccordionContent>{t('noData')}</AccordionContent>
                  </AccordionItem>
                  <AccordionItem
                    value="item-4"
                    className="bg-interfacesurfacecomponent border border-InterfaceStrokesoft p-[16px] xl:p-[18] 3xl:p-[1.042vw]"
                  >
                    <AccordionTrigger className="group flex items-center justify-between text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium">
                      {t('needNewLogin')}
                      <i className="cloud-fillarrowdown text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                    </AccordionTrigger>
                    <AccordionContent className="pb-0">
                      <ul className="list-disc list-inside ml-1 text-BrandSupport1pure text-[12px] xl:text-[14px] 3xl:text-[0.729vw] space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw] mt-[10px] xl:mt-[12px] 3xl:mt-[0.625vw]">
                        <li>partner.cloudquarks.in/partner/login </li>
                        <li>partner.cloudquarks.com/partner/login </li>
                        <li>partner.cloudquarks.tr/partner/login</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem
                    value="item-5"
                    className="bg-interfacesurfacecomponent border border-InterfaceStrokesoft p-[16px] xl:p-[18] 3xl:p-[1.042vw]"
                  >
                    <AccordionTrigger className="group flex items-center justify-between text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium">
                      {t('existingTransactions')}
                      <i className="cloud-fillarrowdown text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                    </AccordionTrigger>
                    <AccordionContent>{t('noData')}</AccordionContent>
                  </AccordionItem>
                  <AccordionItem
                    value="item-6"
                    className="bg-interfacesurfacecomponent border border-InterfaceStrokesoft p-[16px] xl:p-[18] 3xl:p-[1.042vw]"
                  >
                    <AccordionTrigger className="group flex items-center justify-between text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium">
                      {t('issuesContact')}
                      <i className="cloud-fillarrowdown text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                    </AccordionTrigger>
                    <AccordionContent>{t('noData')}</AccordionContent>
                  </AccordionItem>
                  <AccordionItem
                    value="item-7"
                    className="bg-interfacesurfacecomponent border border-InterfaceStrokesoft p-[16px] xl:p-[18] 3xl:p-[1.042vw]"
                  >
                    <AccordionTrigger className="group flex items-center justify-between text-InterfaceTexttitle text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium">
                      {t('stillUseCQ1')}
                      <i className="cloud-fillarrowdown text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                    </AccordionTrigger>
                    <AccordionContent>{t('noData')}</AccordionContent>
                  </AccordionItem>
                </div>
              </Accordion>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
