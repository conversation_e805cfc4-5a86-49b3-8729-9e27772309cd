'use client';
'use client';
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON>er,
  SheetTitle,
  SheetClose,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import * as React from 'react';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface InvoiceHeader {
  number: string;
  date: string;
  value: string;
}

interface InvoiceLineItem {
  subscriptionName: string;
  subscriptionId: string;
  description: string;
  dateRange: string;
  qty: number;
  amount: string;
  vat: string;
  netAmount: string;
}

interface Props {
  header: InvoiceHeader;
  lineItems: InvoiceLineItem[];
  sectionTitle?: string;
}

export default function ResendEmailPopup({
  header,
  lineItems,
  // sectionTitle = 'Commercial Marketplace Test',
}: Props) {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTrigger className="w-full">
        <div className="cursor-pointer px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-mail-tracking text-InterfaceTextdefault text-[16px] xl:text-[16px] 3xl:text-[0.779vw]"></i>
          {t('resendMail')}
        </div>
      </SheetTrigger>

      <SheetContent
        className={`${roboto.className} flex flex-col h-full sm:max-w-[800px] xl:max-w-[1100px] 3xl:max-w-[62.5vw] p-[0px] hideclose`}
        side="right"
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg:[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] font-semibold leading-[140%]">
              {t('information')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[15px] xl:p-[22px] 3xl:p-[1.25vw]">
          {/* Invoice Header */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[15px] xl:gap-[22px] 3xl:gap-[1.25vw]">
            {[
              { label: t('invoiceNumber'), value: header.number },
              { label: t('invoiceDate'), value: header.date },
              { label: t('invoiceValue'), value: header.value },
            ].map(({ label, value }, idx) => (
              <div
                key={idx}
                className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft"
              >
                <p className="text-InterfaceTextsubtitle text-[14px] font-[400] leading-[140%]">
                  {label}
                </p>
                <h2 className="text-InterfaceTextdefault text-[14px] font-[500] leading-[140%]">
                  {value}
                </h2>
              </div>
            ))}
          </div>

          <div className="mt-3">
            <h2 className="px-[16px] py-[18px] xl:py-[20px] 3xl:py-[1.042vw] text-InterfaceTexttitle text-[16px] font-[600] leading-[140%]">
              {/* {sectionTitle} */}
              {t('commercialMarketplaceTest')}
            </h2>

            <div className="overflow-x-auto">
              <table className="w-full text-left text-[14px] border-collapse border-b border-[#E5E7EB]">
                <thead className="bg-InterfaceStrokesoft font-[500] text-InterfaceTexttitle">
                  <tr>
                    <th className="px-4 py-[14px]">{t('subscriptionName')}</th>
                    <th className="px-4 py-[14px]">{t('description')}</th>
                    <th className="px-4 py-[14px]">{t('qty')}</th>
                    <th className="px-4 py-[14px]">{t('amount')}</th>
                    <th className="px-4 py-[14px]">{t('vat')}</th>
                    <th className="px-4 py-[14px]">{t('netAmount')}</th>
                  </tr>
                </thead>
                <tbody className="text-gray-800">
                  {lineItems.map((item, idx) => (
                    <tr key={idx} className="border-t">
                      <td className="px-4 py-[14px]">
                        <div className="font-medium text-sm">{item.subscriptionName}</div>
                        <div className="text-xs text-gray-500">{item.subscriptionId}</div>
                      </td>
                      <td className="px-4 py-[14px]">
                        <div className="font-semibold text-sm">{item.description}</div>
                        <div className="text-xs text-gray-500">{item.dateRange}</div>
                      </td>
                      <td className="px-4 py-[14px]">{item.qty}</td>
                      <td className="px-4 py-[14px]">{item.amount}</td>
                      <td className="px-4 py-[14px]">{item.vat}</td>
                      <td className="px-4 py-[14px] font-medium">{item.netAmount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
