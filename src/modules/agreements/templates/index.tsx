'use client';

import React from 'react';
import AgreementTable from '../components/table';
import { useTranslations } from 'next-intl';

export default function Agreements() {
  const t = useTranslations();

  return (
    <>
      <div className=" h-full">
        <div className="relative h-full px-[32px] py-[20px] 3xl:px-[1.667vw] 3xl:py-[1.042vw] bg-[#F6F7F8]">
          <div className="pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw]">
            <div className="flex flex-col pb-[10px] xl:pb-[10px] 3xl:pb-[0.525vw]">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                {t('agreements')}
              </div>
            </div>
            <div className="bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
              <div>
                <AgreementTable />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
