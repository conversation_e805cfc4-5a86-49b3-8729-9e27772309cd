'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet,
  Sheet<PERSON>lose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function Infopopup() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="border-none">
        <div className="w-[150px] xl:w-[150px] 2xl:w-[155px] 3xl:w-[7.833vw] px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] cursor-pointer xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-info text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
          {t('info')}
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('information')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[650px] lg:h-[660px] md:h-[650px] xl:h-[525px] 2xl:h-[652px] 3xl:h-[39.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid grid-cols-2 gap-[16px] 3xl:gap-[0.833vw]">
              <div className=" col-span-2 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('endCustomer')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Google AE 20240424165926770
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('productName')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Google Workspace Business Plus
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('subscriptionId')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  STlnjHQw9KHI3
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('listPrice')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  USD 500.00
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('contractValue')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  USD 2,100.00
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('term')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  1 Year
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('segment')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Commercial
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('provisioningId')}:
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  6905303938
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('createdDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  24-04-2024
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('startDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  04-04-2024
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('endDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  24-04-2025
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('billType')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Monthly
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('quantity')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  4
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('brand')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Google Workspace Commitment
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('productType')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Base
                </div>
              </div>
              <div className="col-span-2 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('domainId')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  google-test.redingtonscm.com.Automation-20240424165957575.com
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
