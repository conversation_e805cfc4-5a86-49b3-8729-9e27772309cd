'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  RadioGroup,
  RadioGroupItem,
  Label,
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  Button,
  Textarea,
  SheetFooter,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { SuccessfullySubmitPopup } from '@/components/common/successfully-submit-popup';
import EndDateAlignmentpopup from '../custom-end-date-popup';
import { useTranslations } from 'next-intl';

export default function Renewpopup() {
  const t = useTranslations();
  const [quantity, setQuantity] = React.useState(12);
  const [showChanges, setShowChanges] = React.useState('no');

  const [endAlignmentpopup, setEndAlignmentpopup] = React.useState(false);

  const increment = () => setQuantity((prev) => prev + 1);
  const decrement = () => setQuantity((prev) => Math.max(prev - 1, 0));
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="border-none">
        <div className="w-[150px] xl:w-[150px] 2xl:w-[155px] 3xl:w-[7.833vw] px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] cursor-pointer xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-refresh text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
          {t('renew')}
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[630px] xl:max-w-[650px] 2xl:max-w-[750px] 3xl:max-w-[40.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('requestForRenewalPrice')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto  h-[420px] xl:h-[541px] 2xl:h-[677px] 3xl:h-[36.25vw] xl:mb-[87px]">
          <div className="space-y-[20px] xl:space-y-[22px] 2xl:space-y-[24px] 3xl:space-y-[1.25vw]">
            <div className="grid grid-cols-3 gap-[16px] 3xl:gap-[0.833vw]">
              <div className=" col-span-1 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('endCustomer')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  {t('teamReactive')}
                </div>
              </div>
              <div className="col-span-2 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('productName')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Microsoft 365 Business Basic
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('subscriptionId')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  c737fa1a-d5b2-4c39-cb48-8df4e61bb09a
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('listPrice')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  USD 5.45
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('discount')} (%)
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  14
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('contractValue')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  USD 59.04
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('term')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  1 Year
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('segment')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Commercial
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('createdDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  2024-05-03
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('startDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  03-05-2024
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('endDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  02-05-2024
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('billType')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Monthly
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('quantity')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  12
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('brand')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  CSP NCE
                </div>
              </div>
              <div className="border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('productType')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Base
                </div>
              </div>
            </div>
            <div>
              <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                {t('doYouWantToMakeChangesInThisSubscriptionDuringRenewal')}
              </div>
              <RadioGroup
                defaultValue="comfortable"
                value={showChanges}
                onValueChange={setShowChanges}
                className="flex gap-[18px] xl:gap-[18px] 2xl:gap-[20px] 3xl:gap-[1.042vw] mt-[10px] xl:mt-[8px] 2xl:mt-[10px] 3xl:mt-[0.525]"
              >
                <div className="flex items-center space-x-2 custrediogroup">
                  <RadioGroupItem value="yes" id="r1" />
                  <Label htmlFor="r1">{t('yes')}</Label>
                </div>
                <div className="flex items-center space-x-2 custrediogroup">
                  <RadioGroupItem value="no" id="r2" />
                  <Label htmlFor="r2">{t('no')}</Label>
                </div>
              </RadioGroup>
            </div>
            {showChanges === 'yes' && (
              <>
                <div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('productName')}
                  </div>
                  <div className="flex items-end gap-[8px] 3xl:gap-[0.417vw] w-full">
                    <Select defaultValue="1">
                      <SelectTrigger className="placeholder-text-sm font-[500] bg-InterfaceSurfacecomponent border-InterfaceStrokehard rounded-none text-[12px] md:text-[12px] xl:text-[1px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder="Microsoft 365 Business Basic | CFQ7TTC0LH18:0001" />
                      </SelectTrigger>
                      <SelectContent className="rounded-none bg-[#FFF]">
                        <SelectGroup className="text-InterfaceTextdefault text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                          <SelectItem value="1">
                            Microsoft 365 Business Basic | CFQ7TTC0LH18:0001
                          </SelectItem>
                          <SelectItem value="2">
                            Microsoft 365 Business Basic (no Teams) | CFQ7TTC0LH18:000P
                          </SelectItem>
                          <SelectItem value="3">
                            Microsoft 365 Business Premium | CFQ7TTC0LCHC:0002
                          </SelectItem>
                          <SelectItem value="4">
                            Microsoft 365 Business Premium (no Teams) | CFQ7TTC0LCHC:000N
                          </SelectItem>
                          <SelectItem value="5">
                            Microsoft 365 Business Standard | CFQ7TTC0LDPB:0001
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <i className="cloud-info cursor-pointer text-InterfaceTextsubtitle font-[400] text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]" />
                        </TooltipTrigger>
                        <TooltipContent className="min-w-[280px] 3xl:min-w-[12.99vw] bg-background">
                          <p className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                            Please Select a Product from the Drop Down <br />
                            to Upgrade
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
                <div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('term')}:
                  </div>
                  <RadioGroup
                    defaultValue="year"
                    className="flex gap-[18px] xl:gap-[18px] 2xl:gap-[20px] 3xl:gap-[1.042vw] mt-[10px] xl:mt-[8px] 2xl:mt-[10px] 3xl:mt-[0.525]"
                  >
                    <div className="flex items-center space-x-2 custrediogroup">
                      <RadioGroupItem value="month" id="1" />
                      <Label htmlFor="1">{t('1Month')}</Label>
                    </div>
                    <div className="flex items-center space-x-2 custrediogroup">
                      <RadioGroupItem value="year" id="2" />
                      <Label htmlFor="2">{t('1Year')}</Label>
                    </div>
                  </RadioGroup>
                </div>
                <div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('billType')}:
                  </div>
                  <RadioGroup
                    defaultValue="monthly"
                    className="flex gap-[18px] xl:gap-[18px] 2xl:gap-[20px] 3xl:gap-[1.042vw] mt-[10px] xl:mt-[8px] 2xl:mt-[10px] 3xl:mt-[0.525]"
                  >
                    <div className="flex items-center space-x-2 custrediogroup">
                      <RadioGroupItem value="monthly" id="monthly" />
                      <Label htmlFor="monthly">{t('monthly')}</Label>
                    </div>
                    <div className="flex items-center space-x-2 custrediogroup">
                      <RadioGroupItem value="annual" id="annual" />
                      <Label htmlFor="annual">{t('annual')}</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('quantity')}:
                  </div>
                  <div className="flex items-center border border-gray-300 rounded-sm w-fit">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={decrement}
                      className="text-BrandSupport1pure text-[22px] font-[400]"
                    >
                      –
                    </Button>
                    <span className="px-4 text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {quantity}
                    </span>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={increment}
                      className="text-BrandSupport1pure text-[22px] font-[400]"
                    >
                      +
                    </Button>
                  </div>
                </div>
                <div className="flex items-center gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                  <div className="flex items-center cursor-pointer gap-[4px]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      {t('renewalStartDate')}
                    </div>
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      :
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      2025-05-03
                    </div>
                  </div>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center gap-[16px] 3xl:gap-[0.833vw]">
                          <div className="flex items-center cursor-pointer gap-[4px]">
                            <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                              {t('renewalEndDate')}
                            </div>
                            <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                              :
                            </div>
                            <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                              2026-05-03
                            </div>
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent className="min-w-[307px] 3xl:min-w-[15.99vw] bg-background">
                        <p className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                          {t(
                            'youCanCoterminateYourSubscriptionWithAnExistingNonTrialCspNceSubscriptionOrAlignTheEndDateWithTheCalendarMonthByChoosingAnAppropriateEndDateDependingOnTheTermDuration'
                          )}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <EndDateAlignmentpopup
                    open={endAlignmentpopup}
                    onClose={() => setEndAlignmentpopup(false)}
                  />
                </div>
              </>
            )}
            <div>
              <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                {t('comments')}
              </div>
              <Textarea className="border border-InterfaceStrokehard mt-[6px]" placeholder="" />
            </div>
          </div>
        </div>

        <SheetFooter className="absolute bg-background mt-[16px] bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <Link
                href=""
                className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                  {t('cancel')}
                </div>
              </Link>
              <SuccessfullySubmitPopup />
            </div>
          </div>
        </SheetFooter>
        <EndDateAlignmentpopup
          open={endAlignmentpopup}
          onClose={() => setEndAlignmentpopup(false)}
        />
      </SheetContent>
    </Sheet>
  );
}
