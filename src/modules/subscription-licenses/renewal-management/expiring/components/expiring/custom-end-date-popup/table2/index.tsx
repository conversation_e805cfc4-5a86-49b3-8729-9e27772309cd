import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { Checkbox } from '@redington-gulf-fze/cloudquarks-component-library';

const notificationColumns: ColumnDef<User>[] = [
  {
    accessorKey: 'productname',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Product Name
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('productname')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'subscriptionid',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Subscription ID
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('subscriptionid')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'term',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Term
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('term')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'billtype',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Bill Type
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('billtype')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'enddate',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              End Date
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('enddate')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'select',
    enableSorting: true,
    header: ({ column }) => {
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Select to Apply
            </div>
          </button>
        </div>
      );
    },
    cell: () => (
      <div className="flex items-center justify-center text-InterfaceTextdefault">
        {' '}
        <Checkbox id="recentpurchases" />
      </div>
    ),

    minSize: 400,
  },
];

type User = {
  productname: string;
  subscriptionid: string;
  term: string;
  billtype: string;
  enddate: string;
};
const data = [
  {
    productname: 'Microsoft 365 Business Standard',
    subscriptionid: '4400328e-acba-4641-dfda-f00341df117e',
    term: '1 Year',
    billtype: 'Monthly',
    enddate: '09-03-2025',
  },
  {
    productname: 'Exchange Plan (Plan 1)',
    subscriptionid: '808fed76-62ce-4ae1-d71c-3ba1b34d79a',
    term: '1 Year',
    billtype: 'Monthly',
    enddate: '09-03-2026',
  },
  {
    productname: 'Microsoft Defender for Office 365 (Plan 1)',
    subscriptionid: 'e0c0badc-2119-4c2fce5b-b49a7b546369',
    term: '1 Year',
    billtype: 'Monthly',
    enddate: '09-03-2026',
  },
];
export function Table2() {
  return (
    <div className="overflow-x-auto h-full w-full">
      <DataTable data={data} columns={notificationColumns} withCheckbox={false} />
    </div>
  );
}
