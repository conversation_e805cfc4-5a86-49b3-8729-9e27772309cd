'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  SheetClose,
  <PERSON>etContent,
  RadioGroup,
  RadioGroupItem,
  Label,
  Button,
  SheetFooter,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import Image from 'next/image';
import { Table1 } from './table1';
import { Table2 } from './table2';
import { Table3 } from './table3';
import { DefaultInfoPopup } from './default-info-popup';
import { ActivitylogSheetProps } from '@/types';

export default function EndDateAlignmentpopup({ open, onClose }: ActivitylogSheetProps) {
  const [showChanges, setShowChanges] = React.useState('default');
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>

      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[630px] xl:max-w-[650px] 2xl:max-w-[750px] 3xl:max-w-[40.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between items-center">
            <div className="flex items-center gap-[12px] xl:gap-[10px] 2xl:gap-[12px] 3xl:gap-[0.625vw] text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              <SheetClose>
                <Image
                  src="/images/back-square.svg"
                  alt="back-square"
                  width={32}
                  height={32}
                  className="h-[30px] xl:h-[30px] 2xl:h-[32px] 3xl:h-[1.67vw] w-[30px] xl:w-[30px] 2xl:w-[32px] 3xl:w-[1.67vw]"
                />
              </SheetClose>
              Select Custom End Date
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[470px] 2xl:h-[620px] 3xl:h-[37.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 2xl:space-y-[24px] 3xl:space-y-[1.25vw]">
            <div className="grid grid-cols-4 gap-[22px] xl;gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
              <div className=" col-span-1 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  Product Name
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Office 365 E5
                </div>
              </div>
              <div className="col-span-1 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  SKUID
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  CFQ7TTCOLF8S:0002
                </div>
              </div>
              <div className="col-span-1 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  Term
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  1 Year
                </div>
              </div>
              <div className="col-span-1 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  Bill Type
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Monthly
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-[10px] 3xl:gap-[0.525vw]">
              <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                Custom End Date By :
              </div>
              <div>
                <RadioGroup
                  defaultValue="default"
                  value={showChanges}
                  onValueChange={setShowChanges}
                  className="flex gap-[18px] xl:gap-[18px] 2xl:gap-[20px] 3xl:gap-[1.042vw] mt-[10px] xl:mt-[8px] 2xl:mt-[10px] 3xl:mt-[0.525]"
                >
                  <div className="flex items-center space-x-2 custrediogroup">
                    <RadioGroupItem value="default" id="1" />
                    <Label htmlFor="1">Default</Label>
                  </div>
                  <div className="flex items-center space-x-2 custrediogroup">
                    <RadioGroupItem value="existing" id="2" />
                    <Label htmlFor="2">Co-terminate with Existing</Label>
                  </div>
                  <div className="flex items-center space-x-2 custrediogroup">
                    <RadioGroupItem value="monthend" id="3" />
                    <Label htmlFor="3" className="pr-[6px]">
                      Align with Month End
                    </Label>

                    <DefaultInfoPopup />
                  </div>
                </RadioGroup>
              </div>
            </div>

            {showChanges === 'default' && (
              <>
                <Table1 />
              </>
            )}
            {showChanges === 'existing' && (
              <>
                <Table2 />
              </>
            )}
            {showChanges === 'monthend' && (
              <>
                <Table3 />
              </>
            )}
          </div>
        </div>

        <SheetFooter className="absolute bg-background mt-[16px] bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose>
                <Link
                  href=""
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    Cancel
                  </div>
                </Link>
              </SheetClose>
              <SheetClose>
                <Button className="flex items-center applybtn gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none">
                  <div>
                    <i className="cloud-fillcircletick text-interfacetextinverse flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    Apply
                  </div>
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
