import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { Checkbox } from '@redington-gulf-fze/cloudquarks-component-library';

const notificationColumns: ColumnDef<User>[] = [
  {
    accessorKey: 'productname',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Product Name
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('productname')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'term',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Term
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('term')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'billtype',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Bill Type
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('billtype')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'enddate',
    enableSorting: true,
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              End Date
            </div>
            {isSorted === 'asc' ? (
              <ArrowUp className="h-[12px] w-[12px]" />
            ) : isSorted === 'desc' ? (
              <ArrowDown className="h-[12px] w-[12px]" />
            ) : (
              <ArrowUpDown className="h-[12px] w-[12px]" />
            )}
          </button>
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.getValue('enddate')}</div>,

    minSize: 400,
  },
  {
    accessorKey: 'select',
    enableSorting: true,
    header: ({ column }) => {
      return (
        <div className="flex flex-col">
          <button
            type="button"
            className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
              Select to Apply
            </div>
          </button>
        </div>
      );
    },
    cell: () => (
      <div className="flex items-center justify-center text-InterfaceTextdefault">
        {' '}
        <Checkbox id="recentpurchases" />
      </div>
    ),

    minSize: 400,
  },
];

type User = {
  productname: string;
  term: string;
  billtype: string;
  enddate: string;
};
const data = [
  {
    productname: 'Office 365 E5',
    term: '1 Year',
    billtype: 'Monthly',
    enddate: '02-05-2025 ',
  },
];
export function Table1() {
  return (
    <div className="overflow-x-auto h-full w-full">
      <DataTable data={data} columns={notificationColumns} withCheckbox={false} />
    </div>
  );
}
