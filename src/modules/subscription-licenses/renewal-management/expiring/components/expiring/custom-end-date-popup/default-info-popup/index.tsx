'use client';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';

export function DefaultInfoPopup() {
  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <i className="cloud-info cursor-pointer text-InterfaceTextsubtitle font-[400] text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw]" />
        </PopoverTrigger>
        <PopoverContent className="absolute top-0 right-0 p-0  w-[460px] xl:w-[470px] 2xl:w-[480px] 3xl:w-[25.04vw] rounded-none bg-[#FFF]">
          <div>
            <div className="p-[10px] 3xl:p-[0.525vw]">
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
                Default
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                This default option ensures that the end date of the new subscription aligns with
                the selected Term Length.
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
                Co-terminate with Existing
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                Choose this option if you desire the new subscription to co-terminate simultaneously
                with your other NCE subscriptions. You can review all NCE subscriptions and their
                associated term end dates. Select the desired end date from one of the existing
                subscriptions to align with. If the term length of the new subscription is shortened
                based on your selection, you will be charged a pro-rated amount, and youll observe
                corresponding adjustments to your order value
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
                Align with Month End
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                Choose this option if you aim to synchronize the billing cycle of the new
                subscription with the end of a calendar month. You can set the subscription to end
                as close to the months end as possible, adhering to monthly, yearly, or other
                billing durations without surpassing the chosen term length. If the term length of
                the new subscription is shortened based on your selection, you will be charged a
                pro-rated amount, and youll see corresponding adjustments to your order value.
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                Click <span className="text-InterfaceTextprimary underline">Here</span> to learn
                more on End Date Alignment or Coterminosity
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
