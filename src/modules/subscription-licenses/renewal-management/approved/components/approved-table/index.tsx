import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React, { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import Infopopup from '../info-popup';
import ApprovedFilter from '../approvedfilter';
import { RejectPopup } from '../reject-popup';
import AcceptRenewPopup from '../accept-renew-popup';
import { useTranslations } from 'next-intl';

export default function Approvedtable() {
  const t = useTranslations();
  const [endCustomer, setEndCustomer] = useState('Alpha Systems');

  // Clear All handler
  const handleClearAll = () => {
    setEndCustomer('');
  };

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endCustomer')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('endCustomer')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('endcustomer')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'productname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('productName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('productName')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('productname')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'qty',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('qty')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('qty')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('qty')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'enddate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('endDate')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('enddate')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[215px] xl:w-[215px] 2xl:w-[210px] 3xl:w-[11vw] z-20 bg-interfacesurfacecomponent ">
                <div className="">
                  <Infopopup />
                  <RejectPopup />
                  <AcceptRenewPopup />
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    endcustomer: string;
    productname: string;
    qty: string;
    enddate: string;
  };

  const registrationdata = [
    {
      endcustomer: 'Alpha Systems',
      productname: 'Computer Systems',
      qty: '1',
      enddate: '05/05/2025',
    },
    {
      endcustomer: 'Alpha Systems',
      productname: 'Google Workspace Frontline Started',
      qty: '1',
      enddate: '05/05/2025',
    },
    {
      endcustomer: 'Alpha Systems',
      productname: 'Computer Systems',
      qty: '1',
      enddate: '05/05/2025',
    },
    {
      endcustomer: 'Alpha Systems',
      productname: 'Google Workspace Frontline Started',
      qty: '1',
      enddate: '05/05/2025',
    },
    {
      endcustomer: 'Alpha Systems',
      productname: 'Computer Systems',
      qty: '1',
      enddate: '05/05/2025',
    },
    {
      endcustomer: 'Alpha Systems',
      productname: 'Google Workspace Frontline Started',
      qty: '1',
      enddate: '05/05/2025',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('approved')} (# 10/USD 100.10K)
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            {/* <div className="flex items-center gap-2 cursor-pointer">
              <i className="cloud-filtericon"></i>Filter
            </div> */}
            <ApprovedFilter />
          </div>
        </div>

        <div className="overflow-x-auto">
          <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] text-sm font-normal">
            {/* End Customer */}
            <div className="flex items-center bg-[#F4F5F7] px-2 rounded-sm">
              <span className="text-InterfaceTextsubtitle">{t('endCustomer')} :</span>
              <Input
                type="text"
                value={endCustomer}
                placeholder="Enter End Customer"
                onChange={(e) => setEndCustomer(e.target.value)}
                className="font-medium text-InterfaceTextdefault bg-transparent w-[130px] focus:outline-none focus:ring-0 focus:border-transparent placeholder:font-normal mt-[2px] xl:mt-[0px] 2xl:mt-[1px] 3xl:mt-[0.052vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"
              />
              {endCustomer && (
                <i
                  className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black cursor-pointer"
                  onClick={() => setEndCustomer('')}
                ></i>
              )}
            </div>

            {/* Clear All */}
            <div
              className="flex items-center text-[#8B8B8B] font-[400] hover:text-black cursor-pointer"
              onClick={handleClearAll}
            >
              <i className="cloud-closecircle mr-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
              {t('clearAll')}
            </div>
          </div>
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
        </div>
        <TablePagination />
      </div>
    </>
  );
}
