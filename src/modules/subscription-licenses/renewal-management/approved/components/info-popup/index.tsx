'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  Sheet<PERSON>lose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Table } from './table';
import { useTranslations } from 'next-intl';

export default function Infopopup() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="border-none">
        <div className="w-[215px] xl:w-[215px] 2xl:w-[210px] 3xl:w-[11vw] px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] cursor-pointer xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-info text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
          {t('info')}
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[920px] xl:max-w-[920px] 2xl:max-w-[1100px] 3xl:max-w-[70.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('information')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[450px] xl:h-[740px] 2xl:h-[650px] 3xl:h-[39.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid grid-cols-12 gap-[16px] 3xl:gap-[0.833vw]">
              <div className=" col-span-4 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('endCustomerName')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Merup1
                </div>
              </div>
              <div className="col-span-4 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('productName')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Windows Server 2022 CAL - 1 Device CAL-1 Year
                </div>
              </div>
              <div className="col-span-4 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('subscriptionId')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  230dad11-0ace-4150-df85-d483786e0b33
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('listPrice')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  USD 10.16
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('contractValue')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  USD 108.00
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('term')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  1 Year
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('segment')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Commercial
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('createdDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  18-06-2024
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('startDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  18-16-2024
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('endDate')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  17-16-2025
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('billType')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Annual
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('quantity')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  1
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('brand')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Software Subscription
                </div>
              </div>
              <div className="col-span-3 border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('productType')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Base
                </div>
              </div>
              <div className="col-span-3"></div>
              <div className="col-span-3  border-b border-[#E5E7EB] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  {t('adminRemark')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  Testing
                </div>
              </div>
            </div>
            <div>
              <div className="px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] flex items-center text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                {t('existing')}
              </div>
              <Table />
            </div>
            <div>
              <div className="border-t border-InterfaceStrokesoft px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] flex items-center text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                {t('renewalPrice')}
              </div>
              <Table />
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
