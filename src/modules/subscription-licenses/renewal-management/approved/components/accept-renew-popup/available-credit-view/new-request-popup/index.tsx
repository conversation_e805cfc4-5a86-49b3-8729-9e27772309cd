'use client';
import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  Sheet<PERSON>ooter,
  Label,
  Input,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

type Props = {
  onPopupOpen?: () => void;
};

export default function NewRequestPopup({ onPopupOpen }: Props) {
  const t = useTranslations();

  const handleOpen = () => {
    if (onPopupOpen) onPopupOpen();
  };
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button
          onClick={handleOpen}
          className="border border-InterfaceStrokesoft w-full bg-[linear-gradient(180deg,_#FEFEFE_0%,_#F6F8FA_100%)] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
        >
          <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[100%] flex items-center">
            {t('requestCreditLimit')}
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent className="z-[999] hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('creditLimitRevisionNewRequest')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('assignedCreditLimitExisting')}
                {/* <span className="text-[#f52d2d]"> *</span> */}
              </Label>
              <Input
                disabled
                type="text"
                placeholder="USD 200,000.00"
                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
              />
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('availableCreditLimit')}
                {/* <span className="text-[#f52d2d]"> *</span> */}
              </Label>
              <Input
                disabled
                type="text"
                placeholder="USD 250,000.00"
                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
              />
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('requestedAssignedCreditLimit2')}
                <span className="text-[#f52d2d]"> *</span>
              </Label>
              <Input
                type="text"
                placeholder="Enter the value"
                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
              />
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('requestReason')}
                <span className="text-[#f52d2d]"> *</span>
              </Label>
              <Select defaultValue="completed">
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none">
                  <SelectValue placeholder="-- Select --" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="completed">Select</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('partnerRemarks')}
                {/* <span className="text-[#f52d2d]"> *</span> */}
              </Label>
              <Textarea
                placeholder={t('enterRemarks')}
                className="border border-InterfaceStrokedefault rounded-none"
              />
            </div>
            <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
              {t('characterLimit50')}
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full bg-[#FFF] right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose>
                <Button className="border border-InterfaceStrokesoft bg-[linear-gradient(180deg,_#FEFEFE_0%,_#F6F8FA_100%)] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none">
                  <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[100%] flex items-center">
                    {t('cancel')}
                  </div>
                </Button>
              </SheetClose>
              <SheetClose>
                <Button className="border border-BrandPrimary800 bg-BrandPrimarypure py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none">
                  <i className="cloud-send flex items-center text-InterfaceTextwhite text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  <div className="text-InterfaceTextwhite text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[100%] flex items-center">
                    {t('submit')}
                  </div>
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
