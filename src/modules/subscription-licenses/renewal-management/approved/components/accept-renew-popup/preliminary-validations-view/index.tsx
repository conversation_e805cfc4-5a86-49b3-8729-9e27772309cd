'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';
import { useTranslations } from 'next-intl';

export function PreliminaryValidationsView() {
  const t = useTranslations();

  const [open, setOpen] = useState(false);
  return (
    <div className="group bg-InterfaceSurfacecomponentmuted px-[16px] 3xl:px-[0.833vw] py-[4px] 3xl:py-[0.21vw] text-center">
      <div className="text-InterfaceTextdefault group-hover:text-black text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[10px] 3xl:px-[0.525vw] py-[6px] 3xl:py-[0.36vw]">
        {t('preliminaryValidations')}
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="text-[#F2980E] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[100%] flex items-center justify-center gap-[4px] 3xl:gap-[0.21vw] px-[10px] 3xl:px-[0.525vw] py-[6px] 3xl:py-[0.36vw] cursor-pointer">
            <span className="group-hover:hidden flex gap-[4px] 3xl:gap-[0.21vw]">
              <i className="cloud-fillcircletick text-[#5D9D4A] flex items-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw]" />
              {t('pass')}
            </span>
            <span className="hidden group-hover:inline text-InterfaceTextprimary">
              {t('viewMore')}
            </span>
          </div>
        </PopoverTrigger>

        <PopoverContent
          side="top"
          align="center"
          className="p-0 w-[280px] xl:w-[290px] 2xl:w-[300px] 3xl:w-[300px] rounded-none bg-[#FFF] z-[999]"
        >
          <div className="p-[16px] 3xl:p-[0.833vw] flex flex-col gap-[20px] xl:gap-[20px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
            <div className="flex justify-between items-center">
              <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                {t('preliminaryValidations')}
              </div>
              <div onClick={() => setOpen(false)}>
                <i className="cloud-closecircle text-closecolor text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
              </div>
            </div>
            <div className="flex flex-col gap-[12px] 3xl:gap-[0.625vw]">
              <div className="flex justify-between items-center gap-[10px] 3xl:gap-[0.525vw]">
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  {t('overallStatus')}
                </div>
                <div>
                  <Button
                    type="submit"
                    className="bg-BrandHighlight100 text-BrandHighlight600 text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] rounded-none py-[6px] 3xl:py-[0.36vw] px-[8px] 3xl:px-[0.417vw]"
                  >
                    <i className="cloud-fillcircletick flex items-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
                    {t('pass')}
                  </Button>
                </div>
              </div>
              <div className="flex flex-col gap-[4px] 3xl:gap-[0.21vw]">
                <div className="flex justify-between items-center gap-[10px] 3xl:gap-[0.525vw]">
                  <div className="text-InterfaceTextdefault text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                    {t('generaltncs')}
                  </div>
                  <div>
                    <Button
                      type="submit"
                      className="border border-BrandHighlightpure bg-InterfaceSurfacecomponent text-BrandHighlight600 text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] rounded-none px-[6px] 3xl:px-[0.36vw] py-[4px] 3xl:py-[0.21vw]"
                    >
                      <i className="cloud-fillcircletick flex items-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
                      {t('pass')}
                    </Button>
                  </div>
                </div>
                <div className="flex justify-between items-center gap-[10px] 3xl:gap-[0.525vw]">
                  <div className="text-InterfaceTextdefault text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                    {t('partnerReadiness')}
                  </div>
                  <div>
                    <Button
                      type="submit"
                      className="border border-BrandHighlightpure bg-InterfaceSurfacecomponent text-BrandHighlight600 text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] rounded-none px-[6px] 3xl:px-[0.36vw] py-[4px] 3xl:py-[0.21vw]"
                    >
                      <i className="cloud-fillcircletick flex items-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
                      {t('pass')}
                    </Button>
                  </div>
                </div>
                <div className="flex justify-between items-center gap-[10px] 3xl:gap-[0.525vw]">
                  <div className="text-InterfaceTextdefault text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                    {t('vendortncs')}
                  </div>
                  <div>
                    <Button
                      type="submit"
                      className="border border-BrandHighlightpure bg-InterfaceSurfacecomponent text-BrandHighlight600 text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] rounded-none px-[6px] 3xl:px-[0.36vw] py-[4px] 3xl:py-[0.21vw]"
                    >
                      <i className="cloud-fillcircletick flex items-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
                      {t('pass')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
