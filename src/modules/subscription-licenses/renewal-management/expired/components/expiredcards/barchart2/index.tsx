'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';

export default function Barchart2() {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '18%',
      right: '0%',
      bottom: '3%',
      top: '5%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['0-2 Days', '3-4 Days', '5-6 Days', '7-8 Days', '9+ Days'],
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    yAxis: [
      {
        name: 'Count of Subscriptions \n Pending Actions',
        nameLocation: 'center',
        nameGap: 20,
        nameTextStyle: {
          fontSize: 10,
          color: '#7F8488',
          padding: [0, 0, 10, 0],
        },
        type: 'value',
        min: 0,
        max: 10,
        interval: 2,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    series: [
      {
        name: 'Direct',
        type: 'bar',
        barWidth: '70%',
        data: [7, 5, 4, 5, 1],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#F2980E' }, // Top color
            { offset: 1, color: '#F8B720' }, // Bottom color
          ]),
          borderRadius: [2, 2, 0, 0],
        },
      },
    ],
  };
  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '130px' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
