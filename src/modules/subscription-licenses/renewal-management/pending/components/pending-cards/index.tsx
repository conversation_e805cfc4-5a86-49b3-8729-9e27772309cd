'use client';
import React from 'react';
import Barchart1 from './barchart1';
import Barchart2 from './barchart2';
import Barchart3 from './barchart3';
import { useTranslations } from 'next-intl';

export default function ExpiringCards() {
  const t = useTranslations();
  return (
    <div>
      <div className="grid grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative !h-[165px]">
            <div className="col-span-3 xl:col-span-2 2xl:col-span-1 3xl:col-span-1">
              <i className="cloud-cards1 text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-3 xl:col-span-4 2xl:col-span-5 3xl:col-span-5 w-full h-[110px] 3xl:h-[5.885vw]">
              <Barchart1 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-4">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-bold leading-[140%]">
                {t('expiring')} <br /> {t('subscriptions')}*
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[1.70vw] font-semibold truncate">
                100
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative !h-[165px]">
            <div className="col-span-3 xl:col-span-2 2xl:col-span-1 3xl:col-span-1">
              <i className="cloud-cards1 text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-3 xl:col-span-4 2xl:col-span-5 3xl:col-span-5 w-full h-[110px] 3xl:h-[5.885vw]">
              <Barchart2 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-4">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-bold leading-[140%]">
                {t('requestsIn')} <br /> {t('pending')}*
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[1.70vw] font-semibold truncate">
                10
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative !h-[165px]">
            <div className="col-span-3 xl:col-span-2 2xl:col-span-1 3xl:col-span-1">
              <i className="cloud-cards1 text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-3 xl:col-span-4 2xl:col-span-5 3xl:col-span-5 w-full h-[110px] 3xl:h-[5.885vw]">
              <Barchart3 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-4">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-bold leading-[140%]">
                {t('awaitingAction')}*
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[1.70vw] font-semibold truncate">
                10
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
