'use client';

import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';
import { <PERSON>o } from 'next/font/google';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function RejectPopup() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTrigger className="hover:bg-BrandNeutral100 w-full">
        <div className="w-[150px] xl:w-[150px] 2xl:w-[155px] 3xl:w-[7.833vw] px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] cursor-pointer xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-closecircle text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
          {t('reject')}
        </div>
      </SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw] mt-[10px]">
            <i className="cloud-closecircle text-InterfaceTextprimary font-[400] text-[72px]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[36px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                {t('rejectQuote')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[20px] lg:text-[20px] xl:text-[20px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                {t('areYouSureYouWantToRejectThisQuote')}
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
            <Link
              href={''}
              className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-background bg-closecolor font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]"
            >
              <i className="cloud-fillcircletick text-[18px]"></i>
              {t('yesReject')}
            </Link>
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">or</p>
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
            </div>
            <SheetClose>
              <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                <i className="cloud-closecircle text-[18px]"></i>
                {t('noLater')}
              </div>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
