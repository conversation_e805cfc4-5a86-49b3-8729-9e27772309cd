'use client';
import { CommanSheetProps } from '@/types';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { Sheet, SheetContent, SheetTitle } from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SuccessPopupProps extends CommanSheetProps {
  message?: string;
  title?: string;
}

export function SuccessPopup({ open, onClose, message, title = 'Success' }: SuccessPopupProps) {
  const t = useTranslations();

  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetTitle></SheetTitle>
        <SheetContent
          className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw]  p-[0px] hideclose`}
          side={'right'}
        >
          <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[48px] lg:mx-[48px] xl:mx-[48px] 2xl:mx-[48px] 3xl:mx-[2.5vw]">
            <div className="flex flex-col justify-center items-center gap-[35px] xl:gap-[35px] 3xl:gap-[1.863vw] mt-2">
              <i className="cloud-tickcircle text-BrandSupport1pure text-[58px]"></i>
              <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="mx-[30px] text-InterfaceTexttitle text-[28px] lg:text-[28px] xl:text-[28px] 2xl:text-[28px] 3xl:text-[1.85vw] font-normal  text-center">
                  {title}
                </div>
                <div className="text-InterfaceTextdefault text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] font-normal text-center">
                  {message}
                </div>
              </div>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
              {/* <Button
                onClick={onClose}
                className=" applybtn text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw] text-interfacetextinverse font-[400] rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[11px] md:py-[11px] xl:py-[11px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[170px] 3xl:w-[8.854vw]"
              >
                <i className="cloud-closecircle text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw]"></i>
                {t('close')}
              </Button> */}
              <Button
                onClick={onClose}
                className="text-[#3C4146] cancelbtn hover:bg-[#c9c6c6] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] w-[170px] 3xl:w-[8.854vw]"
              >
                <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('close')}
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
