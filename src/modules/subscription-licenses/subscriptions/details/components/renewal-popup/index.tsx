'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  <PERSON>et<PERSON>ooter,
  Button,
  Label,
  Textarea,
  RadioGroupItem,
  RadioGroup,
  SelectTrigger,
  Select,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';
import { Roboto } from 'next/font/google';
import { cn } from '@/lib/utils/util';
import { RenewalSubmitedPopup } from '../renewal-submited';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface RenewalPopupProps extends CommanSheetProps {
  isDecrease?: boolean;
}

export default function RenewalPopup({ open, onClose }: RenewalPopupProps) {
  const t = useTranslations();

  const [RenewalPopupOpen, setRenewalPopupOpen] = React.useState(false);
  const [ProductName, setProductName] = React.useState('Kuwait');
  const [selectedValue, setSelectedValue] = React.useState('c2');
  const [value, setValue] = React.useState<number>(5000);
  const [clickCount, setClickCount] = React.useState<number>(0);
  const increment = () => {
    if (clickCount >= 12 || value >= 10000) return;

    if (value === 0) setValue(5000);
    else if (value === 5000) setValue(10000);

    setClickCount((prev) => prev + 1);
  };
  const decrement = () => {
    if (value === 0) return;

    if (value === 10000) {
      setValue(5000);
      setClickCount((prev) => prev - 1);
    } else if (value === 5000) {
      setValue(0);
      setClickCount(0);
    }
  };

  const inputClasses =
    'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none focus:border-InterfaceStrokehard focus:ring-0 text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]';
  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} hideclose flex flex-col h-full gap-0 sm:max-w-[800px] xl:max-w-[800px] 3xl:max-w-[41.667vw] p-[0px]`}
        >
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-[600] leading-[140%]">
                {t('requestForRenewalPrice')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="bg-InterfaceSurfacecomponent overflow-auto h-full p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <div className="space-y-[14px] lg:space-y-[14px] xl:space-y-[14px] 2xl:space-y-[14px] 3xl:space-y-[0.729vw]">
              <div className="grid grid-cols-2 gap-[20px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('endCustomer')}
                  </div>
                  <div
                    title="Team Reactive"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    Team Reactive
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('productName')}
                  </div>
                  <div
                    title="Microsoft 365 Business Basic"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    Microsoft 365 Business Basic
                  </div>
                </div>
              </div>

              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('subscriptionId')}
                </div>
                <div
                  title="8259a251-f511-4dfd-dab8eff77fe8a45a"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  8259a251-f511-4dfd-dab8eff77fe8a45a
                </div>
              </div>

              <div className="grid grid-cols-3 gap-[20px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('listPrice')}
                  </div>
                  <div
                    title="USD 5.45"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    USD 5.45
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('discount')}
                  </div>
                  <div
                    title="1"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    14
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('contractValue')}
                  </div>
                  <div
                    title="USD 59.04"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    USD 59.04
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('term')}
                  </div>
                  <div
                    title="1 Year"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    1 Year
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('segment')}
                  </div>
                  <div
                    title="Commercial"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    Commercial
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('createdDate')}
                  </div>
                  <div
                    title="2024-05-03"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    2024-05-03
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('startDate')}
                  </div>
                  <div
                    title="03-05-2024"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    03-05-2024
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('endDate')}
                  </div>
                  <div
                    title="02-05-2025"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    02-05-2025
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('billType')}
                  </div>
                  <div
                    title="Monthly"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    Monthly
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('quantity')}
                  </div>
                  <div
                    title="12"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    12
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('brand')}
                  </div>
                  <div
                    title="CSP NCE"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    CSP NCE
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('productType')}
                  </div>
                  <div
                    title="CSP NCE"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    Base
                  </div>
                </div>
              </div>
              <div>
                <div className="flex flex-col gap-[10px] 3xl:gap-[0.521vw] mb-[20px] lg:mb-[20px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                  <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                    {t('doYouWantToMakeChangesInThisSubscriptionDuringRenewal')}
                  </Label>
                  <RadioGroup
                    defaultValue="c2"
                    value={selectedValue}
                    onValueChange={(value) => {
                      setSelectedValue(value);
                    }}
                    className="flex items-center gap-[20px] 3xl:gap-[1.042vw]"
                  >
                    <div className="font-[500] flex items-center gap-2">
                      <RadioGroupItem value="c1" id="r1" />
                      <Label
                        htmlFor="r1"
                        className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle cursor-pointer"
                      >
                        {t('yes')}
                      </Label>
                    </div>
                    <div className="font-[500] flex items-center gap-2">
                      <RadioGroupItem value="c2" id="r2" />
                      <Label
                        htmlFor="r2"
                        className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle cursor-pointer"
                      >
                        {t('no')}
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
              {selectedValue === 'c1' && (
                <>
                  <div>
                    <div className="flex flex-col gap-1.5 mb-[20px] lg:mb-[20px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                      <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        {t('productName')}
                      </Label>
                      <div className="flex items-center gap-2">
                        <Select value={ProductName} onValueChange={setProductName}>
                          <SelectTrigger
                            className={cn(
                              inputClasses,
                              ProductName
                                ? 'text-blackcolor' // option text color
                                : 'text-InterfaceTextsubtitle' // placeholder color
                            )}
                          >
                            <SelectValue placeholder="" />
                          </SelectTrigger>
                          <SelectContent className="rounded-none bg-[#FFF] border-none">
                            <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                              <SelectItem value="Kuwait">
                                Microsoft 365 Business Basic | cfq7ttc0lh18:0001
                              </SelectItem>
                              <SelectItem value="India">
                                Microsoft 365 Business Basic | cfq7ttc0lh18:0001
                              </SelectItem>
                              <SelectItem value="MEA">
                                Microsoft 365 Business Basic | cfq7ttc0lh18:0001
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <div className="cursor-pointer">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <i className="cloud-info text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                              </TooltipTrigger>
                              <TooltipContent
                                side="top"
                                align="center"
                                className="w-[338px] 3xl:w-[17.604vw] bg-background"
                              >
                                <p className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] text-center">
                                  Please Select a Product from the Drop Down to Upgrade
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="flex flex-col gap-[10px] 3xl:gap-[0.521vw] mb-[20px] lg:mb-[20px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                      <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        {t('term')} :
                      </Label>
                      <RadioGroup
                        defaultValue="c1"
                        className="flex items-center gap-[20px] 3xl:gap-[1.042vw]"
                      >
                        <div className="font-[500] flex items-center gap-2">
                          <RadioGroupItem value="c1" id="Month" />
                          <Label
                            htmlFor="Month"
                            className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle cursor-pointer"
                          >
                            1 Month
                          </Label>
                        </div>
                        <div className="font-[500] flex items-center gap-2">
                          <RadioGroupItem value="c2" id="Year" />
                          <Label
                            htmlFor="Year"
                            className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle cursor-pointer"
                          >
                            1 Year
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                  <div>
                    <div className="flex flex-col gap-[10px] 3xl:gap-[0.521vw] mb-[20px] lg:mb-[20px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                      <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        {t('billType')} :
                      </Label>
                      <RadioGroup
                        defaultValue="c1"
                        className="flex items-center gap-[20px] 3xl:gap-[1.042vw]"
                      >
                        <div className="font-[500] flex items-center gap-2">
                          <RadioGroupItem value="c1" id="Monthly" />
                          <Label
                            htmlFor="Monthly"
                            className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle cursor-pointer"
                          >
                            Monthly
                          </Label>
                        </div>
                        <div className="font-[500] flex items-center gap-2">
                          <RadioGroupItem value="c2" id="Annual" />
                          <Label
                            htmlFor="Annual"
                            className="text-[12px] md:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle cursor-pointer"
                          >
                            Annual
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                  <div>
                    <div className="flex flex-col gap-1.5 mb-[20px] lg:mb-[20px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                      <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        {t('quantity')} :
                      </Label>
                      <div className="w-[15%] justify-between px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] flex items-center rounded-none border border-InterfaceStrokedefault bg-background text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[100%]">
                        <div
                          onClick={decrement}
                          className={`cursor-pointer ${value === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          -
                        </div>
                        <div>{clickCount}</div>
                        <div
                          onClick={increment}
                          className={`cursor-pointer ${clickCount >= 2 || value === 10000 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          +
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-[60px] 3xl:gap-[3.125vw] mb-[20px] lg:mb-[20px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                      <div className="flex items-center gap-2">
                        <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                          {t('renewal')} {t('startDate')} :
                        </div>
                        <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                          2025-05-03
                        </div>
                      </div>
                      <div className="flex items-center gap-[20px] 3xl:gap-[1.042vw]">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center gap-2">
                                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('renewal')} {t('endDate')} :
                                </div>
                                <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  2026-05-03
                                </div>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent
                              side="top"
                              align="center"
                              className="w-[338px] 3xl:w-[17.604vw] bg-background"
                            >
                              <p className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] text-left">
                                {/* You can coterminate your subscription with an existing non-trial CSP
                                NCE subscription or align the end date with the calendar month by
                                choosing an appropriate end date depending on the term duration. */}
                                {t(
                                  'youCanCoterminateYourSubscriptionWithAnExistingNonTrialCspNceSubscriptionOrAlignTheEndDateWithTheCalendarMonthByChoosingAnAppropriateEndDateDependingOnTheTermDuration'
                                )}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <div className="flex items-center gap-2 text-InterfaceTextprimary">
                          <i className="cloud-canlendar text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          <div className="text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                            {t('endDateAlignment')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              <div>
                <div className="flex flex-col gap-1.5 mb-[4px] lg:mb-[4px] xl:mb-[4px] 2xl:mb-[4px] 3xl:mb-[0.208vw]">
                  <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                    {t('comment')}
                  </Label>
                  <Textarea
                    cols={4}
                    placeholder=""
                    className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none focus:border-InterfaceStrokehard focus:ring-0 text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]`}
                  />
                </div>
              </div>
            </div>
          </div>
          <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft">
            <Button
              type="button"
              className="cancelbtn py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
              variant="outline"
              size="sm"
              onClick={() => onClose()}
            >
              <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              {t('cancel')}
            </Button>
            <Button
              onClick={() => {
                setRenewalPopupOpen(true);
                onClose();
              }}
              type="submit"
              className="newGreenBtn text-interfacesurfacecomponent rounded-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              <i className="cloud-fillcircletick"></i>
              {t('submit')}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      <RenewalSubmitedPopup open={RenewalPopupOpen} onClose={() => setRenewalPopupOpen(false)} />
    </div>
  );
}
