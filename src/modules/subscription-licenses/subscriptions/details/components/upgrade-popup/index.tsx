'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  Button,
  Input,
  Label,
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  Checkbox,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';
import { Roboto } from 'next/font/google';
import { SuccessPopup } from '../success-popup';
import { cn } from '@/lib/utils/util';
import { PreliminaryValidationsView } from '@/modules/subscription-licenses/renewal-management/approved/components/accept-renew-popup/preliminary-validations-view';
import { AvailableCreditView } from '@/modules/subscription-licenses/renewal-management/approved/components/accept-renew-popup/available-credit-view';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface UpgradePopupProps extends CommanSheetProps {
  isDecrease?: boolean;
}

export default function UpgradePopup({ open, onClose, isDecrease }: UpgradePopupProps) {
  const t = useTranslations();

  const [BillFrequency, setBillFrequency] = React.useState<string>('');
  const [Term, setTerm] = React.useState<string>('');
  const [DestinationSubscription, setDestinationSubscription] = React.useState<string>('');
  const [subscription, setSubscription] = React.useState<string>('usd');
  const [successPopupOpen, setSuccessPopupOpen] = React.useState(false);
  const inputClasses =
    'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none focus:border-InterfaceStrokehard focus:ring-0 text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]';

  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]`}
        >
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-[600] leading-[140%]">
                {t('softwareUpgradeRequest')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="bg-InterfaceSurfacecomponent overflow-auto h-full p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <div className="space-y-[20px] lg:space-y-[20px] xl:space-y-[20px] 2xl:space-y-[20px] 3xl:space-y-[1.042vw]">
              <div className="flex flex-col gap-1.5 mb-[4px] lg:mb-[4px] xl:mb-[4px] 2xl:mb-[4px] 3xl:mb-[0.208vw]">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  {t('chooseAnEligibleUpgradeForYourSubscription')}
                </Label>
                <Select value={subscription} onValueChange={setSubscription}>
                  <SelectTrigger
                    className={cn(
                      inputClasses,
                      subscription
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Sector" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      <SelectItem value="Kuwait">
                        Microsoft 365 Business Basic | cfq7ttc0lh18:0001
                      </SelectItem>
                      <SelectItem value="India">
                        Microsoft 365 Business Basic | cfq7ttc0lh18:0001
                      </SelectItem>
                      <SelectItem value="MEA">
                        Microsoft 365 Business Basic | cfq7ttc0lh18:0001
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col gap-1.5 mb-[4px] lg:mb-[4px] xl:mb-[4px] 2xl:mb-[4px] 3xl:mb-[0.208vw]">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  {t('quantity')}
                </Label>
                <Input placeholder="" className={`${inputClasses}`} />
              </div>
              <div className="flex flex-col gap-1.5 mb-[4px] lg:mb-[4px] xl:mb-[4px] 2xl:mb-[4px] 3xl:mb-[0.208vw]">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  {t('destinationSubscription')}
                </Label>
                <Select value={DestinationSubscription} onValueChange={setDestinationSubscription}>
                  <SelectTrigger
                    className={cn(
                      inputClasses,
                      DestinationSubscription
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      <SelectItem value="Kuwait">Create New Subscription</SelectItem>
                      <SelectItem value="India">Create New Subscription</SelectItem>
                      <SelectItem value="MEA">Create New Subscription</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col gap-1.5 mb-[4px] lg:mb-[4px] xl:mb-[4px] 2xl:mb-[4px] 3xl:mb-[0.208vw]">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  {t('term')}
                </Label>
                <Select value={Term} onValueChange={setTerm}>
                  <SelectTrigger
                    className={cn(
                      inputClasses,
                      Term
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      <SelectItem value="Kuwait">1 Year</SelectItem>
                      <SelectItem value="India">1 Year</SelectItem>
                      <SelectItem value="MEA">1 Year</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col gap-1.5 mb-[4px] lg:mb-[4px] xl:mb-[4px] 2xl:mb-[4px] 3xl:mb-[0.208vw]">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  {t('billFrequency')}
                </Label>
                <Select value={BillFrequency} onValueChange={setBillFrequency}>
                  <SelectTrigger
                    className={cn(
                      inputClasses,
                      BillFrequency
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      <SelectItem value="Kuwait">Monthly</SelectItem>
                      <SelectItem value="India">Yearly</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-start gap-2 py-1">
                <Checkbox id="estimated" className="custcheck mt-1" />
                <label
                  htmlFor="estimated"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('selectTheCheckboxToViewTheEstimatedCharges')}
                </label>
              </div>
              <div className="flex items-start gap-2 py-1">
                <Checkbox id="confirm" className="custcheck mt-1" />
                <label
                  htmlFor="confirm"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('pleaseNoteThatTheSubscriptionUpgradeCannotBeReversedOnceRequested')}
                  {t('confirmToProceed')}
                </label>
              </div>
              <div>
                {/* <div className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
                  Validation
                </div> */}
                {/* <div className="grid grid-cols-2 gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="bg-InterfaceSurfacecomponentmuted px-[16px] lg:px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] lg:py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] text-center">
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[2px] 3xl:mb-[0.104vw] ">
                      Preliminary Validations
                    </div>
                    <div className="flex items-center justify-center gap-2 text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%] px-[14px] 3xl:px-[0.833vw] py-[8px] 3xl:py-[0.417vw]">
                      <i className="cloud-fillcircletick text-BrandHighlight500"></i>
                      <span className="text-BrandSupport2500">Pass</span>
                    </div>
                  </div>
                  <div className="bg-InterfaceSurfacecomponentmuted px-[16px] lg:px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] lg:py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] text-center">
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[2px] 3xl:mb-[0.104vw] ">
                      Available Credit
                    </div>
                    <div className="flex items-center justify-center gap-2 text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%] px-[14px] 3xl:px-[0.833vw] py-[8px] 3xl:py-[0.417vw]">
                      <i className="cloud-fillcircletick text-BrandHighlight500"></i>
                      <span className="text-BrandSupport2500">Pass</span>
                    </div>
                  </div>
                </div> */}

                <div>
                  <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] flex items-center text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                    {t('validation')}
                  </div>
                  <div className="grid grid-cols-2 gap-[8px] 3xl:gap-[0.417vw]">
                    <PreliminaryValidationsView />

                    <AvailableCreditView />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft">
            <Button
              onClick={() => {
                setSuccessPopupOpen(true);
                onClose();
              }}
              type="submit"
              className="newGreenBtn text-interfacesurfacecomponent rounded-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              <i className="cloud-circletick"></i>
              {t('update')}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
      <SuccessPopup
        open={successPopupOpen}
        onClose={() => setSuccessPopupOpen(false)}
        message={`${isDecrease ? 'Subscription Update Request has been submitted. The changes will take 15 minutes to reflect' : t('subscriptionUpdateRequestHasBeenSubmitted')}`}
      />
    </div>
  );
}
