'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';
import { Roboto } from 'next/font/google';
import { SuspendedTerminatePopup } from '../suspended-terminate-popup';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface TerminatePopupProps extends CommanSheetProps {
  isDecrease?: boolean;
}

export default function TerminatePopup({ open, onClose }: TerminatePopupProps) {
  const t = useTranslations();

  const [SuspendedTerminatePopupOpen, setSuspendedTerminatePopupOpen] = React.useState(false);
  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]`}
        >
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-[600] leading-[140%]">
                {t('subscriptionTerminate')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="bg-InterfaceSurfacecomponent overflow-auto h-full p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <div className="space-y-[14px] lg:space-y-[14px] xl:space-y-[14px] 2xl:space-y-[14px] 3xl:space-y-[0.729vw]">
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('endCustomer')}
                </div>
                <div
                  title="Azure Plan Usage AE3"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  Azure Plan Usage AE3
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('brandName')}
                </div>
                <div
                  title="CSP NCE"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  CSP NCE
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('productName')}
                </div>
                <div
                  title="Microsoft 365 F1"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  Microsoft 365 F1
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('subscriptionId')}
                </div>
                <div
                  title="8259a251-f511-4dfd-dab8eff77fe8a45a"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  8259a251-f511-4dfd-dab8eff77fe8a45a
                </div>
              </div>
              <div className="grid grid-cols-2 gap-[20px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('segment')}
                  </div>
                  <div
                    title="Commercial"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    Commercial
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('quantity')}
                  </div>
                  <div
                    title="1"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    1
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('startDate')}
                  </div>
                  <div
                    title="02-03-2025"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    02-03-2025
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('endDate2')}
                  </div>
                  <div
                    title="08-01-2026"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    08-01-2026
                  </div>
                </div>
              </div>
            </div>
          </div>
          <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft">
            <Button
              onClick={() => {
                setSuspendedTerminatePopupOpen(true);
                onClose();
              }}
              type="submit"
              className="bg-Interfacefeedbackerror700 text-interfacesurfacecomponent rounded-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              <i className="cloud-save"></i>
              {t('terminate')}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      <SuspendedTerminatePopup
        open={SuspendedTerminatePopupOpen}
        terminate={true}
        onClose={() => setSuspendedTerminatePopupOpen(false)}
        title={'Are you sure you want to terminate this subscription?'}
        message={`Note: Terminated subscriptions cannot be reactivated. This Subscription Termination is subject to the Vendor's cancellation policy. You will be notified through email once your cancellation request is accepted by the Vendor.`}
      />
    </div>
  );
}
