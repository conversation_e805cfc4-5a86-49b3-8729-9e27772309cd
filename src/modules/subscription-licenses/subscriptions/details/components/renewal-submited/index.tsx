'use client';
import { CommanSheetProps } from '@/types';
import { Sheet, SheetContent, SheetTitle } from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function RenewalSubmitedPopup({ open, onClose }: CommanSheetProps) {
  const t = useTranslations();

  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetTitle></SheetTitle>
        <SheetContent
          className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw]  p-[0px] hideclose`}
          side={'right'}
        >
          <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[48px] lg:mx-[48px] xl:mx-[48px] 2xl:mx-[48px] 3xl:mx-[2.5vw]">
            <div className="flex flex-col justify-center items-center gap-[35px] xl:gap-[35px] 3xl:gap-[1.863vw] mt-2">
              <i className="cloud-tickcircle text-BrandSupport1pure text-[58px]"></i>
              <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="mx-[30px] text-InterfaceTexttitle text-[28px] lg:text-[28px] xl:text-[28px] 2xl:text-[28px] 3xl:text-[1.85vw] font-normal  text-center">
                  {t('renevalSuccessMsg')}
                </div>
              </div>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
              <Link onClick={onClose} href="/subscription-licenses/subscriptions/details">
                <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-[400] rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                  <i className="cloud-back text-[13px]"></i>
                  {t('goToExpiringList')}
                </div>
              </Link>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
