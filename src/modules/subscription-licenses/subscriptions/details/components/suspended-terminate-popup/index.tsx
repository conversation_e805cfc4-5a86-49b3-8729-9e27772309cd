'use client';
import { CommanSheetProps } from '@/types';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { Sheet, SheetContent, SheetTitle } from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SuspendedTerminatePopupProps extends CommanSheetProps {
  message?: string;
  title?: string;
  terminate?: boolean;
}

export function SuspendedTerminatePopup({
  open,
  onClose,
  message,
  title,
  terminate,
}: SuspendedTerminatePopupProps) {
  const t = useTranslations();

  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetTitle></SheetTitle>
        <SheetContent
          className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw]  p-[0px] hideclose`}
          side={'right'}
        >
          <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[48px] lg:mx-[48px] xl:mx-[48px] 2xl:mx-[48px] 3xl:mx-[2.5vw]">
            <div className="flex flex-col justify-center items-center gap-[35px] xl:gap-[35px] 3xl:gap-[1.863vw] mt-2">
              <i className="cloud-dangerlight text-BrandSupport1pure text-[58px]"></i>
              <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="mx-[30px] text-InterfaceTexttitle text-[28px] lg:text-[28px] xl:text-[28px] 2xl:text-[28px] 3xl:text-[1.85vw] font-normal  text-center">
                  {title ? title : `Are you sure you want to suspend this subscription?`}
                </div>
                <div className="text-InterfaceTextsubtitle text-[20px] lg:text-[20px] xl:text-[20px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal text-center">
                  {message}
                </div>
              </div>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
              <Button
                onClick={() => {
                  onClose();
                }}
                className="text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw] text-background bg-Interfacefeedbackerror700 hover:bg-Interfacefeedbackerror700 font-[400] rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[11px] md:py-[11px] xl:py-[11px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[240px] 3xl:w-[12.5vw]"
              >
                <i className="cloud-fillcircletick text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw]"></i>
                {terminate === true ? t('yesTerminate') : t('yesSuspend')}
              </Button>
              <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
                <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">or</p>
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              </div>
              <Button
                onClick={() => {
                  onClose();
                }}
                className="text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw] text-InterfaceTextdefault cancelbtn font-[400] rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[11px] md:py-[11px] xl:py-[11px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[240px] 3xl:w-[12.5vw]"
              >
                <i className="cloud-closecircle text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw]"></i>
                {t('noLater')}
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
