import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import AdditionalInformation from '../additional-information';
import CommitmentConfirmationPopup from '../commitment-confirmation-popup';
import SubscriptionIncreaseDecreasePopup from '../subscription-increase-decrease-popup';
import { SuspendedTerminatePopup } from '../suspended-terminate-popup';
import TerminatePopup from '../terminate-popup';
import UpgradePopup from '../upgrade-popup';
import RenewalPopup from '../renewal-popup';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

export default function LifeCycleRenewal() {
  const t = useTranslations();

  const [renewalPopupOpen, setRenewalPopupOpen] = React.useState(false);
  const [upgradePopupOpen, setUpgradePopupOpen] = React.useState(false);
  const [SubscriptionDecreasePopupShow, setSubscriptionDecreasePopupShow] = React.useState(false);
  const [isCommitmentConfirmationPopupOpen, setIsCommitmentConfirmationPopupOpen] =
    React.useState(false);
  const [isSuspendedPopupOpen, setIsSuspendedPopupOpen] = React.useState(false);
  const [terminatePopupOpen, setTerminatePopupOpen] = React.useState(false);
  const [tabShow, setShow] = React.useState(0);
  return (
    <>
      <div className="inline-flex items-center">
        <Button
          type="button"
          onClick={() => setShow(0)}
          className={`${tabShow === 0 ? 'text-interfacetextinverse border-BrandNeutral950 bg-BrandNeutral950' : 'text-InterfaceTextdefault bg-BrandNeutral50 border-InterfaceStrokesoft'}  border relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%]`}
          size="sm"
        >
          <i className="cloud-medalstar text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
          {t('lifeCycleManagement')}
        </Button>
        <Button
          type="button"
          onClick={() => setShow(1)}
          className={`${tabShow === 1 ? 'text-interfacetextinverse border-BrandNeutral950 bg-BrandNeutral950' : 'text-InterfaceTextdefault bg-BrandNeutral50 border-InterfaceStrokesoft'} border relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%]`}
        >
          <i className="cloud-refresh-square-2 text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[20px] 3xl:text-[0.729vw]"></i>
          {t('renewal')}
        </Button>
      </div>

      <div className="mt-[16px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
        {tabShow === 0 && (
          <>
            <div className="grid grid-cols-3 gap-[20px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw] border-b border-b-InterfaceStrokesoft pb-[18px] 3xl:pb-[0.938vw]">
              <div className="bg-InterfaceSurfacecomponent py-[10px] 3xl:py-[0.521vw]">
                <div className="mb-[10px] 3xl:mb-[0.521vw]">
                  <Button
                    onClick={() => setIsCommitmentConfirmationPopupOpen(true)}
                    type="button"
                    className="graygradientbtn border border-InterfaceStrokesoft px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-[500] leading-[140%]"
                  >
                    <Image
                      src={'/images/svg/add-circle.svg'}
                      width={17}
                      height={17}
                      alt={t('increase')}
                    />
                    {t('increase')}
                  </Button>
                </div>
                <div className="text-InterfaceTextsubtitle text-[13px] 2xl:text-[13px] 3xl:text-[0.729w] font-[400] leading-[140%]">
                  {/* Add more users, storage, features, or other resources under the current
                  subscription. Often involves additional cost. */}
                  {t(
                    'addMoreUsersStorageFeaturesOrOtherResourcesUnderTheCurrentSubscriptionOftenInvolvesAdditionalCost'
                  )}
                </div>
              </div>

              <div className="bg-InterfaceSurfacecomponent py-[10px] 3xl:py-[0.521vw]">
                <div className="mb-[10px] 3xl:mb-[0.521vw]">
                  <Button
                    onClick={() => setIsSuspendedPopupOpen(true)}
                    type="button"
                    className="graygradientbtn border border-InterfaceStrokesoft px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-[500] leading-[140%]"
                  >
                    <i className="cloud-saveminus text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    {t('suspend')}
                  </Button>
                </div>
                <div className="text-InterfaceTextsubtitle text-[13px] 2xl:text-[13px] 3xl:text-[0.729w] font-[400] leading-[140%]">
                  {t(
                    'temporarilyDisableTheSubscriptionWithoutTerminatingItAccessIsBlockedButDataIsRetainedOftenUsedForSeasonalNeeds'
                  )}
                </div>
              </div>

              <div className="bg-InterfaceSurfacecomponent py-[10px] 3xl:py-[0.521vw]">
                <div className="mb-[10px] 3xl:mb-[0.521vw]">
                  <Button
                    onClick={() => setSubscriptionDecreasePopupShow(true)}
                    type="button"
                    className="graygradientbtn border border-InterfaceStrokesoft px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-[500] leading-[140%]"
                  >
                    <i className="cloud-subcircle text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    {t('decrease')}
                  </Button>
                </div>
                <div className="text-InterfaceTextsubtitle text-[13px] 2xl:text-[13px] 3xl:text-[0.729w] font-[400] leading-[140%]">
                  {/* Reduce the number of users, features, or resource usage. May lower costs, but
                  often restricted to renewal periods. */}
                  {t(
                    'reduceTheNumberOfUsersFeaturesOrResourceUsageMayLowerCostsButOftenRestrictedToRenewalPeriods'
                  )}
                </div>
              </div>

              <div className="bg-InterfaceSurfacecomponent py-[10px] 3xl:py-[0.521vw]">
                <div className="mb-[10px] 3xl:mb-[0.521vw]">
                  <Button
                    onClick={() => setUpgradePopupOpen(true)}
                    type="button"
                    className="graygradientbtn border border-InterfaceStrokesoft px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-[500] leading-[140%]"
                  >
                    <i className="cloud-export text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    {t('upgrade')}
                  </Button>
                </div>
                <div className="text-InterfaceTextsubtitle text-[13px] 2xl:text-[13px] 3xl:text-[0.729w] font-[400] leading-[140%]">
                  {t('upgradePlanNote')}
                </div>
              </div>
              <div className="bg-InterfaceSurfacecomponent py-[10px] 3xl:py-[0.521vw]">
                <div className="mb-[10px] 3xl:mb-[0.521vw]">
                  <Button
                    onClick={() => setTerminatePopupOpen(true)}
                    type="button"
                    className="graygradientbtn border border-InterfaceStrokesoft px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-[500] leading-[140%]"
                  >
                    <i className="cloud-saveremove text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    {t('terminate')}
                  </Button>
                </div>
                <div className="text-InterfaceTextsubtitle text-[13px] 2xl:text-[13px] 3xl:text-[0.729w] font-[400] leading-[140%]">
                  {t('endSubscriptionNotice')}
                </div>
              </div>
              <div className="bg-InterfaceSurfacecomponent py-[10px] 3xl:py-[0.521vw]">
                <div className="mb-[10px] 3xl:mb-[0.521vw]">
                  <Button
                    type="button"
                    className="graygradientbtn border border-InterfaceStrokesoft px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-[500] leading-[140%]"
                  >
                    <i className="cloud-repeat text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    {t('change')}
                  </Button>
                </div>
                <div className="text-InterfaceTextsubtitle text-[13px] 2xl:text-[13px] 3xl:text-[0.729w] font-[400] leading-[140%]">
                  {/* Change product or term length or bill frequency. */}
                  {t('changeProductOrTermLengthOrBillFrequency')}
                </div>
              </div>
            </div>
            <div className="mt-[24px] xl:mt-[24px] 2xl:mt-[24px] 3xl:mt-[1.25vw]">
              <AdditionalInformation />
            </div>
          </>
        )}
        {tabShow === 1 && (
          <>
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] mb-[18px] 3xl:mb-[1.042vw]">
              <div className="text-InterfaceTextsubtitle text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                {t('renewalStatus')}
              </div>
              <div className="inline-block py-[4px] xl:py-[4px] 3xl:py-[0.208vw] px-[10px] rounded-[2px] text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-semibold leading-[140%] border border-Interfacefeedbackerror400 bg-Interfacefeedbackerror100 text-Interfacefeedbackerror700">
                Expiring
              </div>
            </div>
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] mb-[18px] 3xl:mb-[1.042vw]">
              <Button
                onClick={() => setRenewalPopupOpen(true)}
                type="button"
                className={`text-InterfaceTextdefault graygradientbtn border-InterfaceStrokesoft border relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] flex items-center`}
              >
                <i className="cloud-circletick"></i>
                {t('renew')}
              </Button>
              <Button
                type="button"
                className={`text-InterfaceTextdefault graygradientbtn border-InterfaceStrokesoft border relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] leading-[140%] flex items-center`}
              >
                <i className="cloud-closecircle"></i>
                {t('doNotRenew')}
              </Button>
            </div>
            <div className="text-InterfaceTexttitle text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
              {t('details')}
            </div>
            <div className="grid grid-cols-4 gap-[20px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('productName')}
                </div>
                <div
                  title="Dynamics 365 Business Central Premium"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  Dynamics 365 Business Central Premium
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('quantity')}
                </div>
                <div
                  title="6"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  6
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('contractValue')}
                </div>
                <div
                  title="USD 2.87"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  USD 2.87
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('dueDays')}
                </div>
                <div
                  title="20"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  20
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      <CommitmentConfirmationPopup
        open={isCommitmentConfirmationPopupOpen}
        onClose={() => setIsCommitmentConfirmationPopupOpen(false)}
      />

      <SubscriptionIncreaseDecreasePopup
        open={SubscriptionDecreasePopupShow}
        onClose={() => setSubscriptionDecreasePopupShow(false)}
        isDecrease={true}
      />
      <SuspendedTerminatePopup
        open={isSuspendedPopupOpen}
        onClose={() => setIsSuspendedPopupOpen(false)}
        message={
          'Note: Suspending a subscription immediately causes disruption of service to customer, but does not stop your billing. You will continue to be charged for this subscription.'
        }
      />
      <TerminatePopup open={terminatePopupOpen} onClose={() => setTerminatePopupOpen(false)} />
      <UpgradePopup open={upgradePopupOpen} onClose={() => setUpgradePopupOpen(false)} />
      <RenewalPopup open={renewalPopupOpen} onClose={() => setRenewalPopupOpen(false)} />
    </>
  );
}
