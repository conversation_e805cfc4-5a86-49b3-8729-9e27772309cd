'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Input,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';
import { Roboto } from 'next/font/google';
import { SuccessPopup } from '../success-popup';
import { PreliminaryValidationsView } from '@/modules/subscription-licenses/renewal-management/approved/components/accept-renew-popup/preliminary-validations-view';
import { AvailableCreditView } from '@/modules/subscription-licenses/renewal-management/approved/components/accept-renew-popup/available-credit-view';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SubscriptionIncreaseDecreasePopupProps extends CommanSheetProps {
  isDecrease?: boolean;
}

export default function SubscriptionIncreaseDecreasePopup({
  open,
  onClose,
  isDecrease,
}: SubscriptionIncreaseDecreasePopupProps) {
  const t = useTranslations();

  const [successPopupOpen, setSuccessPopupOpen] = React.useState(false);
  const [showvalidation, setShowvalidation] = React.useState(false);
  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 2xl:max-w-[620px] 3xl:max-w-[31.25vw] p-[0px]`}
        >
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-[600] leading-[140%]">
                Subscription - {isDecrease ? t('decrease') : t('increase')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="bg-InterfaceSurfacecomponent overflow-auto h-full p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <div className="space-y-[14px] lg:space-y-[14px] xl:space-y-[14px] 2xl:space-y-[14px] 3xl:space-y-[0.729vw]">
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('endCustomer')}
                </div>
                <div
                  title="Azure Plan Usage AE3"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  Azure Plan Usage AE3
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('brandName')}
                </div>
                <div
                  title="CSP NCE"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  CSP NCE
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('productName')}
                </div>
                <div
                  title="Microsoft 365 F1"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  Microsoft 365 F1
                </div>
              </div>
              <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                  {t('subscriptionId')}
                </div>
                <div
                  title="8259a251-f511-4dfd-dab8eff77fe8a45a"
                  className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                >
                  8259a251-f511-4dfd-dab8eff77fe8a45a
                </div>
              </div>
              <div className="grid grid-cols-2 gap-[20px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('segment')}
                  </div>
                  <div
                    title="Commercial"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    Commercial
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('quantity')}
                  </div>
                  <div
                    title="1"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    1
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('startDate')}
                  </div>
                  <div
                    title="02-03-2025"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    02-03-2025
                  </div>
                </div>
                <div className="py-[10px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokesoft">
                  <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                    {t('endDate2')}
                  </div>
                  <div
                    title="08-01-2026"
                    className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%] truncate"
                  >
                    08-01-2026
                  </div>
                </div>
              </div>
              <div>
                <div className="flex flex-col gap-1.5 mb-[4px] lg:mb-[4px] xl:mb-[4px] 2xl:mb-[4px] 3xl:mb-[0.208vw]">
                  <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                    {isDecrease ? t('decreaseBy') : t('increaseBy')}
                  </Label>
                  <Input
                    placeholder=""
                    className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none focus:border-InterfaceStrokehard focus:ring-0 text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]`}
                    onChange={() => setShowvalidation(true)}
                  />
                </div>
                <div
                  title="New Subscription Qty will be"
                  className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] truncate"
                >
                  {t('newSubscriptionQtyWillBe6')}{' '}
                  <span className="text-InterfaceTexttitle">6</span>
                </div>
                <div
                  title="The estimated prorate cost is"
                  className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] truncate"
                >
                  The estimated prorate cost is{' '}
                  <span className="text-InterfaceTexttitle">
                    {isDecrease ? '-3.49 USD' : '2,240.25 USD'}{' '}
                  </span>
                </div>
              </div>
              <div>
                {/* <div className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
                  Validation
                </div> */}
                {/* <div className="grid grid-cols-2 gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="bg-InterfaceSurfacecomponentmuted px-[16px] lg:px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] lg:py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] text-center">
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[2px] 3xl:mb-[0.104vw] ">
                      Preliminary Validations
                    </div>
                    <div className="flex items-center justify-center gap-2 text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%] px-[14px] 3xl:px-[0.833vw] py-[8px] 3xl:py-[0.417vw]">
                      <i className="cloud-fillcircletick text-BrandHighlight500"></i>
                      <span className="text-BrandSupport2500">Pass</span>
                    </div>
                  </div>
                  <div className="bg-InterfaceSurfacecomponentmuted px-[16px] lg:px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] lg:py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] text-center">
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[2px] 3xl:mb-[0.104vw] ">
                      Available Credit
                    </div>
                    <div className="flex items-center justify-center gap-2 text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%] px-[14px] 3xl:px-[0.833vw] py-[8px] 3xl:py-[0.417vw]">
                      <i className="cloud-fillcircletick text-BrandHighlight500"></i>
                      <span className="text-BrandSupport2500">Pass</span>
                    </div>
                  </div>
                </div> */}
                {showvalidation && (
                  <div>
                    <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] flex items-center text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                      {t('validation')}
                    </div>
                    <div className="grid grid-cols-2 gap-[8px] 3xl:gap-[0.417vw]">
                      <PreliminaryValidationsView />

                      <AvailableCreditView />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft">
            <Button
              onClick={() => {
                setSuccessPopupOpen(true);
                onClose();
              }}
              type="submit"
              className="newGreenBtn text-interfacesurfacecomponent rounded-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              <i className="cloud-circletick"></i>
              {t('update')}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
      <SuccessPopup
        open={successPopupOpen}
        onClose={() => setSuccessPopupOpen(false)}
        message={`${isDecrease ? 'Subscription Update Request has been submitted. The changes will take 15 minutes to reflect' : t('subscriptionUpdateRequestHasBeenSubmitted')}`}
      />
    </div>
  );
}
