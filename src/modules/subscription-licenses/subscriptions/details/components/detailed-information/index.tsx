import React from 'react';
import { useTranslations } from 'next-intl';

export default function DetailedInformation() {
  const t = useTranslations();

  return (
    <>
      <div className="text-InterfaceTexttitle text-[14px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[100%] mb-[14px] lg:mb-[16px] xl:mb-[16px] 2xl:mb-[16px] 3xl:mb-[0.833vw]">
        {t('detailedInformation')}
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 1366xl:grid-cols-4 1440xl:grid-cols-4 gap-[20px] xl:gap-[20px] 2xl:gap-[22px] 3xl:gap-[1.667vw]">
        <div className="p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-semibold leading-[100%] mb-[16px] 3xl:mb-[0.833vw]">
            {t('productDetails2')} :
          </div>
          <div className="space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('skuId')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                C03atjo
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('brand')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                Amazon
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('brandCategory')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                Amazon Web Services
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('productName')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                AWS
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('quantity')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                1
              </span>
            </div>
          </div>
        </div>

        <div className="p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-semibold leading-[100%] mb-[16px] 3xl:mb-[0.833vw]">
            {t('customerDetails')}
          </div>
          <div className="space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('endCustomer')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                Qatar Air
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('segment')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                Education
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('segment')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                thefirstgroup.onmicrosoft.com
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('organizationTenant')} ID :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                f6e191f8-5aed-4670-86d4-616277ebe4c0
              </span>
            </div>
          </div>
        </div>

        <div className="p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-semibold leading-[100%] mb-[16px] 3xl:mb-[0.833vw]">
            {t('term')} & {t('billType')} :
          </div>
          <div className="space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('term')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                Annual
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('billType')}:{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                Monthly
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('creationDate')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                03/05/2024
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('commitmentStart')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                04/05/2024
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('commitmentEnd')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                04/05/2026
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('cancellationUntil')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                05/05/2026
              </span>
            </div>
          </div>
        </div>

        <div className="p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-semibold leading-[100%] mb-[16px] 3xl:mb-[0.833vw]">
            {t('purchaseInfo')}
          </div>
          <div className="space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('quoteID')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                13144
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('cartID')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                123456
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('couponCode')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                423dydo274
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('promotionId')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                35252452428
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('orderId')}:{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                12131231
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('listPrice')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                USD 500.00
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('discount')} :{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                10%
              </span>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
              {t('unitPrice')}:{' '}
              <span className="font-[400] text-[14px] xl:text-[12px] 1336xl:text-[12px] 2xLtext-[14px] 3xl:text-[0.729vw]">
                USD 450.00
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
