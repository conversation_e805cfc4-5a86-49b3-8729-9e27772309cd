'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON>ton,
  Checkbox,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';
import { Roboto } from 'next/font/google';
import SubscriptionIncreaseDecreasePopup from '../subscription-increase-decrease-popup';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function CommitmentConfirmationPopup({ open, onClose }: CommanSheetProps) {
  const t = useTranslations();

  const [SubscriptionIncreaseDecreasePopupShow, setSubscriptionIncreaseDecreasePopupShow] =
    React.useState(false);
  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]`}
        >
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-[600] leading-[140%]">
                {t('endCustomerPurchaseCommitmentConfirmation')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="bg-InterfaceSurfacecomponent overflow-auto h-full p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <div className="border-b border-b-InterfaceStrokedefault pb-[24px] lg:pb-[24px] xl:pb-[24px] 2xl:pb-[24px] 3xl:pb-[1.25vw]">
              <div className="text-InterfaceTextdefault text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] mb-[16px] lg:mb-[16px] xl:mb-[16px] 2xl:mb-[16px] 3xl:mb-[0.833vw]">
                {t('purchaseCommitmentObligations')}
              </div>

              <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%] mb-[16px] lg:mb-[16px] xl:mb-[16px] 2xl:mb-[16px] 3xl:mb-[0.833vw]">
                {t('orderPrerequisiteNotice')}
              </div>

              <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                {/* By submitting an order or order revision, Your Company represents that any Customer
                Purchase Commitment provided is complete and accurate in all respects and agrees to
                pay Redington for all orders it submits for Products */}
                {t(
                  'bysubmittinganorderororderrevisionYourCompanyrepresentsthatanyCustomerPurchaseCommitmentprovidediscompleteandaccurateinallrespectsandagreestopayRedingtonforallordersitsubmitsforProducts'
                )}
              </div>
            </div>
            <div className="pt-[24px] lg:pt-[24px] xl:pt-[24px] 2xl:pt-[24px] 3xl:pt-[1.042vw]">
              <div className="flex items-start gap-2 py-1">
                <Checkbox id="confirm" className="custcheck mt-1" />
                <label
                  htmlFor="confirm"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                >
                  {t('purchaseCommitmentCheck')}
                </label>
              </div>
            </div>
          </div>
          <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft">
            <Button
              onClick={() => {
                setSubscriptionIncreaseDecreasePopupShow(true);
                onClose();
              }}
              type="submit"
              className="newGreenBtn text-interfacesurfacecomponent rounded-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              <i className="cloud-circletick"></i>
              {t('accept')}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
      <SubscriptionIncreaseDecreasePopup
        open={SubscriptionIncreaseDecreasePopupShow}
        onClose={() => setSubscriptionIncreaseDecreasePopupShow(false)}
      />
    </div>
  );
}
