import React from 'react';
import { DataTable } from '@/components/common/ui/data-table';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { useTranslations } from 'next-intl';

export default function AdditionalInformation() {
  const t = useTranslations();

  const InvoicesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'subscriptionname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('subscriptionName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('subscriptionname')}</div>,
      minSize: 500,
    },
    {
      accessorKey: 'subscriptionid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('subscriptionId')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('subscriptionid')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      // cell: ({ row }) => <div className="">{row.getValue('status')}</div>,
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Active: {
            colorClass: 'bg-BrandPrimary100 border border-BrandHighlight300 text-BrandPrimary800',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[4px] xl:py-[4px] 3xl:py-[0.208vw] px-[10px] rounded-[2px] text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-semibold leading-[140%] ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 100,
      maxSize: 100,
    },
  ];

  type User = {
    subscriptionname: string;
    subscriptionid: string;
    status: string;
  };

  const Invoicesdata = [
    {
      subscriptionname: 'Azure Pln Usage AE3 Azure',
      subscriptionid: '1e0f391c-1d47-482a-d123-6a18436160e9HE',
      status: 'Active',
    },
    {
      subscriptionname: 'Test Azure Fraud 2',
      subscriptionid: '51cfe6eb-a74a-40ac-a5fc-c575134c0339',
      status: 'Active',
    },
  ];

  return (
    <>
      <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] font-semibold leading-[140%] mb-[14px] lg:mb-[16px] xl:mb-[16px] 2xl:mb-[16px] 3xl:mb-[0.833vw]">
        {t('additionalInformation')}
      </div>
      <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-semibold leading-[140%] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[16px] xl:py-[16px] 2xl:py-[18px] 3xl:py-[0.938vw] border border-InterfaceStrokesoft">
        {t('azureEntitlements')}
      </div>
      <div className="overflow-x-auto">
        <DataTable data={Invoicesdata} columns={InvoicesColumns} withCheckbox={false} />
      </div>
    </>
  );
}
