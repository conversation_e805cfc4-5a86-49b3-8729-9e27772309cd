'use client';
import React from 'react';
import Link from 'next/link';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import DetailedInformation from '../components/detailed-information';
import LifeCycleRenewal from '../components/life-cycle-renewal';
import { useTranslations } from 'next-intl';

export default function SubscriptionsDetailsTemplate() {
  const t = useTranslations();

  return (
    <div className="">
      <div className=" h-full">
        <div className="relative h-full px-[32px] py-[20px] 3xl:px-[1.667vw] 3xl:py-[1.042vw] bg-[#F6F7F8]">
          <div className="text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600] text-InterfaceTexttitle">
            ID:11123455_Amazon<span className="text-InterfaceTextsubtitle font-[400]"> / </span>
            <span className="text-interfacetexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[400]">
              {t('details')}
            </span>
          </div>

          <div className="flex gap-[2px] mt-[16px] xl:mt-[16px] 3xl:mt-[0.833vw]">
            <Link href="/subscription-licenses/subscriptions">
              <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
                <i className="cloud-back text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                {t('back')}
              </Button>
            </Link>
          </div>

          <div className="mt-[20px] xl:mt-[20px] 3xl:mt-[1.042vw]">
            <div className="bg-white border border-InterfaceStrokesoft p-[22px] xl:p-[25px] 2xl:p-[28x] 3xl:p-[1.458vw]">
              <DetailedInformation />
              <div className="mt-[20px] xl:mt-[20px] 2xl:mt-[25px] 3xl:mt-[1.667vw]">
                <LifeCycleRenewal />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
