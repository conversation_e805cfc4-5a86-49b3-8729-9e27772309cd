'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function EditSubscriptionDetailsPopup() {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <i className="cloud-folder text-InterfaceTextdefault cursor-pointer flex " title="Doc"></i>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[550px] xl:max-w-[550px] 3xl:max-w-[28.65vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('subscriptionDetail')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="flex flex-col space-y-[24px]">
                <div className=" space-y-1 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    {t('endCustomer')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    innovate Itd
                  </div>
                </div>
                <div className=" space-y-1 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    {t('productName')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Amazon Web Services
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]  space-y-3 mt-[20px]">
                <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] mt-[12px]">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    {t('subscriptionId')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    345678901256
                  </div>
                </div>
                <div className=" space-y-1 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    {t('quantity')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    1
                  </div>
                </div>
                <div className=" space-y-1 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    Order Status
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Failed
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="absolute top-[16px] right-[20px]">
            <div className="cursor-pointer ">
              <div
                className={`inline-block py-[2px] xl:py-[2px] 3xl:py-[0.1vw] px-[10px] rounded-[2px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold border bg-[rgba(255,181,165,0.3)] text-[#D42600] border-[rgba(212,38,0,0.4)]`}
              >
                <div>Failed</div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
