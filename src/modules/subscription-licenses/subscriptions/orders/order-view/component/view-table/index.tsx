import { DataTable } from '@/components/common/ui/data-table';
// import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
// import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import { Popover, PopoverTrigger } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import SubscriptionDetailsPopup from '../subscription-details-popup';
import EditSubscriptionDetailsPopup from '../edit-subscription-details-popup';
import DeletelineItemPopup from '../delete-item-popup';
import ResubmitLineItemPopup from '../resubmit-item-popup';
import { useTranslations } from 'next-intl';

export default function ViewTable() {
  const t = useTranslations();

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('productName')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Product Name"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: () => {
        return (
          <div className="flex flex-col">
            <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600]">
              Amazon Web Services
            </div>
            <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
              SKU ID:AWS-001
              <br />
              Brand: AWS <br />
              Segment:Commercial
              <br /> Term: 1 Year <br />
              Bill Type: Annual
              <br />
              Discount Coupon: 1122434 <br />
              promotion ID: #2233
            </div>
          </div>
        );
      },

      minSize: 400,
    },

    {
      accessorKey: 'qty',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('qty')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>

            {/* <ColumnHeaderFilter placeholder="QTY " onChange={(val) => column.setFilterValue(val)} /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('qty')}</div>,

      minSize: 100,
    },
    {
      accessorKey: 'listprice',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('listPrice')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="List Price"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('listprice')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'discount',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('discount')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Discount"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('discount')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'unitprice',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('unitPrice')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Unit Price"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('unitprice')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'subtotal',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('subTotal')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Subtotal"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('subtotal')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'vat',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('VAT')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="VAT %"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('vat')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'estimatedvat',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('estimatedVAT')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Estimated VAT"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('estimatedvat')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'netprice',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('estimatedVAT')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Net Price"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('netprice')}</div>,

      minSize: 400,
    },

    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        // const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {/* {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )} */}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Status"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Fulfilled: {
            colorClass: 'bg-BrandHighlight100 text-BrandHighlight800 border-BrandHighlight300',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800 border-gray-300',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[2px] xl:py-2px] 3xl:py-[0.1vw] px-[8px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold border  ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('doc')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <SubscriptionDetailsPopup />
              </PopoverTrigger>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <EditSubscriptionDetailsPopup />
              </PopoverTrigger>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <DeletelineItemPopup />
              </PopoverTrigger>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <ResubmitLineItemPopup />
              </PopoverTrigger>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    productname: string;
    qty: string;
    listprice: string;
    discount: string;
    unitprice: string;
    subtotal: string;
    vat: string;
    estimatedvat: string;
    netprice: string;
    status: string;
  };

  const registrationdata = [
    {
      productname: '',
      qty: '1',
      listprice: 'USD 1 ',
      discount: 'USD 0.00 ',
      unitprice: 'USD 1.00 ',
      subtotal: 'USD 1.00 ',
      vat: '0.00 ',
      estimatedvat: 'USD 0.00 ',
      netprice: 'USD 1.00 ',
      status: 'Fulfilled',
    },
    {
      productname: '',
      qty: '1',
      listprice: 'USD 1 ',
      discount: 'USD 0.00 ',
      unitprice: 'USD 1.00 ',
      subtotal: 'USD 1.00 ',
      vat: '0.00 ',
      estimatedvat: 'USD 0.00 ',
      netprice: 'USD 1.00 ',
      status: 'Fulfilled',
    },
  ];
  return (
    <>
      <div>
        <div className="flex justify-between items-center p-[12px] 3xl:p-[0.625vw]">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600] leading-[100%]">
            {t('itemsOrdered')}
          </div>
          <div className="flex items-center gap-[40px] 3xl:gap-[2.08vw]">
            <div className="flex items-center gap-[10px] 3xl:gap-[0.525vw]">
              <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[100%]">
                {' '}
                {t('lpoReference')}:
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                {' '}
                #11223344
              </div>
            </div>

            <div className="flex items-center gap-[10px] 3xl:gap-[0.525vw]">
              <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[100%]">
                {' '}
                {t('attachedDocument')} :{' '}
              </div>
              <div className="flex gap-[8px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                <Image
                  width={16}
                  height={16}
                  src="/images/document_img.svg"
                  className="h-[16px] xl:h-[16px] 2xl:h-[18px] 3xl:h-[0.938vw] w-[16px] xl:w-[16px] 2xl:w-[18px] 3xl:w-[0.938vw]"
                  alt="document_img"
                />
                Document 001.pdf
              </div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1  border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
          <div className="overflow-x-auto">
            <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
          </div>
        </div>
      </div>
    </>
  );
}
