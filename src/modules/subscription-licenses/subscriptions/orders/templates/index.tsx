// For the List of Orders page, a single unified page has been designed for both editing and viewing orders.
// This page currently serves as a base and needs to be integrated and customized further according to specific requirements.
'use client';
import * as React from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Orderstable from '../component/orders-table';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function SubscriptionsOrdersTemplate() {
  const t = useTranslations();

  return (
    <div className="py-[20px] xl:py-[16px] 3xl:py-[1.042vw] px-[32px] xl:px-[24px] 3xl:px-[1.667vw] w-full">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600] text-InterfaceTexttitle">
          ID:11123455_Amazon <span className="text-InterfaceTextsubtitle font-[400]"> / </span>
          <span className="text-interfacetexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[400]">
            {t('orders')}
          </span>
        </div>
      </div>
      <div className="space-y-[16px] xl:space-y-[16px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <Link href={'/subscription-licenses/subscriptions'}>
            <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault">
              <i className="cloud-back font-medium text-[16px] xl:text-[14px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
              {t('back')}
            </Button>
          </Link>
        </div>

        <div className="bg-interfacesurfacecomponent tbl-shadow">
          <Orderstable />
        </div>
      </div>
    </div>
  );
}
