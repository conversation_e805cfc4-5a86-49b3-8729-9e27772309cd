'use client';
import * as React from 'react';
import {
  Select,
  SheetTitle,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Calendar,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { useTranslations } from 'next-intl';

export default function FilterPopup() {
  const t = useTranslations();

  const [fromDate, setFromDate] = React.useState<Date>();
  const [toDate, setToDate] = React.useState<Date>();
  const [activityType, setActivityType] = React.useState<string>('');
  const [user, setUser] = React.useState<string>('');
  const [open, setOpen] = React.useState(false);

  const clearAllFilters = () => {
    setFromDate(undefined);
    setToDate(undefined);
    setActivityType('');
    setUser('');
  };

  const handleCancel = () => {
    clearAllFilters();
    setOpen(false);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" cursor-pointer flex items-center gap-2 text:InterfaceTexttitle hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="">
            <i className="cloud-filtericon text:InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTexttitle font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor="ActivityType"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('endCustomer')}
              </Label>
              <Select value={activityType} onValueChange={setActivityType}>
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="login">Login</SelectItem>
                    <SelectItem value="logout">Logout</SelectItem>
                    <SelectItem value="create">Create</SelectItem>
                    <SelectItem value="update">Update</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor="User"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Service Provider
              </Label>
              <Select value={user} onValueChange={setUser}>
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="user1">User 1</SelectItem>
                    <SelectItem value="user2">User 2</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div>
              <div className="pb-[6px]">
                <Label
                  htmlFor="ActivityDateRange"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Usage %
                </Label>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                <div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-blackcolor justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                          !fromDate && 'text-muted-foreground'
                        )}
                      >
                        {fromDate ? (
                          format(fromDate, 'PPP')
                        ) : (
                          // <span className="text-InterfaceTextsubtitle"> {t('fromDate')}</span>
                          <span className="text-InterfaceTextsubtitle"> From</span>
                        )}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar
                        mode="single"
                        selected={fromDate}
                        onSelect={setFromDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-blackcolor justify-between border border-InterfaceStrokehard text-left font-normal rounded-none',
                          !toDate && 'text-muted-foreground'
                        )}
                      >
                        {toDate ? (
                          format(toDate, 'PPP')
                        ) : (
                          // <span className="text-InterfaceTextsubtitle">{t('toDate')}</span>
                          <span className="text-InterfaceTextsubtitle">To</span>
                        )}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={toDate} onSelect={setToDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
              <Button
                type="button"
                size="sm"
                className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                onClick={handleCancel}
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                  {t('cancel')}
                </div>
              </Button>
              <Button
                type="button"
                size="sm"
                className="flex items-center applybtn gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-filtericon text-interfacetextinverse flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                  {t('applyFilter')}
                </div>
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
