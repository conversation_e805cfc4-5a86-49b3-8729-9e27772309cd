'use client';

import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  TooltipProvider,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import { ColumnDef, Column, Row } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import CustomerFilter from '../filterpopup';
import { format } from 'date-fns';
import { useTranslations } from 'next-intl';

export default function CustomersTable() {
  const t = useTranslations();

  const [fromDate, setFromDate] = React.useState<Date | undefined>(new Date('2025-01-01'));
  const [toDate, setToDate] = React.useState<Date | undefined>(new Date('2025-03-01'));

  const clearAll = () => {
    setFromDate(undefined);
    setToDate(undefined);
  };
  type User = {
    endCustomer: string;
    serviceProvider: string;
    subscriptionName: string;
    subscriptionID: string;
    billType: string;
    budget: string;
    usage: string;
    unbilledUsage: string;
  };

  const customerdata: User[] = [
    {
      endCustomer: 'Wade Warren',
      serviceProvider: 'Azure Plan',
      subscriptionName: 'DZH318Z0BPS6:0008',
      subscriptionID: '#112233',
      billType: 'Monthly',
      budget: 'USD 100,000.00',
      usage: '50%',
      unbilledUsage: '1,500.00',
    },
    {
      endCustomer: 'Robert Fox',
      serviceProvider: 'Microsoft CSP',
      subscriptionName: 'XYZ999999:0010',
      subscriptionID: '#112244',
      billType: 'Yearly',
      budget: 'USD 200,000.00',
      usage: '70%',
      unbilledUsage: '2,000.00',
    },
    {
      endCustomer: 'Annette Black',
      serviceProvider: 'Azure Plan',
      subscriptionName: 'XYZ222222:0020',
      subscriptionID: '#112255',
      billType: 'Monthly',
      budget: 'USD 75,000.00',
      usage: '30%',
      unbilledUsage: '800.00',
    },
    {
      endCustomer: 'Wade Warren',
      serviceProvider: 'Azure Plan',
      subscriptionName: 'DZH318Z0BPS6:0008',
      subscriptionID: '#112233',
      billType: 'Monthly',
      budget: 'USD 100,000.00',
      usage: '50%',
      unbilledUsage: '1,500.00',
    },
    {
      endCustomer: 'Robert Fox',
      serviceProvider: 'Microsoft CSP',
      subscriptionName: 'XYZ999999:0010',
      subscriptionID: '#112244',
      billType: 'Yearly',
      budget: 'USD 200,000.00',
      usage: '70%',
      unbilledUsage: '2,000.00',
    },
    {
      endCustomer: 'Annette Black',
      serviceProvider: 'Azure Plan',
      subscriptionName: 'XYZ222222:0020',
      subscriptionID: '#112255',
      billType: 'Monthly',
      budget: 'USD 75,000.00',
      usage: '30%',
      unbilledUsage: '800.00',
    },
    {
      endCustomer: 'Wade Warren',
      serviceProvider: 'Azure Plan',
      subscriptionName: 'DZH318Z0BPS6:0008',
      subscriptionID: '#112233',
      billType: 'Monthly',
      budget: 'USD 100,000.00',
      usage: '50%',
      unbilledUsage: '1,500.00',
    },
    {
      endCustomer: 'Robert Fox',
      serviceProvider: 'Microsoft CSP',
      subscriptionName: 'XYZ999999:0010',
      subscriptionID: '#112244',
      billType: 'Yearly',
      budget: 'USD 200,000.00',
      usage: '70%',
      unbilledUsage: '2,000.00',
    },
    {
      endCustomer: 'Annette Black',
      serviceProvider: 'Azure Plan',
      subscriptionName: 'XYZ222222:0020',
      subscriptionID: '#112255',
      billType: 'Monthly',
      budget: 'USD 75,000.00',
      usage: '30%',
      unbilledUsage: '800.00',
    },
  ];

  const customerColumns: ColumnDef<User>[] = [
    ...[
      'endCustomer',
      'serviceProvider',
      'subscriptionName',
      'subscriptionID',
      'billType',
      'budget',
      'usage',
      'unbilledUsage',
    ].map((key) => ({
      accessorKey: key,
      enableSorting: true,
      header: ({ column }: { column: Column<User> }) => {
        const isSorted = column.getIsSorted();
        const titleMap: Record<string, string> = {
          endCustomer: t('endCustomer'),
          serviceProvider: 'Service Provider',
          subscriptionName: t('subscriptionName'),
          subscriptionID: t('subscriptionId'),
          billType: t('billType'),
          budget: 'Budget',
          usage: 'Usage %',
          unbilledUsage: `${t('unbilledUsage')} (USD)`,
        };

        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center justify-between gap-[40px] text-end font-medium pt-[8px]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className="text-[14px] text-InterfaceTexttitle">{titleMap[key]}</div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={titleMap[key]}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }: { row: Row<User> }) => (
        <div className="text-InterfaceTextdefault text-[12px] pl-[12px]">{row.getValue(key)}</div>
      ),
      minSize: 400,
    })),
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] text-InterfaceTexttitle text-center w-full">{t('action')}</div>
      ),
      cell: () => (
        <div className="flex items-center gap-2 justify-center ">
          <Popover>
            <PopoverTrigger asChild>
              <i
                className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex"
                title={t('info')}
              ></i>
            </PopoverTrigger>
            <PopoverContent className="p-0 mr-[60px] w-[150px] bg-interfacesurfacecomponent z-20">
              <div className="px-[14px] py-[10px] text-[14px] text-InterfaceTextdefault flex gap-2 items-center border-t hover:bg-BrandNeutral100">
                <i className="cloud-info text-[14px]"></i>
                <p>{t('info')}</p>
              </div>
              <div className="px-[14px] py-[10px] text-[14px] text-InterfaceTextdefault flex gap-2 items-center border-t hover:bg-BrandNeutral100">
                <i className="cloud-folder text-[14px]"></i>
                <p>{t('info')}</p>
              </div>
              <div className="px-[14px] py-[10px] text-[14px] text-InterfaceTextdefault flex gap-2 items-center border-t hover:bg-BrandNeutral100">
                <i className="cloud-task text-[14px]"></i>
                <p>Configuration</p>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      ),
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0 z-10',
        cellClassName: 'sticky right-0 z-10',
      },
    },
  ];

  return (
    <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100">
      <div className="flex flex-wrap gap-[8px] justify-between border-b border-InterfaceStrokesoft px-[16px] py-[12px]">
        <div className="flex items-center gap-2 ">
          <div className="text-InterfaceTexttitle text-[18px] font-semibold">
            {t('listOfRecords')}
          </div>
          <div className="text-InterfaceTextsubtitle text-[14px] leading-[140%] mt-[4px]">
            {t('showingRecords')}
          </div>
        </div>
        <div className="flex gap-[20px] items-center">
          <div className="relative">
            <Input
              type="text"
              placeholder={t('search')}
              className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
            />
            <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
          </div>
          <CustomerFilter />
        </div>
      </div>
      <div className="overflow-x-auto">
        <div className="flex flex-wrap items-center gap-[8px] 3xl:gap-[0.417vw] px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] text-sm font-normal">
          {/* From Date */}
          <div className="flex items-center bg-[#F4F5F7] px-2 py-[6px] rounded-sm">
            <span className="text-[#6B7280]">{t('fromDate')} :</span>
            <Popover>
              <PopoverTrigger asChild>
                <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                  {fromDate ? format(fromDate, 'dd-MM-yyyy') : 'Select Date'}
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 bg-[#FFF]">
                <Calendar mode="single" selected={fromDate} onSelect={setFromDate} />
              </PopoverContent>
            </Popover>
            {fromDate && (
              <button onClick={() => setFromDate(undefined)} className="ml-1">
                <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
              </button>
            )}
          </div>

          {/* To Date */}
          <div className="flex items-center bg-[#F4F5F7] px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.366vw] rounded-sm">
            <span className="text-[#6B7280]">{t('toDate')} :</span>
            <Popover>
              <PopoverTrigger asChild>
                <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                  {toDate ? format(toDate, 'dd-MM-yyyy') : 'Select Date'}
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 bg-[#FFF]">
                <Calendar mode="single" selected={toDate} onSelect={setToDate} />
              </PopoverContent>
            </Popover>
            {toDate && (
              <button onClick={() => setToDate(undefined)} className="ml-1">
                <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
              </button>
            )}
          </div>
          <div className="flex items-center bg-[#F4F5F7] px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.366vw] rounded-sm">
            <span className="text-[#6B7280]">{t('endCustomer')}:</span>
            <Popover>
              <PopoverTrigger asChild>
                <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                  All
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 bg-[#FFF]"></PopoverContent>
            </Popover>

            <button onClick={() => setToDate(undefined)} className="ml-1">
              <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
            </button>
          </div>

          {/* Clear All */}
          {(fromDate || toDate) && (
            <button
              onClick={clearAll}
              className="flex items-center text-[#8B8B8B] font-[400] hover:text-black"
            >
              <i className="cloud-closecircle mr-[4px]"></i>
              Clear All
            </button>
          )}
        </div>
        <TooltipProvider>
          <DataTable data={customerdata} columns={customerColumns} withCheckbox={false} />
        </TooltipProvider>
      </div>
      <TablePagination />
    </div>
  );
}
