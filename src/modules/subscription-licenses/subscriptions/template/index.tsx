'use client';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import SubscriptionCards from '../components/subscriptionscards';
import Subscrptionstable from '../components/subscriptiontable';
import { useTranslations } from 'next-intl';

export default function SubscriptionTemplate() {
  const t = useTranslations();

  return (
    <div className="bg-[#F6F7F8] w-full">
      <div className=" h-full">
        <div className="relative h-full px-[32px] py-[20px] 3xl:px-[1.667vw] 3xl:py-[1.042vw] ">
          <div className="">
            <div className="flex flex-col pb-[10px] xl:pb-[10px] 3xl:pb-[0.525vw]">
              <div className="text-interfacetexttitle2 text-[20px] xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                {t('subscriptions')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                * {t('BasedOnTheCreateDateSelectedDateRange')}
              </div>
            </div>
            <div>
              <SubscriptionCards />
            </div>
            <div className="flex gap-[2px] my-[20px] xl:my-[20px] 3xl:my-[1.042vw]">
              <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
                <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
                {t('download')}
              </Button>
            </div>

            <div className="bg-interfacesurfacecomponent  rounded-1 shadow-md shadow-BrandNeutral100 ">
              <div>
                <Subscrptionstable />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
