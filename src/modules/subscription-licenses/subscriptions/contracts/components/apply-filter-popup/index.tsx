'use client';
import * as React from 'react';
import {
  Select,
  SheetT<PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

export default function ApplyFilterPopup() {
  const t = useTranslations();

  const [open, setOpen] = React.useState(false);

  const [brand, setBrand] = React.useState('All');
  const [brandCategory, setBrandCategory] = React.useState('All');
  const [product, setProduct] = React.useState('All');
  const [term, setTerm] = React.useState('All');
  const [billType, setBillType] = React.useState('All');
  const [contractStatus, setContractStatus] = React.useState('All');
  const [endCustomer, setEndCustomer] = React.useState('');
  const [fromDate, setFromDate] = React.useState<Date>();
  const [toDate, setToDate] = React.useState<Date>();

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className="cursor-pointer flex items-center gap-2 text:InterfaceTexttitle hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] px-[10px]">
          <i className="cloud-filtericon text-[14px]"></i>
          <p className="text-[14px] font-medium">{t('filter')}</p>
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full p-0 sm:max-w-[600px] hideclose">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] xl:p-[22px]">
          <SheetTitle className="flex justify-between">
            <div className="text-[22px] font-[600] text-[#19212A]">{t('applyFilter')}</div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        {/* Body */}
        <div className="px-[20px] xl:px-[22px] pb-[20px] xl:pb-[22px] overflow-auto space-y-[16px] h-[450px] lg:h-[600px] xl:h-[80vh] 2xl:h-[80vh] 3xl:h-[83vh]">
          {/* End Customer Input */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">
              {t('endCustomer')}
            </Label>
            <input
              type="text"
              placeholder={t('customerName')}
              value={endCustomer}
              onChange={(e) => setEndCustomer(e.target.value)}
              className="w-full border border-InterfaceStrokehard rounded-none px-[10px] py-[8px] text-[12px] text-blackcolor placeholder:text-InterfaceTextsubtitle"
            />
          </div>

          {/* Brand */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">{t('brand')}</Label>
            <Select value={brand} onValueChange={setBrand}>
              <SelectTrigger className="text-InterfaceTextsubtitle border border-InterfaceStrokehard rounded-none px-[10px] py-[8px] text-[14px]">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectGroup>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Brand1">Brand 1</SelectItem>
                  <SelectItem value="Brand2">Brand 2</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {/* Brand Category */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">
              {t('brandCategory')}
            </Label>
            <Select value={brandCategory} onValueChange={setBrandCategory}>
              <SelectTrigger className="text-InterfaceTextsubtitle border border-InterfaceStrokehard rounded-none px-[10px] py-[8px] text-[14px]">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectGroup>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Cat1">Category 1</SelectItem>
                  <SelectItem value="Cat2">Category 2</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {/* Product */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">
              {' '}
              {t('products')}
            </Label>
            <Select value={product} onValueChange={setProduct}>
              <SelectTrigger className="text-InterfaceTextsubtitle border border-InterfaceStrokehard rounded-none px-[10px] py-[8px] text-[14px]">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectGroup>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Product1">Product 1</SelectItem>
                  <SelectItem value="Product2">Product 2</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {/* Term */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">{t('term')}</Label>
            <Select value={term} onValueChange={setTerm}>
              <SelectTrigger className="text-InterfaceTextsubtitle border border-InterfaceStrokehard rounded-none px-[10px] py-[8px] text-[14px]">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectGroup>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Short">Short Term</SelectItem>
                  <SelectItem value="Long">Long Term</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {/* Bill Type */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">
              {t('billType')}
            </Label>
            <Select value={billType} onValueChange={setBillType}>
              <SelectTrigger className="text-InterfaceTextsubtitle border border-InterfaceStrokehard rounded-none px-[10px] py-[8px] text-[14px]">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectGroup>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Prepaid">Prepaid</SelectItem>
                  <SelectItem value="Postpaid">Postpaid</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {/* Contract Status */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">
              Contract Status
            </Label>
            <Select value={contractStatus} onValueChange={setContractStatus}>
              <SelectTrigger className="text-InterfaceTextsubtitle border border-InterfaceStrokehard rounded-none px-[10px] py-[8px] text-[14px]">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectGroup>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Expired">Expired</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {/* Contract End Date */}
          <div className="grid gap-1.5">
            <Label className="text-[14px] font-medium text-InterfaceTexttitle">
              Contract End Date
            </Label>
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px] mt-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] py-2 text-[14px]',
                      !fromDate ? 'text-InterfaceTextsubtitle' : 'text-blackcolor'
                    )}
                  >
                    {fromDate ? format(fromDate, 'PPP') : t('fromDate')}
                    <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-white" align="start">
                  <Calendar mode="single" selected={fromDate} onSelect={setFromDate} initialFocus />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] py-2 text-[14px]',
                      !toDate ? 'text-InterfaceTextsubtitle' : 'text-blackcolor'
                    )}
                  >
                    {toDate ? format(toDate, 'PPP') : t('toDate')}
                    <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-white" align="start">
                  <Calendar mode="single" selected={toDate} onSelect={setToDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Footer */}
        <SheetFooter className="absolute bottom-0 w-full border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] w-full flex justify-end">
            <div className="flex gap-[20px]">
              <Button
                type="button"
                size="sm"
                onClick={handleCancel}
                className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                  {t('cancel')}
                </div>
              </Button>
              <Link
                href=""
                className="flex items-center applybtn gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-filtericon text-interfacetextinverse flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                  {t('applyFilter')}
                </div>
              </Link>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
