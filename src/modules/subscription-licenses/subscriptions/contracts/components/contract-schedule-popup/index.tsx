'use client';
import * as React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import BillingScheduletable from './billing-schedule-table';
import { useTranslations } from 'next-intl';

export default function ContractSchedulePopup() {
  const t = useTranslations();

  return (
    <form>
      <Sheet>
        <SheetTitle></SheetTitle>
        <SheetTrigger>
          <div className="">
            <div className="w-[180px] xl:w-[180px] 2xl:w-[180px] 3xl:w-[9.833vw] px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
              <i className="cloud-folder text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
              {t('contractSchedule')}
            </div>
          </div>
        </SheetTrigger>
        <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[900px] xl:max-w-[900px] 3xl:max-w-[46.875vw] p-[0px]">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('contractSchedule')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[450px] lg:h-[620px] xl:h-[82vh] 2xl:h-[82vh] 3xl:h-[85vh]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="relative bg-interfacesurfacecomponent">
                <div className="space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw]">
                  <div className="flex flex-col gap-[24px] xl:gap-[24px] 3xl:gap-[1.25vw] ">
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft bg-BrandNeutral100">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left"> {t('basicInformation')}</div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="">
                            <div className="grid grid-cols-3 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('endCustomer')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Alpha Systems
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('contractId')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  0000998646
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('subscriptionId')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  1234567890
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('tenantName')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  thefirstgroup.onmicrosoft.com
                                </div>
                              </div>
                              <div className="col-span-2 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('organizationTenant')} Id
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  f6e191f8-5aed-4670-86d4-616277ebe4cO
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('startDate')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  01/01/2025
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('startDate')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  31/12/2025
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>

                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft bg-BrandNeutral100">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left">{t('purchaseInformation')}</div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="flex flex-col gap-[22px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
                            <div>
                              <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                                {t('productDetails2')}
                              </div>
                              <div className="grid grid-cols-12 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                                <div className="col-span-8 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('productName')}& {t('SKU')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    Amazon Web Service, SKU12121231351
                                  </div>
                                </div>
                                <div className="col-span-4 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('brand')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    AWS
                                  </div>
                                </div>
                                <div className="col-span-4 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('brand')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    1
                                  </div>
                                </div>
                                <div className="col-span-4 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('billFrequency')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    Monthly
                                  </div>
                                </div>
                                <div className="col-span-4 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('term')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    1 Year
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div>
                              <div className="text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                                {t('finance')}
                              </div>
                              <div className="grid grid-cols-4 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                                <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('listPrice')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    USD 200.00
                                  </div>
                                </div>
                                <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('discount')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    10%
                                  </div>
                                </div>
                                <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('discountValue')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    USD 20.00
                                  </div>
                                </div>
                                <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                    {t('discountValue')}
                                  </div>
                                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                    USD 180.00
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>

                    <div>
                      <BillingScheduletable />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </form>
  );
}
