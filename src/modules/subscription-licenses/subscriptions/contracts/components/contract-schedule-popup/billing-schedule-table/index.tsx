import { DataTable } from '@/components/common/ui/data-table';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import { useTranslations } from 'next-intl';

export default function BillingScheduletable() {
  const t = useTranslations();

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'invoice',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('invoice2')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('invoice2')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('invoice')}</div>,
    },
    {
      accessorKey: 'invoicedate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('invoiceDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('invoiceDate')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('invoicedate')}</div>,
    },
    {
      accessorKey: 'chargedescription',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('chargeDescription')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('chargeDescription')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('chargedescription')}</div>,
    },
    {
      accessorKey: 'chargestart',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('chargeStart')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('chargeStart')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('chargestart')}</div>,
    },
    {
      accessorKey: 'chargeEnd',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('chargeEnd')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('chargeEnd')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('chargeEnd')}</div>,
    },
    {
      accessorKey: 'revenue',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Revenue
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Revenue"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('revenue')}</div>,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('status')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Billed: {
            colorClass: 'bg-BrandPrimary100 text-BrandPrimary800 border-[#71FFA8]',
          },
          Unbilled: {
            colorClass:
              'bg-BrandSupport250 text-Interfacefeedbackneutral700 border-BrandSupport2300',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800 border-gray-300',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[2px] 3xl:py-[0.104vw] px-[8px] 3xl:px-[0.417vw] rounded-[2px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] border  ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },
    },
  ];

  type User = {
    invoice: string;
    invoicedate: string;
    chargedescription: string;
    chargestart: string;
    chargeEnd: string;
    revenue: string;
    status: string;
  };

  const registrationdata = [
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Billed',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Unbilled',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Billed',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Unbilled',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Billed',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Billed',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Unbilled',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Unbilled',
    },
    {
      invoice: 'invoice 1',
      invoicedate: '01/01/2025',
      chargedescription: 'Description...',
      chargestart: '01/01/2025',
      chargeEnd: '01/01/2025',
      revenue: 'USD',
      status: 'Billed',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="text-InterfaceTexttitle text-[18px] font-semibold">Billing Schedule</div>
        </div>

        <div className="overflow-x-auto">
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
        </div>
      </div>
    </>
  );
}
