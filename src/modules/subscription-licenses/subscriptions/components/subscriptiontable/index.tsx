import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import SubscriptionFilter from '../subscriptionfilter';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function Subscrptionstable() {
  const t = useTranslations();

  const [fromDate, setFromDate] = React.useState<Date | undefined>(new Date('2025-01-01'));
  const [toDate, setToDate] = React.useState<Date | undefined>(new Date('2025-01-03'));

  const clearAll = () => {
    setFromDate(undefined);
    setToDate(undefined);
  };

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'subscriptionID',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('subscriptionId')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('subscriptionId')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('subscriptionID')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endCustomer')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('endCustomer')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('endcustomer')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'brandcategory',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brandCategory')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('brandCategory')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('brandcategory')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'productname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('productName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('productName')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('productname')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'quantity',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('quantity')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('quantity')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('quantity')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'term',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('term')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('term')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('term')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'billtype',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('billType')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('billType')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('billtype')}</div>,

      minSize: 600,
    },
    {
      accessorKey: 'expirydate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('expiryDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('expiryDate')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('expirydate')}</div>,

      minSize: 400,
    },

    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('status')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Active: {
            colorClass: 'bg-[#E4F4E6] text-[#2A5130] border-[#ACD69F]',
          },
          InActive: {
            colorClass: 'bg-[#FFF3F0] text-[#D42600] border-[#FFB5A5]',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800 border-gray-300',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[6px] xl:py-[6px] 3xl:py-[0.357vw] px-[10px] rounded-[2px] text-[15px] xl:text-[15px] 3xl:text-[0.781vw] font-semibold border  ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title={t('info')}
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[90px] xl:w-[90px] 3xl:w-[4.533vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <Link href="/subscription-licenses/subscriptions/details">
                  <div className="  px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                    <div className="">
                      <i className="cloud-readeye1 text:InterfaceTextdefault  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
                    </div>
                    <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTextdefault  ">
                      {t('view')}
                    </p>
                  </div>
                </Link>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    subscriptionID: string;
    endcustomer: string;
    brandcategory: string;
    productname: string;
    quantity: number;
    term: string;
    billtype: string;
    expirydate: string;
    status: string;
  };

  const registrationdata = [
    {
      subscriptionID: '11223344',
      endcustomer: 'Customer 1',
      brandcategory: 'Category 1',
      productname: 'Product 1',
      quantity: 10,
      term: 'Term 1',
      billtype: 'yearly',
      expirydate: '01/12/2035',
      status: 'Active',
    },
    {
      subscriptionID: '11223344',
      endcustomer: 'Customer 1',
      brandcategory: 'Category 1',
      productname: 'Product 1',
      quantity: 10,
      term: 'Term 1',
      billtype: 'yearly',
      expirydate: '01/12/2035',
      status: 'Active',
    },
    {
      subscriptionID: '11223344',
      endcustomer: 'Customer 1',
      brandcategory: 'Category 1',
      productname: 'Product 1',
      quantity: 10,
      term: 'Term 1',
      billtype: 'yearly',
      expirydate: '01/12/2035',
      status: 'Active',
    },

    {
      subscriptionID: '11223344',
      endcustomer: 'Customer 1',
      brandcategory: 'Category 1',
      productname: 'Product 1',
      quantity: 10,
      term: 'Term 1',
      billtype: 'yearly',
      expirydate: '01/12/2035',
      status: 'Active',
    },
    {
      subscriptionID: '11223344',
      endcustomer: 'Customer 1',
      brandcategory: 'Category 1',
      productname: 'Product 1',
      quantity: 10,
      term: 'Term 1',
      billtype: 'yearly',
      expirydate: '01/12/2035',
      status: 'Active',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex flex-wrap gap-[8px] justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('listofSubscriptions')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%]  mt-[4px] xl:mt-[0px] 3xl:mt-[0.1vw]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            <SubscriptionFilter />
          </div>
        </div>

        <div className="overflow-x-auto">
          <div className="flex flex-wrap items-center gap-[8px] 3xl:gap-[0.417vw] px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] text-sm font-normal">
            <div className="flex items-center bg-[#F4F5F7] px-2 py-[6px] rounded-sm">
              <span className="text-[#6B7280]">{t('expiryDate')} :</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 mr-1 font-semibold text-InterfaceTextdefault outline-none">
                    {fromDate ? format(fromDate, 'dd-MM-yyyy') : t('fromDate')}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={fromDate} onSelect={setFromDate} />
                </PopoverContent>
              </Popover>
              <span className="text-[#6B7280] font-medium">–</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {toDate ? format(toDate, 'dd-MM-yyyy') : t('toDate')}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={toDate} onSelect={setToDate} />
                </PopoverContent>
              </Popover>
              {fromDate || toDate ? (
                <button onClick={() => setFromDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              ) : null}
            </div>

            <div className="flex items-center bg-[#F4F5F7] px-2 rounded-sm">
              <span className="text-[#6B7280]">{t('endCustomer')} :</span>
              <Select defaultValue="all">
                <SelectTrigger className=" bg-transparent hidearrow w-fit text-InterfaceTextdefault placeholder-text-sm leading-6  rounded-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] py-[10px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.36vw] px-[14px] xl:px-[2px] 3xl:px-[0.833vw] border-none font-medium">
                  <SelectValue placeholder="all" className="" />
                </SelectTrigger>
                <SelectContent className="bg-white">
                  <SelectGroup className=" text-InterfaceTextsubtitle border-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] ">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="india">India</SelectItem>
                    <SelectItem value="mea">UAE</SelectItem>
                    <SelectItem value="tukry">Turky</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>

              <button className="ml-1">
                <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
              </button>
            </div>

            <div className="flex items-center bg-[#F4F5F7] px-2 rounded-sm">
              <span className="text-[#6B7280]">{t('status')}:</span>
              <Select defaultValue="active">
                <SelectTrigger className=" bg-transparent hidearrow w-fit text-InterfaceTextdefault placeholder-text-sm leading-6  rounded-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] py-[10px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.36vw] px-[14px] xl:px-[2px] 3xl:px-[0.833vw] border-none font-medium ">
                  <SelectValue placeholder="all" className="" />
                </SelectTrigger>
                <SelectContent className="bg-white">
                  <SelectGroup className=" text-InterfaceTextsubtitle border-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] ">
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="nonactive">NonActive</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>

              <button className="ml-1">
                <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
              </button>
            </div>

            {(fromDate || toDate) && (
              <button
                onClick={clearAll}
                className="flex items-center text-[#8B8B8B] font-[400] hover:text-black"
              >
                <i className="cloud-closecircle mr-[4px]"></i>
                {t('clearAll')}
              </button>
            )}
          </div>
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
        </div>
        <TablePagination />
      </div>
    </>
  );
}
