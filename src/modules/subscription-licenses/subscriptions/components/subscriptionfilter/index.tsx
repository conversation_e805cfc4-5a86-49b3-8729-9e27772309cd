'use client';
import * as React from 'react';
import {
  Select,
  SheetTitle,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Calendar,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { useTranslations } from 'next-intl';

export default function SubscriptionFilter() {
  const t = useTranslations();

  const [date, setDate] = React.useState<Date>();
  const [vendor, setVendor] = React.useState('all');
  const [endcustomer, setEndCustomer] = React.useState('alphasystems');
  const [orgtenent, setOrgTenent] = React.useState('all');
  const [brand, setBrand] = React.useState('all');
  const [category, setCategory] = React.useState('all');
  const [productname, setProductName] = React.useState('all');
  const [productid, setProductId] = React.useState('all');
  const [term, setTerm] = React.useState('all');
  const [billtype, setBillType] = React.useState('all');

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-2 text:InterfaceTexttitle hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="">
            <i className="cloud-filtericon text:InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTexttitle font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[450px] lg:h-[600px] xl:h-[80vh] 2xl:h-[80vh] 3xl:h-[83vh]">
          <div className="space-y-[20px] xl:space-y-[16px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="pb-[6px]">
                <Label
                  htmlFor="LastUpdatedDate "
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('expiryDate')}
                </Label>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                <div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-2 text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ',
                          !date ? ' text-InterfaceTextsubtitle ' : 'text-blackcolor'
                        )}
                      >
                        {date ? format(date, 'PPP') : <span className=""> {t('fromDate')}</span>}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-2 text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ',
                          !date ? ' text-InterfaceTextsubtitle ' : 'text-blackcolor'
                        )}
                      >
                        {date ? format(date, 'PPP') : <span>{t('toDate')}</span>}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            <div>
              <div className="pb-[6px]">
                <Label
                  htmlFor="LastUpdatedDate "
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('createDate')}
                </Label>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                <div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-2 text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ',
                          !date ? ' text-InterfaceTextsubtitle ' : 'text-blackcolor'
                        )}
                      >
                        {date ? format(date, 'PPP') : <span className="">{t('fromDate')}</span>}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-2 text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ',
                          !date ? ' text-InterfaceTextsubtitle ' : 'text-blackcolor'
                        )}
                      >
                        {date ? format(date, 'PPP') : <span>{t('toDate')}</span>}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]"
              >
                {t('endCustomer')}
              </Label>
              <Select value={endcustomer} onValueChange={setEndCustomer}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    endcustomer
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="alphasystems">Alpha Systems</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('organizationTenant')}
              </Label>
              <Select value={orgtenent} onValueChange={setOrgTenent}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    orgtenent
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('vendor')}
              </Label>
              <Select value={vendor} onValueChange={setVendor}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    vendor
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('brand')}
              </Label>
              <Select value={brand} onValueChange={setBrand}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    brand
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('brandCategory')}
              </Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    category
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('productName')}
              </Label>
              <Select value={productname} onValueChange={setProductName}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    productname
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('productId')}
              </Label>
              <Select value={productid} onValueChange={setProductId}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    brand
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('term')}
              </Label>
              <Select value={term} onValueChange={setTerm}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    term
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('billType')}
              </Label>
              <Select value={billtype} onValueChange={setBillType}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    brand
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-white p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose>
                <div
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </div>
              </SheetClose>

              <Button
                className="flex items-center gap-[8px] 3xl:gap-[0.417vw] applybtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                size="md"
              >
                <div>
                  <i className="cloud-filter text-interfacetextinverse flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                  {t('applyFilter')}
                </div>
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
