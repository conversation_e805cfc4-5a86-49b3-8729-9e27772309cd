'use client';
import Piechart from '@/components/common/charts/piechart';
import React, { useState } from 'react';
import Barchart from '@/components/common/charts/barchart';
import {
  Label,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function SubscriptionCards() {
  const t = useTranslations();

  const [selectedOption, setSelectedOption] = useState('value');
  return (
    <div>
      <div className="grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-3 2xl:grid-cols-3 3xl:grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-12  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.83vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[29px]"></i>
              {selectedOption === 'value' ? (
                <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
                  <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                    {t('subscriptionValue')}
                  </div>
                  <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                    USD 100K
                  </div>
                </div>
              ) : (
                <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
                  <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                    {t('subscriptionCount')}
                  </div>
                  <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                    100
                  </div>
                </div>
              )}
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full h-[110px] 3xl:h-[6.885vw] 2xl:ml-[-20px]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 0, // distance from the right edge
                  // left: 168, // distance from the right edge
                  top: 35, // vertically centered
                  itemGap: 8,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: [t('amazon'), t('microsoft'), t('googleCloud'), t('others')],
                }}
                name={'Nightingale Chart'}
                radius={[30, 55]}
                center={['35%', '50%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 5,
                  length2: 5,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 330000, name: t('amazon'), itemStyle: { color: '#73609B' } },
                  { value: 250000, name: t('microsoft'), itemStyle: { color: '#4D9E99' } },
                  { value: 250000, name: t('googleCloud'), itemStyle: { color: ' #FACE4F' } },
                ]}
              />
            </div>

            <div className="absolute right-3 3xl:right-4 top-2 3xl:top-3">
              <RadioGroup
                defaultValue="value"
                value={selectedOption}
                onValueChange={setSelectedOption}
                className="flex justify-end custrediogroup"
              >
                <div className="flex items-center space-x-2 ">
                  <RadioGroupItem value="value" id="r1" />
                  <Label
                    htmlFor="r1"
                    className="text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] text-[#000]"
                  >
                    {t('value')}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="count" id="r2" />
                  <Label
                    htmlFor="r2"
                    className="text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('count')}
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-2 2xl:col-span-2 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-4 2xl:col-span-4 3xl:col-span-4 w-full h-[110px] 3xl:h-[6.885vw]">
              <Barchart />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-5 3xl:bottom-10">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-bold leading-[140%]">
                {/* Subscription - <br />
                Top 5 Customers* */}
                {t('subscriptionTop5Customers')}
              </div>
            </div>

            <div className="absolute top-2 right-2">
              <Select>
                <SelectTrigger className="w-[100px] xl:w-[115px] 2xl:w-[145px] 3xl:w-[7.208vw] border-[#E5E7EB] text-[#7F8488]">
                  <SelectValue placeholder={t('selectBrand')} />
                </SelectTrigger>
                <SelectContent className="bg-[#fff] text-[#7F8488]">
                  <SelectGroup>
                    <SelectItem value="apple">Azure</SelectItem>
                    <SelectItem value="banana">Banana</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-12  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full h-[110px] 3xl:h-[6.885vw] 2xl:ml-[-30px]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: -10, // distance from the right edge
                  top: 25, // vertically centered
                  itemGap: 10,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: [t('seatBased'), t('usageBased')],
                }}
                name={'Nightingale Chart'}
                radius={[30, 55]}
                center={['40%', '50%']}
                // rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 8,
                  length2: 8,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 70, name: t('seatBased'), itemStyle: { color: '#1570EF' } },
                  { value: 30, name: t('usageBased'), itemStyle: { color: ' #FACE4F' } },
                ]}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-bold leading-[140%] mb-[10px]">
                {/* Subscription <br />
                Count* */}
                {t('subscriptionCount')}
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[1.40vw] font-semibold truncate">
                100
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
