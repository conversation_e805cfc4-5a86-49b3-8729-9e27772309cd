'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import Barchart2 from '@/components/common/charts/barchart2';
import HorizontalBarChart from '@/components/common/charts/horizontalbarchart';
import { useTranslations } from 'next-intl';

export default function ContractCards() {
  const t = useTranslations();
  return (
    <div>
      <div className="grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-3 2xl:grid-cols-3 3xl:grid-cols-3 items-stretch gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-6 h-full p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-2 2xl:col-span-2 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw]"></i>
            </div>

            <div className="col-span-4 2xl:col-span-4 3xl:col-span-4 w-full h-[110px] 3xl:h-[5.885vw]">
              <Barchart2 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-8">
              <div className="text-[#3C4146] text-[11px] lg:text-[10px] xl:text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-bold leading-[140%]">
                {t('totalActiveContractsCount')}
              </div>
              <div className="text-[13px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[1.70vw] font-bold truncate relative 3xl:top-6">
                100
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-12 h-full p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-3 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw]"></i>
            </div>

            <div className="col-span-9 3xl:col-span-8 w-full h-[110px] 3xl:h-[6.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 0, // distance from the right edge
                  top: 15, // vertically centered
                  itemGap: 10,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: [t('billed'), t('unbilled')],
                }}
                name={'Nightingale Chart'}
                radius={[25, 45]}
                center={['40%', '50%']}
                // rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 10,
                  length2: 15,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 35000000, name: t('billed'), itemStyle: { color: '#1570EF' } },
                  { value: 15000000, name: t('unbilled'), itemStyle: { color: ' #FACE4F' } },
                ]}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-8">
              <div className="text-[#3C4146] text-[11px] lg:text-[10px] xl:text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-bold leading-[140%]">
                {t('totalContractsValue')} *
              </div>
              <div className="text-[13px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[1.70vw] font-semibold truncate relative 3xl:top-6">
                USD 500K
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="h-full p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="flex items-center gap-[10px]">
              <i className="cloud-cards1 text-[#7F8488] text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw]"></i>
              <div className="">
                <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw]  font-bold leading-[140%]">
                  {t('upcomingExpiration')} *
                </div>
              </div>
            </div>

            <div className=" w-full h-[86px] 3xl:h-[5.885vw]">
              <HorizontalBarChart
                legend={{
                  show: false,
                  left: '2%',
                  bottom: '-6%',
                  itemHeight: 10,
                  itemWidth: 10,
                  textStyle: {
                    color: '#2C363F',
                    fontSize: 10,
                  },
                }}
                grid={{
                  top: '5%',
                  left: '2%',
                  right: '2%',
                  bottom: '-2%',
                  containLabel: true,
                }}
                min={0}
                max={50}
                xAxisLabel={{ show: true, fontSize: 9 }}
                xAxisSplitLine={{
                  show: false,
                  lineStyle: {
                    type: 'dashed',
                    color: '#C8CBD0',
                  },
                }}
                yAxisLabel={{
                  color: '#7F8488',
                  fontSize: 9,
                  padding: [0, 0, 0, 0],
                  textAlign: 'left',
                  fontWeight: 400,
                }}
                yAxisTick={{ show: false }}
                yAxisLine={{
                  show: false,
                  lineStyle: {
                    color: '#E4E7EC',
                  },
                }}
                yAxisLine2={{
                  show: false,
                  lineStyle: {
                    color: '#E4E7EC',
                  },
                }}
                yAxisLabel2={{
                  inside: false,
                  position: 'top',
                  zIndex: 1,
                  color: '#7F8488',
                  fontSize: 9,
                  fontWeight: 400,
                }}
                name={'# Quotes'}
                showBackground={true}
                backgroundStyle={{
                  color: '#EDEEF1',
                  borderRadius: [0, 4, 4, 0],
                }}
                label={{
                  show: false,
                  position: 'outside',
                  color: '#344054',
                  formatter: '{c}',
                  fontSize: 12,
                }}
                itemStyle={{
                  color: '#6480AB',
                  borderRadius: [2, 2, 2, 2],
                }}
                yAxisdata={['> 91 days', '61-90 days', '31-61 days', '0-30 days']}
                yAxisdata2={['40', '35', '40', '40']}
                data={[40, 40, 35, 40]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
