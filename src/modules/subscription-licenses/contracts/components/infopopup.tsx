'use client';
import * as React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function Infopopup() {
  const t = useTranslations();

  return (
    <form>
      <Sheet>
        <SheetTitle></SheetTitle>
        <SheetTrigger className="w-full">
          <div className="w-full">
            <div className=" w-full px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
              <i className="cloud-info text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
              {t('info')}
            </div>
          </div>
        </SheetTrigger>
        <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[700px] xl:max-w-[700px] 3xl:max-w-[36.458vw] p-[0px]">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('contractInfo')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="relative bg-interfacesurfacecomponent">
                <div className="space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw]">
                  <div className="flex flex-col gap-[24px] xl:gap-[24px] 3xl:gap-[1.25vw] ">
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[1.042vw] border border-InterfaceStrokesoft bg-BrandNeutral100">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left">{t('basicInformation')}</div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="">
                            <div className="grid grid-cols-3 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('endCustomer')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Alpha Systems
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  Brand
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  AWS
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('productName')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Amazon Web Services
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('segment')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Education
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('quantity')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  1
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('term')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  1 Year
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('billType')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Monthly
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('paymentTerm')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  R032 Advance Payment
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>

                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[1.042vw] border border-InterfaceStrokesoft bg-BrandNeutral100">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left">{t('subscriptionInformation')}</div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="">
                            <div className="grid grid-cols-3 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('subscriptionId')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  1234566
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('endCustomer')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  01/01/2025
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('subscriptionStartDate')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  01/01/2025
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('subscriptionEndDate')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  31/12/2025
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('status')}
                                </div>
                                <div className="text-[#00953A] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Active
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('subStatus')}
                                </div>
                                <div className="text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  New Purchase
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>

                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[1.042vw] border border-InterfaceStrokesoft bg-BrandNeutral100">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left">{t('contractInformation')}</div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="">
                            <div className="grid grid-cols-3 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('contractId')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  #112233
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('contractValueTotal')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  USD 20,000.00
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('valueBilledUnbilled')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  USD 10,000.00 / USD 10,000.00
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('scheduleTotal')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  12
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('schedulesBilledUnbilled')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  6/6
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </form>
  );
}
