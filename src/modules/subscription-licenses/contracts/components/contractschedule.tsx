'use client';
import * as React from 'react';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  SheetTitle,
  SheetHeader,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function Contractschedule() {
  const t = useTranslations();
  const InvoicesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'invoice',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('invoice')} #
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="text-InterfaceTextdefault">{row.getValue('invoice')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'invoicedate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('invoiceDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault">{row.getValue('invoicedate')}</div>
      ),

      minSize: 500,
    },
    {
      accessorKey: 'chargedescription',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('chargeDescription')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('chargedescription')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'chargestart',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('chargeStart')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault">{row.getValue('chargestart')}</div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'chargeend',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('chargeEnd')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault">{row.getValue('chargeend')}</div>
      ),

      minSize: 500,
    },
    {
      accessorKey: 'amount',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('amount')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('amount')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      // cell: ({ row }) => <div className="">{row.getValue('status')}</div>,
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Billed: {
            colorClass: 'bg-BrandPrimary100 text-BrandPrimary800 border border-BrandPrimary300new',
          },
          Unbilled: {
            colorClass:
              'bg-BrandSupport250 border border-BrandSupport2300 text-Interfacefeedbackneutral700',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[4px] xl:py-[4px] 3xl:py-[0.208vw] px-[10px] rounded-[2px] text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-semibold leading-[140%] ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 500,
    },
  ];

  type User = {
    invoice: string;
    invoicedate: string;
    chargedescription: string;
    chargestart: string;
    chargeend: string;
    amount: string;
    status: string;
  };

  const Invoicesdata = [
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      chargedescription: 'Descroption...',
      chargestart: '01/01/2025',
      chargeend: '01/01/2025',
      amount: '5,000.00',
      status: 'Billed',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      chargedescription: 'Descroption...',
      chargestart: '01/01/2025',
      chargeend: '01/01/2025',
      amount: '5,000.00',
      status: 'Unbilled',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      chargedescription: 'Descroption...',
      chargestart: '01/01/2025',
      chargeend: '01/01/2025',
      amount: '5,000.00',
      status: 'Billed',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      chargedescription: 'Descroption...',
      chargestart: '01/01/2025',
      chargeend: '01/01/2025',
      amount: '5,000.00',
      status: 'Unbilled',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      chargedescription: 'Descroption...',
      chargestart: '01/01/2025',
      chargeend: '01/01/2025',
      amount: '5,000.00',
      status: 'Billed',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      chargedescription: 'Descroption...',
      chargestart: '01/01/2025',
      chargeend: '01/01/2025',
      amount: '5,000.00',
      status: 'Unbilled',
    },
  ];
  return (
    <form>
      <Sheet>
        <SheetTitle></SheetTitle>
        <SheetTrigger className="w-full">
          <div className="w-full">
            <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
              <i className="cloud-folder text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
              {t('contractSchedule')}
            </div>
          </div>
        </SheetTrigger>
        <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[700px] xl:max-w-[700px] 3xl:max-w-[36.458vw] p-[0px]">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('contractSchedule')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="relative bg-interfacesurfacecomponent">
                <div className="space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw]">
                  <div className="flex flex-col gap-[24px] xl:gap-[24px] 3xl:gap-[1.25vw] ">
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[1.042vw] border border-InterfaceStrokesoft bg-BrandNeutral100">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left">{t('basicInformation')}</div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="">
                            <div className="grid grid-cols-3 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle ...">
                                  {t('endCustomer')}
                                </div>
                                <div className="text-InterfaceTextdefault ...">Alpha Systems</div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle ...">
                                  {t('contractId')}
                                </div>
                                <div className="text-InterfaceTextdefault ...">0000998646</div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle ...">
                                  {t('subscriptionId')}
                                </div>
                                <div className="text-InterfaceTextdefault ...">1234567890</div>
                              </div>

                              <div className="col-span-1 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px]">
                                <div className="text-InterfaceTextsubtitle ...">
                                  {t('tenantName')}
                                </div>
                                <div className="text-InterfaceTextdefault ...">
                                  thefirstgroup.onmicrosoft.com
                                </div>
                              </div>
                              <div className="col-span-2 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px]">
                                <div className="text-InterfaceTextsubtitle ...">
                                  {t('organisationAndTenantId')}
                                </div>
                                <div className="text-InterfaceTextdefault ...">
                                  f6e191f8-5aed-4670-86d4-616277ebe4cO
                                </div>
                              </div>

                              <div className="col-span-2 grid grid-cols-2 gap-[22px]">
                                <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] ...">
                                  <div className="text-InterfaceTextsubtitle ...">
                                    {t('startDate')}
                                  </div>
                                  <div className="text-InterfaceTextdefault ...">01/01/2025</div>
                                </div>
                                <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] ...">
                                  <div className="text-InterfaceTextsubtitle ...">
                                    {t('endDate')}
                                  </div>
                                  <div className="text-InterfaceTextdefault ...">31/12/2025</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>

                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[1.042vw] border border-InterfaceStrokesoft bg-BrandNeutral100">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left">{t('purchaseInformation')}</div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="">
                            <div className="text-InterfaceTextdefault font-medium leading-[140%] text-[15px] xl:text-[15px] 2xl:text-[15px]  3xl:text-[0.833vw]">
                              {t('productDetails')}
                            </div>
                            <div className="grid grid-cols-3 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                              <div className="col-span-2 flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('productNameSku')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Amazon Web Service, SKU12121231351
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('brandCategory')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  AWS
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('quantity')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  1
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('billFrequency')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  Monthly
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('term')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  1 Year
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="mt-[25px] xl:mt-[25px] 2xl:mt-[25px] 3xl:mt-[1.302vw]">
                            <div className="text-InterfaceTextdefault font-medium leading-[140%] text-[15px] xl:text-[15px] 2xl:text-[15px]  3xl:text-[0.833vw]">
                              {t('finance')}
                            </div>
                            <div className="grid grid-cols-4 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('listPrice')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  USD 200.00
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('discount')} %
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  10%
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('discountValue')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  USD 20.00
                                </div>
                              </div>
                              <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                                  {t('unitPrice')}
                                </div>
                                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                                  USD 180.00
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>

                  <div className="mt-[25px] xl:mt-[25px] 2xl:mt-[25px] 3xl:mt-[1.302vw]">
                    <div className="py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[1.042vw] border border-InterfaceStrokesoft bg-white">
                      <div className="w-full flex justify-between items-center">
                        <div className="text-left">{t('billingScheduleUsd')}</div>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <DataTable
                        data={Invoicesdata}
                        columns={InvoicesColumns}
                        withCheckbox={false}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </form>
  );
}
