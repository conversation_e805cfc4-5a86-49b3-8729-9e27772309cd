import Link from 'next/link';
import React from 'react';

export default function OrderPlacementSection() {
  return (
    <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] bg-background my-[22px] 3xl:my-[1.25vw] cardShadow">
      <div className="flex flex-wrap items-center justify-between gap-2 border-b border-b-InterfaceStrokedefault pb-[16px] 2xl:pb-[16px]">
        <div className="text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] text-InterfaceTexttitle font-semibold leading-[140%]">
          Order Placement
        </div>
      </div>
      <div className="mt-[20px] 3xl:mt-[1.042vw]">
        <div className="bg-BrandHighlight100 p-[16px] 3xl:p-[0.833vw]">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-[16px] 3xl:gap-[0.833vw]">
              <div className="w-[70px] 3xl:w-[3.646vw] h-[70px] 3xl:h-[3.646vw] bg-background flex items-center justify-center">
                <i className="cloud-tickcircle text-BrandPrimarypure text-[45px] 3xl:text-[2.344vw]"></i>
              </div>
              <div>
                <div className="font20 text-BrandHighlight600 font-semibold leading-[140%]">
                  Your order is Confirmed
                </div>
                <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                  Your order number(s): 4140000428, 4140000429, 4140000430. We{"'"}ll email you the
                  order details.
                </div>
                <div className="font14 text-InterfaceTexttitle font-medium leading-[140%]">
                  Order Date: 01/04/2025
                </div>
              </div>
            </div>
            <div>
              <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                <Link
                  href={'/marketplace'}
                  className="border border-BrandPrimary50new text-[14px] 3xl:text-[0.729vw] rounded-none py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw font-[500] text-interfacetextinverse bg-BrandPrimarypurenew text-center"
                >
                  Continue Purchase
                </Link>
                <Link
                  href={'/sales/orders'}
                  className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] font-[500] text-center"
                >
                  Back to My Orders
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
