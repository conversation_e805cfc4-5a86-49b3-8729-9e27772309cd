'use client';
import OrderStatus from '@/components/common/orderstatus';
import React from 'react';
import { Roboto } from 'next/font/google';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ActivityLogPopup } from '../../components/activitylog';
import { PartnerRemarkPopup } from '../../components/partnerremark';
import OrderPlacementSection from '../components/order-placement-section';
import Link from 'next/link';
import AddressDetails from '../../order-review/components/address-details-section';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
export default function OrderPlacement() {
  const [activitylog, setActivitylog] = React.useState<boolean>(false);
  const [partnerremark, setPartnerRemark] = React.useState<boolean>(false);
  return (
    <div className="mt-[22px] 3xl:mt-[1.25vw] px-[28px] 3xl:px-[1.458vw]">
      <div className={`${roboto.className} grid grid-cols-12 gap-[22px] 3xl:gap-[1.25vw]`}>
        <div className="col-span-12 xl:col-span-9">
          <OrderStatus currentStepId={4} />
        </div>
        <div className="col-span-12 xl:col-span-3"></div>
      </div>

      <OrderPlacementSection />

      <div className="px-[20px] 3xl:px-[1.042vw] py-[14px] 3xl:py-[0.729vw] bg-BrandNeutral950 mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.938vw] mb-[22px] 3xl:mb-[1.25vw]">
        <div className="flex items-center justify-between gap-2">
          <div className="font14 text-InterfaceTextwhite font-normal leading-[140%]">
            How was your recent purchase? We{"'"}d love to hear your thoughts. This will take less
            than 30 seconds!
          </div>
          <div className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault font14 py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] font-[500] text-center">
            <Link href={'/order/feedback'}>Submit Your Feedback</Link>
          </div>
        </div>
      </div>

      <div className="font18 text-InterfaceTexttitle font-semibold leading-[140%]">
        Order Details
      </div>

      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="authorized-signatory">
          <AccordionTrigger className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] mt-[16px] 3xl:mt-[0.833vw] border-b border-InterfaceStrokesoft bg-white hover:no-underline hover:bg-BrandNeutral100 data-[state=open]:bg-BrandNeutral100">
            <div className="w-full flex items-center justify-between">
              <div className="flex">
                <div className="pr-[24px] xl:pr-[24px] 2xl:pr-[24px] 3xl:pr-[1.25vw] border-r-2 border-InterfaceStrokehard text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  Order#: 4140000428
                </div>
                <div className="pl-[24px] xl:pl-[24px] 2xl:pl-[24px] 3xl:pl-[1.25vw]  text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  Grand Total: USD 1,800.00
                </div>
              </div>
            </div>
          </AccordionTrigger>

          <AccordionContent className=" border border-InterfaceStrokesoft bg-[#FFF]">
            <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] bg-background">
              <AddressDetails />
              <div className=" mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center justify-between gap-[10px] px-[7px] xl:px-[7px] 2xl:px-[7px] 3xl:px-[0.365vw] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.208vw]  bg-InterfaceSurfacecomponentmuted text-sm rounded-none">
                <span className="truncate max-w-[200px] text-InterfaceTextprimary text-[13px]">
                  LPO Document One.pdf
                </span>
              </div>
              <div className="product-table custtableborder custtable custtable1  pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
                <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
                  <div className="border rounded-md overflow-x-auto w-full">
                    <table className="min-w-[1400px] w-full text-sm">
                      <thead className="bg-white text-center">
                        <tr>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium text-left">
                            Product Name
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Status
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Quantity
                          </th>

                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Currency
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            List Price
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Discount
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Unit Price
                          </th>

                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Sub Total
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            VAT%
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Estimated VAT
                          </th>
                          <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                            Net Price
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="px-4 py-2 align-top">
                            <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                              Google Workspace Enterprise Essentials Plus
                            </div>
                            <div className="grid grid-cols-3">
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Brand:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  Google
                                </span>
                              </div>
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Segment:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  Commercial
                                </span>
                              </div>
                            </div>
                            <div className="grid grid-cols-3">
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Term:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  {' '}
                                  1 Year
                                </span>
                              </div>
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Bill Type:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  Monthly
                                </span>
                              </div>
                            </div>
                            <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                              <div className="flex items-center">
                                <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                              </div>
                              Applied Coupon: AYETL123THJ
                            </div>
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-BrandSupport2800 font-semibold  ">
                            <div className="bg-BrandSupport2100 !border !border-BrandSupport2300 py-[2px] px-[8px]">
                              Pending
                            </div>
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            4
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            USD
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            200.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            10.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            190.00
                          </td>

                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            790.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            0.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            0.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            790.00
                          </td>
                        </tr>

                        <tr className="border-b">
                          <td className="px-4 py-2 align-top">
                            <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                              Google Workspace Enterprise Essentials Plus
                            </div>
                            <div className="grid grid-cols-3">
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Brand:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  Google
                                </span>
                              </div>
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Segment:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  Commercial
                                </span>
                              </div>
                            </div>
                            <div className="grid grid-cols-3">
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Term:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  {' '}
                                  1 Year
                                </span>
                              </div>
                              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                                Bill Type:{' '}
                                <span className="text-InterfaceTextdefault font-normal">
                                  Monthly
                                </span>
                              </div>
                            </div>
                            <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                              <div className="flex items-center">
                                <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                              </div>
                              Applied Coupon: AYETL123THJ
                            </div>
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-BrandSupport2800 font-semibold  ">
                            <div className="bg-BrandSupport2100 !border !border-BrandSupport2300 py-[2px] px-[8px]">
                              Pending
                            </div>
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            4
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            USD
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            200.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            10.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            190.00
                          </td>

                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            790.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            0.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            0.00
                          </td>
                          <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                            790.00
                          </td>
                        </tr>

                        <tr className="bg-white">
                          <td colSpan={7}></td>

                          <td colSpan={4} className="px-4">
                            <table className="w-full text-right text-sm">
                              <tbody>
                                <tr>
                                  <td className=" text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                    Sub Total
                                  </td>
                                  <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                    2,000.00
                                  </td>
                                </tr>
                                <tr>
                                  <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                    VAT
                                  </td>
                                  <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                    0.00
                                  </td>
                                </tr>
                                <tr>
                                  <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                    Discount
                                  </td>
                                  <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                    200.00
                                  </td>
                                </tr>
                                <tr className=" text-InterfaceTexttitle font14 font-semibold leading-[140%]">
                                  <td className="">Pre Order 1 Total</td>
                                  <td className="">1,800.00</td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <ActivityLogPopup open={activitylog} onClose={() => setActivitylog(false)} />
      <PartnerRemarkPopup open={partnerremark} onClose={() => setPartnerRemark(false)} />
    </div>
  );
}
