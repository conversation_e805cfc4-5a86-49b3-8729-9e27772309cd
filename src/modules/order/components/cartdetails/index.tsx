import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import EditPopup from '../edit-popup';
import { ActivityLogPopup } from '../activitylog';
import { PartnerRemarkPopup } from '../partnerremark';
import { useTranslations } from 'next-intl';

interface cartDetailsProps {
  partnerRemarksBtn?: boolean;
  isEdit?: boolean;
}

export default function CartDetails({ partnerRemarksBtn = true, isEdit = true }: cartDetailsProps) {
  const t = useTranslations();
  const [edit, setEdit] = React.useState<boolean>(false);
  const [partnerremark, setPartnerRemark] = React.useState<boolean>(false);
  const [activitylog, setActivitylog] = React.useState<boolean>(false);
  return (
    <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] bg-interfacesurfacecomponent mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.938vw] mb-[22px] 3xl:mb-[1.25vw]">
      <div className="flex flex-wrap items-center justify-between gap-2 border-b border-b-InterfaceStrokesoft pb-[16px] 3xl:pb-[0.833vw]">
        <div className="text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] text-InterfaceTexttitle font-semibold leading-[140%]">
          {t('cartDetails')}
        </div>
        <div className="flex items-center gap-[10px] 3xl:gap-[0.521vw]">
          {partnerRemarksBtn && (
            <Button
              onClick={() => setPartnerRemark(true)}
              type="button"
              className="graygradientbtn border border-InterfaceStrokesoft text-InterfaceTextdefault px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]"
            >
              <i className="cloud-notepad text-[18px] 3xl:text-[0.938vw]"></i>
              {t('partnerRemarks')}
            </Button>
          )}
          <Button
            onClick={() => setActivitylog(true)}
            type="button"
            className="graygradientbtn border border-InterfaceStrokesoft text-InterfaceTextdefault px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]"
          >
            <i className="cloud-doc1 text-[18px] 3xl:text-[0.938vw]"></i>
            {t('activityLog')}
          </Button>
        </div>
      </div>
      <div className="relative px-[16px] 3xl:px-[0.833vw] py-[8px] 3xl:py-[0.417vw] bg-InterfaceSurfacecomponentmuted mt-[16px] 3xl:mt-[0.833vw]">
        {isEdit ? (
          <span className="absolute right-2 top-2">
            <i
              onClick={() => setEdit(true)}
              className="cloud-edit2 cursor-pointer text-[18px] 3xl:text-[1.042vw]"
            ></i>
          </span>
        ) : null}

        <div className="grid grid-cols-11 gap-[16px] 2xl:gap-[20px] 3xl:gap-[2.333vw]">
          <div className="col-span-5 lg:col-span-2">
            <div className="grid grid-cols-2 gap-1 3xl:gap-[0.208vw] mb-[6px] 3xl:mb-[0.313vw]">
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                {t('cartID')}:
              </div>
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%] truncate">
                CT100001
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1 3xl:gap-[0.208vw] mb-[6px] 3xl:mb-[0.313vw]">
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                {t('quoteID')}:
              </div>
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%] truncate">
                RFQ1001
              </div>
            </div>
          </div>
          <div className="col-span-6 lg:col-span-2">
            <div className="grid grid-cols-2 gap-1 3xl:gap-[0.208vw] mb-[6px] 3xl:mb-[0.313vw]">
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                {t('createdOn')}:
              </div>
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%] truncate">
                1/3/2025
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1 3xl:gap-[0.208vw] mb-[6px] 3xl:mb-[0.313vw]">
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                {t('expiringOn')}:
              </div>
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%] truncate">
                11/3/2025
              </div>
            </div>
          </div>
          <div className="col-span-5 lg:col-span-3">
            <div className="grid grid-cols-3 gap-1 3xl:gap-[0.208vw] mb-[6px] 3xl:mb-[0.313vw]">
              <div className="col-span-1 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                {t('createdBy')}:
              </div>
              <div className="col-span-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%] truncate">
                Jese Leos
              </div>
            </div>
            <div className="grid grid-cols-3 gap-1 3xl:gap-[0.208vw] mb-[6px] 3xl:mb-[0.313vw]">
              <div className="col-span-1 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                {t('cartName')}:
              </div>
              <div className="col-span-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%] truncate">
                {t('sampleCartName')}
              </div>
            </div>
          </div>
          <div className="col-span-6 lg:col-span-4">
            <div className="flex justify-between gap-1 mb-[6px] 3xl:mb-[0.313vw] pr-[18px] 2xl:pr-[22px] 3xl:pr-[1.9vw]">
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                {t('description')}:
              </div>
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%]">
                One description about the products added in the cart and offers
              </div>
            </div>
          </div>
        </div>
      </div>

      <EditPopup
        open={edit}
        onClose={() => {
          setEdit(false);
        }}
      />
      <PartnerRemarkPopup open={partnerremark} onClose={() => setPartnerRemark(false)} />
      <ActivityLogPopup open={activitylog} onClose={() => setActivitylog(false)} />
    </div>
  );
}
