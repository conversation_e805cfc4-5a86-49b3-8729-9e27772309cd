'use client';

import { CommanSheetProps } from '@/types';
import {
  <PERSON>ton,
  SheetClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Textare<PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function PartnerRemarkPopup({ open, onClose }: CommanSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between items-center">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              Partner Remarks{' '}
            </div>

            <SheetClose className="flex justify-between items-center">
              <i className=" cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[24px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer leading-[140%]"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="h-full overflow-y-auto p-[20px] lg:p-[20px] xl:p-[24px] 2xl:p-[24px] 3xl:p-[1.25vw]">
          <div>
            <label
              htmlFor="required-email"
              className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium  "
            >
              Partner Remarks
            </label>
            <div>
              <Textarea
                className="rounded-[2px] border border-InterfaceStrokehard my-[6px] xl:my-[6px] 2xl:my-[6px] 3xl:my-[0.313vw]"
                placeholder="Add your remarks"
              />
              <div className=" text-[16px]  xl:text-[16px] 3xl:text-[0.833vw] font-medium text-InterfaceStrokehard ">
                Max 50 Characters
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose>
                <Button
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[14px]
                3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[0.933vw] font-[400] leading-[16px] flex items-center">
                    Cancel
                  </div>
                </Button>
              </SheetClose>
              <Button
                className="flex applybtn items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] rounded-none"
                size="md"
              >
                <div>
                  <i className="cloud-send text-interfacetextinverse flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                </div>
                <div className=" text-InterfaceTextwhite font-[400] leading-[16px] flex items-center text-[14px] xl:text-[14px] 3xl:text-[0.833vw]">
                  Submit
                </div>
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
