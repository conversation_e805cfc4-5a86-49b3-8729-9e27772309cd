'use client';
import { useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>over,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
  Sheet<PERSON>lose,
  <PERSON>etHeader,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';

import { Sheet, SheetContent } from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import EndDateAlignmentpopup from '@/modules/subscription-licenses/renewal-management/expiring/components/expiring/custom-end-date-popup';
const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SheetProps {
  open: boolean;
  onClose: () => void;
}

export function EndDateAlignment({ open, onClose }: SheetProps) {
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [endAlignmentpopup, setEndAlignmentpopup] = useState(false);

  return (
    <>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} max-w-[700px] sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[36.458vw] p-[0px] hideclose`}
          side={'right'}
        >
          <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[24px] lg-[20px] xl:p-[16px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[18px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                Select Available Promotions
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[24    px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>
          <form>
            <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[140px] 2xl:mt-[180px] 3xl:mt-[10.417vw] mx-[69px] lg:mx-[69px] xl:mx-[120px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
              <div className="flex flex-col justify-center items-center gap-[40px] xl:gap-[30px] 3xl:gap-[2.083vw]">
                <i className="cloud-info2 text-BrandSupport1pure text-[72px] xl:text-[42px] 2xl:text-[50px] 3xl:text-[2.804vw]"></i>
                <div className="flex flex-col justify-center items-center gap-[8px] xl:gap-[6px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[24px] 2xl:text-[30px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                    End Date Alignment
                  </div>
                  <div className="text-InterfaceTextdefault text-[20px] lg:text-[20px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                    Do you want to choose End Date Alignment for the product in your cart?
                  </div>

                  <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                    <PopoverTrigger asChild>
                      <div className="text-InterfaceTextprimary font-normal text-[20px] lg:text-[20px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[1.042vw]">
                        More Info
                      </div>
                    </PopoverTrigger>

                    <PopoverContent className="absolute top-0 right-[-232px] w-[460px] xl:w-[470px] 2xl:w-[480px] 3xl:w-[25.04vw] bg-[#F8FAFC] rounded-[8px] p-4 flex items-start gap-3 shadow-sm border border-[#E5E7EB]">
                      <div>
                        <div className="flex justify-between items-center gap-[8px] 3xl:gap-[0.417vw]">
                          <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                            <i className="cloud-fillinfo text-InterfaceTextdefault text-[18px] xl:text-[16px] 3xl:text-[0.938vw]"></i>
                            <div className="text-InterfaceTextdefault text-[16px] xl:text-[14px] 3xl:text-[0.833vw] font-[600]">
                              Info
                            </div>
                          </div>
                          <i
                            onClick={() => setPopoverOpen(false)}
                            className="cloud-fillcroscircle text-InterfaceTextdefault text-[18px] xl:text-[16px] 3xl:text-[0.938vw]"
                          ></i>
                        </div>
                        <div className="mt-[8px] 3xl:mt-[0.417vw] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%]">
                          You can coterminate your subscription with an existing non-trial CSP NCE
                          subscription or align the end date with the calendar month by choosing an
                          appropriate end date depending on the term duration.
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              {!popoverOpen && (
                <div className="flex flex-col justify-center items-center gap-[16px] xl:gap-[12px] 3xl:gap-[0.833vw] mt-[40px] xl:mt-[30px] 3xl:mt-[2.083vw] text-center">
                  <SheetClose>
                    <Button className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-background bg-[#00953A] hover:bg-[#067532] font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] xl:w-[140px] 2xl:w-[160px] 3xl:w-[9.375vw]"></Button>
                    <div
                      onClick={() => setEndAlignmentpopup(true)}
                      className=" cursor-pointer text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-background bg-[#00953A] hover:bg-[#067532] font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] xl:w-[140px] 2xl:w-[160px] 3xl:w-[9.375vw]"
                    >
                      Yes
                    </div>
                  </SheetClose>
                  <SheetClose>
                    <Button className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] xl:w-[140px] 2xl:w-[160px] 3xl:w-[9.375vw]">
                      No
                    </Button>
                  </SheetClose>
                </div>
              )}
              <EndDateAlignmentpopup
                open={endAlignmentpopup}
                onClose={() => setEndAlignmentpopup(false)}
              />
            </div>
          </form>
        </SheetContent>
      </Sheet>
    </>
  );
}
