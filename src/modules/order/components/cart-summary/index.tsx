'use client';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import { QuatePopup } from '../quotepopup';
import Discountcoupons from '../discountcoupons';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function CartSummary() {
  const t = useTranslations();
  const [quatepopup, setQuatepopup] = React.useState(false);
  return (
    <>
      <div className="flex flex-col gap-6 xl:gap-4 3xl:gap-[1.25vw]">
        <div className="bg-background shadow-sm py-[16px] xl:py-[14px] 3xl:py-[0.833vw] px-[20px] xl:px-[16px] 3xl:px-[1.042vw]">
          <div className="flex justify-between pb-[16px] xl:pb-[14px] 3xl:pb-[0.833vw]">
            <h3 className="text-InterfaceTexttitle text-[18px] xl:text-[16px] 3xl:text-[0.938vw] font-[600]">
              {t('cartSummary')}
            </h3>
            <h3 className="text-InterfaceTexttitle text-[18px] xl:text-[16px] 3xl:text-[0.938vw] font-[600]">
              {t('usd')}
            </h3>
          </div>
          <div className="bg-InterfaceSurfacecomponentmuted py-[6px] px-[8px]">
            <div className="text-InterfaceTextdefault text-[16px] xl:text-[14px] 3xl:text-[0.833vw] font-[400]">
              <div className="flex justify-between border-b border-InterfaceStrokesoft py-3 xl:py-[8px] 3xl:py-[0.625vw]">
                <span>{t('subTotal')}</span>
                <span>8,000.00</span>
              </div>
              <div className="flex justify-between border-b border-InterfaceStrokesoft py-3 xl:py-[8px] 3xl:py-[0.625vw]">
                <span>{t('VAT')}</span>
                <span>0.00</span>
              </div>
              <div className="flex justify-between border-b border-InterfaceStrokesoft py-3 xl:py-[8px] 3xl:py-[0.625vw]">
                <span>{t('discounts')}</span>
                <span>0.00</span>
              </div>
              <div className="flex justify-between py-3 xl:py-[8px] 3xl:py-[0.625vw]">
                <span>{t('totalOrder')}</span>
                <span className="font-bold">8,000.00</span>
              </div>
            </div>
          </div>
        </div>
        <Discountcoupons />
        <div className="flex flex-col gap-[4px]">
          <Link
            href={'/order/checkout'}
            className="border border-BrandPrimary50new text-[14px] 3xl:text-[0.729vw] rounded-none py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw font-[500] text-interfacetextinverse bg-BrandPrimarypurenew text-center"
          >
            {t('checkOut')}
          </Link>
          <Button className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] font-[500]">
            {t('continuePurchase')}
          </Button>
          <Button
            onClick={() => setQuatepopup(true)}
            className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] font-[500]"
          >
            {t('requestAQuote')}
          </Button>
        </div>
      </div>
      <QuatePopup open={quatepopup} onClose={() => setQuatepopup(false)} />
    </>
  );
}
