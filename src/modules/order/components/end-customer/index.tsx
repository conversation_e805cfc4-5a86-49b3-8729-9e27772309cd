'use client';

import { ActivitylogSheetProps } from '@/types';
import { Button, SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';
import { Sheet, SheetContent, SheetTitle } from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import { useState } from 'react';
import { SelectEndCustomerPopup } from '../selectendcustomer';
import AddNewCustomerPopup from '@/modules/marketplace/components/add-new-customer-popup';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
interface props extends ActivitylogSheetProps {
  addNewCustomer: () => void;
}
export function EndcustomerPopup({ open, onClose, addNewCustomer }: props) {
  // const t = useTranslations();
  const [addNewCustomerPopupOpen, setAddNewCustomerPopupOpen] = useState(false);
  const [selectEndCustomer, setSelectEndCustomer] = useState(false);
  const [endcustomer, setEndcustomer] = useState('');
  const endCustomerOptions: Select2Option[] = [
    { label: 'Alpha Systems - Binghati Towers, 201, AI Mankhool, Dubai, UAE', value: 'Binghati' },
    { label: 'Alpha Systems - ABC Towers, 201, AI Mankhool, Dubai, UAE', value: 'ABC' },
    { label: 'Alpha Systems - 123 Block, 201, AI Mankhool, Dubai, UAE', value: '123' },
    { label: 'Alpha Systems - SkyHigh Towers, 201, AI Mankhool, Dubai, UAE', value: 'SkyHigh' },
  ];

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] h-screen`}
        side={'right'}
      >
        <div className="h-screen overflow-y-auto p-[20px] lg:p-[20px] xl:p-[24px] 2xl:p-[24px] 3xl:p-[1.25vw]">
          <div className="space-y-[24px] flex flex-col items-center justify-center h-full">
            <i className="cloud-user1 text-[45px] xl:text-[45px] 2xl:text-[60px] 3xl:text-[3.125vw] text-[#1570EF]"></i>
            <div className=" text-InterfaceTexttitle text-[36px] xl:text-[26px] 2xl:text-[34px] 3xl:text-[1.875vw] font-normal leading-[140%]">
              Select End Customer
            </div>
            <Select2
              options={endCustomerOptions}
              value={endcustomer}
              placeholder="--Select End Customer--"
              onChange={(val) => setEndcustomer(val as string)}
            />

            <div className="flex items-center justify-center gap-[16px] xl:gap-[16px] 2xl:gap-[16px] 3xl:gap-[0.833vw] ">
              <SheetClose>
                <Button className="text-[#3C4146] bg-transparent border border-[#E5E7EB] px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.833vw]">
                  <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.781vw]"></i>
                  Cancel
                </Button>
              </SheetClose>
              <Button
                onClick={() => {
                  setSelectEndCustomer(true);
                  onClose();
                }}
                className=" applybtn px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.781vw] text-white"
              >
                <i className="cloud-refresh text-white  text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.781vw]"></i>
                Select
              </Button>
            </div>
            <div className="flex items-center gap-[10px]">
              <div className="bg-InterfaceStrokesoft w-[91px] h-[1px]"></div>
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal">
                or
              </div>
              <div className="bg-InterfaceStrokesoft w-[91px] h-[1px]"></div>
            </div>
            <div className="">
              <Button
                onClick={addNewCustomer}
                className="text-[#3C4146] bg-transparent border border-[#E5E7EB] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.833vw] bgcolor "
              >
                <i className="cloud-plus-square text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.781vw]"></i>
                Add End Customer
              </Button>
            </div>
          </div>
        </div>
        {selectEndCustomer && (
          <SelectEndCustomerPopup
            open={selectEndCustomer}
            onClose={() => setSelectEndCustomer(false)}
          />
        )}
        <AddNewCustomerPopup
          open={addNewCustomerPopupOpen}
          onClose={() => setAddNewCustomerPopupOpen(false)}
        />
      </SheetContent>
    </Sheet>
  );
}
