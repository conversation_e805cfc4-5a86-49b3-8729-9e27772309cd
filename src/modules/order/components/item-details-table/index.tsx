'use client';
import React, { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import { Button, Input } from '@redington-gulf-fze/cloudquarks-component-library';
import EligibleOffersPopup from '../eligibleofferspopup';
// import Image from 'next/image';
import AvailablePromotionsPopup from '../availablepromorionpopup';
import { useTranslations } from 'next-intl';

type User = {
  id: string;
  productname: string;
  brand: string;
  segment: string;
  term: string;
  billtype: string;
  currency: string;
  price: string;
  quantity: string;
  subtotal: string;
};

type Items = {
  id: string;
  productname: string;
  currency: string;
  listprice: string;
  discount: string;
  unitprice: string;
  quantity: string;
  subtotal: string;
  vat: string;
  estvat: string;
  netprice: string;
};

export default function ItemDetailsTable() {
  const t = useTranslations();
  const initialData: User[] = [
    {
      id: '1',
      productname: 'Google Workspace Enterprise Essentials Plus',
      brand: 'Google',
      segment: 'Commercial',
      term: '1 Year',
      billtype: 'Monthly',
      currency: 'USD',
      price: '500.00',
      quantity: '4',
      subtotal: '500.00',
    },
    {
      id: '2',
      productname: 'Microsoft 365 E5 Security',
      brand: 'Microsoft',
      segment: 'Enterprise',
      term: '1 Years',
      billtype: 'Monthly',
      currency: 'USD',
      price: '1,500.00',
      quantity: '1',
      subtotal: '1,500.00',
    },
    {
      id: '3',
      productname: 'Amazon Web Services',
      brand: 'Amazon',
      segment: '-',
      term: '-',
      billtype: 'Monthly',
      currency: 'USD',
      price: '1.00',
      quantity: '1',
      subtotal: '1,000.00',
    },
    {
      id: '4',
      productname: 'Google Workspace Enterprise Plus',
      brand: 'Google',
      segment: 'Commercial',
      term: '1 Year',
      billtype: 'Monthly',
      currency: 'USD',
      price: '2,000.00',
      quantity: '1',
      subtotal: '2,000.00',
    },
    {
      id: '5',
      productname: 'Microsoft Entra ID P2',
      brand: 'Google',
      segment: 'Commercial',
      term: '1 Year',
      billtype: 'Monthly',
      currency: 'USD',
      price: '2,000.00',
      quantity: '1',
      subtotal: '2,000.00',
    },
  ];

  const itemdetailsData: Items[] = [
    {
      id: '1',
      productname: 'Google Workspace Enterprise Essentials Plus',
      currency: 'USD',
      listprice: '500.00',
      discount: '10.00',
      unitprice: '190.00',
      quantity: '4',
      subtotal: '790.00',
      vat: '0.00',
      estvat: '0.00',
      netprice: '790.00',
    },
    {
      id: '2',
      productname: 'Microsoft 365 E5 Security',
      currency: 'USD',
      listprice: '500.00',
      discount: '10.00',
      unitprice: '190.00',
      quantity: '4',
      subtotal: '790.00',
      vat: '0.00',
      estvat: '0.00',
      netprice: '790.00',
    },
    {
      id: '3',
      productname: 'Amazon Web Services',
      currency: 'USD',
      listprice: '1.00',
      discount: '10.00',
      unitprice: '190.00',
      quantity: '1',
      subtotal: '1.00',
      vat: '0.00',
      estvat: '0.00',
      netprice: '790.00',
    },
    {
      id: '4',
      productname: 'Google Workspace Enterprise Plus',
      currency: 'USD',
      listprice: '500.00',
      discount: '10.00',
      unitprice: '190.00',
      quantity: '4',
      subtotal: '790.00',
      vat: '0.00',
      estvat: '0.00',
      netprice: '790.00',
    },
  ];

  const [eligibleofferspopup, setEligibleofferspopup] = useState(false);
  const [promotionpopup, setPromotionpopup] = useState(false);

  const [show, setShow] = useState(false);
  const toggleDetails = () => setShow((prev) => !prev);

  const itemdetailsColumns: ColumnDef<Items>[] = [
    {
      accessorKey: 'productname',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('productName')}
        </div>
      ),
      cell: ({ row }) => (
        <>
          <div className="text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold flex flex-wrap gap-[6px] 3xl:gap-[0.313vw]">
            {row.original.productname}
            <span
              onClick={toggleDetails}
              className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] font-[500] cursor-pointer"
            >
              {' '}
              {show ? 'View Less' : 'View More'}
            </span>
          </div>
          {show && (
            <div className="mt-1 text-[12px] 3xl:text-[0.625vw] flex gap-4 3xl:gap-[0.833vw]">
              <div>
                <div className="font-normal text-InterfaceTextdefault">
                  <span className="font-medium text-InterfaceTexttitle">Brand:</span> Google
                </div>
                <div className="font-normal text-InterfaceTextdefault">
                  <span className="font-medium text-InterfaceTexttitle">Term:</span> 1 Year
                </div>
              </div>
              <div>
                <div className="font-normal text-InterfaceTextdefault">
                  <span className="font-medium text-InterfaceTexttitle">Segment:</span> Commercial
                </div>
                <div className="font-normal text-InterfaceTextdefault">
                  <span className="font-medium text-InterfaceTexttitle">Bill Type:</span> Monthly
                </div>
              </div>
            </div>
          )}
          <Button
            onClick={() => setEligibleofferspopup(true)}
            className="mt-2 flex items-center gap-1 text-sm xl:text-[12px] 3xl:text-[0.729vw] seobtnborder text-InterfaceTextdefault px-3 py-1 rounded-none bg-BrandHighlight50 font-medium"
          >
            <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
            {t('selectEligibleOffers')}
            {/* <span>Applied Coupon: AYETL123THJ</span> */}
            {/* <i className='cloud-fillcroscircle text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 3xl:text-[0.833vw]'></i> */}
          </Button>
          <Button
            onClick={() => setPromotionpopup(true)}
            className="mt-2 flex items-center gap-2 text-sm xl:text-[12px] 3xl:text-[0.729vw] sapbtnborder text-InterfaceTextdefault px-3 py-1 rounded-none bg-BrandSupport150 font-medium"
          >
            <i className="cloud-promotion-tag text-InterfaceTextprimary text-[16px] xl:text-[12px] 3xl:text-[0.833vw]"></i>
            {t('selectAvailablePromotions')}
            {/* <span>Applied Promotion: ASETS12453</span> */}
            {/* <Image
            src="/images/svg/edit-2.svg"
            width={16}
            height={16}
            className="h-[16px] xl:h-[14px] 3xl:h-[0.833vw] w-[16px] xl:w-[14px] 3xl:w-[0.833vw]"
            alt="edit"
          /> */}
          </Button>
        </>
      ),
    },
    {
      accessorKey: 'currency',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('currency')}
        </div>
      ),
      cell: ({ row }) => (
        <div className=" w-[100px] text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.currency}
        </div>
      ),
    },
    {
      accessorKey: 'listprice',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('listPrice')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.listprice}
        </div>
      ),
    },
    {
      accessorKey: 'discount',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('discount')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.discount}
        </div>
      ),
    },
    {
      accessorKey: 'unitprice',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('unitPrice')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.unitprice}
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('quantity')}
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex justify-center items-center h-full">
            <div className="flex items-center justify-center custborder rounded-none px-3 gap-3 bg-white">
              <div onClick={() => updateQuantity(item.id, -1)} className="cursor-pointer">
                <span className="text-[18px] text-InterfaceTextdefault font-[400]">-</span>
              </div>
              <Input
                value={row.original.quantity}
                readOnly
                className="w-[40px] h-[32px] text-center text-InterfaceTextdefault font-[500] border-none focus:ring-0 shadow-none"
              />
              <div onClick={() => updateQuantity(item.id, 1)} className="cursor-pointer">
                <span className="text-[18px] text-InterfaceTextdefault font-[400]">+</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'subtotal',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('subTotal')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.subtotal}
        </div>
      ),
    },
    {
      accessorKey: 'vat',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('VAT')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.vat}
        </div>
      ),
    },
    {
      accessorKey: 'estvat',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('estimatedVAT')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.estvat}
        </div>
      ),
    },
    {
      accessorKey: 'netprice',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          Net Price
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-sm text-InterfaceTextdefault text-center font-[500]">
          {row.original.netprice}
        </div>
      ),
    },
    {
      id: 'actions',
      header: () => (
        <>
          <div className="text-center">
            <i className=" cloud-trash text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextprimary"></i>
          </div>
        </>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex justify-center items-center h-full">
            <i
              onClick={() => deleteRow(item.id)}
              className="cloud-trash p-[10px] 3xl:p-[0.525vw] text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] text-center text-InterfaceTextsubtitle"
            ></i>
          </div>
        );
      },
    },
  ];

  const [data, setData] = useState<User[]>(initialData);

  const updateQuantity = (id: string, delta: number) => {
    setData((prevData) =>
      prevData.map((item) => {
        if (item.id === id) {
          const currentQuantity = parseInt(item.quantity) || 0;
          const newQuantity = Math.max(currentQuantity + delta, 0);
          const pricePerUnit = parseFloat(item.price) || 0;
          const newSubtotal = (pricePerUnit * newQuantity).toFixed(2);
          return {
            ...item,
            quantity: newQuantity.toString(),
            subtotal: newSubtotal,
          };
        }
        return item;
      })
    );
  };

  const deleteRow = (id: string) => {
    setData((prevData) => prevData.filter((item) => item.id !== id));
  };

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('productName')}
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div>
            <div className="text-[14px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
              {item.productname}
            </div>
            <div className="flex items-center gap-[12px] 3xl:gap-[0.625vw]">
              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                Brand: <span className="text-InterfaceTextdefault font-normal">{item.brand}</span>
              </div>
              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                Segment:{' '}
                <span className="text-InterfaceTextdefault font-normal">{item.segment}</span>
              </div>
              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                Term: <span className="text-InterfaceTextdefault font-normal">{item.term}</span>
              </div>
              <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                Bill Type:{' '}
                <span className="text-InterfaceTextdefault font-normal">{item.billtype}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'currency',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('currency')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.currency}
            readOnly
            className="w-[100px] text-center text-InterfaceTextdefault font-[500] bg-transparent"
          />
        </div>
      ),
    },
    {
      accessorKey: 'price',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('price')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.price}
            readOnly
            className="w-[100px] text-center text-InterfaceTextdefault font-[500] bg-transparent"
          />
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('quantity')}
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex justify-center items-center h-full">
            <div className="flex items-center justify-center custborder rounded-none px-3 gap-3 bg-white">
              <div onClick={() => updateQuantity(item.id, -1)} className="cursor-pointer">
                <span className="text-[18px] text-InterfaceTextdefault font-[400]">-</span>
              </div>
              <Input
                value={item.quantity}
                readOnly
                className="w-[40px] h-[32px] text-center text-InterfaceTextdefault font-[500] border-none focus:ring-0 shadow-none"
              />
              <div onClick={() => updateQuantity(item.id, 1)} className="cursor-pointer">
                <span className="text-[18px] text-InterfaceTextdefault font-[400]">+</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'subtotal',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('subTotal')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.subtotal}
            readOnly
            className="w-[100px] text-center text-InterfaceTextdefault font-[500] bg-transparent"
          />
        </div>
      ),
    },
    {
      id: 'actions',
      header: () => (
        <>
          <div className="flex gap-2 items-center text-[12px] xl:text-[14px] text-center text-InterfaceTextprimary">
            <i className="cloud-trash text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextprimary"></i>
            {t('deleteAll')}
          </div>
        </>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex justify-center items-center h-full">
            <i
              onClick={() => deleteRow(item.id)}
              className="cloud-trash p-[10px] 3xl:p-[0.525vw] text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] text-center text-InterfaceTextsubtitle"
            ></i>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className="bg-[#FFF] rounded-[4px]">
        <div className="border-b border-InterfaceStrokesoft px-[18px] xl:px-[18px] 2xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw]">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
            {t('itemDetails')}
          </div>
        </div>
        <div className="product-table custtableborder custtable custtable1 px-[16px] 3x:px-[0.833vw] pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
            <DataTable data={data} columns={notificationColumns} />
          </div>
          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
            <DataTable data={itemdetailsData} columns={itemdetailsColumns} />
          </div>
        </div>
      </div>

      <EligibleOffersPopup
        open={eligibleofferspopup}
        onClose={() => setEligibleofferspopup(false)}
      />

      <AvailablePromotionsPopup open={promotionpopup} onClose={() => setPromotionpopup(false)} />
    </>
  );
}
