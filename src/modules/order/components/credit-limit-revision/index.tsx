'use client';

import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import { CommanSheetProps } from '@/types';
import {
  Button,
  Input,
  Label,
  SheetClose,
  SheetFooter,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import Image from 'next/image';
import { useState } from 'react';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function CreditLimitRevisionPopup({ open, onClose }: CommanSheetProps) {
  const [requestresion, setRequestResion] = useState('');
  const requestresionOptions: Select2Option[] = [
    { label: 'AWS - Tenant 1 (1940HSDdfdkojkI76H)', value: ' Tenant 1' },
    { label: 'AWS - Tenant 2 (1940HSDdfdkojkI76H)', value: 'Tenant 2' },
    { label: 'AWS - Tenant 3 (1940HSDdfdkojkI76H)', value: 'Tenant 3' },
    { label: 'AWS - Tenant 4 (1940HSDdfdkojkI76H)', value: 'Tenant 4' },
  ];
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTrigger></SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              Credit Limit Revision -New Request
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[20px] lg:text-[20px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="relative h-[465px] xl:h-[465px] 2xl:h-[654px] 3xl:h-[36.308vw] overflow-y-auto ">
          <div className="p-[20px] xl:p-[24px] 2xl:p-[24px] 3xl:p-[1.25vw] space-y-[16px] xl:space-y-[16px] 2xl:space-y-[16px] 3xl:space-y-[0.833vw]">
            <div className="grid gap-1.5">
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Assigned Credit Limit (Existing)
              </Label>
              <Input
                type="text"
                id="email"
                placeholder="USD 2,00,000.00"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                disabled
              />
            </div>

            <div className="grid gap-1.5">
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Available Credit Limit
              </Label>
              <Input
                type="text"
                id="email"
                placeholder="USD 5,000,000.00"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                disabled
              />
            </div>

            <div className="grid gap-1.5">
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Requested - Assigned Credit Limit <span className="text-[red]">*</span>
              </Label>
              <Input
                type="text"
                id="email"
                placeholder="Enter the Value"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
              />
            </div>

            <div className="grid gap-1.5">
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] "
              >
                Request Reason <span className="text-[red]">*</span>
              </Label>
              <div className="">
                <Select2
                  options={requestresionOptions}
                  value={requestresion}
                  placeholder="-- Select Request Reason --"
                  onChange={(val) => setRequestResion(val as string)}
                />
              </div>
            </div>

            <div>
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] "
              >
                Partner Remarks <span className="text-[red]">*</span>
              </Label>

              <div>
                <Textarea
                  className="rounded-[2px] border border-InterfaceStrokehard my-[6px] xl:my-[6px] 2xl:my-[6px] 3xl:my-[0.313vw]"
                  placeholder="Enter Remarks"
                />
                <div className=" text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-normal text-InterfaceTextsubtitle leading-[140%] ">
                  Character Limit: 50
                </div>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="w-full absolute bottom-0">
          <div className="w-full bg-white">
            <div className="flex justify-end items-center gap-[8px] 3xl:gap-[0.417vw] px-[18px] xl:px-[18px] 2xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw] border-t border-[#BECDE3]">
              <Button className="text-[#3C4146] cancelbtn px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                <i className="cloud-circletick text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                Cancel
              </Button>

              <Button className="border applybtn text-[#FFF]  px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                {/* <i className="cloud-fillnotepad text-[#fff] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i> */}
                <Image src="/images/send-2.svg" width={20} height={20} alt="" />
                Submit
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
