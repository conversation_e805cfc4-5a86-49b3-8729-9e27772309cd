'use client';
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  SheetClose,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { TermsSheetProps } from '@/types/components';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { EndDateAlignment } from '../enddatealignmentpopup';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface ChangePasswordProps extends TermsSheetProps {
  userData?: {
    userId?: string;
  };
}

export default function AvailablePromotionsPopup({ open, onClose }: ChangePasswordProps) {
  const [promotionpopup, set<PERSON>romotionpopup] = useState(false);
  const t = useTranslations();

  return (
    <>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} flex flex-col h-full max-w-[600px] sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[36.458vw] p-[0px] gap-0 hideclose`}
          side={'right'}
        >
          {/* Header */}
          <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[24px] lg-[20px] xl:p-[16px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[18px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('selectAvailablePromotions')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[24    px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>
          <form className="flex-grow flex flex-col p-8 xl:p-6 3xl:p-[1.667vw] bg-[#F6F7F8]">
            <div className="p-4 3xl:p-[0.833vw] border border-InterfaceStrokesoft bg-background flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
              <div>
                <div className="text-[20px] xl:text-[16px] 3xl:text-[1.042vw] font-semibold text-black">
                  Dynamics 365 E3 (No Teams)-Microsoft 365 E3 15% Promo (2024-2025)
                </div>
                <div className="text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTextdefault font-normal">
                  ID: 39NFJQT206QX-0002-39NFJQT1Q5KK
                </div>
                <div className="text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTextsubtitle font-normal">
                  Do more with Microsoft 365 E3 Promo
                </div>
              </div>

              <div>
                <table className="text-sm 3xl:text-[0.833vw] text-left">
                  <thead className="text-InterfaceTextsubtitle font14font-normal">
                    <tr>
                      <th className="w-[25%] font-normal">Segment</th>
                      <th className="w-[10%] font-normal">Term</th>
                      <th className="w-[10%] font-normal">Bill Type</th>
                      <th className="w-[10%] font-normal">Discount</th>
                      <th className="w-[20%] font-normal">End Date</th>
                      <th className="w-[10%] font-normal">Action</th>
                    </tr>
                  </thead>
                  <tbody className="text-InterfaceTextdefault">
                    <tr className="border-t">
                      <td className="pt-[16px] pb-[8px] font-medium">Commercial</td>
                      <td className="pt-[16px] pb-[8px] font-medium">1 Year</td>
                      <td className="pt-[16px] pb-[8px] font-medium">Monthly</td>
                      <td className="pt-[16px] pb-[8px] font-medium">15%</td>
                      <td className="pt-[16px] pb-[8px] font-medium">2025-06-30</td>
                      <td className="pt-[16px] pb-[8px]">
                        <button
                          type="button"
                          className="py-[10px] xl:py-[8px] 3xl:py-[0.521vw] px-[12px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#067532] bg-BrandPrimarypurenew text-[#FFF] text-[14px] xl:text-[12px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[500] leading-[100%]"
                        >
                          Apply
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <SheetFooter className="absolute bottom-0 w-full right-0 bg-InterfaceTextwhite border-t border-InterfaceStrokedefault">
              <div className="p-[20px] xl:p-[16px] 3xl:p-[1.25vw] w-full flex justify-end items-center gap-[16px] 3xl:gap-[0.833vw]">
                <Button
                  type="button"
                  className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    onClose(false);
                  }}
                >
                  <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                  Cancel
                </Button>

                <Button
                  type="button"
                  className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#067532] bg-BrandPrimarypurenew text-[#FFF] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                  // variant="outline"
                  size="sm"
                  onClick={() => {
                    setPromotionpopup(true);
                    onClose(false);
                  }}
                >
                  <Image
                    src="/images/svg/send.svg"
                    width={16}
                    height={16}
                    className="h-[16px] xl:h-[14px] 3xl:h-[0.833vw] w-[16px] xl:w-[14px] 3xl:w-[0.833vw]"
                    alt="edit"
                  />
                  Apply Selected Promotions
                </Button>
              </div>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>
      <EndDateAlignment open={promotionpopup} onClose={() => setPromotionpopup(false)} />
    </>
  );
}
