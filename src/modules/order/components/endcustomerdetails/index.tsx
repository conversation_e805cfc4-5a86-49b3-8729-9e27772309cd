import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React, { useState } from 'react';
import { EndcustomerPopup } from '../end-customer';
import { CreditLimitRevisionPopup } from '../credit-limit-revision';
import { useTranslations } from 'next-intl';
import AddNewCustomerPopup from '@/modules/marketplace/components/add-new-customer-popup';

interface EndCustomerDetailsProps {
  isEditEndCustomer?: boolean;
  isSectondaryValidation?: boolean;
  isAvailabelCredit?: boolean;
}

export default function EndCustomerDetails({
  isEditEndCustomer,
  isSectondaryValidation,
  isAvailabelCredit,
}: EndCustomerDetailsProps) {
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const [open2, setOpen2] = React.useState(false);
  const [addNewCustomerPopupOpen, setAddNewCustomerPopupOpen] = useState(false);
  const [endcust, setEndcust] = React.useState<boolean>(false);
  const [secondaryValidationShow, setSecondaryValidationShow] = React.useState<boolean>(false);
  const [availableCredit, setAvailableCredit] = React.useState<boolean>(false);
  const [creditLimitRevision, setCreditLimitRevision] = React.useState<boolean>(false);
  return (
    <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] bg-interfacesurfacecomponent mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.938vw] mb-[22px] 3xl:mb-[1.25vw]">
      <div className="flex flex-wrap items-center justify-between gap-2 pb-[16px] 3xl:pb-[0.833vw]">
        <div className="text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] text-InterfaceTexttitle font-semibold leading-[140%]">
          {t('endCustomerDetails')}
        </div>
        <div className="flex items-center gap-[10px] 3xl:gap-[0.521vw]">
          <Button
            type="button"
            onClick={() => setEndcust(true)}
            className="graygradientbtn border border-InterfaceStrokesoft text-InterfaceTextdefault px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]"
          >
            {isEditEndCustomer ? (
              <>
                <i className="cloud-edit2 text-[14px] 3xl:text-[0.625vw]"></i>
                <span>Edit End Customer</span>
              </>
            ) : (
              <>
                <i className="cloud-Add text-[11px] 3xl:text-[0.573vw]"></i>
                <span>{t('selectEndCustomer')}</span>
              </>
            )}
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-[16px] 3xl:gap-[0.833vw]">
        <div className="border border-InterfaceStrokesoft px-[8px] 3xl:px-[0.417vw]">
          <div className="space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-semibold leading-[140%] py-[6px] 3xl:py-[0.313vw] border-b border-b-InterfaceStrokesoft">
              {t('customerDetails')}
            </div>
            <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%]">
              Alpha Systems - Binghati Towerrs, 201, AI Mankhool, Dubai, UAE
            </div>
            {/* add bellow component when data not available */}
            {/* <NoDataAvailable /> */}
          </div>
        </div>
        <div className="border border-InterfaceStrokesoft px-[8px] 3xl:px-[0.417vw]">
          <div className="space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="flex items-center justify-between py-[6px] 3xl:py-[0.313vw] border-b border-b-InterfaceStrokesoft">
              <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-semibold leading-[140%]">
                {t('organizationTenants')}
              </div>
              <Popover open={open2} onOpenChange={setOpen2}>
                <PopoverTrigger asChild>
                  <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextprimary font-medium leading-[140%] cursor-pointer">
                    +2 {t('more')}
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-[433px] 3xl:w-[22.552vw] p-0 bg-[#fff]">
                  <div className="flex items-center justify-between gap-2 border-b border-b-InterfaceStrokedefault px-[28px] 3xl:px-[1.458vw] py-[20px] 3xl:py-[1.042vw]">
                    <div className="text-[16px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[1.042vw] text-InterfaceTexttitle font-bold leading-[140%]">
                      {t('organizationTenants')}
                    </div>
                    <div
                      className="cursor-pointer flex items-center"
                      onClick={() => setOpen2(false)}
                    >
                      <i className="cloud-closecircle text-Interfacefeedbackerror700 text-[22px] 3xl:text-[1.146vw]"></i>
                    </div>
                  </div>

                  <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                    <div className="px-[28px] 3xl:px-[1.458vw] py-[10px] 3xl:py-[0.521vw] border-b border-b-InterfaceStrokesoft">
                      <div className="text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault font-semibold leading-[140%]">
                        {t('aws')}
                      </div>
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%]">
                        AWS - Tenant 2 (1940HSDdfdkojkI76H)
                      </div>
                    </div>
                    <div className="px-[28px] 3xl:px-[1.458vw] py-[10px] 3xl:py-[0.521vw] border-b border-b-InterfaceStrokesoft">
                      <div className="text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault font-semibold leading-[140%]">
                        {t('microsoft')}
                      </div>
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%]">
                        Microsoft - Tenant 2 (1940HSDdfdkojkI76H)
                      </div>
                    </div>
                    <div className="px-[28px] 3xl:px-[1.458vw] py-[10px] 3xl:py-[0.521vw] border-b border-b-InterfaceStrokesoft">
                      <div className="text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault font-semibold leading-[140%]">
                        {t('oracle')}
                      </div>
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%]">
                        Oracle - Tenant 2 (1940HSDdfdkojkI76H)
                      </div>
                    </div>
                    <div className="px-[28px] 3xl:px-[1.458vw] py-[10px] 3xl:py-[0.521vw] border-b border-b-InterfaceStrokesoft">
                      <div className="text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault font-semibold leading-[140%]">
                        Dell
                      </div>
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%]">
                        Dell - Tenant 2 (1940HSDdfdkojkI76H)
                      </div>
                    </div>
                    <div className="px-[28px] 3xl:px-[1.458vw] py-[10px] 3xl:py-[0.521vw] border-b border-b-InterfaceStrokesoft">
                      <div className="text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault font-semibold leading-[140%]">
                        {t('oracle')}
                      </div>
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%]">
                        Oracle - Tenant 2 (1940HSDdfdkojkI76H)
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
            <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
              <div className="grid grid-cols-5 gap-1">
                <div className="col-span-1 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%]">
                  {t('aws')}
                </div>
                <div
                  title="AWS - Tenant 2 (1940HSDdfdkojkI76H)"
                  className="col-span-4 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%] truncate"
                >
                  <span className="text-InterfaceTextdefault">:</span> AWS - Tenant 2
                  (1940HSDdfdkojkI76H)
                </div>
              </div>
              <div className="grid grid-cols-5 gap-1">
                <div className="col-span-1 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%]">
                  {t('microsoft')}
                </div>
                <div
                  title="Microsoft - Tenant 3 (1940HSDdfdkojkI76H)"
                  className="col-span-4 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%] truncate"
                >
                  <span className="text-InterfaceTextdefault">:</span> Microsoft - Tenant 3
                  (1940HSDdfdkojkI76H)
                </div>
              </div>
              <div className="grid grid-cols-5 gap-1">
                <div className="col-span-1 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%]">
                  {t('oracle')}
                </div>
                <div
                  title="Oracle - Tenant 1 (1940HSDdfdkojkI76H)"
                  className="col-span-4 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%] truncate"
                >
                  <span className="text-InterfaceTextdefault">:</span> Oracle - Tenant 1
                  (1940HSDdfdkojkI76H)
                </div>
              </div>
            </div>
            {/* add bellow component when data not available */}
            {/* <NoDataAvailable /> */}
          </div>
        </div>
        <div className="border border-InterfaceStrokesoft px-[8px] 3xl:px-[0.417vw]">
          <div className="space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-semibold leading-[140%] py-[6px] 3xl:py-[0.313vw] border-b border-b-InterfaceStrokesoft">
              {t('validation')}
            </div>
            <div className="grid grid-cols-3 gap-[8px] 3xl:gap-[0.417vw] pb-[6px] 3xl:pb-[0.313vw]">
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <div className="bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[10px] 2xl:px-[13px] 3xl:px-[0.833vw] py-[6px] 3xl:py-[0.313vw] group cursor-pointer">
                    <div className="text-[12px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal group-hover:font-medium leading-[140%] text-center mb-[8px] 3xl:mb-[0.521vw]">
                      {t('preliminaryValidations')}
                    </div>
                    {open === true ? (
                      <div
                        className={`${open === true ? 'group-hover:block !block' : ''} group-hover:block hidden text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextprimary font-medium leading-[140%] cursor-pointer text-center relative`}
                      >
                        {t('view')}
                      </div>
                    ) : (
                      <>
                        <div className="group-hover:block hidden text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextprimary font-medium leading-[140%] cursor-pointer text-center relative">
                          {t('view')}
                        </div>
                        <div className="group-hover:hidden flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandSupport2500 font-medium leading-[140%]">
                          <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                          {t('pass')}
                        </div>
                      </>
                    )}
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-[320px] 3xl:w-[16.667vw] p-[16px] 3xl:p-[0.833vw] bg-[#fff]">
                  <div className="flex items-center justify-between gap-2">
                    <div className="text-[14px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold leading-[140%]">
                      {t('preliminaryValidations')}
                    </div>
                    <div
                      className="cursor-pointer flex items-center"
                      onClick={() => setOpen(false)}
                    >
                      <i className="cloud-closecircle text-Interfacefeedbackerror700 text-[22px] 3xl:text-[1.146vw]"></i>
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-[20px] 3xl:mt-[1.042vw]">
                    <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-medium leading-[140%]">
                      {t('overallStatus')}
                    </div>
                    <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-medium leading-[140%] bg-BrandHighlight100 px-[10px] 3xl:px-[0.521vw] py-[6px] 3xl:py-[0.313vw]">
                      <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                      {t('pass')}
                    </div>
                  </div>
                  <div className="space-y-[4px] 3xl:space-y-[0.208vw] mt-[12px] 3xl:mt-[0.625vw]">
                    <div className="flex items-center justify-between">
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                        {t('generalT&Cs')}
                      </div>
                      <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-normal leading-[140%] border border-BrandHighlightpure px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                        <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        {t('pass')}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                        {t('partnerReadiness')}
                      </div>
                      <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-normal leading-[140%] border border-BrandHighlightpure px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                        <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        {t('pass')}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                        {t('vendorT&C')}
                      </div>
                      <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-normal leading-[140%] border border-BrandHighlightpure px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                        <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        {t('pass')}
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
              {isSectondaryValidation === true ? (
                <Popover open={secondaryValidationShow} onOpenChange={setSecondaryValidationShow}>
                  <PopoverTrigger asChild>
                    <div className="bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[10px] 2xl:px-[13px] 3xl:px-[0.833vw] py-[6px] 3xl:py-[0.313vw] group cursor-pointer">
                      <div className="text-[12px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal group-hover:font-medium leading-[140%] text-center mb-[8px] 3xl:mb-[0.521vw]">
                        {t('secondaryValidations')}
                      </div>
                      {secondaryValidationShow === true ? (
                        <div
                          className={`${secondaryValidationShow === true ? 'group-hover:block !block' : ''} group-hover:block hidden text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextprimary font-medium leading-[140%] cursor-pointer text-center relative`}
                        >
                          {t('view')}
                        </div>
                      ) : (
                        <>
                          <div className="group-hover:block hidden text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextprimary font-medium leading-[140%] cursor-pointer text-center relative">
                            {t('view')}
                          </div>
                          <div className="group-hover:hidden flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandSupport2500 font-medium leading-[140%]">
                            <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                            {t('pass')}
                          </div>
                        </>
                      )}
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="w-[320px] 3xl:w-[16.667vw] p-[16px] 3xl:p-[0.833vw] bg-[#fff]">
                    <div className="flex items-center justify-between gap-2">
                      <div className="text-[14px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold leading-[140%]">
                        {t('secondaryValidations')}
                      </div>
                      <div
                        className="cursor-pointer flex items-center"
                        onClick={() => setSecondaryValidationShow(false)}
                      >
                        <i className="cloud-closecircle text-Interfacefeedbackerror700 text-[22px] 3xl:text-[1.146vw]"></i>
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-[20px] 3xl:mt-[1.042vw]">
                      <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-medium leading-[140%]">
                        Overall Status
                      </div>
                      <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-medium leading-[140%] bg-BrandHighlight100 px-[10px] 3xl:px-[0.521vw] py-[6px] 3xl:py-[0.313vw]">
                        <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        Pass
                      </div>
                    </div>
                    <div className="space-y-[4px] 3xl:space-y-[0.208vw] mt-[12px] 3xl:mt-[0.625vw]">
                      <div className="flex items-center justify-between">
                        <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                          Customer Readiness
                        </div>
                        <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-normal leading-[140%] border border-BrandHighlightpure px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                          <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                          Pass
                        </div>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              ) : (
                <div className="bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[10px] 2xl:px-[13px] 3xl:px-[0.833vw] py-[6px] 3xl:py-[0.313vw] group cursor-pointer">
                  <div className="text-[12px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%] text-center mb-[8px] 3xl:mb-[0.521vw]">
                    {t('secondaryValidations')}
                  </div>
                  <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium leading-[140%]">
                    <i className="cloud-pending text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                    Pending
                  </div>
                </div>
              )}

              {isAvailabelCredit === true ? (
                <Popover open={availableCredit} onOpenChange={setAvailableCredit}>
                  <PopoverTrigger asChild>
                    <div className="bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[10px] 2xl:px-[8px] 3xl:px-[0.833vw] py-[6px] 3xl:py-[0.313vw] group cursor-pointer">
                      <div className="text-[12px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal group-hover:font-medium leading-[140%] text-center mb-[8px] 3xl:mb-[0.521vw]">
                        Available Credit
                      </div>
                      {availableCredit === true ? (
                        <div
                          className={`${availableCredit === true ? 'group-hover:block !block' : ''} group-hover:block hidden text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextprimary font-medium leading-[140%] cursor-pointer text-center relative`}
                        >
                          View
                        </div>
                      ) : (
                        <>
                          <div className="group-hover:block hidden text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextprimary font-medium leading-[140%] cursor-pointer text-center relative">
                            View
                          </div>
                          <div
                            title="USD 50,000.00"
                            className="group-hover:hidden flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium leading-[140%] text-center truncate"
                          >
                            USD 50,000.00
                          </div>
                        </>
                      )}
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="w-[320px] 3xl:w-[16.667vw] p-[16px] 3xl:p-[0.833vw] bg-[#fff]">
                    <div className="flex items-center justify-between gap-2">
                      <div className="text-[14px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold leading-[140%]">
                        Credit Check
                      </div>
                      <div
                        className="cursor-pointer flex items-center"
                        onClick={() => setAvailableCredit(false)}
                      >
                        <i className="cloud-closecircle text-Interfacefeedbackerror700 text-[22px] 3xl:text-[1.146vw]"></i>
                      </div>
                    </div>
                    <div className="space-y-[4px] 3xl:space-y-[0.208vw] mt-[22px] 3xl:mt-[1.25vw]">
                      <div className="flex items-center justify-between">
                        <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                          Assigned Credit
                        </div>
                        <div className="font14 text-InterfaceTextdefault font-medium leading-[140%]">
                          10,000.00
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                          Total Outstanding
                        </div>
                        <div className="font14 text-InterfaceTextdefault font-medium leading-[140%]">
                          2,000.00
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                          Total Unbilled
                        </div>
                        <div className="font14 text-InterfaceTextdefault font-medium leading-[140%]">
                          6,000.00
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                          Available Credit
                        </div>
                        <div className="font14 text-InterfaceTextdefault font-medium leading-[140%]">
                          2,000.00
                        </div>
                      </div>
                    </div>
                    <div className="mt-[22px] 3xl:mt-[1.25vw]">
                      <Button
                        onClick={() => setCreditLimitRevision(true)}
                        className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault font14 py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] font-medium w-full"
                      >
                        Request Credit Limit
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              ) : (
                <div className="bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[10px] 2xl:px-[13px] 3xl:px-[0.833vw] py-[6px] 3xl:py-[0.313vw] group cursor-pointer">
                  <div className="text-[12px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal leading-[140%] text-center mb-[8px] 3xl:mb-[0.521vw]">
                    {t('availableCredit')}
                  </div>
                  <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextlighter font-medium leading-[140%]">
                    N/A
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <EndcustomerPopup
        open={endcust}
        onClose={() => setEndcust(false)}
        addNewCustomer={() => {
          setEndcust(false);
          setAddNewCustomerPopupOpen(true);
        }}
      />
      <AddNewCustomerPopup
        open={addNewCustomerPopupOpen}
        onClose={() => setAddNewCustomerPopupOpen(false)}
      />
      <CreditLimitRevisionPopup
        open={creditLimitRevision}
        onClose={() => setCreditLimitRevision(false)}
      />
    </div>
  );
}
