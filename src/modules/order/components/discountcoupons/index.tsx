import React from 'react';
import { CouponcodePopup } from '../couponcode';
import { useTranslations } from 'next-intl';

export default function Discountcoupons() {
  const t = useTranslations();
  const [couponcode, setCouponcode] = React.useState<boolean>(false);
  const [applied, setApplied] = React.useState<boolean>(false);
  const [applied2, setApplied2] = React.useState<boolean>(false);
  const togglebtn = () => {
    setApplied(!applied);
  };
  const togglebtn2 = () => {
    setApplied2(!applied2);
  };
  return (
    <div className="p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw] bg-background shadow-sm">
      <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[140%]">
        {t('discountCoupons')} {t('optional')}
      </div>
      <div
        className={`${applied === true ? 'border border-BrandSupport1pure' : null} mt-[14px] xl:mt-[14px] 2xl:mt-[14px] 3xl:mt-[0.729vw] flex items-center justify-between p-[6px] xl:p-[6px] 2xl:p-[6px] 3xl:p-[0.313vw] bg-InterfaceSurfacecomponentmuted hover:bg-BrandNeutral100 `}
      >
        <div>
          <div className="flex items-center gap-[10px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.521vw]">
            <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
              {t('couponCode')}: 1122334455
            </div>
            <div onClick={() => setCouponcode(true)} className=" cursor-pointer">
              <i className="cloud-information text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
            </div>
          </div>
          <div className="text-InterfaceTextdefault text-[14px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-normal leading-[140%] ">
            {t('itemCovered')}: 2
          </div>
        </div>
        <div className="">
          <div
            onClick={togglebtn}
            className={`${applied === true ? 'bg-BrandSupport1pure text-[#fff]' : 'applybtnbg hover:bg-BrandNeutral100'} py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] px-[10px] xl:px-[10px] 2xl:px-[10px] 3xl:px-[0.521vw] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-medium leading-[100%] border border-InterfaceStrokesoft  cursor-pointer`}
          >
            {t('apply')}
          </div>
        </div>
      </div>

      <div
        className={`${applied2 === true ? 'border border-BrandSupport1pure' : null} mt-[4px] xl:mt-[4px] 2xl:mt-[4px] 3xl:mt-[0.208vw] flex items-center justify-between  p-[6px] xl:p-[6px] 2xl:p-[6px] 3xl:p-[0.313vw] bg-InterfaceSurfacecomponentmuted hover:bg-BrandNeutral100 `}
      >
        <div>
          <div className="flex items-center gap-[10px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.521vw]">
            <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
              {t('couponCode')}: 1122334455
            </div>
            <div onClick={() => setCouponcode(true)} className=" cursor-pointer">
              <i className="cloud-information text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
            </div>
          </div>
          <div className="text-InterfaceTextdefault text-[14px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-normal leading-[140%]">
            {t('itemCovered')}: 2
          </div>
        </div>
        <div>
          <div
            onClick={togglebtn2}
            className={`${applied2 === true ? 'bg-[#1570EF] text-[#fff]' : 'applybtnbg hover:bg-BrandNeutral100'} py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] px-[10px] xl:px-[10px] 2xl:px-[10px] 3xl:px-[0.521vw] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-medium leading-[100%] border border-InterfaceStrokesoft  cursor-pointer`}
          >
            {t('apply')}
          </div>
        </div>
      </div>
      <CouponcodePopup open={couponcode} onClose={() => setCouponcode(false)} />
    </div>
  );
}
