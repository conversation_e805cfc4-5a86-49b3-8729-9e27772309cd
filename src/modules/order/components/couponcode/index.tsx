'use client';

import { ActivitylogSheetProps } from '@/types';
import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';
import { DataTable } from '@/components/common/ui/data-table';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function CouponcodePopup({ open, onClose }: ActivitylogSheetProps) {
  const PurchasesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'product',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Product
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTexttitle font-medium text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('product')}
        </div>
      ),

      minSize: 500,
    },
    {
      accessorKey: 'skuid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                SKU ID
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTexttitle font-normal text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('skuid')}
        </div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'discount',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Discount %
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTexttitle font-normal text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('discount')}
        </div>
      ),

      minSize: 500,
    },
    {
      accessorKey: 'minqty',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Min Qty
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTexttitle font-normal text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('minqty')}
        </div>
      ),

      minSize: 500,
    },
    {
      accessorKey: 'validtill',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Valid Till
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTexttitle font-normal text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
          {row.getValue('validtill')}
        </div>
      ),

      minSize: 500,
    },
  ];

  type User = {
    skuid: string;
    discount: number;
    minqty: number;
    product: string;
    validtill: string;
  };
  const Purchasesdata = [
    {
      skuid: 'JKIW12HKSD78J',
      discount: 10,
      minqty: 5,
      product: 'Advanced Communication',
      validtill: '15/04/2025',
    },
    {
      skuid: 'JKIW12HKSD78J',
      discount: 10,
      minqty: 5,
      product: 'Advanced Communication',
      validtill: '15/04/2025',
    },
  ];

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between items-center">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              Coupon Code: 1122334455
            </div>

            <SheetClose className="flex justify-between items-center">
              <i className=" cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[24px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer leading-[140%]"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="h-full overflow-y-auto p-[20px] lg:p-[20px] xl:p-[24px] 2xl:p-[24px] 3xl:p-[1.25vw]">
          <div className="overflow-x-auto">
            <DataTable data={Purchasesdata} columns={PurchasesColumns} withCheckbox={false} />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
