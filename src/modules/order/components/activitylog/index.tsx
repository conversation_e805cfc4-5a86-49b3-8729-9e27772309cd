'use client';

import { ActivitylogSheetProps } from '@/types';
import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';
import { DataTable } from '@/components/common/ui/data-table';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function ActivityLogPopup({ open, onClose }: ActivitylogSheetProps) {
  const PurchasesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'datetime',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Date and Time
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('datetime')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'activity',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Activity
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('activity')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'user',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                User
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('user')}</div>,

      minSize: 500,
    },
  ];

  type User = {
    activity: string;
    user: string;
    datetime: string;
  };
  const Purchasesdata = [
    {
      activity: 'Cart created',
      user: 'Brooklyn Simmons',
      datetime: '10:30 PM, 10/03/2025',
    },
    {
      activity: 'Discount coupon applied',
      user: 'Jerome Bell',
      datetime: '11:25 AM, 05/03/2025',
    },
    {
      activity: 'End Customer selected',
      user: 'Leslie Alexander',
      datetime: '08:42 AM, 05/03/2025',
    },
    {
      activity: 'Promotion applied',
      user: 'Courtney Henry',
      datetime: '10:20 AM, 05/03/2025',
    },
  ];

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between items-center">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              Activity Log{' '}
            </div>

            <SheetClose className="flex justify-between items-center">
              <i className=" cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[24px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer leading-[140%]"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="h-full overflow-y-auto p-[20px] lg:p-[20px] xl:p-[24px] 2xl:p-[24px] 3xl:p-[1.25vw]">
          <div className="overflow-x-auto">
            <DataTable data={Purchasesdata} columns={PurchasesColumns} withCheckbox={false} />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
