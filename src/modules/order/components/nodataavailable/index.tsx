'use client';
import Image from 'next/image';
import React from 'react';

export default function NoDataAvailable() {
  return (
    <div className="flex items-center justify-center gap-[10px] 3xl:gap-[0.521vw] py-[8px] 3xl:py-[0.417vw]">
      <Image
        src={'/images/svg/no-data-img.svg'}
        className="w-[90px] 3xl:w-[4.688vw] h-[60px] 3xl:h-[3.125vw]"
        width={90}
        height={60}
        alt="no-data"
      />
      <div className="text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-BrandNeutral400 font-normal leading-[140%]">
        No Data Available
      </div>
    </div>
  );
}
