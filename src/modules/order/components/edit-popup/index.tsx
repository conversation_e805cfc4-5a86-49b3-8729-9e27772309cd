'use client';
import React from 'react';
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetClose,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import { TermsSheetProps } from '@/types/components';
import { Input, Label, Button } from '@redington-gulf-fze/cloudquarks-component-library';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface ChangePasswordProps extends TermsSheetProps {
  userData?: {
    userId?: string;
  };
}

export default function EditPopup({ open, onClose }: ChangePasswordProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              Edit Cart Details
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <form className="flex-grow flex flex-col">
          <div className="h-[320px] xl:h-[435px] 2xl:h-[590px] 3xl:h-[34.967vw] overflow-auto">
            <div className="flex-grow overflow-y-auto space-y-3">
              <div className=" px-6 xl:px-[16px] 3xl:px-[0.833vw] grid grid-cols-1 xl:grid-cols-1 md:grid-cols-1 gap-[20px]">
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="currentPassword"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Cart ID
                  </Label>
                  <Input
                    placeholder="CT100001"
                    disabled
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                  />
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="newPassword"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Quote ID
                  </Label>
                  <Input
                    placeholder="RFQ1001"
                    disabled
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                  />
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="confirmPassword"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Created On
                  </Label>
                  <Input
                    placeholder="1/3/2025"
                    disabled
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                  />
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="confirmPassword"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Expiring On
                  </Label>
                  <Input
                    placeholder="11/3/2025"
                    disabled
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                  />
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="confirmPassword"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Created By
                  </Label>
                  <Input
                    placeholder="Jese Leos"
                    disabled
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                  />
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="confirmPassword"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Cart Name
                  </Label>
                  <Input
                    placeholder="Sample Cart Name"
                    // disabled
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                  />
                </div>
                <div className="flex flex-col gap-1.5">
                  <div className="flex flex-col gap-1.5">
                    <Label
                      htmlFor="confirmPassword"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Description
                    </Label>
                    <Textarea
                      placeholder="One description about the products added in the cart and offers"
                      className="w-full border-[#BBC1C7]"
                    />
                  </div>
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
                    Character Limit: 50
                  </div>
                </div>
              </div>
            </div>
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 bg-InterfaceTextwhite border-t border-InterfaceStrokedefault">
            <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end items-center gap-[16px] 3xl:gap-[0.833vw]">
              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                variant="outline"
                size="sm"
                onClick={() => {
                  onClose(false);
                }}
              >
                <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                Cancel
              </Button>

              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-BrandPrimary800 bg-BrandPrimarypure text-[#FFF] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                // variant="outline"
                size="sm"
                onClick={() => {
                  onClose(false);
                }}
              >
                <i className="cloud-save-2 text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                Save
              </Button>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
