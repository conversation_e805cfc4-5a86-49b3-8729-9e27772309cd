'use client';

import { Button, SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import { Sheet, SheetContent } from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import { useRouter } from 'next/navigation';
const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SheetProps {
  open: boolean;
  onClose: () => void;
}

export function QuatePopup({ open, onClose }: SheetProps) {
  const router = useRouter();
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[500px] 2xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[140px] 2xl:mt-[180px] 3xl:mt-[10.417vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] xl:gap-[30px] 3xl:gap-[2.083vw]">
            <i className="cloud-info2 text-BrandSupport1pure text-[72px] xl:text-[42px] 2xl:text-[50px] 3xl:text-[2.804vw]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] xl:gap-[6px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[24px] 2xl:text-[30px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                Convert Cart to Quote
              </div>
              <div className="text-InterfaceTextdefault text-[20px] lg:text-[20px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                Would you like to convert this Cart to Quote? please confirm to proceed.
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] xl:gap-[12px] 3xl:gap-[0.833vw] mt-[40px] xl:mt-[24px] 3xl:mt-[2.083vw] text-center">
            <SheetClose>
              <Button
                onClick={() => router.push('/sales')}
                className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-background bg-[#00953A] hover:bg-[#067532] font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] xl:w-[140px] 2xl:w-[160px] 3xl:w-[9.375vw]"
              >
                Yes
              </Button>
            </SheetClose>
            <SheetClose>
              <div className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] xl:w-[140px] 2xl:w-[160px] 3xl:w-[9.375vw]">
                No
              </div>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
