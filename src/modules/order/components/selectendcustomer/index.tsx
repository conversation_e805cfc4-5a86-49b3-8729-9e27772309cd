'use client';

import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import LinkPopup from '@/modules/customer-management/customers/organization-tenants/component/link-popup';
import { SuccessPopup } from '@/modules/subscription-licenses/subscriptions/details/components/success-popup';
import { CommanSheetProps } from '@/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
  SheetClose,
  SheetFooter,
} from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import Image from 'next/image';
import { useState } from 'react';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function SelectEndCustomerPopup({ open, onClose }: CommanSheetProps) {
  const [successpopup, setSuccesspopup] = useState(false);
  const [linkinkpopup, setLinkpopup] = useState(false);
  const [awstenantOptions, setAwstenantOptions] = useState('');
  const [microsofttenantOptions, setMicrosofttenantOptions] = useState('');
  const [googletenantOptions, setGoogletenantOptions] = useState('');
  const awsOptions: Select2Option[] = [
    { label: 'AWS - Tenant 1 (1940HSDdfdkojkI76H)', value: ' Tenant 1' },
    { label: 'AWS - Tenant 2 (1940HSDdfdkojkI76H)', value: 'Tenant 2' },
    { label: 'AWS - Tenant 3 (1940HSDdfdkojkI76H)', value: 'Tenant 3' },
    { label: 'AWS - Tenant 4 (1940HSDdfdkojkI76H)', value: 'Tenant 4' },
  ];
  const microsoftOptions: Select2Option[] = [
    { label: 'AWS - Tenant 1 (1940HSDdfdkojkI76H)', value: ' Tenant 1' },
    { label: 'AWS - Tenant 2 (1940HSDdfdkojkI76H)', value: 'Tenant 2' },
    { label: 'AWS - Tenant 3 (1940HSDdfdkojkI76H)', value: 'Tenant 3' },
    { label: 'AWS - Tenant 4 (1940HSDdfdkojkI76H)', value: 'Tenant 4' },
  ];
  const googleOptions: Select2Option[] = [
    { label: 'AWS - Tenant 1 (1940HSDdfdkojkI76H)', value: ' Tenant 1' },
    { label: 'AWS - Tenant 2 (1940HSDdfdkojkI76H)', value: 'Tenant 2' },
    { label: 'AWS - Tenant 3 (1940HSDdfdkojkI76H)', value: 'Tenant 3' },
    { label: 'AWS - Tenant 4 (1940HSDdfdkojkI76H)', value: 'Tenant 4' },
  ];
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTrigger></SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              Select End Customer
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[20px] lg:text-[20px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="relative h-[465px] xl:h-[465px] 2xl:h-[654px] 3xl:h-[36.308vw] overflow-y-auto ">
          <div className="p-[24px] xl:p-[24px] 2xl:p-[24px] 3xl:p-[1.25vw] space-y-[24px] xl:space-y-[24px] 2xl:space-y-[24px] 3xl:space-y-[1.25vw]">
            <div className="p-[20px] xl:p-[20px] 2xl:p-[20px] 3xl:p-[1.042vw] bg-[#F6F7F8]">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-medium">
                End Customer Details
              </div>

              <div className="mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw] text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-normal">
                Alpha Systems - Binghati Towers, 201, AI Mankhool, Dubai, UAE
              </div>
            </div>
            <div className="p-[20px] xl:p-[20px] 2xl:p-[20px] 3xl:p-[1.042vw] bg-[#F6F7F8] space-y-[16px] xl:space-y-[16px] 2xl:space-y-[16px] 3xl:space-y-[0.833vw]">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-medium">
                Organization & Tenants Details
              </div>

              <div className="flex items-center">
                <div className="pr-[24px] border-r border-[#E5E7EB]">
                  <Image
                    src="/images/amazon.svg"
                    height={28}
                    width={47}
                    alt="amazon"
                    className=""
                  />
                </div>
                <div className="pl-[24px] w-full">
                  <Select2
                    options={awsOptions}
                    value={awstenantOptions}
                    placeholder="-- Select Organization Tenant --"
                    onChange={(val) => setAwstenantOptions(val as string)}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <div className="pr-[24px] border-r border-[#E5E7EB]">
                  <Image
                    src="/images/amazon.svg"
                    height={28}
                    width={47}
                    alt="amazon"
                    className=""
                  />
                </div>
                <div className="pl-[24px] w-full">
                  <Select2
                    options={microsoftOptions}
                    value={microsofttenantOptions}
                    placeholder="-- Select Organization Tenant --"
                    onChange={(val) => setMicrosofttenantOptions(val as string)}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <div className="pr-[24px] border-r border-[#E5E7EB]">
                  <Image
                    src="/images/microsoft.svg"
                    height={28}
                    width={47}
                    alt="amazon"
                    className=""
                  />
                </div>
                <div className="pl-[24px] w-full">
                  <Select2
                    options={googleOptions}
                    value={googletenantOptions}
                    placeholder="-- Select Organization Tenant --"
                    onChange={(val) => setGoogletenantOptions(val as string)}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <div className="pr-[24px] border-r border-[#E5E7EB]">
                  <Image
                    src="/images/microsoft.svg"
                    height={28}
                    width={47}
                    alt="amazon"
                    className=""
                  />
                </div>
                <div className="pl-[24px] w-full">
                  <Select2
                    options={awsOptions}
                    value={awstenantOptions}
                    placeholder="-- Select Organization Tenant --"
                    onChange={(val) => setAwstenantOptions(val as string)}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <div className="pr-[24px] border-r border-[#E5E7EB]">
                  <Image
                    src="/images/google.svg"
                    height={28}
                    width={47}
                    alt="amazon"
                    className=""
                  />
                </div>
                <div className="pl-[24px] w-full">
                  <Select2
                    options={awsOptions}
                    value={awstenantOptions}
                    placeholder="-- Select Organization Tenant --"
                    onChange={(val) => setAwstenantOptions(val as string)}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <div className="pr-[24px] border-r border-[#E5E7EB]">
                  <Image
                    src="/images/google.svg"
                    height={28}
                    width={47}
                    alt="amazon"
                    className=""
                  />
                </div>
                <div className="pl-[24px] w-full">
                  <Select2
                    options={awsOptions}
                    value={awstenantOptions}
                    placeholder="-- Select Organization Tenant --"
                    onChange={(val) => setAwstenantOptions(val as string)}
                  />
                </div>
              </div>

              <div
                onClick={() => setLinkpopup(true)}
                className="flex items-center justify-center px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[#1570EF] text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-[#ECEFF3] cursor-pointer"
              >
                Click Here to Link Organization and Tenant Details
              </div>
            </div>
            <div className="p-[20px] xl:p-[20px] 2xl:p-[20px] 3xl:p-[1.042vw] bg-[#F6F7F8] space-y-[16px]">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] font-medium">
                Validation Details
              </div>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="authorized-signatory">
                  <AccordionTrigger className="p-[8px] xl:p-[8px] 2xl:p-[8px] 3xl:p-[0.417vw] border border-InterfaceStrokesoft bg-white hover:no-underline">
                    <div className="w-full flex items-center">
                      <div className="text-left text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                        Preliminary Validations
                      </div>

                      <div className="flex items-center justify-center gap-2 text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandSupport2500 font-medium leading-[140%]  px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                        <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        <span className="mt-[2px] xl:mt-[2px] 3xl:mt-[0.104vw]">Pass</span>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-[12px] xl:px-[12px] 3xl:px-[0.625vw] py-[16px] xl:py-[16px] 3xl:py-[0.833vw] border border-InterfaceStrokesoft">
                    <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                      <div className="flex items-center justify-between">
                        <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                          General T&C{"'"}s
                        </div>
                        <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-normal leading-[140%] border border-BrandHighlightpure px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                          <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                          Pass
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                          Partner Readiness
                        </div>
                        <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-normal leading-[140%] border border-BrandHighlightpure px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                          <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                          Pass
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                          Vendor T&C{"'"}s
                        </div>
                        <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-BrandHighlight600 font-normal leading-[140%] border border-BrandHighlightpure px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                          <i className="cloud-fillcircletick text-BrandHighlight500 text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                          Pass
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="authorized-signatory">
                  <AccordionTrigger className="p-[8px] xl:p-[8px] 2xl:p-[8px] 3xl:p-[0.417vw] border border-InterfaceStrokesoft bg-white hover:no-underline">
                    <div className="w-full flex items-center">
                      <div className="text-left text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                        Secondary Validations
                      </div>

                      <div className="flex items-center justify-center gap-2 text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium leading-[140%]  px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                        <i className="cloud-pending text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        <span className="mt-[2px] xl:mt-[2px] 3xl:mt-[0]">Pending</span>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-[12px] xl:px-[12px] 3xl:px-[0.625vw] py-[16px] xl:py-[16px] 3xl:py-[0.833vw] border border-InterfaceStrokesoft">
                    <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                      <div className="flex items-center justify-between">
                        <div className="text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-normal leading-[140%]">
                          Customer Readiness
                        </div>
                        <div className="flex items-center justify-center gap-2 text-[11px] xl:text-[11px] 2xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-normal leading-[140%] border border-InterfaceStrokesoft px-[6px] 3xl:px-[0.313vw] py-[4px] 3xl:py-[0.208vw]">
                          <i className="cloud-pending text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                          Pending
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
        </div>
        <SheetFooter className="w-full absolute bottom-0">
          <div className="w-full bg-white">
            <div className="flex justify-end items-center gap-[8px] 3xl:gap-[0.417vw] px-[18px] xl:px-[18px] 2xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw] border-t border-[#BECDE3]">
              <SheetClose>
                <div className="gap-[8px] 3xl:gap-[0.417vw] flex">
                  <Button className="text-[#3C4146] cancelbtn px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    <i className="cloud-circletick text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    Cancel
                  </Button>
                  <Button className="text-[#3C4146] cancelbtn px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    <i className="cloud-brush2 text-[#3C4146] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    Reset
                  </Button>
                </div>
              </SheetClose>
              <Button
                onClick={() => setSuccesspopup(true)}
                className="border applybtn text-[#FFF]  px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"
              >
                <i className="cloud-fillnotepad text-[#fff] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                Validate
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
      {linkinkpopup && <LinkPopup open={linkinkpopup} onClose={() => setLinkpopup(false)} />}
      {successpopup && (
        <SuccessPopup
          open={successpopup}
          onClose={() => setSuccesspopup(false)}
          message="Your Cart contains products which have eligible offers, promotions and discount coupons. Please select and apply them at a line item level."
          title={'Successfully Submitted!'}
        />
      )}
    </Sheet>
  );
}
