'use client';
import React from 'react';
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  SheetClose,
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { TermsSheetProps } from '@/types/components';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface ChangePasswordProps extends TermsSheetProps {
  userData?: {
    userId?: string;
  };
}

export default function EligibleOffersPopup({ open, onClose }: ChangePasswordProps) {
  const t = useTranslations();
  const [tab, setTab] = React.useState(0);
  const [showBreakup, setShowBreakup] = useState(false);
  const [showSecondButton, setShowSecondButton] = useState(false);

  const handleClick = () => {
    setShowSecondButton(true);
  };
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[700px] 3xl:max-w-[42.558vw] p-[0px] gap-0 hideclose`}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[24px] lg-[20px] xl:p-[16px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[18px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {tab === 0 ? t('selectEligibleOffers') : t('selectAlternateOffers')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[24    px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <form className="flex-grow flex flex-col p-6 xl:p-4 3xl:p-[1.25vw] bg-[#F6F7F8]">
          <div className="h-[320px] xl:h-[435px] 2xl:h-[590px] 3xl:h-[34.967vw] overflow-auto">
            <div className="flex-grow overflow-y-auto space-y-3">
              <div className="py-3 xl:py-[10px] 3xl:py-[0.625vw] px-4 xl:px-[12px] 3xl:px-[0.833vw] ">
                <div className="text-[20px] xl:text-[16px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle">
                  Appsheet Enterprise Plus (Additional Service)
                </div>
                <div className="mt-[6px] xl:mt-[3px] 3xl:mt-[0.313vw] text-sm text-gray-700 flex flex-wrap gap-x-4 xl:gap-x-3 3xl:gap-x-[0.833vw] gap-y-1">
                  <span className="text-InterfaceTextdefault font-[400] text-[14px] xl:text-[12px] 3xl:text-[0.729vw]">
                    <span className="text-InterfaceTexttitle font-[500]">{t('skuId')}:</span>{' '}
                    SW54CJEN821
                  </span>
                  <span className="text-InterfaceTextdefault font-[400] text-[14px] xl:text-[12px] 3xl:text-[0.729vw]">
                    <span className="text-InterfaceTexttitle font-[500]">{t('brand')}:</span>{' '}
                    Microsoft
                  </span>
                  <span className="text-InterfaceTextdefault font-[400] text-[14px] xl:text-[12px] 3xl:text-[0.729vw]">
                    <span className="text-InterfaceTexttitle font-[500]">{t('segment')}:</span>{' '}
                    Commercial
                  </span>
                  <span className="text-InterfaceTextdefault font-[400] text-[14px] xl:text-[12px] 3xl:text-[0.729vw]">
                    <span className="text-InterfaceTexttitle font-[500]">{t('term')}:</span> 1 Year
                  </span>
                  <span className="text-InterfaceTextdefault font-[400] text-[14px] xl:text-[12px] 3xl:text-[0.729vw]">
                    <span className="text-InterfaceTexttitle font-[500]">{t('billType')}:</span>{' '}
                    Monthly
                  </span>
                </div>
              </div>
            </div>

            <div className="py-6">
              <div className="flex items-center">
                <Button
                  type="button"
                  onClick={() => setTab(0)}
                  className={`${tab === 0 ? 'bg-InterfaceSurfacehcinverse text-InterfaceTextwhite' : 'bg-interfacesurfacecomponent text-InterfaceTextdefault'} font-normal px-[20px] xl:px-[16px] 3xl:px-[1.042vw] py-[10px] xl:py-[8px] 3xl:py-[0.521vw] font14 leading-[140%] rounded-none text-wrap`}
                >
                  <i
                    className={`${tab === 0 ? 'text-InterfaceTextwhite' : 'text-InterfaceTextprimary'} cloud-discount-shap text-[20px] xl:text-[16px] 3xl:text-[1.042vw]`}
                  ></i>
                  {t('eligibleOffers')}
                </Button>
                <Button
                  type="button"
                  onClick={() => setTab(1)}
                  className={`${tab === 1 ? 'bg-InterfaceSurfacehcinverse text-InterfaceTextwhite' : 'bg-interfacesurfacecomponent text-InterfaceTextdefault'} font-normal px-[20px] xl:px-[16px] 3xl:px-[1.042vw] py-[10px] xl:py-[8px] 3xl:py-[0.521vw] font14 leading-[140%] rounded-none shadow-sm text-wrap`}
                >
                  <i
                    className={`${tab === 1 ? 'text-InterfaceTextwhite' : 'text-InterfaceTextprimary'} cloud-ticketDiscount  text-[20px] xl:text-[16px] 3xl:text-[1.042vw]`}
                  ></i>
                  {t('alternateOffers')}
                </Button>
              </div>
            </div>

            {tab === 0 && (
              <>
                <Accordion type="single" collapsible className="w-full space-y-4">
                  {/* Flexible Plan 1 */}
                  <AccordionItem value="flexible-plan-1">
                    <AccordionTrigger className="bg-background border border-InterfaceStrokesoft border-b-0 py-3 3xl:py-[0.625vw] px-4 3xl:px-[0.833vw] flex justify-between items-center">
                      <div className="flex flex-col text-left space-y-1">
                        <span className="font-[600] text-[#000000] text-[18px] 3xl:text-[0.938vw]">
                          Flexible Plan 1
                        </span>
                        <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                          <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                          <span>{t('offerEndson')}: March 22, 2025</span>
                        </div>
                        <span className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw]">
                          {t('viewMoreDetails')}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="bg-background border border-t-0 p-4 3xl:p-[0.833vw] pb-3 3xl:pb-[0.625vw]">
                      <div className="bg-InterfaceSurfacecomponentmuted p-4 3xl:p-[16px] mb-[16px] xl:mb-[14px] 3xl:mb-[0.833vw]">
                        <div className="flex justify-between items-center gap-2">
                          <h2 className="text-[16px] 3xl:text-[0.833vw] font-semibold text-gray-800">
                            {t('offerDetails')}
                          </h2>
                          <div className="flex items-center gap-2 text-sm">
                            <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                              <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                              <span>{t('offerEndson')}: March 22, 2025</span>
                            </div>

                            <div>
                              {/* First Button: Apply Offer (only visible before click) */}
                              {!showSecondButton && (
                                <button
                                  type="button"
                                  onClick={handleClick}
                                  className="bg-yellowbg text-InterfaceTexttitle font-[400] px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] flex items-center text-[12px] 3xl:text-[0.625vw] gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                                >
                                  <i className="cloud-discount-shap"></i>
                                  <span>{t('applyOffer')}</span>
                                </button>
                              )}

                              {/* Second Button: Visible after first is clicked */}
                              {showSecondButton && (
                                <button
                                  type="button"
                                  onClick={handleClick}
                                  className="bg-BrandSupport1pure text-white font-[400] px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] flex items-center text-[12px] 3xl:text-[0.625vw] gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                                >
                                  <i className="cloud-discount-shap"></i>
                                  <span>Offer Applied</span>
                                </button>
                              )}
                            </div>

                            <button
                              type="button"
                              onClick={() => setShowBreakup(!showBreakup)}
                              className="bg-BrandNeutralpure text-InterfaceTextwhite font-normal px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] text-[12px] 3xl:text-[0.625vw] flex items-center gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                            >
                              <i className="cloud-readeye1"></i>
                              <span> {showBreakup ? 'Hide Breakup' : 'View Breakup'}</span>
                            </button>
                          </div>
                        </div>

                        <div className="flex gap-[10px] 3xl:gap-[0.521vw] py-5 3xl:py-[1.042vw]">
                          <i className="cloud-info text-InterfaceTextdefault mt-[3px]"></i>
                          <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault">
                            Add and Delete users at any time. You will be billed at the end of the
                            month for the actual number of accounts in service during the month
                          </p>
                        </div>

                        {showBreakup ? (
                          <div className="bg-background overflow-x-auto py-[8px] 3xl:py-[0.417vw] px-[12px] 3xl:px-[0.625vw]">
                            <table className="text-sm 3xl:text-[0.833vw] text-left">
                              <thead className="text-InterfaceTextsubtitle font14font-normal">
                                <tr>
                                  <th className="w-[20%] font-normal">Phase Range</th>
                                  <th className="w-[13%] font-normal">Quantity Tier Range</th>
                                  <th className="w-[6%] font-normal">Qty</th>
                                  <th className="w-[15%] font-normal">List Price</th>
                                  <th className="w-[18%] font-normal">Discount/Unit</th>
                                  <th className="w-[15%] font-normal">Unit Price</th>
                                  <th className="w-[15%] font-normal">Sub Total</th>
                                </tr>
                              </thead>
                              <tbody className="text-InterfaceTextdefault">
                                <tr className="border-t">
                                  <td className="pt-[16px] pb-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1-5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 788.57</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 78.86</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 709.71</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1717.05</td>
                                </tr>
                                <tr className="border-t">
                                  <td className="pb-[16px] pt-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pb-[16px] pt-[8px] font-medium">6-8</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">3</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 985.71</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 98.57</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 887.14</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">1287.79</td>
                                </tr>
                              </tbody>
                              <tfoot className=" border-t">
                                <tr>
                                  <td
                                    colSpan={7}
                                    className="px-3 3xl:px-[0.625vw] py-2 3xl:py-[0.417vw] font16 text-right text-InterfaceTexttitle font-semibold"
                                  >
                                    Subtotal: 3004.84
                                  </td>
                                </tr>
                              </tfoot>
                            </table>
                            <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault mt-2">
                              *Please note that the Monthly/Quarterly/Half-Yearly prices on the
                              offer details are prorated.
                            </p>
                          </div>
                        ) : (
                          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2025 to Mar 16, 2026
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    40% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    25% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2026 to Onwards
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    35% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    20% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div>
                        <a
                          href="#"
                          className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] "
                        >
                          View Less Details
                        </a>
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  {/* Flexible Plan 2 */}
                  <AccordionItem value="flexible-plan-2">
                    <AccordionTrigger className="bg-background border border-InterfaceStrokesoft border-b-0 py-3 3xl:py-[0.625vw] px-4 3xl:px-[0.833vw] flex justify-between items-center">
                      <div className="flex flex-col text-left space-y-1">
                        <span className="font-[600] text-[#000000] text-[18px] 3xl:text-[0.938vw]">
                          Flexible Plan 2
                        </span>
                        <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                          <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                          <span>{t('offerEndson')}: March 22, 2025</span>
                        </div>
                        <span className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw]">
                          {t('viewMoreDetails')}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="bg-background border border-t-0 p-4 3xl:p-[0.833vw] pb-3 3xl:pb-[0.625vw]">
                      <div className="bg-InterfaceSurfacecomponentmuted p-4 3xl:p-[16px] mb-[16px] xl:mb-[14px] 3xl:mb-[0.833vw]">
                        <div className="flex justify-between items-center gap-2">
                          <h2 className="text-[16px] 3xl:text-[0.833vw] font-semibold text-gray-800">
                            {t('offerDetails')}
                          </h2>
                          <div className="flex items-center gap-2 text-sm">
                            <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                              <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                              <span>{t('offerEndson')}: March 22, 2025</span>
                            </div>
                            <button
                              type="button"
                              className="bg-yellowbg text-InterfaceTexttitle font-[400] px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] flex items-center text-[12px] 3xl:text-[0.625vw] gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                            >
                              <i className="cloud-discount-shap"></i>
                              <span>{t('applyOffer')}</span>
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowBreakup(!showBreakup)}
                              className="bg-BrandNeutralpure text-InterfaceTextwhite font-normal px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] text-[12px] 3xl:text-[0.625vw] flex items-center gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                            >
                              <i className="cloud-readeye1"></i>
                              <span>{showBreakup ? 'Hide Breakup' : 'View Breakup'}</span>
                            </button>
                          </div>
                        </div>

                        <div className="flex gap-[10px] 3xl:gap-[0.521vw] py-5 3xl:py-[1.042vw]">
                          <i className="cloud-info text-InterfaceTextdefault mt-[3px]"></i>
                          <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault">
                            Add and Delete users at any time. You will be billed at the end of the
                            month for the actual number of accounts in service during the month
                          </p>
                        </div>

                        {showBreakup ? (
                          <div className="bg-background overflow-x-auto py-[8px] 3xl:py-[0.417vw] px-[12px] 3xl:px-[0.625vw]">
                            <table className="text-sm 3xl:text-[0.833vw] text-left">
                              <thead className="text-InterfaceTextsubtitle font14font-normal">
                                <tr>
                                  <th className="w-[20%] font-normal">Phase Range</th>
                                  <th className="w-[13%] font-normal">Quantity Tier Range</th>
                                  <th className="w-[6%] font-normal">Qty</th>
                                  <th className="w-[15%] font-normal">List Price</th>
                                  <th className="w-[18%] font-normal">Discount/Unit</th>
                                  <th className="w-[15%] font-normal">Unit Price</th>
                                  <th className="w-[15%] font-normal">Sub Total</th>
                                </tr>
                              </thead>
                              <tbody className="text-InterfaceTextdefault">
                                <tr className="border-t">
                                  <td className="pt-[16px] pb-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1-5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 788.57</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 78.86</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 709.71</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1717.05</td>
                                </tr>
                                <tr className="border-t">
                                  <td className="pb-[16px] pt-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pb-[16px] pt-[8px] font-medium">6-8</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">3</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 985.71</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 98.57</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 887.14</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">1287.79</td>
                                </tr>
                              </tbody>
                              <tfoot className="border-t">
                                <tr>
                                  <td
                                    colSpan={7}
                                    className="px-3 3xl:px-[0.625vw] py-2 3xl:py-[0.417vw] font16 text-right text-InterfaceTexttitle font-semibold"
                                  >
                                    Subtotal: 3004.84
                                  </td>
                                </tr>
                              </tfoot>
                            </table>
                            <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault mt-2">
                              *Please note that the Monthly/Quarterly/Half-Yearly prices on the
                              offer details are prorated.
                            </p>
                          </div>
                        ) : (
                          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2025 to Mar 16, 2026
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    40% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    25% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2026 to Onwards
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    35% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    20% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div>
                        <a
                          href="#"
                          className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] "
                        >
                          View Less Details
                        </a>
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  {/* Flexible Plan 3 */}
                  <AccordionItem value="flexible-plan-3">
                    <AccordionTrigger className="bg-background border border-InterfaceStrokesoft border-b-0 py-3 3xl:py-[0.625vw] px-4 3xl:px-[0.833vw] flex justify-between items-center">
                      <div className="flex flex-col text-left space-y-1">
                        <span className="font-[600] text-[#000000] text-[18px] 3xl:text-[0.938vw]">
                          Flexible Plan 3
                        </span>
                        <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                          <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                          <span>{t('offerEndson')}n: March 22, 2025</span>
                        </div>
                        <span className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw]">
                          {t('viewMoreDetails')}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="bg-background border border-t-0 p-4 3xl:p-[0.833vw] pb-3 3xl:pb-[0.625vw]">
                      <div className="bg-InterfaceSurfacecomponentmuted p-4 3xl:p-[16px] mb-[16px] xl:mb-[14px] 3xl:mb-[0.833vw]">
                        <div className="flex justify-between items-center gap-2">
                          <h2 className="text-[16px] 3xl:text-[0.833vw] font-semibold text-gray-800">
                            {t('offerDetails')}
                          </h2>
                          <div className="flex items-center gap-2 text-sm">
                            <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                              <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                              <span> {t('offerEndson')}: March 22, 2025</span>
                            </div>
                            <button
                              type="button"
                              className="bg-yellowbg text-InterfaceTexttitle font-[400] px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] flex items-center text-[12px] 3xl:text-[0.625vw] gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] rounded-0 space-x-1"
                            >
                              <i className="cloud-discount-shap"></i>
                              <span>{t('applyOffer')}</span>
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowBreakup(!showBreakup)}
                              className="bg-BrandNeutralpure text-InterfaceTextwhite font-normal px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] text-[12px] 3xl:text-[0.625vw] flex items-center gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] rounded-0 space-x-1"
                            >
                              <i className="cloud-readeye1"></i>
                              <span>{showBreakup ? 'Hide Breakup' : 'View Breakup'}</span>
                            </button>
                          </div>
                        </div>

                        <div className="flex gap-[10px] 3xl:gap-[0.521vw] py-5 3xl:py-[1.042vw]">
                          <i className="cloud-info text-InterfaceTextdefault mt-[3px]"></i>
                          <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault">
                            Add and Delete users at any time. You will be billed at the end of the
                            month for the actual number of accounts in service during the month
                          </p>
                        </div>
                        {showBreakup ? (
                          <div className="bg-background overflow-x-auto py-[8px] 3xl:py-[0.417vw] px-[12px] 3xl:px-[0.625vw]">
                            <table className="text-sm 3xl:text-[0.833vw] text-left">
                              <thead className="text-InterfaceTextsubtitle font14font-normal">
                                <tr>
                                  <th className="w-[20%] font-normal">Phase Range</th>
                                  <th className="w-[13%] font-normal">Quantity Tier Range</th>
                                  <th className="w-[6%] font-normal">Qty</th>
                                  <th className="w-[15%] font-normal">List Price</th>
                                  <th className="w-[18%] font-normal">Discount/Unit</th>
                                  <th className="w-[15%] font-normal">Unit Price</th>
                                  <th className="w-[15%] font-normal">Sub Total</th>
                                </tr>
                              </thead>
                              <tbody className="text-InterfaceTextdefault">
                                <tr className="border-t">
                                  <td className="pt-[16px] pb-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1-5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 788.57</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 78.86</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 709.71</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1717.05</td>
                                </tr>
                                <tr className="border-t">
                                  <td className="pb-[16px] pt-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pb-[16px] pt-[8px] font-medium">6-8</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">3</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 985.71</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 98.57</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 887.14</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">1287.79</td>
                                </tr>
                              </tbody>
                              <tfoot className="border-t">
                                <tr>
                                  <td
                                    colSpan={7}
                                    className="px-3 3xl:px-[0.625vw] py-2 3xl:py-[0.417vw] font16 text-right text-InterfaceTexttitle font-semibold"
                                  >
                                    Subtotal: 3004.84
                                  </td>
                                </tr>
                              </tfoot>
                            </table>
                            <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault mt-2">
                              *Please note that the Monthly/Quarterly/Half-Yearly prices on the
                              offer details are prorated.
                            </p>
                          </div>
                        ) : (
                          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2025 to Mar 16, 2026
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    40% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    25% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2026 to Onwards
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    35% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    20% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div>
                        <a
                          href="#"
                          className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] "
                        >
                          View Less Details
                        </a>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </>
            )}

            {tab === 1 && (
              <>
                <Accordion type="single" collapsible className="w-full space-y-4">
                  {/* Annual Plan (Monthly Payment) */}
                  <AccordionItem value="flexible-plan-1">
                    <AccordionTrigger className="bg-background border border-InterfaceStrokesoft border-b-0 py-3 3xl:py-[0.625vw] px-4 3xl:px-[0.833vw] flex justify-between items-center">
                      <div className="flex flex-col text-left space-y-1">
                        <span className="font-[600] text-[#000000] text-[18px] 3xl:text-[0.938vw]">
                          {t('annualPlan(MonthlyPayment)')}
                        </span>
                        <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                          <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                          <span>{t('offerEndson')}: March 22, 2025</span>
                        </div>
                        <span className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw]">
                          {t('viewMoreDetails')}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="bg-background border border-t-0 p-4 3xl:p-[0.833vw] pb-3 3xl:pb-[0.625vw]">
                      <div className="bg-InterfaceSurfacecomponentmuted p-4 3xl:p-[16px] mb-[16px] xl:mb-[14px] 3xl:mb-[0.833vw]">
                        <div className="flex justify-between items-center gap-2">
                          <h2 className="text-[16px] 3xl:text-[0.833vw] font-semibold text-gray-800">
                            {t('offerDetails')}
                          </h2>
                          <div className="flex items-center gap-2 text-sm">
                            <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                              <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                              <span>{t('offerEndson')}: March 22, 2025</span>
                            </div>
                            <button
                              type="button"
                              className="bg-yellowbg text-InterfaceTexttitle font-[400] px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] flex items-center text-[12px] 3xl:text-[0.625vw] gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                            >
                              <i className="cloud-discount-shap"></i>
                              <span>{t('applyOffer')}</span>
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowBreakup(!showBreakup)}
                              className="bg-BrandNeutralpure text-InterfaceTextwhite font-normal px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] text-[12px] 3xl:text-[0.625vw] flex items-center gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                            >
                              <i className="cloud-readeye1"></i>
                              <span> {showBreakup ? 'Hide Breakup' : 'View Breakup'}</span>
                            </button>
                          </div>
                        </div>

                        <div className="flex gap-[10px] 3xl:gap-[0.521vw] py-5 3xl:py-[1.042vw]">
                          <i className="cloud-info text-InterfaceTextdefault mt-[3px]"></i>
                          <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault">
                            Commit to a 12 month service plan. You will be billed at the end of the
                            month based on your committed number of licenses. Additional licenses
                            may be added to the commitmet
                          </p>
                        </div>

                        {showBreakup ? (
                          <div className="bg-background overflow-x-auto py-[8px] 3xl:py-[0.417vw] px-[12px] 3xl:px-[0.625vw]">
                            <table className="text-sm 3xl:text-[0.833vw] text-left">
                              <thead className="text-InterfaceTextsubtitle font14font-normal">
                                <tr>
                                  <th className="w-[20%] font-normal">Phase Range</th>
                                  <th className="w-[13%] font-normal">Quantity Tier Range</th>
                                  <th className="w-[6%] font-normal">Qty</th>
                                  <th className="w-[15%] font-normal">List Price</th>
                                  <th className="w-[18%] font-normal">Discount/Unit</th>
                                  <th className="w-[15%] font-normal">Unit Price</th>
                                  <th className="w-[15%] font-normal">Sub Total</th>
                                </tr>
                              </thead>
                              <tbody className="text-InterfaceTextdefault">
                                <tr className="border-t">
                                  <td className="pt-[16px] pb-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1-5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 788.57</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 78.86</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 709.71</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1717.05</td>
                                </tr>
                                <tr className="border-t">
                                  <td className="pb-[16px] pt-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pb-[16px] pt-[8px] font-medium">6-8</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">3</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 985.71</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 98.57</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 887.14</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">1287.79</td>
                                </tr>
                              </tbody>
                              <tfoot className="border-t">
                                <tr>
                                  <td
                                    colSpan={7}
                                    className="px-3 3xl:px-[0.625vw] py-2 3xl:py-[0.417vw] font16 text-right text-InterfaceTexttitle font-semibold"
                                  >
                                    Subtotal: 3004.84
                                  </td>
                                </tr>
                              </tfoot>
                            </table>
                            <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault mt-2">
                              *Please note that the Monthly/Quarterly/Half-Yearly prices on the
                              offer details are prorated.
                            </p>
                          </div>
                        ) : (
                          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2025 to Mar 16, 2026
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    40% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    25% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2026 to Onwards
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    35% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    20% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div>
                        <a
                          href="#"
                          className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] "
                        >
                          View Less Details
                        </a>
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  {/* Annual Plan (Yearly Payment) */}
                  <AccordionItem value="flexible-plan-2">
                    <AccordionTrigger className="bg-background border border-InterfaceStrokesoft border-b-0 py-3 3xl:py-[0.625vw] px-4 3xl:px-[0.833vw] flex justify-between items-center">
                      <div className="flex flex-col text-left space-y-1">
                        <span className="font-[600] text-[#000000] text-[18px] 3xl:text-[0.938vw]">
                          {t('AnnualPlan(YearlyPayment)')}
                        </span>
                        <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                          <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                          <span>{t('offerEndson')}: March 22, 2025</span>
                        </div>
                        <span className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw]">
                          {t('viewMoreDetails')}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="bg-background border border-t-0 p-4 3xl:p-[0.833vw] pb-3 3xl:pb-[0.625vw]">
                      <div className="bg-InterfaceSurfacecomponentmuted p-4 3xl:p-[16px] mb-[16px] xl:mb-[14px] 3xl:mb-[0.833vw]">
                        <div className="flex justify-between items-center gap-2">
                          <h2 className="text-[16px] 3xl:text-[0.833vw] font-semibold text-gray-800">
                            {t('offerDetails')}
                          </h2>
                          <div className="flex items-center gap-2 text-sm">
                            <div className="flex items-center space-x-1 font14 text-BrandHighlight500 font-[400]">
                              <i className="cloud-timer text-[16px] 3xl:text-[0.833vw]"></i>
                              <span>{t('offerEndson')}: March 22, 2025</span>
                            </div>
                            <button
                              type="button"
                              className="bg-yellowbg text-InterfaceTexttitle font-[400] px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] flex items-center text-[12px] 3xl:text-[0.625vw] gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                            >
                              <i className="cloud-discount-shap"></i>
                              <span>{t('applyOffer')}</span>
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowBreakup(!showBreakup)}
                              className="bg-BrandNeutralpure text-InterfaceTextwhite font-normal px-3 3xl:px-[0.625vw] py-[6px] 3xl:py-[0.313vw] text-[12px] 3xl:text-[0.625vw] flex items-center gap-[4px] xl:gap-[2px] 3xl:gap-[0.208vw] space-x-1"
                            >
                              <i className="cloud-readeye1"></i>
                              <span>{showBreakup ? 'Hide Breakup' : 'View Breakup'}</span>
                            </button>
                          </div>
                        </div>

                        <div className="flex gap-[10px] 3xl:gap-[0.521vw] py-5 3xl:py-[1.042vw]">
                          <i className="cloud-info text-InterfaceTextdefault mt-[3px]"></i>
                          <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault">
                            Add and Delete users at any time. You will be billed at the end of the
                            month for the actual number of accounts in service during the month
                          </p>
                        </div>

                        {showBreakup ? (
                          <div className="bg-background overflow-x-auto py-[8px] 3xl:py-[0.417vw] px-[12px] 3xl:px-[0.625vw]">
                            <table className="text-sm 3xl:text-[0.833vw] text-left">
                              <thead className="text-InterfaceTextsubtitle font14font-normal">
                                <tr>
                                  <th className="w-[20%] font-normal">Phase Range</th>
                                  <th className="w-[13%] font-normal">Quantity Tier Range</th>
                                  <th className="w-[6%] font-normal">Qty</th>
                                  <th className="w-[15%] font-normal">List Price</th>
                                  <th className="w-[18%] font-normal">Discount/Unit</th>
                                  <th className="w-[15%] font-normal">Unit Price</th>
                                  <th className="w-[15%] font-normal">Sub Total</th>
                                </tr>
                              </thead>
                              <tbody className="text-InterfaceTextdefault">
                                <tr className="border-t">
                                  <td className="pt-[16px] pb-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1-5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">5</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 788.57</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 78.86</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">INR 709.71</td>
                                  <td className="pt-[16px] pb-[8px] font-medium">1717.05</td>
                                </tr>
                                <tr className="border-t">
                                  <td className="pb-[16px] pt-[8px] font-medium">
                                    Mar 17, 2025 to Mar 31, 2025
                                  </td>
                                  <td className="pb-[16px] pt-[8px] font-medium">6-8</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">3</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 985.71</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 98.57</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">INR 887.14</td>
                                  <td className="pb-[16px] pt-[8px] font-medium">1287.79</td>
                                </tr>
                              </tbody>
                              <tfoot className="border-t">
                                <tr>
                                  <td
                                    colSpan={7}
                                    className="px-3 3xl:px-[0.625vw] py-2 3xl:py-[0.417vw] font16 text-right text-InterfaceTexttitle font-semibold"
                                  >
                                    Subtotal: 3004.84
                                  </td>
                                </tr>
                              </tfoot>
                            </table>
                            <p className="text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault mt-2">
                              *Please note that the Monthly/Quarterly/Half-Yearly prices on the
                              offer details are prorated.
                            </p>
                          </div>
                        ) : (
                          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2025 to Mar 16, 2026
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    40% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    25% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="bg-background py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-md space-y-2 border border-gray-100">
                              <h3 className="font-semibold text-InterfaceTexttitle font16 border-b border-Interface-Stroke-soft pb-[8px] 3xl:pb-[0.417vw]">
                                Mar 17, 2026 to Onwards
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    1 - 5 Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    35% {t('discountIncluded')}
                                  </p>
                                </div>
                                <div className="flex flex-col gap-[4px] 3xl:gap-[0.208vw]">
                                  <p className="font-semibold text-InterfaceTexttitle font14">
                                    6 - Onwards Licenses
                                  </p>
                                  <p className="font14 text-InterfaceTextdefault font-medium">
                                    INR 788.57/user/month
                                  </p>
                                  <p className="text-BrandPrimary600 font14 font-semibold">
                                    20% {t('discountIncluded')}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div>
                        <a
                          href="#"
                          className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] "
                        >
                          View Less Details
                        </a>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </>
            )}
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 bg-InterfaceTextwhite border-t border-InterfaceStrokedefault">
            <div className="p-[20px] xl:p-[16px] 3xl:p-[1.25vw] w-full flex justify-end items-center gap-[16px] 3xl:gap-[0.833vw]">
              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                variant="outline"
                size="sm"
                onClick={() => {
                  onClose(false);
                }}
              >
                <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('cancel')}
              </Button>

              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#067532] bg-BrandPrimarypurenew text-[#FFF] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                // variant="outline"
                size="sm"
                onClick={() => {
                  onClose(false);
                }}
              >
                <Image
                  src="/images/svg/send.svg"
                  width={16}
                  height={16}
                  className="h-[16px] xl:h-[14px] 3xl:h-[0.833vw] w-[16px] xl:w-[14px] 3xl:w-[0.833vw]"
                  alt="edit"
                />
                {t('apply')}
              </Button>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
