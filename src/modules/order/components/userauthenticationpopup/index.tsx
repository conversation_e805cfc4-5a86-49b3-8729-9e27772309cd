'use client';
import React from 'react';
import {
  Sheet,
  SheetContent,
  SheetClose,
  Label,
  Input,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import { TermsSheetProps } from '@/types/components';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface ChangePasswordProps extends TermsSheetProps {
  userData?: {
    userId?: string;
  };
}

export default function UserauthenticationPopup({ open, onClose }: ChangePasswordProps) {
  // const router = useRouter();

  return (
    <>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent
          className={`${roboto.className} max-w-[700px] sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[36.458vw] p-[0px] hideclose`}
          side={'right'}
        >
          <form>
            <div className="h-screen overflow-y-auto flex flex-col justify-center items-center">
              <div className="flex flex-col justify-center items-center gap-[40px] xl:gap-[30px] 3xl:gap-[2.083vw] mx-[110px] xl:mx-[110px] 2xl:mx-[110px] 3xl:mx-[5.729vw]">
                <i className="cloud-info2 text-BrandSupport1pure text-[72px] xl:text-[42px] 2xl:text-[50px] 3xl:text-[2.804vw]"></i>
                <div className="flex flex-col justify-center items-center gap-[8px] xl:gap-[6px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[24px] 2xl:text-[30px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                    User Authentication
                  </div>
                  <div className="text-InterfaceTextdefault text-[20px] lg:text-[20px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                    To proceed with the order placement, please complete the user authentication. A
                    OTP has be sent to your registered email ID.
                  </div>

                  <div
                    className="grid gap-1.5 w-full mt-[25px] xl:mt-[25px] 3xl:mt-[1.302vw]
                mb-[35px] xl:mb-[35px] 3xl:mb-[1.823vw] px-[10px] xl:px-[10px] 2xl:px-[10px] 3xl:px-[0.521vw]"
                  >
                    <Label
                      htmlFor="email"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Please Enter Your OTP
                    </Label>
                    <Input
                      type="text"
                      id="email"
                      placeholder="Enter your six digit code"
                      className="w-full placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                    <div className="flex items-center gap-[10px]">
                      <div className="text-InterfaceTextdefault font14 font-normal leading-[140%]">
                        Time remaining: 5:00 mins
                      </div>
                      <div className="cursor-pointer text-InterfaceTextprimary font14 leading-[140%] font-medium">
                        Resend OTP
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-center items-center gap-[16px] xl:gap-[12px] 3xl:gap-[0.833vw]  text-center">
                <SheetClose>
                  <Button className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTextdefault cancelbtn font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[25px] 2xl:px-[25px] 3xl:px-[1.302vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 ">
                    Cancel
                  </Button>
                </SheetClose>
                <Link
                  href={'/order/order-placement'}
                  className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-background submittbtn font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[25px] 2xl:px-[25px] 3xl:px-[1.302vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 "
                >
                  Submit
                </Link>
              </div>
            </div>
          </form>
        </SheetContent>
      </Sheet>
    </>
  );
}
