import { PartnerRemarkPopup } from '@/modules/order/components/partnerremark';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
  Input,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React, { useRef, useState } from 'react';

export default function Preorderdetails() {
  const [partnerremark, setPartnerRemark] = React.useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemove = () => {
    setFileName(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div>
      <div className="border-b border-InterfaceStrokesoft px-[18px] xl:px-[18px] 2xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw] bg-white">
        <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
          Pre-order Details
        </div>
      </div>
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="authorized-signatory">
          <AccordionTrigger className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[20px] xl:px-[20px] 2xl:px-[20px] 3xl:px-[1.042vw] border-b border-InterfaceStrokesoft bg-white hover:no-underline hover:bg-BrandNeutral100 data-[state=open]:bg-BrandNeutral100">
            <div className="w-full flex items-center justify-between">
              <div className="flex">
                <div className="pr-[24px] xl:pr-[24px] 2xl:pr-[24px] 3xl:pr-[1.25vw] border-r-2 border-InterfaceStrokehard text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  Pre Order 1
                </div>
                <div className="pl-[24px] xl:pl-[24px] 2xl:pl-[24px] 3xl:pl-[1.25vw]  text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  USD 1,800.00
                </div>
              </div>
              <div
                onClick={() => setPartnerRemark(true)}
                className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[100%] font-medium border border-InterfaceStrokesoft mr-[10px] xl:mr-[10px] 2xl:mr-[10px] 3xl:mr-[0.521vw] remarkbtn"
              >
                Partner Remarks
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className=" border border-InterfaceStrokesoft bg-[#FFF]">
            <div className="product-table custtableborder custtable custtable1 px-[16px] 3x:px-[0.833vw] pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
              <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
                <div className="border rounded-md overflow-x-auto w-full">
                  <table className="min-w-[1300px] w-full text-sm">
                    <thead className="bg-white text-center">
                      <tr>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium text-left">
                          Product Name
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Currency
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          List Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Discount
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Unit Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Quantity
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Sub Total
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          VAT%
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Estimated VAT
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Net Price
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Google Workspace Enterprise Essentials Plus
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                          <div className="mt-[6px] xl:mt-[6px] 2xl:mt-[6px] 3xl:mt-[0.313vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandSupport150 !border !border-InterfaceTextprimary">
                            <div className="flex items-center">
                              <i className="cloud-promotion-tag text-InterfaceTextprimary"></i>
                            </div>
                            Applied Promotion: ASETS12453
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Google Workspace Enterprise Essentials Plus
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="bg-white">
                        <td colSpan={6}></td>

                        <td colSpan={4} className="px-4">
                          <table className="w-full text-right text-sm">
                            <tbody>
                              <tr>
                                <td className=" text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Sub Total
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  2,000.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  VAT
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  0.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Discount
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  200.00
                                </td>
                              </tr>
                              <tr className=" text-InterfaceTexttitle font14 font-semibold leading-[140%]">
                                <td className="">Pre Order 1 Total</td>
                                <td className="">1,800.00</td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="flex flex-col gap-1 mt-[8px] xl:mt-[8px] 2xl:mt-[8px] 3xl:mt-[0.417vw]">
                  <Label className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium">
                    LPO{' '}
                  </Label>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                  />

                  <div className="flex items-center">
                    <div>
                      <Input
                        type="text"
                        placeholder="Enter Reference Number"
                        className="w-[400px] xl:w-[350px] 2xl:w-[425px] 3xl:w-[22.79vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:font-normal placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor !border !border-InterfaceStrokehard focus:outline-none focus:ring-0 focus:border-InterfaceStrokehard h-full relative"
                      />
                      <div className="absolute mt-[5px] xl:mt-[5px] 2xl:mt-[5px] 3xl:mt-[0.26vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                        *JPG, PNG, PDF - File Upload Size 2MB
                      </div>
                    </div>
                    <div className="px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] leading-[140%]">
                      OR
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleUploadClick}
                      className="w-fit gap-2 py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.417vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-white hover:text-white bg-BrandNeutralpure !border !border-BrandNeutral700 hover:bg-BrandNeutralpure rounded-none text-[14px] font-normal leading-[140%]"
                    >
                      Upload
                    </Button>
                  </div>

                  {fileName && (
                    <div className="mt-[20px] xl:mt-[20px] 2xl:mt-[20px] 3xl:mt-[1.042vw] w-fit flex items-center justify-between gap-[10px] px-[7px] xl:px-[7px] 2xl:px-[7px] 3xl:px-[0.365vw] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.208vw] border bg-InterfaceSurfacecomponentmuted text-sm rounded-none">
                      <span className="truncate max-w-[200px] text-InterfaceTextprimary">
                        {fileName}
                      </span>
                      <Button
                        variant="outline"
                        type="button"
                        onClick={handleRemove}
                        className="p-0 bg-InterfaceSurfacecomponentmuted"
                      >
                        <i className="cloud-trash text-sm"></i>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Accordion type="single" collapsible className="w-full mt-[6px]">
        <AccordionItem value="authorized-signatory">
          <AccordionTrigger className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[20px] xl:px-[20px] 2xl:px-[20px] 3xl:px-[1.042vw] border-b border-InterfaceStrokesoft bg-white hover:no-underline hover:bg-BrandNeutral100 data-[state=open]:bg-BrandNeutral100">
            <div className="w-full flex items-center justify-between">
              <div className="flex">
                <div className="pr-[24px] xl:pr-[24px] 2xl:pr-[24px] 3xl:pr-[1.25vw] border-r-2 border-InterfaceStrokehard text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  Pre Order 2
                </div>
                <div className="pl-[24px] xl:pl-[24px] 2xl:pl-[24px] 3xl:pl-[1.25vw]  text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  USD 1,800.00
                </div>
              </div>
              <div
                onClick={() => setPartnerRemark(true)}
                className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[100%] font-medium border border-InterfaceStrokesoft mr-[10px] xl:mr-[10px] 2xl:mr-[10px] 3xl:mr-[0.521vw] remarkbtn"
              >
                Partner Remarks
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className=" border border-InterfaceStrokesoft bg-[#FFF]">
            <div className="product-table custtableborder custtable custtable1 px-[16px] 3x:px-[0.833vw] pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
              <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
                <div className="border rounded-md overflow-x-auto w-full">
                  <table className="min-w-[1300px] w-full text-sm">
                    <thead className="bg-white text-center">
                      <tr>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium text-left">
                          Product Name
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Currency
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          List Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Discount
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Unit Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Quantity
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Sub Total
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          VAT%
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Estimated VAT
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Net Price
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Advanced Communication
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                          <div className="mt-[6px] xl:mt-[6px] 2xl:mt-[6px] 3xl:mt-[0.313vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandSupport150 !border !border-InterfaceTextprimary">
                            <div className="flex items-center">
                              <i className="cloud-promotion-tag text-InterfaceTextprimary"></i>
                            </div>
                            Available Promotions
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Google Workspace Enterprise Essentials Plus
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="bg-white">
                        <td colSpan={6}></td>

                        <td colSpan={4} className="px-4">
                          <table className="w-full text-right text-sm">
                            <tbody>
                              <tr>
                                <td className=" text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Sub Total
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  2,000.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  VAT
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  0.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Discount
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  200.00
                                </td>
                              </tr>
                              <tr className=" text-InterfaceTexttitle font14 font-semibold leading-[140%]">
                                <td className="">Pre Order 1 Total</td>
                                <td className="">1,800.00</td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="flex flex-col gap-1 mt-[8px] xl:mt-[8px] 2xl:mt-[8px] 3xl:mt-[0.417vw]">
                  <Label className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium">
                    LPO{' '}
                  </Label>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                  />

                  <div className="flex items-center">
                    <div>
                      <Input
                        type="text"
                        placeholder="Enter Reference Number"
                        className="w-[400px] xl:w-[350px] 2xl:w-[425px] 3xl:w-[22.79vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:font-normal placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor !border !border-InterfaceStrokehard focus:outline-none focus:ring-0 focus:border-InterfaceStrokehard h-full relative"
                      />
                      <div className="absolute mt-[5px] xl:mt-[5px] 2xl:mt-[5px] 3xl:mt-[0.26vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                        *JPG, PNG, PDF - File Upload Size 2MB
                      </div>
                    </div>
                    <div className="px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] leading-[140%]">
                      OR
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleUploadClick}
                      className="w-fit gap-2 py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.417vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-white hover:text-white bg-BrandNeutralpure !border !border-BrandNeutral700 hover:bg-BrandNeutralpure rounded-none text-[14px] font-normal leading-[140%]"
                    >
                      Upload
                    </Button>
                  </div>

                  {fileName && (
                    <div className="mt-[20px] xl:mt-[20px] 2xl:mt-[20px] 3xl:mt-[1.042vw] w-fit flex items-center justify-between gap-[10px] px-[7px] xl:px-[7px] 2xl:px-[7px] 3xl:px-[0.365vw] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.208vw] border bg-InterfaceSurfacecomponentmuted text-sm rounded-none">
                      <span className="truncate max-w-[200px] text-InterfaceTextprimary">
                        {fileName}
                      </span>
                      <Button
                        variant="outline"
                        type="button"
                        onClick={handleRemove}
                        className="p-0 bg-InterfaceSurfacecomponentmuted"
                      >
                        <i className="cloud-trash text-sm"></i>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Accordion type="single" collapsible className="w-full mt-[6px]">
        <AccordionItem value="authorized-signatory">
          <AccordionTrigger className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[20px] xl:px-[20px] 2xl:px-[20px] 3xl:px-[1.042vw] border-b border-InterfaceStrokesoft bg-white hover:no-underline hover:bg-BrandNeutral100 data-[state=open]:bg-BrandNeutral100">
            <div className="w-full flex items-center justify-between">
              <div className="flex">
                <div className="pr-[24px] xl:pr-[24px] 2xl:pr-[24px] 3xl:pr-[1.25vw] border-r-2 border-InterfaceStrokehard text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  Pre Order 3
                </div>
                <div className="pl-[24px] xl:pl-[24px] 2xl:pl-[24px] 3xl:pl-[1.25vw]  text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  USD 1,800.00
                </div>
              </div>
              <div
                onClick={() => setPartnerRemark(true)}
                className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[100%] font-medium border border-InterfaceStrokesoft mr-[10px] xl:mr-[10px] 2xl:mr-[10px] 3xl:mr-[0.521vw] remarkbtn"
              >
                Partner Remarks
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className=" border border-InterfaceStrokesoft bg-[#FFF]">
            <div className="product-table custtableborder custtable custtable1 px-[16px] 3x:px-[0.833vw] pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
              <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
                <div className="border rounded-md overflow-x-auto w-full">
                  <table className="min-w-[1300px] w-full text-sm">
                    <thead className="bg-white text-center">
                      <tr>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium text-left">
                          Product Name
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Currency
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          List Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Discount
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Unit Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Quantity
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Sub Total
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          VAT%
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Estimated VAT
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Net Price
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Advanced Communication
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                          <div className="mt-[6px] xl:mt-[6px] 2xl:mt-[6px] 3xl:mt-[0.313vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandSupport150 !border !border-InterfaceTextprimary">
                            <div className="flex items-center">
                              <i className="cloud-promotion-tag text-InterfaceTextprimary"></i>
                            </div>
                            Available Promotions
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Google Workspace Enterprise Essentials Plus
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="bg-white">
                        <td colSpan={6}></td>

                        <td colSpan={4} className="px-4">
                          <table className="w-full text-right text-sm">
                            <tbody>
                              <tr>
                                <td className=" text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Sub Total
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  2,000.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  VAT
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  0.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Discount
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  200.00
                                </td>
                              </tr>
                              <tr className=" text-InterfaceTexttitle font14 font-semibold leading-[140%]">
                                <td className="">Pre Order 1 Total</td>
                                <td className="">1,800.00</td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="flex flex-col gap-1 mt-[8px] xl:mt-[8px] 2xl:mt-[8px] 3xl:mt-[0.417vw]">
                  <Label className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium">
                    LPO{' '}
                  </Label>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                  />

                  <div className="flex items-center">
                    <div>
                      <Input
                        type="text"
                        placeholder="Enter Reference Number"
                        className="w-[400px] xl:w-[350px] 2xl:w-[425px] 3xl:w-[22.79vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:font-normal placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor !border !border-InterfaceStrokehard focus:outline-none focus:ring-0 focus:border-InterfaceStrokehard h-full relative"
                      />
                      <div className="absolute mt-[5px] xl:mt-[5px] 2xl:mt-[5px] 3xl:mt-[0.26vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                        *JPG, PNG, PDF - File Upload Size 2MB
                      </div>
                    </div>
                    <div className="px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] leading-[140%]">
                      OR
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleUploadClick}
                      className="w-fit gap-2 py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.417vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-white hover:text-white bg-BrandNeutralpure !border !border-BrandNeutral700 hover:bg-BrandNeutralpure rounded-none text-[14px] font-normal leading-[140%]"
                    >
                      Upload
                    </Button>
                  </div>

                  {fileName && (
                    <div className="mt-[20px] xl:mt-[20px] 2xl:mt-[20px] 3xl:mt-[1.042vw] w-fit flex items-center justify-between gap-[10px] px-[7px] xl:px-[7px] 2xl:px-[7px] 3xl:px-[0.365vw] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.208vw] border bg-InterfaceSurfacecomponentmuted text-sm rounded-none">
                      <span className="truncate max-w-[200px] text-InterfaceTextprimary">
                        {fileName}
                      </span>
                      <Button
                        variant="outline"
                        type="button"
                        onClick={handleRemove}
                        className="p-0 bg-InterfaceSurfacecomponentmuted"
                      >
                        <i className="cloud-trash text-sm"></i>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Accordion type="single" collapsible className="w-full mt-[6px]">
        <AccordionItem value="authorized-signatory">
          <AccordionTrigger className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[20px] xl:px-[20px] 2xl:px-[20px] 3xl:px-[1.042vw] border-b border-InterfaceStrokesoft bg-white hover:no-underline hover:bg-BrandNeutral100 data-[state=open]:bg-BrandNeutral100">
            <div className="w-full flex items-center justify-between">
              <div className="flex">
                <div className="pr-[24px] xl:pr-[24px] 2xl:pr-[24px] 3xl:pr-[1.25vw] border-r-2 border-InterfaceStrokehard text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  Pre Order 4
                </div>
                <div className="pl-[24px] xl:pl-[24px] 2xl:pl-[24px] 3xl:pl-[1.25vw]  text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  USD 1,800.00
                </div>
              </div>
              <div
                onClick={() => setPartnerRemark(true)}
                className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[100%] font-medium border border-InterfaceStrokesoft mr-[10px] xl:mr-[10px] 2xl:mr-[10px] 3xl:mr-[0.521vw] remarkbtn"
              >
                Partner Remarks
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className=" border border-InterfaceStrokesoft bg-[#FFF]">
            <div className="product-table custtableborder custtable custtable1 px-[16px] 3x:px-[0.833vw] pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
              <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
                <div className="border rounded-md overflow-x-auto w-full">
                  <table className="min-w-[1300px] w-full text-sm">
                    <thead className="bg-white text-center">
                      <tr>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium text-left">
                          Product Name
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Currency
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          List Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Discount
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Unit Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Quantity
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Sub Total
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          VAT%
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Estimated VAT
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Net Price
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Advanced Communication
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                          <div className="mt-[6px] xl:mt-[6px] 2xl:mt-[6px] 3xl:mt-[0.313vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandSupport150 !border !border-InterfaceTextprimary">
                            <div className="flex items-center">
                              <i className="cloud-promotion-tag text-InterfaceTextprimary"></i>
                            </div>
                            Available Promotions
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Google Workspace Enterprise Essentials Plus
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="bg-white">
                        <td colSpan={6}></td>

                        <td colSpan={4} className="px-4">
                          <table className="w-full text-right text-sm">
                            <tbody>
                              <tr>
                                <td className=" text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Sub Total
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  2,000.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  VAT
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  0.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Discount
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  200.00
                                </td>
                              </tr>
                              <tr className=" text-InterfaceTexttitle font14 font-semibold leading-[140%]">
                                <td className="">Pre Order 1 Total</td>
                                <td className="">1,800.00</td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="flex flex-col gap-1 mt-[8px] xl:mt-[8px] 2xl:mt-[8px] 3xl:mt-[0.417vw]">
                  <Label className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium">
                    LPO{' '}
                  </Label>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                  />

                  <div className="flex items-center">
                    <div>
                      <Input
                        type="text"
                        placeholder="Enter Reference Number"
                        className="w-[400px] xl:w-[350px] 2xl:w-[425px] 3xl:w-[22.79vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:font-normal placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor !border !border-InterfaceStrokehard focus:outline-none focus:ring-0 focus:border-InterfaceStrokehard h-full relative"
                      />
                      <div className="absolute mt-[5px] xl:mt-[5px] 2xl:mt-[5px] 3xl:mt-[0.26vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                        *JPG, PNG, PDF - File Upload Size 2MB
                      </div>
                    </div>
                    <div className="px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] leading-[140%]">
                      OR
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleUploadClick}
                      className="w-fit gap-2 py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.417vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-white hover:text-white bg-BrandNeutralpure !border !border-BrandNeutral700 hover:bg-BrandNeutralpure rounded-none text-[14px] font-normal leading-[140%]"
                    >
                      Upload
                    </Button>
                  </div>

                  {fileName && (
                    <div className="mt-[20px] xl:mt-[20px] 2xl:mt-[20px] 3xl:mt-[1.042vw] w-fit flex items-center justify-between gap-[10px] px-[7px] xl:px-[7px] 2xl:px-[7px] 3xl:px-[0.365vw] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.208vw] border bg-InterfaceSurfacecomponentmuted text-sm rounded-none">
                      <span className="truncate max-w-[200px] text-InterfaceTextprimary">
                        {fileName}
                      </span>
                      <Button
                        variant="outline"
                        type="button"
                        onClick={handleRemove}
                        className="p-0 bg-InterfaceSurfacecomponentmuted"
                      >
                        <i className="cloud-trash text-sm"></i>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Accordion type="single" collapsible className="w-full mt-[6px]">
        <AccordionItem value="authorized-signatory">
          <AccordionTrigger className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[20px] xl:px-[20px] 2xl:px-[20px] 3xl:px-[1.042vw] border-b border-InterfaceStrokesoft bg-white hover:no-underline hover:bg-BrandNeutral100 data-[state=open]:bg-BrandNeutral100">
            <div className="w-full flex items-center justify-between">
              <div className="flex">
                <div className="pr-[24px] xl:pr-[24px] 2xl:pr-[24px] 3xl:pr-[1.25vw] border-r-2 border-InterfaceStrokehard text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  Pre Order 5
                </div>
                <div className="pl-[24px] xl:pl-[24px] 2xl:pl-[24px] 3xl:pl-[1.25vw]  text-blackcolor text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-medium">
                  USD 1,800.00
                </div>
              </div>
              <div
                onClick={() => setPartnerRemark(true)}
                className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[100%] font-medium border border-InterfaceStrokesoft mr-[10px] xl:mr-[10px] 2xl:mr-[10px] 3xl:mr-[0.521vw] remarkbtn"
              >
                Partner Remarks
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className=" border border-InterfaceStrokesoft bg-[#FFF]">
            <div className="product-table custtableborder custtable custtable1 px-[16px] 3x:px-[0.833vw] pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
              <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
                <div className="border rounded-md overflow-x-auto w-full">
                  <table className="min-w-[1300px] w-full text-sm">
                    <thead className="bg-white text-center">
                      <tr>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium text-left">
                          Product Name
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Currency
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          List Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Discount
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Unit Price
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Quantity
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Sub Total
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          VAT%
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Estimated VAT
                        </th>
                        <th className="px-4 py-2 text-[13px] xl:text-[13px] 2xl:xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle font-medium">
                          Net Price
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Advanced Communication
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                          <div className="mt-[6px] xl:mt-[6px] 2xl:mt-[6px] 3xl:mt-[0.313vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandSupport150 !border !border-InterfaceTextprimary">
                            <div className="flex items-center">
                              <i className="cloud-promotion-tag text-InterfaceTextprimary"></i>
                            </div>
                            Available Promotions
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="border-b">
                        <td className="px-4 py-2 align-top">
                          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
                            Google Workspace Enterprise Essentials Plus
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Brand:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Google</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Segment:{' '}
                              <span className="text-InterfaceTextdefault font-normal">
                                Commercial
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-3">
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Term:{' '}
                              <span className="text-InterfaceTextdefault font-normal"> 1 Year</span>
                            </div>
                            <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                              Bill Type:{' '}
                              <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                            </div>
                          </div>
                          <div className="mt-[10px] xl:mt-[10px] 2xl:mt-[10px] 3xl:mt-[0.521vw] w-fit flex items-center gap-[4px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.729vw] font-medium leaidng-[140%] px-[9px] xl:px-[9px] 2xl:px-[9px] 3xl:px-[0.469vw] py-[3px] xl:py-[3px] 2xl:py-[3px] 3xl:py-[0.156vw] bg-BrandHighlight50 !border !border-BrandHighlightpure">
                            <div className="flex items-center">
                              <i className="cloud-discount-fillshap text-BrandSupport2400"></i>
                            </div>
                            Applied Coupon: AYETL123THJ
                          </div>
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          USD
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          200.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          10.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          190.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          4
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          0.00
                        </td>
                        <td className="px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-center text-InterfaceTextdefault font-[500]">
                          790.00
                        </td>
                      </tr>

                      <tr className="bg-white">
                        <td colSpan={6}></td>

                        <td colSpan={4} className="px-4">
                          <table className="w-full text-right text-sm">
                            <tbody>
                              <tr>
                                <td className=" text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Sub Total
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  2,000.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  VAT
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  0.00
                                </td>
                              </tr>
                              <tr>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  Discount
                                </td>
                                <td className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-normal leading-[140%]">
                                  200.00
                                </td>
                              </tr>
                              <tr className=" text-InterfaceTexttitle font14 font-semibold leading-[140%]">
                                <td className="">Pre Order 1 Total</td>
                                <td className="">1,800.00</td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="flex flex-col gap-1 mt-[8px] xl:mt-[8px] 2xl:mt-[8px] 3xl:mt-[0.417vw]">
                  <Label className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium">
                    LPO{' '}
                  </Label>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                  />

                  <div className="flex items-center">
                    <div>
                      <Input
                        type="text"
                        placeholder="Enter Reference Number"
                        className="w-[400px] xl:w-[350px] 2xl:w-[425px] 3xl:w-[22.79vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:font-normal placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor !border !border-InterfaceStrokehard focus:outline-none focus:ring-0 focus:border-InterfaceStrokehard h-full relative"
                      />
                      <div className="absolute mt-[5px] xl:mt-[5px] 2xl:mt-[5px] 3xl:mt-[0.26vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                        *JPG, PNG, PDF - File Upload Size 2MB
                      </div>
                    </div>
                    <div className="px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] leading-[140%]">
                      OR
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleUploadClick}
                      className="w-fit gap-2 py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.417vw] px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] text-white hover:text-white bg-BrandNeutralpure !border !border-BrandNeutral700 hover:bg-BrandNeutralpure rounded-none text-[14px] font-normal leading-[140%]"
                    >
                      Upload
                    </Button>
                  </div>

                  {fileName && (
                    <div className="mt-[20px] xl:mt-[20px] 2xl:mt-[20px] 3xl:mt-[1.042vw] w-fit flex items-center justify-between gap-[10px] px-[7px] xl:px-[7px] 2xl:px-[7px] 3xl:px-[0.365vw] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.208vw] border bg-InterfaceSurfacecomponentmuted text-sm rounded-none">
                      <span className="truncate max-w-[200px] text-InterfaceTextprimary">
                        {fileName}
                      </span>
                      <Button
                        variant="outline"
                        type="button"
                        onClick={handleRemove}
                        className="p-0 bg-InterfaceSurfacecomponentmuted"
                      >
                        <i className="cloud-trash text-sm"></i>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      <PartnerRemarkPopup open={partnerremark} onClose={() => setPartnerRemark(false)} />
    </div>
  );
}
// bg-InterfaceSurfacecomponentmuted
