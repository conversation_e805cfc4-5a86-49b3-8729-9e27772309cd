import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import Link from 'next/link';
import React from 'react';

export default function BillingAddress() {
  const [endcustomer, setEndcustomer] = React.useState('');

  const endCustomerOptions: Select2Option[] = [
    { label: 'address one', value: 'Binghati' },
    { label: 'address two', value: 'ABC' },
  ];
  return (
    <>
      <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] bg-interfacesurfacecomponent min-h-[245px] 3xl:min-h-[12.76vw]">
        <div className="font16 font-medium text-InterfaceTexttitle mb-[16px] 3xl:mb-[0.833vw]">
          Billing Address/Delivery Sequence
        </div>
        <div>
          <Select2
            className={'text-wrap text-left'}
            options={endCustomerOptions}
            value={endcustomer}
            placeholder="Select Billing Address"
            onChange={(val) => setEndcustomer(val as string)}
          />
        </div>
        <div className="mt-[16px] 3xl:mt-[0.833vw]">
          <div className="font14 font-medium text-InterfaceTextdefault">Curly Binaries Lab LLP</div>
          <div className="font12 font-normal text-InterfaceTextdefault">B-4, Komal RROw House</div>
          <div className="font12 font-normal text-InterfaceTextdefault">
            Ashoka Marg, Kalptaru Nagar, Nashik
          </div>
          <div className="font12 font-normal text-InterfaceTextdefault">Maharashtra - 422214</div>
        </div>
      </div>
      <div className="flex items-center gap-[16px] 3xl:gap-[0.833vw] w-full mt-[22px] 3xl:mt-[1.25vw]">
        <Link
          href={'/order'}
          className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] font-[500] text-center w-full"
        >
          Go Back
        </Link>
        <Link
          href={'/order/order-review'}
          className="border border-BrandPrimary50new text-[14px] 3xl:text-[0.729vw] rounded-none py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw font-[500] text-interfacetextinverse bg-BrandPrimarypurenew hover:bg-BrandPrimary700 text-center w-full"
        >
          Review Order
        </Link>
      </div>
    </>
  );
}
