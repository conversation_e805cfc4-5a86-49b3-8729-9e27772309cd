'use client';
import OrderStatus from '@/components/common/orderstatus';
import React from 'react';
import { Roboto } from 'next/font/google';
import CartDetails from '../../components/cartdetails';
import EndCustomerDetails from '../../components/endcustomerdetails';
import BillingAddress from '../components/billingaddress';
import Preorderdetails from '../components/preorder-details';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
export default function CheckOut() {
  return (
    <>
      <div
        className={`${roboto.className} grid grid-cols-12 gap-[22px] 3xl:gap-[1.25vw] mt-[22px] 3xl:mt-[1.25vw] px-[28px] 3xl:px-[1.458vw]`}
      >
        <div className="col-span-12 xl:col-span-9">
          <OrderStatus currentStepId={2} />

          <CartDetails partnerRemarksBtn={false} isEdit={false} />
          <EndCustomerDetails
            isEditEndCustomer={true}
            isSectondaryValidation={true}
            isAvailabelCredit={true}
          />

          <Preorderdetails />
        </div>
        <div className="col-span-12 xl:col-span-3">
          <BillingAddress />
        </div>
      </div>
    </>
  );
}
