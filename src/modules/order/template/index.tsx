'use client';
import OrderStatus from '@/components/common/orderstatus';
import React from 'react';
import { Roboto } from 'next/font/google';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import ItemDetailsTable from '../components/item-details-table';
import CartSummary from '../components/cart-summary';
import Image from 'next/image';
import CartDetails from '../components/cartdetails';
import EndCustomerDetails from '../components/endcustomerdetails';
import { useTranslations } from 'next-intl';
import ProductDetailedView from '@/modules/marketplace/components/product-details-view';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
export default function Order() {
  const t = useTranslations();
  const [productdetailsPopupOpen, setProductdetailsPopupOpen] = React.useState(false);
  const [tab, setTab] = React.useState(0);

  const productData = [
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 5',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 2',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 3',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 4',
      segment: 'Commercial',
    },
  ];
  const productData1 = [
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 1',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 2',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 3',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 4',
      segment: 'Commercial',
    },
  ];
  const productData2 = [
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 3',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 2',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 3',
      segment: 'Commercial',
    },
    {
      logo: '/images/svg/microsoft_icon.svg',
      title: 'Microsoft 365 Apps for Enterprise 4',
      segment: 'Commercial',
    },
  ];
  return (
    <>
      <div
        className={`${roboto.className} grid grid-cols-12 gap-[22px] 3xl:gap-[1.25vw] mt-[22px] 3xl:mt-[1.25vw] px-[28px] 3xl:px-[1.458vw]`}
      >
        <div className="col-span-12 xl:col-span-9">
          <OrderStatus currentStepId={1} />

          <CartDetails />
          <EndCustomerDetails />
          <div>
            <ItemDetailsTable />
          </div>
        </div>
        <div className="col-span-12 xl:col-span-3">
          <CartSummary />
          <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] bg-interfacesurfacecomponent mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.938vw]">
            <div className="flex items-center">
              <Button
                onClick={() => setTab(0)}
                className={`${tab === 0 ? 'bg-BrandNeutral950 text-interfacetextinverse' : 'bg-BrandNeutral50 text-InterfaceTextdefault'} font-normal px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] font14 leading-[140%] rounded-none rounded-l-[6px] 3xl:rounded-l-[0.333vw] text-wrap`}
              >
                {t('relatedProducts')}
              </Button>
              <Button
                onClick={() => setTab(1)}
                className={`${tab === 1 ? 'bg-BrandNeutral950 text-interfacetextinverse' : 'bg-BrandNeutral50 text-InterfaceTextdefault'} font-normal px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] font14 leading-[140%] rounded-none text-wrap`}
              >
                {t('crosssellProducts')}
              </Button>
              <Button
                onClick={() => setTab(2)}
                className={`${tab === 2 ? 'bg-BrandNeutral950 text-interfacetextinverse' : 'bg-BrandNeutral50 text-InterfaceTextdefault'} font-normal px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] font14 leading-[140%] rounded-none rounded-r-[6px] 3xl:rounded-r-[0.333vw] text-wrap`}
              >
                {t('upsellProducts')}
              </Button>
            </div>

            <div className="space-y-[12px] 3xl:space-y-[0.625vw] overflow-auto h-[400px] 3xl:h-[20.833vw]">
              {tab === 0 && (
                <>
                  {productData.map((items, index) => {
                    return (
                      <div
                        key={index}
                        className="p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft mt-[16px] 3xl:mt-[0.833vw]"
                      >
                        <div className="grid grid-cols-12 gap-1">
                          <div className="col-span-1 xl:col-span-3 2xl:col-span-2 w-[48px] 3xl:w-[2.5vw] h-[48px] 3xl:h-[2.5vw] flex items-center justify-center">
                            <Image
                              width={40}
                              height={40}
                              className="w-[48px] 3xl:w-[2.5vw] h-[48px] 3xl:h-[2.5vw]"
                              src={`${items.logo}`}
                              alt="microsoft logo"
                            />
                          </div>
                          <div className="col-span-11 xl:col-span-9 2xl:col-span-10">
                            <div
                              title={items.title}
                              className="text-InterfaceTexttitle font16 font-semibold leading-[140%] truncate"
                            >
                              {items.title}
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="col-span-3 text-InterfaceTextdefault font14 font-normal leading-[140%]">
                                Segment{' '}
                              </div>
                              <div className="col-span-9 text-InterfaceTextsubtitle font14 font-normal leading-[140%]">
                                : {items.segment}
                              </div>
                            </div>
                          </div>
                        </div>
                        <Button
                          onClick={() => setProductdetailsPopupOpen(true)}
                          className="flex justify-center bg-yellowbg px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.729vw] py-[6px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none font-[500] text-InterfaceTexttitle mt-[12px] 3xl:mt-[0.625vw]"
                        >
                          <i className="cloud-cart text-[16px] 3xl:text-[0.833vw]"></i>
                          <span className="text text-[14px] xl:text-[16px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                            {t('addToCart')}
                          </span>
                        </Button>
                      </div>
                    );
                  })}
                </>
              )}
              {tab === 1 && (
                <>
                  {productData1.map((items, index) => {
                    return (
                      <div
                        key={index}
                        className="p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft mt-[16px] 3xl:mt-[0.833vw]"
                      >
                        <div className="grid grid-cols-12 gap-1">
                          <div className="col-span-1 xl:col-span-3 2xl:col-span-2 w-[48px] 3xl:w-[2.5vw] h-[48px] 3xl:h-[2.5vw]">
                            <Image
                              width={40}
                              height={40}
                              className="w-[48px] 3xl:w-[2.5vw] h-[48px] 3xl:h-[2.5vw]"
                              src={`${items.logo}`}
                              alt="microsoft logo"
                            />
                          </div>
                          <div className="col-span-11 xl:col-span-9 2xl:col-span-10">
                            <div
                              title={items.title}
                              className="text-InterfaceTexttitle font16 font-semibold leading-[140%] truncate"
                            >
                              {items.title}
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="col-span-3 text-InterfaceTextdefault font14 font-normal leading-[140%]">
                                Segment{' '}
                              </div>
                              <div className="col-span-9 text-InterfaceTextsubtitle font14 font-normal leading-[140%]">
                                : {items.segment}
                              </div>
                            </div>
                          </div>
                        </div>
                        <Button
                          onClick={() => setProductdetailsPopupOpen(true)}
                          className="flex justify-center bg-yellowbg px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.729vw] py-[6px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none font-[500] text-InterfaceTexttitle mt-[12px] 3xl:mt-[0.625vw]"
                        >
                          <i className="cloud-cart text-[16px] 3xl:text-[0.833vw]"></i>
                          <span className="text text-[14px] xl:text-[16px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                            {t('addToCart')}
                          </span>
                        </Button>
                      </div>
                    );
                  })}
                </>
              )}
              {tab === 2 && (
                <>
                  {productData2.map((items, index) => {
                    return (
                      <div
                        key={index}
                        className="p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft mt-[16px] 3xl:mt-[0.833vw]"
                      >
                        <div className="grid grid-cols-12 gap-1">
                          <div className="col-span-1 xl:col-span-3 2xl:col-span-2 w-[48px] 3xl:w-[2.5vw] h-[48px] 3xl:h-[2.5vw]">
                            <Image
                              width={40}
                              height={40}
                              className="w-[48px] 3xl:w-[2.5vw] h-[48px] 3xl:h-[2.5vw]"
                              src={`${items.logo}`}
                              alt="microsoft logo"
                            />
                          </div>
                          <div className="col-span-11 xl:col-span-9 2xl:col-span-10">
                            <div
                              title={items.title}
                              className="text-InterfaceTexttitle font16 font-semibold leading-[140%] truncate"
                            >
                              {items.title}
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="col-span-3 text-InterfaceTextdefault font14 font-normal leading-[140%]">
                                Segment{' '}
                              </div>
                              <div className="col-span-9 text-InterfaceTextsubtitle font14 font-normal leading-[140%]">
                                : {items.segment}
                              </div>
                            </div>
                          </div>
                        </div>
                        <Button
                          onClick={() => setProductdetailsPopupOpen(true)}
                          className="flex justify-center bg-yellowbg px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.729vw] py-[6px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none font-[500] text-InterfaceTexttitle mt-[12px] 3xl:mt-[0.625vw]"
                        >
                          <i className="cloud-cart text-[16px] 3xl:text-[0.833vw]"></i>
                          <span className="text text-[14px] xl:text-[16px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                            Add to Cart
                          </span>
                        </Button>
                      </div>
                    );
                  })}
                </>
              )}
            </div>
          </div>
        </div>
        <ProductDetailedView
          open={productdetailsPopupOpen}
          onClose={() => setProductdetailsPopupOpen(false)}
        />
      </div>
    </>
  );
}
