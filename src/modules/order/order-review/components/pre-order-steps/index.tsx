import UserauthenticationPopup from '@/modules/order/components/userauthenticationpopup';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
// import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

export default function PreOrderSteps() {
  type Step = {
    id: number;
    title: string;
  };

  const stepsData: Step[] = [
    { id: 1, title: 'Pre Order 1' },
    { id: 2, title: 'Pre Order 2' },
    { id: 3, title: 'Pre Order 3' },
    { id: 4, title: 'Pre Order 4' },
    { id: 5, title: 'Pre Order 5' },
  ];
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [userauthenticationpopup, setUserauthenticationpopup] = useState(false);
  // const router = useRouter();

  const handlePrev = (): void => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = (): void => {
    setCurrentStep((prev) => Math.min(prev + 1, stepsData.length));
  };

  return (
    <div className="flex flex-wrap items-center justify-between gap-2 px-[20px] 3xl:px-[1.042vw] py-[10px] 3xl:py-[0.521vw] bg-interfacesurfacecomponent cardShadow">
      <div className="flex items-center gap-[20px] 3xl:gap-[1.667vw]">
        {/* Prev Button */}
        <div>
          <Button
            type="button"
            onClick={handlePrev}
            disabled={currentStep === 1}
            className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault font14 py-[8px] 3xl:py-[0.521vw] px-[14px] 3xl:px-[0.833vw] font-medium text-center w-full"
          >
            <i className="cloud-arrowleft text-[10px] 3xl:text-[0.625vw]"></i>
            Prev
          </Button>
        </div>

        {/* Steps */}
        <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] w-full">
          {stepsData.map((step, index) => {
            const isCompleted = step.id < currentStep;
            const isActive = step.id === currentStep;

            return (
              <div key={step.id} className="flex items-center">
                {/* Step Circle */}
                {isCompleted ? (
                  <i className="cloud-fillcircletick text-BrandHighlightpure text-[28px] 3xl:text-[1.667vw]" />
                ) : (
                  <div
                    className={`font14 font-normal w-[28px] h-[28px] 3xl:w-[1.667vw] 3xl:h-[1.667vw] rounded-full flex items-center justify-center
                                        ${
                                          isCompleted
                                            ? 'bg-BrandHighlightpure text-InterfaceTextwhite'
                                            : isActive
                                              ? 'bg-background border border-InterfaceStrokehard'
                                              : 'bg-background border border-dashed border-InterfaceStrokehard'
                                        }`}
                  >
                    {step.id}
                  </div>
                )}

                <div
                  className={`font14 font-medium ml-2 ${
                    isCompleted
                      ? 'text-BrandHighlight600'
                      : isActive
                        ? 'text-InterfaceTexttitle'
                        : 'text-InterfaceTextsubtitle'
                  }`}
                >
                  {step.title}
                </div>

                {index < stepsData.length - 1 && (
                  <div className="bg-InterfaceStrokesoft w-[60px] 3xl:w-[6.25vw] h-[2px] 3xl:h-[0.104vw] mx-2"></div>
                )}
              </div>
            );
          })}
        </div>

        {/* Next Button */}
        <div>
          <Button
            type="button"
            disabled={currentStep === stepsData.length}
            onClick={handleNext}
            className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault font14 py-[8px] 3xl:py-[0.521vw] px-[14px] 3xl:px-[0.833vw] font-medium text-center w-full"
          >
            Next
            <i className="cloud-rightarrow1 text-[10px] 3xl:text-[0.625vw]"></i>
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-[14px] 3xl:gap-[0.833vw]">
        <Link
          href={'/order/checkout'}
          className="border border-InterfaceStrokesoft rounded-none graygradientbtn text-InterfaceTextdefault font14 py-[8px] 3xl:py-[0.521vw] px-[14px] 3xl:px-[0.833vw] font-medium text-center w-full"
        >
          Go Back
        </Link>
        <Button
          type="button"
          // onClick={() => router.push('/order/order-placement')}
          onClick={() => setUserauthenticationpopup(true)}
          disabled={!(currentStep === stepsData.length)}
          className="border border-BrandPrimary50new font14 rounded-none py-[8px] 3xl:py-[0.521vw] px-[14px] 3xl:px-[0.833vw] font-medium text-interfacetextinverse bg-BrandPrimarypurenew text-center w-full"
        >
          Place Order
        </Button>
      </div>
      <UserauthenticationPopup
        open={userauthenticationpopup}
        onClose={() => setUserauthenticationpopup(false)}
      />
    </div>
  );
}
