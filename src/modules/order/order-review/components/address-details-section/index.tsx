import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';

export default function AddressDetails() {
  return (
    <div className="">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="authorized-signatory">
          <AccordionTrigger className="py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[20px] xl:px-[20px] 2xl:px-[20px] 3xl:px-[1.042vw] border-b border-InterfaceStrokesoft bg-BrandNeutral50 hover:no-underline hover:bg-BrandNeutral100 data-[state=open]:bg-BrandNeutral100">
            <div className="font14 text-blackcolor font-medium leading-[140%]">Address Details</div>
          </AccordionTrigger>
          <AccordionContent className=" border border-InterfaceStrokesoft bg-background">
            <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw]">
              <div className="grid grid-cols-9 gap-[32px] 3xl:gap-[1.667vw]">
                <div className="col-span-9 xl:col-span-2">
                  <div className="font18 text-InterfaceTexttitle font-semibold leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
                    Bill From :
                  </div>
                  <div className="font16 text-InterfaceTexttitle font-medium leading-[140%]">
                    Redington Gulf FZE
                  </div>
                  <div className="font14 text-InterfaceTextdefault font-normal leading-[140%] my-[8px] 3xl:my-[0.417vw]">
                    Plot No. S 30902 South, Jabel Ali Free Zone Dubai, United Arab Emirates
                  </div>
                  <div className="font14 text-InterfaceTextdefault font-normal leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
                    <span className="font-medium">VAT TRN:</span> ***************
                  </div>
                </div>
                <div className="col-span-9 xl:col-span-2">
                  <div className="font18 text-InterfaceTexttitle font-semibold leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
                    Bill To :
                  </div>
                  <div className="font16 text-InterfaceTexttitle font-medium leading-[140%]">
                    Delphi Consulting LLC
                  </div>
                  <div className="font14 text-InterfaceTextdefault font-normal leading-[140%] my-[8px] 3xl:my-[0.417vw]">
                    Suite 1407, Concord Tower, Al Safouh 2 Media city, Dubai. Dubai, DubaiUnited
                    Arab Emirates
                  </div>
                  <div className="font14 text-InterfaceTextdefault font-normal leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
                    <span className="font-medium">TRN:</span> ***************
                  </div>
                  <div className="font14 text-InterfaceTextdefault font-normal leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
                    <span className="font-medium">Redington Account Number:</span> **********
                  </div>
                </div>
                <div className="col-span-9 xl:col-span-3">
                  <div className="font18 text-InterfaceTexttitle font-semibold leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
                    Service Recipient :
                  </div>
                  <div className="font16 text-InterfaceTexttitle font-medium leading-[140%]">
                    The First Group
                  </div>
                  <div className="font14 text-InterfaceTextdefault font-normal leading-[140%] my-[8px] 3xl:my-[0.417vw]">
                    22nd Floor Tameem House Barsha Heights Dubai, Dubai, 24573 United Arab Emirates.
                    <p>
                      <span className="font-medium">TEL:</span> *********
                    </p>
                  </div>

                  <div className="font16 text-InterfaceTexttitle font-medium leading-[140%] mt-[14px] 3xl:mt-[0.729vw]">
                    Organization & Tenant
                  </div>
                  <div className="my-[8px] 3xl:my-[0.417vw]">
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Tenant ID:</span>{' '}
                      f6e191f8-5aed-4670-86d4-616277ebe4c0
                    </div>
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Tenant Name:</span>{' '}
                      thefirstgroup.onmicrosoft.com
                    </div>
                  </div>
                </div>
                <div className="col-span-9 xl:col-span-2">
                  <div className="font18 text-InterfaceTexttitle font-semibold leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
                    Order Details
                  </div>
                  <div className="space-y-[2px] 3xl:space-y-[0.104vw]">
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Store:</span> BH-AR (New)
                    </div>
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Vendor:</span> Amazon
                    </div>
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Brand:</span> AWS
                    </div>
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Brand Category:</span> AWS
                    </div>
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Payment Terms:</span> R032-Advance Terms
                    </div>
                    <div className="font14 text-InterfaceTextdefault font-normal leading-[140%]">
                      <span className="font-medium">Reference:</span> LPO-543543...{' '}
                      <span className="text-InterfaceTextprimary underline cursor-pointer">
                        View
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
