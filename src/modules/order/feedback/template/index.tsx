'use client';
import { Button, Toaster } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import React from 'react';
import FeedbackSteps from '../components/feedbacksteps';
import { <PERSON>o } from 'next/font/google';
import toast from 'react-hot-toast';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function Feedback() {
  const notify = (type: 'success' | 'info' | 'warning' | 'alert') =>
    toast.custom((t) => {
      const baseStyle =
        'flex flex-wrap gap-[8px] 3xl:gap-[0.417vw] p-[14px] rounded-tl-[12px] rounded-tr-[12px]';

      let config = {
        bg: '',
        border: '',
        icon: '',
        iconColor: '',
        title: '',
        titleColor: '',
        paragraphColor: '',
      };

      switch (type) {
        case 'success':
          config = {
            bg: '#F5FAF3',
            border: '#019049',
            icon: 'cloud-fillcircletick',
            iconColor: '#019049',
            title: 'Submitted!',
            titleColor: '#019049',
            paragraphColor: '#019049',
          };
          break;
      }

      return (
        <div
          className={`${baseStyle}`}
          style={{ backgroundColor: config.bg, borderBottom: `3px solid ${config.border}` }}
        >
          <div>
            <i className={`${config.icon}`} style={{ color: config.iconColor }}></i>
          </div>
          <div>
            <div
              className="text-[16px] 3xl:text-[0.833vw] font-semibold leading-[150%]"
              style={{ color: config.titleColor }}
            >
              {config.title}
            </div>
            <p
              className="text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]"
              style={{ color: config.paragraphColor }}
            >
              Your post order satisfaction survey for partners is successfully submitted!
            </p>
          </div>
          <div>
            <i
              className="cloud-closecircle text-Interfacefeedbackerror700 cursor-pointer"
              onClick={() => toast.dismiss(t.id)}
            ></i>
          </div>
        </div>
      );
    });
  const surveyData = [
    { label: 'Strongly Disagree', value: -1 },
    { label: 'Disagree', value: -0.5 },
    { label: 'Average', value: 0 },
    { label: 'Agree', value: 0.5 },
    { label: 'Strongly Agree', value: 1 },
  ];

  const [selected1, setSelected1] = React.useState<number | null>(null);
  const [selected2, setSelected2] = React.useState<number | null>(null);
  const [selected3, setSelected3] = React.useState<number | null>(null);
  const [selected4, setSelected4] = React.useState<number | null>(null);
  const [selected5, setSelected5] = React.useState<number | null>(null);

  return (
    <div className={`mx-auto w-full flex justify-center items-center ${roboto.className}`}>
      <div className="bg-background cardShadow p-[22px] 3xl:p-[1.25vw] my-[30px] 3xl:my-[1.563vw] w-[80%] lg:w-[55%] 3xl:w-[45%] relative h-full">
        <div className="font20 text-InterfaceTexttitle font-semibold leading-[140%] border-b border-b-InterfaceStrokesoft pb-[16px] 3xl:pb-[0.833vw]">
          Post Order Satisfaction Survey for Partners
        </div>
        {/* <div className='mt-[22px] 3xl:mt-[1.25vw] space-y-[22px] 3xl:space-y-[1.25vw] h-[350px] 3xl:h-[25.042vw] overflow-auto'> */}
        <div className="mt-[22px] 3xl:mt-[1.25vw] space-y-[22px] 3xl:space-y-[1.25vw]">
          <div>
            <p className="text-InterfaceTextdefault font16 font-normal leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
              1. How satisfied are you with the ease of placing your order on the platform?
            </p>
            <FeedbackSteps
              selected={selected1}
              surveyData={surveyData}
              setSelected={setSelected1}
            />
          </div>
          <div>
            <p className="text-InterfaceTextdefault font16 font-normal leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
              2. How satisfied are you with the availability of the products you needed?
            </p>
            <FeedbackSteps
              selected={selected2}
              surveyData={surveyData}
              setSelected={setSelected2}
            />
          </div>
          <div>
            <p className="text-InterfaceTextdefault font16 font-normal leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
              3. How satisfied are you with the pricing?
            </p>
            <FeedbackSteps
              selected={selected3}
              surveyData={surveyData}
              setSelected={setSelected3}
            />
          </div>
          <div>
            <p className="text-InterfaceTextdefault font16 font-normal leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
              4. How satisfied are you with the updates and communication after placing your order?
            </p>
            <FeedbackSteps
              selected={selected4}
              surveyData={surveyData}
              setSelected={setSelected4}
            />
          </div>
          <div>
            <p className="text-InterfaceTextdefault font16 font-normal leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
              5. How satisfied are you with your overall experience using the platform?
            </p>
            <FeedbackSteps
              selected={selected5}
              surveyData={surveyData}
              setSelected={setSelected5}
            />
          </div>
        </div>
        <div className="px-[16px] 3xl:px-[0.833vw] py-[14px] 3xl:py-[0.729vw] bg-InterfaceSurfacecomponentmuted rounded-[8px] mt-[20px] 3xl:mt-[1.25vw]">
          <div className="flex flex-wrap items-center gap-[6px] 3xl:gap-[0.313vw]">
            <div className="text-InterfaceTextdefault font14 font-semibold leading-[140%]">
              Scoring:
            </div>
            <div className="flex items-center gap-[4px] 3xl:gap-[0.208vw]">
              <div className="font14 font-normal leading-[140%] bg-background px-[8px] 3xl:px-[0.417vw] py-[4px] 3xl:py-[0.208vw]">
                Strongly Agree: 1.0
              </div>
            </div>
            <div className="flex items-center gap-[4px] 3xl:gap-[0.208vw]">
              <div className="font14 font-normal leading-[140%] bg-background px-[8px] 3xl:px-[0.417vw] py-[4px] 3xl:py-[0.208vw]">
                Agree: 0.5
              </div>
            </div>
            <div className="flex items-center gap-[4px] 3xl:gap-[0.208vw]">
              <div className="font14 font-normal leading-[140%] bg-background px-[8px] 3xl:px-[0.417vw] py-[4px] 3xl:py-[0.208vw]">
                Average: 0
              </div>
            </div>
            <div className="flex items-center gap-[4px] 3xl:gap-[0.208vw]">
              <div className="font14 font-normal leading-[140%] bg-background px-[8px] 3xl:px-[0.417vw] py-[4px] 3xl:py-[0.208vw]">
                Disagree: -0.5
              </div>
            </div>
            <div className="flex items-center gap-[4px] 3xl:gap-[0.208vw]">
              <div className="font14 font-normal leading-[140%] bg-background px-[8px] 3xl:px-[0.417vw] py-[4px] 3xl:py-[0.208vw]">
                Strongly Disagree: -1.0
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-between items-center mt-[22px] 3xl:mt-[0.938vw] border-t border-t-InterfaceStrokesoft pt-[16px] 3xl:pt-[0.833vw]">
          <div className="font20 text-InterfaceTexttitle font-medium leading-[140%]">
            Ratings: 4.5
          </div>
          <div className="flex justify-end items-center gap-[8px] 3xl:gap-[0.417vw]">
            <Link
              href="/order/order-placement"
              className="text-[#3C4146] cancelbtn flex gap-2 items-center px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"
            >
              <i className="cloud-circletick text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              Cancel
            </Link>

            <Button
              onClick={() => notify('success')}
              className="border applybtn text-[#FFF]  px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"
            >
              <Image src="/images/send-2.svg" width={20} height={20} alt="" />
              Submit
            </Button>
          </div>
        </div>

        <Toaster />
      </div>
    </div>
  );
}
