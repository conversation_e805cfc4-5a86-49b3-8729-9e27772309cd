import { Textarea } from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON><PERSON> } from 'next/font/google';
import React from 'react';

interface FeedbackProps {
  setSelected: (value: number | null) => void;
  surveyData: { label: string; value: number }[];
  selected: number | null;
}

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function FeedbackSteps({ selected, surveyData, setSelected }: FeedbackProps) {
  const handleClick = (value: number) => {
    setSelected(selected === value ? null : value);
  };

  return (
    <div className={`w-full ${roboto.className}`}>
      <div className="relative grid grid-cols-5 justify-items-center mb-[8px] 3xl:mb-[0.417vw]">
        <div className="absolute top-1/2 left-0 right-0 w-full h-[11px] 3xl:h-[0.525vw] bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokesoft z-0 rounded-[8px] 3xl:rounded-[0.417vw] transform -translate-y-1/2"></div>

        {surveyData.map((items, index) => (
          <button
            key={index}
            onClick={handleClick.bind(null, items.value)}
            className={`z-10 w-[40px] 3xl:w-[2.083vw] h-[40px] 3xl:h-[2.083vw] rounded-full flex items-center justify-center font14 font-medium
                ${selected === items.value ? 'bg-BrandPrimarypurenew text-InterfaceTextwhite' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextsubtitle hover:text-InterfaceTexttitle'}
                border border-InterfaceStrokesoft transition`}
          >
            {items.value}
          </button>
        ))}
      </div>

      <div className="grid grid-cols-5 justify-items-center font14">
        {surveyData.map((items, index) => (
          <span
            key={index}
            onClick={handleClick.bind(null, items.value)}
            className={`text-center ${selected === items.value ? 'text-InterfaceTexttitle font-medium' : 'text-InterfaceTextsubtitle font-normal hover:text-InterfaceTexttitle'} cursor-pointer`}
          >
            {items.label}
          </span>
        ))}
      </div>
      {(selected === -1 || selected === -0.5) && (
        <div className="mt-[12px] 3xl:mt-[0.625vw]">
          <Textarea
            placeholder="Write text here ..."
            className="w-full border-[#BBC1C7] rounded-none"
          />
          <div className="mt-[6px] 3xl:mt-[0.313vw] text-InterfaceTextsubtitle font12">
            Not more that 205 Characters
          </div>
        </div>
      )}
    </div>
  );
}
