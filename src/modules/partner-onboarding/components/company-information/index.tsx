'use client';
import * as React from 'react';
import { useEffect } from 'react';
import { Input, Label } from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ScrollArea } from '@redington-gulf-fze/cloudquarks-component-library';
import { companyInformationSchema, type CompanyInformationFormData } from '../../validations';
import toast from 'react-hot-toast';
import { countryService } from '@/lib/services/api';
import { useQuery } from '@tanstack/react-query';
import { Select2 } from '@/components/common/ui/combo-box';
import { useValidation } from '@/lib/hooks/step-validation';
import { useCompanyInformation } from '../../hooks/usePartnerOnboarding';

export default function CompanyInformation() {
  const { companyInfo, updateCompanyInfo, updateRegisteredAddr } = useCompanyInformation();

  const { setStepValid, validationTriggers } = useValidation();

  const { data: countries } = useQuery({
    queryKey: ['countries'],
    queryFn: () => countryService.getCountries(),
  });

  const {
    control,
    handleSubmit,
    watch,
    reset,
    trigger,
    formState: { errors, isValid },
  } = useForm<CompanyInformationFormData>({
    resolver: zodResolver(companyInformationSchema),
    mode: 'onTouched',
    defaultValues: companyInfo,
  });

  // Track validation trigger changes
  const prevValidationTriggerRef = React.useRef(validationTriggers[0] || 0);

  // Update parent validation state whenever form validity changes
  useEffect(() => {
    setStepValid(0, isValid); // Step 0 is Company Information
  }, [isValid, setStepValid]);

  // Handle validation triggers from parent - only when trigger count actually changes
  useEffect(() => {
    const currentTrigger = validationTriggers[0] || 0;
    if (currentTrigger > prevValidationTriggerRef.current) {
      prevValidationTriggerRef.current = currentTrigger;
      // Force validation of all fields
      trigger();
    }
  }, [validationTriggers, trigger]);

  useEffect(() => {
    // Sync form data with Redux state when form changes
    const subscription = watch((value) => {
      if (value) {
        // Update company information
        updateCompanyInfo({
          redingtonId: value.redingtonId || '',
          gstNumber: value.gstNumber || '',
          companyName: value.companyName || '',
          website: value.website || '',
          address: {
            street1: value.address?.street1 || '',
            street2: value.address?.street2 || '',
            countryBusiness: value.address?.countryBusiness || '',
            city: value.address?.city || '',
            state: value.address?.state || '',
            postalCode: value.address?.postalCode || '',
            // legalStatus: value.address?.legalStatus || '',
          },
        });

        // Update addresses
        if (value.address) {
          updateRegisteredAddr(value.address);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [watch]);

  // Reset form when Redux state changes (e.g., when loading saved data)
  useEffect(() => {
    // Only reset if there's actually meaningful data in Redux state
    if (companyInfo.gstNumber || companyInfo.companyName) {
      const currentFormValues = watch();
      const hasChanges = JSON.stringify(currentFormValues) !== JSON.stringify(companyInfo);

      if (hasChanges) {
        reset(companyInfo);
      }
    }
  }, [companyInfo, reset]); // Added reset back to dependencies

  const onSubmit = () => {
    if (isValid) {
      toast.success('Company information saved successfully!');
    } else {
      toast.error('Please fix the form errors before proceeding.');
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="relative bg-interfacesurfacecomponent">
          {/* Company Information */}
          <div className="relative">
            <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                Company Information
              </div>
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
                * Fields are Mandatory
              </div>
            </div>
            <ScrollArea className="h-[520px]">
              <div className="py-3 text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                Details presented from Redington ERP system, please validate to proceed further, if
                you need any assistance, please create a ticket in the platform under Service Desk.
              </div>
              <div className="pt-[16px] 3xl:pt-[0.833vw] grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                {/* Redington Id */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Redington Account Id *
                  </Label>
                  <div className="flex w-full gap-5">
                    <Controller
                      name="redingtonId"
                      control={control}
                      disabled={true}
                      render={({ field }) => (
                        <Input
                          {...field}
                          required
                          disabled={true}
                          type="text"
                          placeholder="Enter GST Number"
                          className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                            errors.gstNumber ? 'border-red-500' : ''
                          }`}
                        />
                      )}
                    />
                  </div>
                  {errors.redingtonId && (
                    <span className="text-red-500 text-sm">{errors.redingtonId.message}</span>
                  )}
                </div>
                {/* Country */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Country *
                  </Label>
                  <Controller
                    name="address.countryBusiness"
                    control={control}
                    disabled={true}
                    render={({ field }) => (
                      <Select2
                        options={
                          countries?.data?.map((country) => ({
                            label: country.name,
                            value: country.iso_2,
                          })) ?? []
                        }
                        value={field.value}
                        onChange={field.onChange}
                        disabled={true}
                      />
                    )}
                  />
                  {errors.address?.countryBusiness && (
                    <span className="text-red-500 text-sm">
                      {errors.address.countryBusiness.message}
                    </span>
                  )}
                </div>
                {/* GST Registration Number */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Trade Licence/ GST *
                  </Label>
                  <div className="flex w-full gap-5">
                    <Controller
                      name="gstNumber"
                      disabled
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          required
                          type="text"
                          disabled
                          placeholder="Enter GST Number"
                          className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                            errors.gstNumber ? 'border-red-500' : ''
                          }`}
                        />
                      )}
                    />
                  </div>
                  {errors.gstNumber && (
                    <span className="text-red-500 text-sm">{errors.gstNumber.message}</span>
                  )}
                </div>

                {/* Company Name */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Company Name (As per the Trade License) *
                  </Label>
                  <Controller
                    name="companyName"
                    disabled={true}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        placeholder="Company Name"
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                          errors.companyName ? 'border-red-500' : ''
                        }`}
                      />
                    )}
                  />
                  {errors.companyName && (
                    <span className="text-red-500 text-sm">{errors.companyName.message}</span>
                  )}
                </div>

                {/* Address Section */}
                {/* Address Line 1 */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Street Address 1 *
                  </Label>
                  <Controller
                    name="address.street1"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        disabled={true}
                        type="text"
                        placeholder="Address"
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                          errors.address?.street1 ? 'border-red-500' : ''
                        }`}
                      />
                    )}
                  />
                  {errors.address?.street1 && (
                    <span className="text-red-500 text-sm">{errors.address?.street1.message}</span>
                  )}
                </div>

                {/* Address Line 2 */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Street Address 2
                  </Label>
                  <Controller
                    name="address.street2"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        disabled={true}
                        type="text"
                        placeholder="Address"
                        className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                      />
                    )}
                  />
                </div>

                {/* State */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    State/Province/Emirates *
                  </Label>
                  <Controller
                    name="address.state"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        disabled={true}
                        placeholder="State"
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                          errors.address?.state ? 'border-red-500' : ''
                        }`}
                      />
                    )}
                  />
                  {errors.address?.state && (
                    <span className="text-red-500 text-sm">{errors.address.state.message}</span>
                  )}
                </div>

                {/* City */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    City *
                  </Label>
                  <Controller
                    name="address.city"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        disabled={true}
                        placeholder="City"
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                          errors.address?.city ? 'border-red-500' : ''
                        }`}
                      />
                    )}
                  />
                  {errors.address?.city && (
                    <span className="text-red-500 text-sm">{errors.address.city.message}</span>
                  )}
                </div>

                {/* Postal Code */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    ZIP/ Postal Code *
                  </Label>
                  <Controller
                    name="address.postalCode"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        disabled={true}
                        type="text"
                        placeholder="Postal Code"
                        className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                      />
                    )}
                  />
                  {errors.address?.postalCode && (
                    <span className="text-red-500 text-sm">
                      {errors.address.postalCode.message}
                    </span>
                  )}
                </div>

                {/* Website */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Company Website
                  </Label>
                  <Controller
                    name="website"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        placeholder="Enter Website URL"
                        className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                      />
                    )}
                  />
                  {errors?.website && (
                    <span className="text-red-500 text-sm">{errors.website.message}</span>
                  )}
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>
      </form>
    </>
  );
}
