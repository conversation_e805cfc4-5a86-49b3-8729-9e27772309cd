import { Sheet, SheetContent, SheetTitle } from '@redington-gulf-fze/cloudquarks-component-library';

import { <PERSON><PERSON> } from 'next/font/google';

import * as React from 'react';
import { TermsSheetProps } from '@/types';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function SuccessPopup({ open, onClose }: TermsSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[460px] xl:max-w-[450px] 2xl:max-w-[570px] 3xl:max-w-[29.69vw] p-[0px] flex flex-col h-full`}
        side={'right'}
      >
        <div className="flex-1 overflow-y-auto mb-[30px] xl:mb-0 mt-[40px] xl:mt-[40px] 2xl:mt-[55px] 3xl:mt-[6.29vw] mx-[20px] xl:mx-[40px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[20px] xl:gap-[20px] 2xl:gap-[40px] 3xl:gap-[2.08vw]">
            <div>
              {/* <i className="cloud-notification text-BrandSupport1pure text-[50px] xl:text-[40px] 2xl:text-[60px] 3xl:text-[2.6vw] font-[400]"></i> */}
              <i className="cloud-info2 text-BrandSupport1pure text-[50px] xl:text-[40px] 2xl:text-[60px] 3xl:text-[2.6vw] font-[400]"></i>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw]">
              <div className="flex flex-col justify-center items-center">
                <center className="text-InterfaceTexttitle text-[20px] xl:text-[20px] 2xl:text-[34px] 3xl:text-[1.875vw] font-[400] leading-[140%] text-center">
                  Congratulations!! Your Company Registration request has been submitted
                  successfully.
                </center>
              </div>
              <div className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.839vw] font-[400] text-center">
                Thank you for your interest, our team will review and revert at the earliest. An
                email will be sent to you once your registration is complete and your company has
                been onboarded.
              </div>
              <div className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.839vw] font-[400] text-center">
                Please reach out to{' '}
                <span className="text-[#1570EF] font-medium"><EMAIL></span>
                <br />
                for any support related queries{' '}
              </div>
            </div>

            <div className="text-center">
              <Link
                href={'/account'}
                className="flex justify-end items-center gap-3 blueBtn px-[16px] xl:px-[16px] 2xl:px-[18px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 2xl:py-[12px] 3xl:py-[0.521vw] rounded-none font-[500]"
              >
                <i className="cloud-arrowleft"></i>
                <span>Back to Account</span>
              </Link>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
