'use client';
import * as React from 'react';
import { useEffect } from 'react';
import { Input, Label, Button, Checkbox } from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { PhoneInput } from '@/components/common/phone-input';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useBusinessInformation } from '../../hooks/usePartnerOnboarding';
import { businessInformationSchema, type BusinessInformationFormData } from '../../validations';
import { KeyContact } from '@/store/slices/partner-registration';
import toast from 'react-hot-toast';
import { useFormPersistence } from '../../hooks/usePartnerOnboarding';
import { useValidation } from '@/lib/hooks/step-validation';

export default function BusinessInformation() {
  const { businessInfo, updateBusinessInfo, addAuthorizedContact, removeAuthorizedContact } =
    useBusinessInformation();
  const { saveToSessionStorage } = useFormPersistence();
  const { setStepValid, validationTriggers } = useValidation();

  // Track validation trigger changes
  const prevValidationTriggerRef = React.useRef(validationTriggers[1] || 0);

  // State for "same as director" checkboxes
  const [salesSameAsDirector, setSalesSameAsDirector] = React.useState(false);
  const [accountsSameAsDirector, setAccountsSameAsDirector] = React.useState(false);
  const [authorizedSameAsDirector, setAuthorizedSameAsDirector] = React.useState(false);

  const {
    control,
    handleSubmit,
    watch,
    reset,
    trigger,
    formState: { errors, isValid },
  } = useForm<BusinessInformationFormData>({
    resolver: zodResolver(businessInformationSchema),
    mode: 'onChange',
    defaultValues: {
      directorDetails: businessInfo.directorDetails,
      salesDetails: businessInfo.salesDetails || undefined,
      accountsDetails: businessInfo.accountsDetails || undefined,
      authorizedSignatory: businessInfo.authorizedSignatory,
    },
  });

  useEffect(() => {
    // Sync form data with Redux state when form changes
    const subscription = watch((value) => {
      if (value) {
        updateBusinessInfo({
          userDetails: {
            firstName: value.userDetails?.firstName || '',
            lastName: value.userDetails?.lastName || '',
            email: value.userDetails?.email || '',
            mobileNumber: value.userDetails?.mobileNumber || '',
          },
          directorDetails: {
            firstName: value.directorDetails?.firstName || '',
            lastName: value.directorDetails?.lastName || '',
            email: value.directorDetails?.email || '',
            mobileNumber: value.directorDetails?.mobileNumber || '',
          },
          salesDetails:
            value.salesDetails &&
            (value.salesDetails.firstName ||
              value.salesDetails.lastName ||
              value.salesDetails.email ||
              value.salesDetails.mobileNumber)
              ? {
                  firstName: value.salesDetails.firstName || '',
                  lastName: value.salesDetails.lastName || '',
                  email: value.salesDetails.email || '',
                  mobileNumber: value.salesDetails.mobileNumber || '',
                }
              : undefined,
          accountsDetails:
            value.accountsDetails &&
            (value.accountsDetails.firstName ||
              value.accountsDetails.lastName ||
              value.accountsDetails.email ||
              value.accountsDetails.mobileNumber)
              ? {
                  firstName: value.accountsDetails.firstName || '',
                  lastName: value.accountsDetails.lastName || '',
                  email: value.accountsDetails.email || '',
                  mobileNumber: value.accountsDetails.mobileNumber || '',
                }
              : undefined,
          authorizedSignatory: businessInfo.authorizedSignatory, // We'll manage this separately
        });

        // Auto-save to session storage
        setTimeout(() => {
          saveToSessionStorage();
        }, 1000); // Debounce auto-save by 1 second
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, updateBusinessInfo, saveToSessionStorage, businessInfo.authorizedSignatory]);

  // Reset form when Redux state changes
  useEffect(() => {
    const currentFormValues = watch();
    const hasChanges = JSON.stringify(currentFormValues) !== JSON.stringify(businessInfo);

    if (hasChanges) {
      reset(businessInfo);
    }
  }, [businessInfo, watch, reset]);

  // Update parent validation state whenever form validity changes
  useEffect(() => {
    setStepValid(1, isValid); // Step 1 is Business Information
  }, [isValid, setStepValid]);

  // Handle validation triggers from parent - only when trigger count actually changes
  useEffect(() => {
    const currentTrigger = validationTriggers[1] || 0;
    if (currentTrigger > prevValidationTriggerRef.current) {
      prevValidationTriggerRef.current = currentTrigger;
      trigger();
    }
  }, [validationTriggers, trigger]);

  const onSubmit = () => {
    if (isValid) {
      toast.success('Business information saved successfully!');
    } else {
      toast.error('Please fix the form errors before proceeding.');
    }
  };

  const handleAddAuthorizedSignatory = () => {
    if (businessInfo.authorizedSignatory.length < 3) {
      const newContact: KeyContact = {
        firstName: '',
        lastName: '',
        email: '',
        mobileNumber: '',
      };
      addAuthorizedContact(newContact);
    }
  };

  const handleRemoveAuthorizedSignatory = (index: number) => {
    if (businessInfo.authorizedSignatory.length > 1) {
      removeAuthorizedContact(index);
    }
  };

  const handleAuthorizedSignatoryChange = (
    index: number,
    field: keyof KeyContact,
    value: string
  ) => {
    const updatedSignatories = [...businessInfo.authorizedSignatory];
    updatedSignatories[index] = {
      ...updatedSignatories[index],
      [field]: value,
    };
    updateBusinessInfo({
      authorizedSignatory: updatedSignatories,
    });
  };

  const handleSalesSameAsDirector = (checked: boolean) => {
    setSalesSameAsDirector(checked);
    if (checked) {
      // Copy director details to sales
      updateBusinessInfo({
        salesDetails: {
          firstName: businessInfo.directorDetails.firstName,
          lastName: businessInfo.directorDetails.lastName,
          email: businessInfo.directorDetails.email,
          mobileNumber: businessInfo.directorDetails.mobileNumber,
        },
      });
    } else {
      // Clear sales details by setting to undefined (optional)
      updateBusinessInfo({
        salesDetails: undefined,
      });
    }
  };

  const handleAccountsSameAsDirector = (checked: boolean) => {
    setAccountsSameAsDirector(checked);
    if (checked) {
      // Copy director details to accounts
      updateBusinessInfo({
        accountsDetails: {
          firstName: businessInfo.directorDetails.firstName,
          lastName: businessInfo.directorDetails.lastName,
          email: businessInfo.directorDetails.email,
          mobileNumber: businessInfo.directorDetails.mobileNumber,
        },
      });
    } else {
      // Clear accounts details by setting to undefined (optional)
      updateBusinessInfo({
        accountsDetails: undefined,
      });
    }
  };

  const handleAuthorizedSameAsDirector = (checked: boolean) => {
    setAuthorizedSameAsDirector(checked);
    if (checked) {
      // Set only one authorized signatory with director details
      updateBusinessInfo({
        authorizedSignatory: [
          {
            firstName: businessInfo.directorDetails.firstName,
            lastName: businessInfo.directorDetails.lastName,
            email: businessInfo.directorDetails.email,
            mobileNumber: businessInfo.directorDetails.mobileNumber,
          },
        ],
      });
    } else {
      // Reset to one empty authorized signatory
      updateBusinessInfo({
        authorizedSignatory: [
          {
            firstName: '',
            lastName: '',
            email: '',
            mobileNumber: '',
          },
        ],
      });
    }
  };

  const renderContactSection = (
    title: string,
    fieldName: 'userDetails' | 'directorDetails' | 'salesDetails' | 'accountsDetails',
    isRequired: boolean = false,
    isOptional: boolean = false,
    disabled: boolean = false
  ) => {
    const isSales = fieldName === 'salesDetails';
    const isAccounts = fieldName === 'accountsDetails';
    const isDirector = fieldName === 'directorDetails';
    const showCheckbox = !disabled && (isSales || isAccounts);

    // Only apply checkbox state to the specific section, never to Director
    let checkboxChecked = false;
    let handleCheckboxChange: ((checked: boolean) => void) | undefined;

    if (isSales) {
      checkboxChecked = salesSameAsDirector;
      handleCheckboxChange = handleSalesSameAsDirector;
    } else if (isAccounts) {
      checkboxChecked = accountsSameAsDirector;
      handleCheckboxChange = handleAccountsSameAsDirector;
    }
    // Director section should never be disabled by checkbox state

    return (
      <div className="p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={`item-${fieldName}`}>
            <AccordionTrigger className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600]">
              <div className="w-full flex justify-between items-center">
                <div className="text-left">
                  {title} {isRequired && '*'}
                  {isOptional && (
                    <span className="text-InterfaceTextsubtitle text-[12px] ml-2">(Optional)</span>
                  )}
                </div>
                {showCheckbox && handleCheckboxChange && (
                  <div
                    className="flex items-center gap-2 mr-4"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Checkbox
                      id={`${fieldName}-same-as-director`}
                      checked={checkboxChecked}
                      onCheckedChange={handleCheckboxChange}
                    />
                    <label
                      htmlFor={`${fieldName}-same-as-director`}
                      className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] cursor-pointer"
                    >
                      Same as Director/Owner
                    </label>
                  </div>
                )}
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="3xl:pt-[0.833vw] grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                {/* First Name */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    First Name {isRequired && '*'}
                  </Label>
                  <Controller
                    name={`${fieldName}.firstName`}
                    control={control}
                    render={({ field: { value, onChange, onBlur, name, ref } }) => (
                      <Input
                        value={value || ''}
                        onChange={onChange}
                        onBlur={onBlur}
                        name={name}
                        ref={ref}
                        type="text"
                        placeholder="Enter First Name"
                        disabled={disabled || (!isDirector && checkboxChecked)}
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${errors[fieldName]?.firstName ? 'border-red-500' : ''} ${!isDirector && checkboxChecked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                      />
                    )}
                  />
                  {errors[fieldName]?.firstName && (
                    <span className="text-red-500 text-sm">
                      {errors[fieldName]?.firstName?.message}
                    </span>
                  )}
                </div>

                {/* Last Name */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Last Name {isRequired && '*'}
                  </Label>
                  <Controller
                    name={`${fieldName}.lastName`}
                    control={control}
                    render={({ field: { value, onChange, onBlur, name, ref } }) => (
                      <Input
                        value={value || ''}
                        onChange={onChange}
                        onBlur={onBlur}
                        name={name}
                        ref={ref}
                        type="text"
                        placeholder="Enter Last Name"
                        disabled={disabled || (!isDirector && checkboxChecked)}
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${errors[fieldName]?.lastName ? 'border-red-500' : ''} ${!isDirector && checkboxChecked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                      />
                    )}
                  />
                  {errors[fieldName]?.lastName && (
                    <span className="text-red-500 text-sm">
                      {errors[fieldName]?.lastName?.message}
                    </span>
                  )}
                </div>

                {/* Email */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Email {isRequired && '*'}
                  </Label>
                  <Controller
                    name={`${fieldName}.email`}
                    control={control}
                    render={({ field: { value, onChange, onBlur, name, ref } }) => (
                      <Input
                        value={value || ''}
                        onChange={onChange}
                        onBlur={onBlur}
                        name={name}
                        ref={ref}
                        type="email"
                        placeholder="Enter Email"
                        disabled={disabled || (!isDirector && checkboxChecked)}
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${errors[fieldName]?.email ? 'border-red-500' : ''} ${!isDirector && checkboxChecked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                      />
                    )}
                  />
                  {errors[fieldName]?.email && (
                    <span className="text-red-500 text-sm">
                      {errors[fieldName]?.email?.message}
                    </span>
                  )}
                </div>

                {/* Mobile Number */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Mobile Number {isRequired && '*'}
                  </Label>
                  <Controller
                    name={`${fieldName}.mobileNumber`}
                    control={control}
                    render={({ field }) => (
                      <PhoneInput
                        value={field.value || ''}
                        onChange={(value) => field.onChange(value || '')}
                        onCountryChange={() => {
                          // Handle country code change if needed
                        }}
                        defaultCountry="IN"
                        disabled={disabled || (!isDirector && checkboxChecked)}
                        className={`w-full ${!isDirector && checkboxChecked ? 'opacity-50 pointer-events-none' : ''}`}
                      />
                    )}
                  />
                  {errors[fieldName]?.mobileNumber && (
                    <span className="text-red-500 text-sm">
                      {errors[fieldName]?.mobileNumber?.message}
                    </span>
                  )}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    );
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="relative bg-interfacesurfacecomponent">
          <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
            <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
              Business Information
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
              * Fields are Mandatory
            </div>
          </div>

          <div className="py-[32px] xl:py-[32px] 2xl:py-[40px] 3xl:py-[1.667vw] text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500]">
            Key Contacts
          </div>

          <div className="accordian-styles space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw]">
            {renderContactSection('User Details', 'userDetails', true, false, true)}
            {/* Section 1: Director/Owner - Mandatory */}
            {renderContactSection('Director/Owner', 'directorDetails', true)}

            {/* Section 2: Sales - Optional */}
            {renderContactSection('Sales', 'salesDetails', false, true)}

            {/* Section 3: Accounts/Operations Executive - Optional */}
            {renderContactSection('Accounts/Operations Executive', 'accountsDetails', false, true)}

            {/* Section 4: Authorized Signatory - Mandatory (1-3 contacts) */}
            <div className="p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="authorized-signatory">
                  <AccordionTrigger className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600]">
                    <div className="w-full flex justify-between items-center">
                      <div className="text-left">
                        Authorized Signatory*
                        <span className="text-InterfaceTextsubtitle text-[12px] ml-2 font-normal">
                          (Min 1, Max 3 contacts)
                        </span>
                      </div>
                      <div
                        className="flex items-center gap-2 mr-4"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Checkbox
                          id="authorized-same-as-director"
                          checked={authorizedSameAsDirector}
                          onCheckedChange={handleAuthorizedSameAsDirector}
                        />
                        <label
                          htmlFor="authorized-same-as-director"
                          className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] cursor-pointer"
                        >
                          Same as Director/Owner
                        </label>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="3xl:pt-[0.833vw] space-y-[20px]">
                      {businessInfo.authorizedSignatory.map((contact, index) => (
                        <div key={`authorized-${index}`} className="relative">
                          {/* Remove button for additional contacts (only show if not using "same as director" and more than 1 contact) */}
                          {!authorizedSameAsDirector &&
                            businessInfo.authorizedSignatory.length > 1 && (
                              <div className="flex justify-end mb-3">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleRemoveAuthorizedSignatory(index)}
                                  className="text-red-600 border-red-300 hover:bg-red-50"
                                >
                                  Remove
                                </Button>
                              </div>
                            )}

                          <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px] p-4 bg-gray-50 rounded-lg">
                            {/* First Name */}
                            <div className="flex flex-col gap-1.5">
                              <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                                First Name *
                              </Label>
                              <Input
                                value={contact.firstName || ''}
                                onChange={(e) =>
                                  handleAuthorizedSignatoryChange(
                                    index,
                                    'firstName',
                                    e.target.value
                                  )
                                }
                                type="text"
                                placeholder="Enter First Name"
                                disabled={authorizedSameAsDirector}
                                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${errors.authorizedSignatory?.[index]?.firstName ? 'border-red-500' : ''} ${authorizedSameAsDirector ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                              />
                              {errors.authorizedSignatory?.[index]?.firstName && (
                                <span className="text-red-500 text-sm">
                                  {errors.authorizedSignatory[index]?.firstName?.message}
                                </span>
                              )}
                            </div>

                            {/* Last Name */}
                            <div className="flex flex-col gap-1.5">
                              <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                                Last Name *
                              </Label>
                              <Input
                                value={contact.lastName || ''}
                                onChange={(e) =>
                                  handleAuthorizedSignatoryChange(index, 'lastName', e.target.value)
                                }
                                type="text"
                                placeholder="Enter Last Name"
                                disabled={authorizedSameAsDirector}
                                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${errors.authorizedSignatory?.[index]?.lastName ? 'border-red-500' : ''} ${authorizedSameAsDirector ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                              />
                              {errors.authorizedSignatory?.[index]?.lastName && (
                                <span className="text-red-500 text-sm">
                                  {errors.authorizedSignatory[index]?.lastName?.message}
                                </span>
                              )}
                            </div>

                            {/* Email */}
                            <div className="flex flex-col gap-1.5">
                              <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                                Email *
                              </Label>
                              <Input
                                value={contact.email || ''}
                                onChange={(e) =>
                                  handleAuthorizedSignatoryChange(index, 'email', e.target.value)
                                }
                                type="email"
                                placeholder="Enter Email"
                                disabled={authorizedSameAsDirector}
                                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${errors.authorizedSignatory?.[index]?.email ? 'border-red-500' : ''} ${authorizedSameAsDirector ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                              />
                              {errors.authorizedSignatory?.[index]?.email && (
                                <span className="text-red-500 text-sm">
                                  {errors.authorizedSignatory[index]?.email?.message}
                                </span>
                              )}
                            </div>

                            {/* Mobile Number */}
                            <div className="flex flex-col gap-1.5">
                              <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                                Mobile Number *
                              </Label>
                              <PhoneInput
                                value={contact.mobileNumber || ''}
                                onChange={(value) =>
                                  handleAuthorizedSignatoryChange(
                                    index,
                                    'mobileNumber',
                                    value || ''
                                  )
                                }
                                onCountryChange={() => {
                                  // Handle country code change if needed
                                }}
                                defaultCountry="IN"
                                disabled={authorizedSameAsDirector}
                                className={`w-full ${authorizedSameAsDirector ? 'opacity-50 pointer-events-none' : ''}`}
                              />
                              {errors.authorizedSignatory?.[index]?.mobileNumber && (
                                <span className="text-red-500 text-sm">
                                  {errors.authorizedSignatory[index]?.mobileNumber?.message}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Add Authorized Signatory Button (only show if not using "same as director") */}
                      {!authorizedSameAsDirector && businessInfo.authorizedSignatory.length < 3 && (
                        <div className="flex justify-start pt-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleAddAuthorizedSignatory}
                            className="bg-blue-50 border-blue-300 text-blue-600 hover:bg-blue-100"
                          >
                            + Add Another Contact
                          </Button>
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
        </div>
      </form>
    </>
  );
}
