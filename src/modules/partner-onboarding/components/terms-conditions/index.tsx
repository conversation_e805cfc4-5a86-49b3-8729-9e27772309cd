import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { <PERSON>o } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function TermsConditions() {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <span className="cursor-pointer text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[500]">
          <i className="cloud-folder  text-[5px] xl:text-[5px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>{' '}
          Terms and Conditions
        </span>
      </Sheet<PERSON>rigger>
      <SheetContent
        //   className="flex flex-col h-full w-[400px]"
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              Terms and Conditions
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        {/* Scrollable Content */}
        <div className="flex-grow overflow-y-auto p-[30px]  space-y-3">
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px]">
            Redington CloudQuarks - Platform User Account Creation T&Cs
          </h1>
          <div className="space-y-[20px] 3xl:space-y-[1.042vw] my-[20px] 3xl:my-[1.042vw]">
            <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px]">
              This is to notify to the general public that some unscrupulous persons are
              unauthorizedly using the name of Redington Limited ; Redington Group/ Affiliates
              (“Redington”) and posing themselves as employees, representatives, agents of Redington
              and its associated/group companies, with ulterior motive to earn wrongful gain and/or
              cheat the prospective investors and business associates and are fraudulently offering
              investment opportunity online through certain websites or through telephone calls or
              by issuing fake offer letters and also soliciting them to deposit some amount in
              certain bank accounts. These people are also unauthorizedly using the name, trademark,
              domain name and logo of Redington with a view to tarnish the image and reputation of
              Redington.
            </p>
            <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px]">
              Further, Redington does not ask, solicit and accept any monies for any schemes in any
              form from the investors and business associates, whether online or otherwise.
              Redington bears no responsibility for amounts being deposited / withdrawn therefrom in
              response to such fake offers.
            </p>
          </div>
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px] mb-[8px]">
            Redington does not:
          </h1>
          <div>
            <ul className="list-disc ">
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Solicit any investment in schemes from free email services like Gmail, Rediff mail,
                Yahoo mail, etc.;
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Request payment of any kind from prospective investor ;
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Authorise anyone to collect money or arrive at any monetary arrangement in return
                for any business opportunity at Redington ;
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Redington avails certain services through professional investment agencies. Even in
                those cases, offers are always made directly by Redington and not by any third
                parties.
              </li>
            </ul>
          </div>
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px] mb-[8px] mt-[20px]">
            Please Note:
          </h1>
          <div>
            <ul className="list-disc ">
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Redington strongly recommends that the potential investors business associates
                should not respond to such fake solicitations, in any manner;
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Redington will not be responsible to anyone acting on an offer not directly made by
                Redington ;
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Anyone making any investment/business association offer in return for money or who
                is not authorized by Redington ;
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                Redington reserves the right to take legal action, including criminal action,
                against such individuals/entities;
              </li>
            </ul>
          </div>
          <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[140%] mt-[20px] 3xl:mt-[1.042vw] mb-[30px] 3xl:mb-[1.563vw]">
            We request you to kindly visit our official careers website{' '}
            <Link href={'#'} className="text-[#1570EF] underline ">
              <span>https://redingtongroup.com/</span>
            </Link>{' '}
            for authentic information pertaining to business association opportunity at Redington
            and enquire with the company to confirm if the offers or promotions are genuine.
          </p>
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px] mb-[20px] 3xl:mb-[1.042vw]">
            Acceptance of Terms
          </h1>
          <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px]">
            The services that Redington provides to you are subject to the following Terms of use
            (“TOU”). Redington reserves the right to update and modify the TOU at any time without
            notice to you. The most current version of the TOU can be reviewed by clicking on the
            “Terms of Use” hyperlink located at the bottom of our webpages. When we make updates to
            the TOU, Redington will update the date at the top of this page. By using the website
            after a new version of the TOU has been posted, you agree to the terms of such new
            version.
          </p>
        </div>

        {/* Footer */}
        <SheetFooter className="mr-3 mb-2">
          <SheetClose>
            <div
              // type="button"
              // onClick={onClose}
              // variant="skybluegradient"
              className="blueBtn lg:px-[13px] cursor-pointer flex justify-center items-center gap-2 xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              {' '}
              <i className="cloud-closecircle"></i>
              <span>Close</span>
            </div>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
