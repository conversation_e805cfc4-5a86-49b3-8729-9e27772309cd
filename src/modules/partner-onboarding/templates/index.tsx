'use client';
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@redington-gulf-fze/cloudquarks-component-library';
import CompanyInformation from '@/modules/partner-onboarding/components/company-information';
import BusinessInformation from '@/modules/partner-onboarding/components/business-information';
import BrandInformation from '@/modules//partner-onboarding/components/brand-information';
import { Roboto } from 'next/font/google';
import { useRouter } from 'next/navigation';
import {
  useFormPersistence,
  usePartnerOnboarding,
  usePartnerOnboardingSubmission,
} from '../hooks/usePartnerOnboarding';
import toast from 'react-hot-toast';
import { useValidation } from '@/lib/hooks/step-validation';
import SuccessPopup from '../components/success-popup';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

// Simple validation context

const steps = [
  { id: 0, title: 'Company Information', component: CompanyInformation },
  { id: 1, title: 'Business Information', component: BusinessInformation },
  { id: 3, title: 'Brand Information', component: BrandInformation },
];

export default function PartnerOnboarding() {
  const router = useRouter();
  const {
    currentStep,
    completedSteps,
    goToStep,
    nextStep,
    previousStep,
    completeStep,
    saveForm,
    isDirty,
  } = usePartnerOnboarding();
  const { loadFromSessionStorage, saveToSessionStorage } = useFormPersistence();
  const { submitRegistration, isSubmitting, isSuccess } = usePartnerOnboardingSubmission();
  const { isStepValid, triggerValidation } = useValidation();
  const [openModal, setOpenModal] = useState(false);

  useEffect(() => {
    if (isSuccess) {
      setOpenModal(true);
    }
  }, [isSuccess]);
  // Load saved data on component mount
  useEffect(() => {
    loadFromSessionStorage();
  }, [loadFromSessionStorage]);

  const tabs = [
    { id: 0, title: 'Company Information', number: 1 },
    { id: 1, title: 'Business Information', number: 2 },
    { id: 2, title: 'Brand/Brand Category', number: 3 },
  ];

  const handleStepClick = (stepId: number) => {
    // Allow navigation to previous steps or current step
    if (stepId <= currentStep || completedSteps.includes(stepId)) {
      goToStep(stepId);
      return;
    }

    // For forward navigation, check if current step is valid
    if (stepId > currentStep) {
      const isCurrentStepValid = isStepValid(currentStep);

      if (!isCurrentStepValid) {
        // Trigger validation to show errors
        triggerValidation(currentStep);
        toast.error(
          'Please complete all required fields in the current section before proceeding.'
        );
        return;
      }

      // If valid, mark step as completed and navigate
      completeStep(currentStep);
      goToStep(stepId);
    }
  };

  const handleNext = () => {
    // const isCurrentStepValid = isStepValid(currentStep);

    // if (!isCurrentStepValid) {
    //   // Trigger validation to show errors
    //   triggerValidation(currentStep);
    //   if (currentStep == 4) {
    //     toast.error('Atleast one brand must be selected.');
    //     return;
    //   }
    //   toast.error('Please complete all required fields before proceeding to the next section.');
    //   return;
    // }

    // Mark current step as completed and move to next
    completeStep(currentStep);
    saveForm();
    saveToSessionStorage();
    nextStep();
  };

  const handleBack = () => {
    if (isDirty) {
      saveForm();
    }
    saveToSessionStorage();
    previousStep();
  };

  const handleSubmit = () => {
    // Validate only the completed steps before submission (excluding Brand Information for now)
    const allStepsValid = tabs.every((tab) => isStepValid(tab.id));

    if (!allStepsValid) {
      // Trigger validation for current step to show errors
      triggerValidation(currentStep);
      toast.error('Please complete all required fields in all sections before submitting.');
      return;
    }

    // Mark final step as completed and save
    completeStep(currentStep);
    saveForm();
    saveToSessionStorage();

    // Submit the registration
    submitRegistration();
  };

  const CurrentStepComponent = steps[currentStep]?.component || CompanyInformation;

  return (
    <div className={`${roboto.className} p-[40px]`}>
      <div className="relative">
        <div className="">
          <div className="grid grid-cols-12 gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw] px-[60px] xl:px-[80px] 3xl:px-[6.25vw]">
            <div className="flex col-span-12 xl:col-span-3 3xl:col-span-3 ">
              <div className="3xl:space-y-[1.25vw] space-y-[20px] p-5 3xl:p-[1.25vw] border border-InterfaceStrokesoft bg-interfacesurfacecomponent ">
                <div className="">
                  <Button
                    type="button"
                    className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-Interface-Stroke-soft bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] flex items-center"
                    variant="outline"
                    size="sm"
                    onClick={() => router.back()}
                  >
                    {' '}
                    <i className="cloud-back text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    Back to Home
                  </Button>
                  <div className="py-[36px] text-InterfaceTexttitle text-[24px] xl:text-[24px] 2xl:text-[26px] 3xl:text-[1.042vw] font-[400]">
                    {`Welcome to CloudQuarks Onboarding, Let's get Started`}.
                  </div>
                  <div className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                    Please complete the below steps and submit your company onboarding form.
                  </div>
                </div>
                <div>
                  <div className="space-y-[8px] xl:space-y-[8px] 3xl:space-y-[0.417vw] ">
                    {tabs.map((tab) => (
                      <div
                        key={tab.id}
                        className={`${
                          currentStep === tab.id
                            ? 'bg-[#42536D] text-[#FFFFFF]'
                            : 'bg-InterfaceSurfacecomponentmuted'
                        }  rounded-none p-[16px] xl:p-[16px] 2xl:p-[0.833vw] 3xl:p-[0.833vw]  text-[#3B4854] cursor-pointer ${
                          tab.id <= currentStep || completedSteps.includes(tab.id)
                            ? 'cursor-pointer'
                            : 'cursor-not-allowed opacity-60'
                        }`}
                        onClick={() => handleStepClick(tab.id)}
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`${
                              currentStep === tab.id
                                ? 'bg-white text-[#42536D]'
                                : completedSteps.includes(tab.id)
                                  ? 'bg-[#11B87C] border border-[#11B87C]'
                                  : 'bg-InterfaceSurfacecomponentmuted'
                            } text-[#42536D] ${
                              tab.id > 0 ? 'border border-[#9CA3AF]' : ''
                            } h-[32px] w-[32px] xl:h-[32px] xl:w-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw] rounded-full flex justify-center items-center text-[14px]`}
                          >
                            {completedSteps.includes(tab.id) ? (
                              <div className="cloud-circletick text-white text-[35px] xl:text-[35px] 2xl:[38px] 3xl:[1.823vw]"></div>
                            ) : (
                              <span
                                className={`${
                                  currentStep === tab.id
                                    ? ' text-BrandNeutral'
                                    : 'text-InterfaceTextsubtitle'
                                } text-[14px] 3xl:text-[0.729vw] text-[#828A91] font-medium`}
                              >
                                {tab.number}
                              </span>
                            )}
                          </div>
                          <div>
                            <div className="text-sm 3xl:text-[0.833vw] font-[600] leading-[22px] 3xl:leading-[1.146vw]">
                              {tab.title}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="col-span-12 xl:col-span-9 3xl:col-span-9">
              <div className="flex flex-col gap-2 p-[20px] xl:p-[30px] 3xl:p-[1.667vw] border border-InterfaceStrokesoft bg-white">
                <CurrentStepComponent />

                <div className="flex flex-wrap justify-between mt-4">
                  <div>
                    <Button
                      disabled={currentStep === 0}
                      type="button"
                      className="py-[10px] xl:py-[10px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                      variant="outline"
                      size="sm"
                      onClick={handleBack}
                    >
                      <i className="cloud-arrowleft text-[10px]"></i>
                      Back
                    </Button>
                  </div>
                  <div className="flex gap-4">
                    <Button
                      type="button"
                      variant="skybluegradient"
                      className="blueBtn lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none"
                      onClick={currentStep === 5 ? handleSubmit : handleNext}
                      disabled={isSubmitting}
                    >
                      {currentStep === 5 ? (
                        <>
                          <i className="cloud-circletick "></i>
                          {isSubmitting ? 'Submitting...' : 'Submit'}
                        </>
                      ) : (
                        <>
                          Save and Next
                          <i className=" cloud-rightarrow1 text-[10px]"></i>
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <SuccessPopup open={openModal} onClose={() => setOpenModal(false)} />
    </div>
  );
}
