import { useCallback, useEffect } from 'react';
import { useAppDispatch, useAppSelector, store } from '@/store';
import { logger } from '@/lib/utils/logger';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import {
  setCurrentStep,
  markStepCompleted,
  markStepIncomplete,
  updateCompanyInformation,
  updateAddress,
  updateBusinessInformation,
  addAuthorizedSignatory,
  removeAuthorizedSignatory,
  saveFormData,
  loadFormData,
  resetForm,
  setFormValid,
  // setEmailVerified,
  // setMobileVerified,
  type CompanyInformationState,
  type BusinessInformationState,
  type KeyContact,
} from '@/store/slices/partner-onboarding';
import { isApiResponseError } from '@/lib/utils/util';
import {
  PartnerRegistrationPayload,
  PartnerRegistrationResponse,
  partnerRegistrationService,
} from '@/lib/services/api/partner-registration';

// Main hook for partner registration
export const usePartnerOnboarding = () => {
  const dispatch = useAppDispatch();
  const state = useAppSelector((state) => state.partnerOnboarding);

  // Navigation functions
  const goToStep = useCallback(
    (step: number) => {
      dispatch(setCurrentStep(step));
    },
    [dispatch]
  );

  const nextStep = useCallback(() => {
    if (state.currentStep <= 4) {
      dispatch(setCurrentStep(state.currentStep + 1));
    }
  }, [dispatch, state.currentStep]);

  const previousStep = useCallback(() => {
    if (state.currentStep > 0) {
      dispatch(setCurrentStep(state.currentStep - 1));
    }
  }, [dispatch, state.currentStep]);

  const completeStep = useCallback(
    (step: number) => {
      dispatch(markStepCompleted(step));
    },
    [dispatch]
  );

  const incompleteStep = useCallback(
    (step: number) => {
      dispatch(markStepIncomplete(step));
    },
    [dispatch]
  );

  // Form management functions
  const saveForm = useCallback(() => {
    dispatch(saveFormData());
  }, [dispatch]);

  const resetFormData = useCallback(() => {
    dispatch(resetForm());
  }, [dispatch]);

  const setValid = useCallback(
    (isValid: boolean) => {
      dispatch(setFormValid(isValid));
    },
    [dispatch]
  );

  // // Verification functions
  // const verifyEmail = useCallback(
  //   (verified: boolean) => {
  //     dispatch(setEmailVerified(verified));
  //   },
  //   [dispatch]
  // );

  // const verifyMobile = useCallback(
  //   (verified: boolean) => {
  //     dispatch(setMobileVerified(verified));
  //   },
  //   [dispatch]
  // );

  return {
    // State
    ...state,

    // Navigation
    goToStep,
    nextStep,
    previousStep,
    completeStep,
    incompleteStep,

    // Form management
    saveForm,
    resetFormData,
    setValid,

    // // Verification
    // verifyEmail,
    // verifyMobile,
  };
};

// Hook for company information
export const useCompanyInformation = () => {
  const dispatch = useAppDispatch();
  const companyInfo = useAppSelector((state) => state.partnerOnboarding.companyInformation);

  const updateCompanyInfo = useCallback(
    (updates: Partial<CompanyInformationState>) => {
      dispatch(updateCompanyInformation(updates));
    },
    [dispatch]
  );

  const updateRegisteredAddr = useCallback(
    (updates: Partial<CompanyInformationState['address']>) => {
      dispatch(updateAddress(updates));
    },
    [dispatch]
  );

  return {
    companyInfo,
    updateCompanyInfo,
    updateRegisteredAddr,
  };
};

// Hook for business information
export const useBusinessInformation = () => {
  const dispatch = useAppDispatch();
  const businessInfo = useAppSelector((state) => state.partnerOnboarding.businessInformation);

  const updateBusinessInfo = useCallback(
    (updates: Partial<BusinessInformationState>) => {
      dispatch(updateBusinessInformation(updates));
    },
    [dispatch]
  );

  const addAuthorizedContact = useCallback(
    (contact: KeyContact) => {
      dispatch(addAuthorizedSignatory(contact));
    },
    [dispatch]
  );

  const removeAuthorizedContact = useCallback(
    (index: number) => {
      dispatch(removeAuthorizedSignatory(index));
    },
    [dispatch]
  );

  return {
    businessInfo,
    updateBusinessInfo,
    addAuthorizedContact,
    removeAuthorizedContact,
  };
};

// Hook for form persistence
export const useFormPersistence = () => {
  const dispatch = useAppDispatch();
  const { isDirty, lastSavedAt } = useAppSelector((state) => state.partnerOnboarding);

  const saveToSessionStorage = useCallback(() => {
    try {
      const state = store.getState()?.partnerOnboarding;
      if (state) {
        // The new document state structure already stores URLs, so it's serializable
        sessionStorage.setItem('partnerOnboardingForm', JSON.stringify(state));
        dispatch(saveFormData());
        logger.log('Form data saved to session storage');
      }
    } catch (error) {
      logger.error('Failed to save form data to sessionStorage:', error);
    }
  }, [dispatch]);

  const loadFromSessionStorage = useCallback(() => {
    try {
      const saved = sessionStorage.getItem('partnerOnboardingForm');
      if (saved) {
        const parsedState = JSON.parse(saved);
        dispatch(loadFormData(parsedState));
        logger.log('Form data loaded from session storage');
        return parsedState;
      }
    } catch (error) {
      logger.error('Failed to load form data from sessionStorage:', error);
    }
    return null;
  }, [dispatch]);

  const clearSessionStorage = useCallback(() => {
    try {
      sessionStorage.removeItem('partnerOnboardingForm');
      logger.log('Form data cleared from session storage');
    } catch (error) {
      logger.error('Failed to clear session storage:', error);
    }
  }, []);

  // Auto-save functionality
  const autoSave = useCallback(() => {
    if (isDirty) {
      saveToSessionStorage();
    }
  }, [isDirty, saveToSessionStorage]);

  // Auto-save every 30 seconds if form is dirty
  useEffect(() => {
    const interval = setInterval(() => {
      autoSave();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [autoSave]);

  // Save on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isDirty) {
        saveToSessionStorage();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isDirty, saveToSessionStorage]);

  return {
    isDirty,
    lastSavedAt,
    saveToSessionStorage,
    loadFromSessionStorage,
    clearSessionStorage,
    autoSave,
  };
};

// Transform Redux state to API payload format
// const transformToApiPayload = (state: partnerOnboardingState): partnerOnboardingPayload => {
//   // Helper function to convert string numbers to actual numbers
//   const toNumber = (value: string | number | undefined): number | undefined => {
//     if (value === undefined || value === null || value === '') return undefined;
//     if (typeof value === 'number') return value;
//     const num = parseInt(value.toString(), 10);
//     return isNaN(num) ? undefined : num;
//   };

//   // Helper function to extract country code from mobile number (assuming format like "+91-9876543210")
//   const extractCountryCode = (countryCode: string): string => {
//     // If countryCode is already in format like "in", convert to "+91"
//     // This is a simplified mapping - you might want to expand this
//     const countryCodeMap: Record<string, string> = {
//       in: '+91',
//       us: '+1',
//       uk: '+44',
//       // Add more mappings as needed
//     };

//     if (countryCode.startsWith('+')) {
//       return countryCode;
//     }

//     return countryCodeMap[countryCode.toLowerCase()] || '+91'; // Default to +91
//   };

//   return {
//     company_info: {
//       gst_number: state.companyInformation.gstNumber,
//       pan_number: state.companyInformation.panNumber,
//       cin_number: state.companyInformation.cinNumber || undefined,
//       company_name: state.companyInformation.companyName,
//       address: {
//         street1: state.companyInformation.address.street1,
//         street2: state.companyInformation.address.street2 || undefined,
//         country_business: state.companyInformation.address.countryBusiness,
//         state: state.companyInformation.address.state,
//         city: state.companyInformation.address.city,
//         postal_code: state.companyInformation.address.postalCode,
//         legal_status: state.companyInformation.address.legalStatus,
//       },
//       contact_info: {
//         mobile_number: state.companyInformation.contactInfo.mobileNumber,
//         country_code: extractCountryCode(state.companyInformation.contactInfo.countryCode),
//       },
//       website: state.companyInformation.website || undefined,
//     },
//     additional_info: {
//       number_of_offices_in_region:
//         state.companyInformation.additionalInfo.numberOfOfficesInRegion || undefined,
//       other_countries_with_offices:
//         state.companyInformation.additionalInfo.otherCountriesWithOffices.length > 0
//           ? state.companyInformation.additionalInfo.otherCountriesWithOffices
//           : undefined,
//       number_of_warehouses_in_region: toNumber(
//         state.companyInformation.additionalInfo.numberOfWareHousesInRegion
//       ),
//       number_of_employees: toNumber(state.companyInformation.additionalInfo.numberOfEmployees),
//       number_of_sales_staff: toNumber(state.companyInformation.additionalInfo.numberOfSalesStaff),
//       number_of_technical_staff: toNumber(
//         state.companyInformation.additionalInfo.numberOfTechnicalStaff
//       ),
//       twitter_account: state.companyInformation.additionalInfo.twitterAccount || undefined,
//       facebook_account: state.companyInformation.additionalInfo.facebookAccount || undefined,
//       linkedin_account: state.companyInformation.additionalInfo.linkedinAccount || undefined,
//       instagram_account: state.companyInformation.additionalInfo.instagramAccount || undefined,
//     },
//     business_info: {
//       director_owner: {
//         first_name: state.businessInformation.directorDetails.firstName,
//         last_name: state.businessInformation.directorDetails.lastName,
//         email: state.businessInformation.directorDetails.email,
//         contact_info: {
//           mobile_number: state.businessInformation.directorDetails.mobileNumber,
//           country_code: '+91', // Default for now, can be enhanced later
//         },
//       },
//       sales: state.businessInformation.salesDetails
//         ? {
//             first_name: state.businessInformation.salesDetails.firstName || '',
//             last_name: state.businessInformation.salesDetails.lastName || '',
//             email: state.businessInformation.salesDetails.email || '',
//             contact_info: {
//               mobile_number: state.businessInformation.salesDetails.mobileNumber || '',
//               country_code: '+91',
//             },
//           }
//         : undefined,
//       accounts_operations: state.businessInformation.accountsDetails
//         ? {
//             first_name: state.businessInformation.accountsDetails.firstName || '',
//             last_name: state.businessInformation.accountsDetails.lastName || '',
//             email: state.businessInformation.accountsDetails.email || '',
//             contact_info: {
//               mobile_number: state.businessInformation.accountsDetails.mobileNumber || '',
//               country_code: '+91',
//             },
//           }
//         : undefined,
//       authorized_signatories: state.businessInformation.authorizedSignatory.map((signatory) => ({
//         first_name: signatory.firstName,
//         last_name: signatory.lastName,
//         email: signatory.email,
//         contact_info: {
//           mobile_number: signatory.mobileNumber,
//           country_code: '+91',
//         },
//       })),
//     },
//     documents: {
//       pan_card_url: state.documentInformation.passportDocuments.find((doc) =>
//         doc.name.toLowerCase().includes('pan')
//       )?.url,
//       gst_certificate_url: state.documentInformation.passportDocuments.find((doc) =>
//         doc.name.toLowerCase().includes('gst')
//       )?.url,
//       tax_exemption_url: state.documentInformation.taxExemptionDocument[0]?.url,
//     },
//     profile_info: state.profileInformation.profile_info.filter((item) => {
//       // Only include single select answers for now
//       return item.question_id && item.answer_id;
//     }),
//     // prepared for final payload
//     user_brand_preferences: state.userBrandPreferences.map((item) => {
//       return {
//         vendor_id: item.vendor?.value,
//         brand_id: item.brand?.value,
//         brand_category_id: item.brandCategory?.value,
//       };
//     }),
//   };
// };

// Hook for partner registration submission
export const usePartnerOnboardingSubmission = () => {
  const state = useAppSelector((state) => state.partnerOnboarding);

  const mutation = useMutation({
    mutationFn: (payload: PartnerRegistrationPayload) =>
      partnerRegistrationService.submitPartnerRegistration(payload),
    onSuccess: (response: PartnerRegistrationResponse) => {
      toast.success(
        response?.message || 'Company Registration request has been submitted successfully.'
      );
    },
    onError: (error: Error) => {
      if (isApiResponseError(error)) {
        toast.error(error?.data?.message);
      } else {
        toast.error(error?.message || 'Failed to submit partner registration');
      }
    },
  });

  const submitRegistration = useCallback(() => {
    try {
      // const payload = transformToApiPayload(state);
      // mutation.mutate(payload);
    } catch {
      toast.error('Failed to prepare registration data');
    }
  }, [state, mutation]);

  return {
    submitRegistration,
    isSubmitting: mutation.isPending,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
    error: mutation.error,
    data: mutation.data,
  };
};
