'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Sheet,
  Sheet<PERSON>lose,
  <PERSON>etContent,
  SheetFooter,
  Label,
  Input,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { CommanSheetProps } from '@/types';
import { Select2, Select2Option } from '@/components/common/ui/combo-box';

export default function NewCartPopup({ open, onClose }: CommanSheetProps) {
  const t = useTranslations();
  const [awstenantOptions, setAwstenantOptions] = React.useState('');
  const awsOptions: Select2Option[] = [
    { label: 'End Customer 1', value: ' Tenant 1' },
    { label: 'End Customer 2', value: 'Tenant 2' },
    { label: 'End Customer 3', value: 'Tenant 3' },
    { label: 'End Customer 4', value: 'Tenant 4' },
  ];
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              New Cart
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[550px] lg:h-[550px] xl:h-[80vh] 2xl:h-[80vh] 3xl:h-[85vh]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid gap-1.5">
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Cart Name <span className="text-red-500">*</span>
              </Label>
              <Input
                type="text"
                id="email"
                placeholder={'Enter Cart Name'}
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
              />
            </div>
            <div className="grid gap-1.5">
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Description
              </Label>
              <Textarea
                placeholder="Cart Description..."
                className="border border-InterfaceStrokedefault rounded-none"
              />
            </div>
            <div className="grid gap-1.5">
              <Label
                htmlFor="email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                End Customer
              </Label>
              <Select2
                options={awsOptions}
                value={awstenantOptions}
                placeholder="Select End Customer"
                onChange={(val) => setAwstenantOptions(val as string)}
              />
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 bg-white border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose>
                <div
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[0.933vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </div>
              </SheetClose>
              <Link href="/order">
                <Button
                  className="flex applybtn items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  size="md"
                >
                  <div>
                    <i className="cloud-fillcircletick text-interfacetextinverse flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    Create and Go to Cart
                  </div>
                </Button>
              </Link>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
