'use client';
import React, { useState } from 'react';
import QuoteSummary from '../component/quote-summary';
import ItemDetails from '../component/item-details';
import RequestQuotation from '../component/request-quotation';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import RedemptionDetails from '../component/redemption-details';
import RedeemDealPopup from '../component/redeem-deal-popup';

export default function DealDetailsTemplate() {
  const [redeemdealpopup, setRedeemdealpopup] = useState(false);
  return (
    <>
      <div className="px-[28px] xl:px-[32px] 3xl:px-[1.667vw] pb-[30px] xl:pb-[40px] 3xl:pb-[2.292vw] pt-[22px] xl:pt-[24px] 3xl:pt-[1.25vw]">
        <div className="dark:bg-[#0F1013] grid grid-cols-12 gap-[22px] xl:gap-[24px] 3xl:gap-[1.25vw]">
          <div className="col-span-9 ">
            <div className="flex flex-col gap-[16px] bg-interfacesurfacecomponent cardShadow pt-[14px] xl:pt-[16px] 3xl:pt-[0.833vw] pb-[18px] xl:pb-[20px] 3xl:pb-[1.042vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-[14px] lg:gap-[14px] xl:gap-[14px] 2xl:gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="text-InterfaceTexttitle font-semibold text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]">
                    Deal Details - 1101
                  </div>
                  <div className="cursor-pointer ">
                    <div
                      className={`inline-block py-[6px] xl:py-[6px] 3xl:py-[0.357vw] px-[10px] rounded-[2px] text-[15px] xl:text-[15px] 3xl:text-[0.781vw] font-semibold border leading-none  bg-[#E4F4E6] text-[#306538] border-[#70B87A]`}
                    >
                      <div>Active</div>
                    </div>
                  </div>
                </div>
                {/* <ActivityLog /> */}
                <Button
                  onClick={() => setRedeemdealpopup(true)}
                  className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-BrandNeutral700 flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-white font-normal"
                >
                  <i className="cloud-folder text-white text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  Create a New Cart
                </Button>
              </div>
              <RequestQuotation />
            </div>
            <div className=" bg-interfacesurfacecomponent cardShadow mt-[18px] xl:mt-[20px] 3xl:mt-[1.042vw] pb-[18px] xl:pb-[20px] 3xl:pb-[1.042vw]">
              <ItemDetails />
            </div>
            <div className=" bg-interfacesurfacecomponent cardShadow mt-[18px] xl:mt-[20px] 3xl:mt-[1.042vw] pb-[18px] xl:pb-[20px] 3xl:pb-[1.042vw]">
              <RedemptionDetails />
            </div>
          </div>
          <div className="col-span-3 relative bg-interfacesurfacecomponent cardShadow py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
            <QuoteSummary />
          </div>
        </div>
      </div>
      <RedeemDealPopup open={redeemdealpopup} onClose={() => setRedeemdealpopup(false)} />
    </>
  );
}
