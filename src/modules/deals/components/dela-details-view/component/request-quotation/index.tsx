'use client';
import React from 'react';

export default function RequestQuotation() {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-1 gap-[22px] xl:gap-[24px] 3xl:gap-[1.25vw]">
        <div className="col-span-1">
          <div className="grid grid-cols-6 gap-[18px] xl:gap-[20px] 3xl:gap-[1.042vw] ">
            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Deal Code
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                1101
              </div>
            </div>
            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Date Created
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                03/03/2025
              </div>
            </div>
            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Valid Till
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                03/05/2025
              </div>
            </div>

            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Redeemed
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                2
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
