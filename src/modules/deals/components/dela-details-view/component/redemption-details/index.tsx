'use client';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';

type User = {
  customername: string;
  dateredeemed: string;
  cartid: string;
  orderid: string;
};

export default function RedemptionDetails() {
  const initialData: User[] = [
    {
      customername: 'Customer Name',
      dateredeemed: '04/03/2025',
      cartid: 'CID - 1101',
      orderid: 'OID- 1100',
    },
    {
      customername: 'Customer Name',
      dateredeemed: '04/03/2025',
      cartid: 'CID - 1101',
      orderid: 'OID- 1100',
    },
    {
      customername: 'Customer Name',
      dateredeemed: '04/03/2025',
      cartid: 'CID - 1101',
      orderid: 'OID- 1100',
    },
    {
      customername: 'Customer Name',
      dateredeemed: '04/03/2025',
      cartid: 'CID - 1101',
      orderid: 'OID- 1100',
    },
  ];

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'customername',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Customer Name
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ">
          {row.getValue('customername')}
        </div>
      ),
    },
    {
      accessorKey: 'dateredeemed',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Date Redeemed
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ">
          {row.getValue('dateredeemed')}
        </div>
      ),
    },
    {
      accessorKey: 'cartid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Cart ID
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ">
          {row.getValue('cartid')}
        </div>
      ),
    },
    {
      accessorKey: 'orderid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Order ID
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ">
          {row.getValue('orderid')}
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="">
        <div className="px-[18px] xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw] border-b border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
            Redemption Details
          </div>
        </div>
        <div className="px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
          <div className="customtabletd flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF] pt-[14px] xl:pt-[14px] 2xl:pt-[16px] 3xl:pt-[0.833vw]">
            <DataTable data={initialData} columns={notificationColumns} />
          </div>
        </div>
      </div>
    </>
  );
}
