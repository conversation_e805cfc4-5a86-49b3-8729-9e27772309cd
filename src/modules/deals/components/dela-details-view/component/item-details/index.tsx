'use client';
import React, { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import { Input } from '@redington-gulf-fze/cloudquarks-component-library';

type User = {
  id: string;
  productname: string;
  brand: string;
  segment: string;
  term: string;
  billtype: string;
  currency: string;
  price: string;
  approvedqty: string;
  approvedprice: string;
  subtotal: string;
};

export default function ItemDetails() {
  const initialData: User[] = [
    {
      id: '1',
      productname: 'Advanced Communications',
      brand: 'Microsoft',
      segment: 'Commercial',
      term: '1 Year',
      billtype: 'Monthly',
      currency: 'USD',
      price: '3,627.00',
      approvedqty: '1',
      approvedprice: '3,627.00',
      subtotal: '3,627.00',
    },
    {
      id: '2',
      productname: 'Appsheet Enterprise Plus (Additional Service)',
      brand: 'Google Workspace Commitment',
      segment: 'Commercial',
      term: '1 Year',
      billtype: 'Monthly',
      currency: 'USD',
      price: '3,627.00',
      approvedqty: '1',
      approvedprice: '3,627.00',
      subtotal: '3,627.00',
    },
    {
      id: '3',
      productname: 'AWS Web Services',
      brand: 'AWS',
      segment: '-',
      term: '-',
      billtype: 'Monthly',
      currency: 'USD',
      price: '3,627.00',
      approvedqty: '1',
      approvedprice: '3,627.00',
      subtotal: '3,627.00',
    },
    {
      id: '4',
      productname: 'AWS Web Services (Additional Service)',
      brand: 'AWS',
      segment: '-',
      term: '-',
      billtype: 'Monthly',
      currency: 'USD',
      price: '3,627.00',
      approvedqty: '1',
      approvedprice: '3,627.00',
      subtotal: '3,627.00',
    },
  ];

  const [showDetails, setShowDetails] = useState(false);

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          Product Name
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div>
            <div className="text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
              {row.original.productname}
            </div>

            {showDetails && (
              <div className="flex flex-col">
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Brand:{' '}
                  <span className="text-InterfaceTextdefault font-normal">
                    {row.original.brand}
                  </span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Segment:{' '}
                  <span className="text-InterfaceTextdefault font-normal">
                    {row.original.segment}
                  </span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Term:{' '}
                  <span className="text-InterfaceTextdefault font-normal">{row.original.term}</span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Bill Type:{' '}
                  <span className="text-InterfaceTextdefault font-normal">
                    {row.original.billtype}
                  </span>
                </div>
              </div>
            )}

            {showDetails ? (
              <div
                className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextprimary font-[400] cursor-pointer"
                onClick={() => setShowDetails(false)}
              >
                Hide Details
              </div>
            ) : (
              <div
                className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextprimary font-[400] cursor-pointer"
                onClick={() => setShowDetails(true)}
              >
                View Details
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'currency',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Currency
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.currency}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
    {
      accessorKey: 'price',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          List Price
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.price}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },

    {
      accessorKey: 'approvedqty ',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Approved Qty
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.approvedqty}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
    {
      accessorKey: 'approvedprice',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Approved Price
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.approvedprice}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
    {
      accessorKey: 'subtotal',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Sub Total
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.subtotal}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="">
        <div className="px-[18px] xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw] border-b border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
            Item Details
          </div>
        </div>
        <div className="h-[220px] xl:h-[250px] 2xl:h-[260px] 3xl:h-[12.25vw] overflow-y-auto">
          <div className="px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
            <div className="product-table custtabletd custtable custtable1">
              <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
                <DataTable data={initialData} columns={notificationColumns} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
