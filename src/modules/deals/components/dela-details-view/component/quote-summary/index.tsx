'use client';
import React from 'react';

export default function QuoteSummary() {
  return (
    <>
      <div className="flex flex-col gap-[14px] xl:gap-[14px] 2xl:gap-[16px] 3xl:gap-[0.833vw]">
        <div className="flex justify-between items-center">
          <div className="text-InterfaceTexttitle font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw]">
            Deal Summary
          </div>
          <div className="text-InterfaceTexttitle font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw]">
            (USD)
          </div>
        </div>
        <div className="flex flex-col">
          <div className="px-[8px] xl:px-[8px] 3xl:px-[0.417vw] py-2 border-b border-InterfaceStrokesoft">
            <div className="flex justify-between items-center text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
              <div>Sub Value</div>
              <div className="font-semibold">8,000.00</div>
            </div>
          </div>
          <div className="px-[8px] xl:px-[8px] 3xl:px-[0.417vw] py-2 border-b border-InterfaceStrokesoft">
            <div className="flex justify-between items-center text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
              <div>Discount in Value</div>
              <div className="font-semibold">100.00</div>
            </div>
          </div>
        </div>
        <div className="bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[8px] 3xl:px-[0.417vw] py-2">
          <div className="flex justify-between items-center text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
            <div>Total Deal Value</div>
            <div className="font-semibold">7,900.00</div>
          </div>
        </div>
      </div>
    </>
  );
}
