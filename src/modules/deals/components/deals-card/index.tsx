'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import { useTranslations } from 'next-intl';
import DealsRedeemedBarchart from '@/components/common/charts/deals-redeemed-barchart';

export default function DealsCards() {
  const t = useTranslations();
  return (
    <div>
      <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-12   p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-doc1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw] "></i>
              <div className=" mt-[16px] xl:mt-[16px] 3xl:mt-[0.833vw]">
                <div className="text-InterfaceTextdefault font14 font-medium leading-[140%] ">
                  Total Deals
                </div>
                <div className="text-[20px] text-InterfaceTextdefault 2xl:text-[30px] 3xl:text-[1.875vw] font-semibold truncate">
                  175
                </div>
              </div>
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full">
              <div className=" h-[120px] xl:h-[120px]  3xl:h-[6.885vw]">
                <Piechart
                  legends={{
                    orient: 'vertical', // vertical layout
                    right: 0, // distance from the right edge
                    top: 50, // vertically centered
                    itemGap: 10,
                    itemHeight: 8,
                    itemWidth: 8,
                    data: [t('Active'), t('Expired')],
                    textStyle: {
                      fontSize: 10,
                      color: '#7F8488',
                    },
                  }}
                  name={'Nightingale Chart'}
                  radius={['40%', '90%']}
                  center={['40%', '50%']}
                  rosetype={'area'}
                  itemstyle={{
                    borderRadius: 5,
                    borderColor: '#fff',
                    borderWidth: 3,
                  }}
                  labelline={{
                    length: 7,
                    length2: 4,
                  }}
                  label={{
                    formatter: '{c}',
                    fontSize: 10,
                  }}
                  data={[
                    { value: 100000, name: t('Active'), itemStyle: { color: '#4D9E99' } },
                    { value: 100000, name: t('Expired'), itemStyle: { color: ' #73609B' } },
                  ]}
                />
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-12   p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-doc1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw] "></i>
              <div className=" mt-[16px] xl:mt-[16px] 3xl:mt-[0.833vw]">
                <div className="text-InterfaceTextdefault font14 font-medium leading-[140%] ">
                  Total Deals
                </div>
                <div className="text-[20px] text-InterfaceTextdefault 2xl:text-[30px] 3xl:text-[1.875vw] font-semibold truncate">
                  175
                </div>
              </div>
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full">
              <div className=" h-[120px] xl:h-[120px]  3xl:h-[6.885vw]">
                <Piechart
                  legends={{
                    orient: 'vertical', // vertical layout
                    right: 0, // distance from the right edge
                    top: 50, // vertically centered
                    itemGap: 10,
                    itemHeight: 8,
                    itemWidth: 8,
                    data: [t('Ordered'), t('In-Cart'), t('Abandoned')],
                    textStyle: {
                      fontSize: 10,
                      color: '#7F8488',
                    },
                  }}
                  name={'Nightingale Chart'}
                  radius={['40%', '90%']}
                  center={['40%', '50%']}
                  rosetype={'area'}
                  itemstyle={{
                    borderRadius: 5,
                    borderColor: '#fff',
                    borderWidth: 3,
                  }}
                  labelline={{
                    length: 7,
                    length2: 4,
                  }}
                  label={{
                    formatter: '{c}',
                    fontSize: 10,
                  }}
                  data={[
                    { value: 45, name: t('Ordered'), itemStyle: { color: '#1570EF' } },
                    { value: 38, name: t('In-Cart'), itemStyle: { color: '#42536D' } },
                    { value: 38, name: t('Abandoned'), itemStyle: { color: '#768FB5' } },
                  ]}
                />
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div>
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                Deals Redeemed by Customers - Top 5
              </div>
            </div>
            <div className="w-full h-[104px] 3xl:h-[6vw]">
              <DealsRedeemedBarchart />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
