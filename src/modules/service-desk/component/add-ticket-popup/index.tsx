'use client';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>etClose,
  <PERSON>etContent,
  <PERSON>etTrigger,
  SheetFooter,
  Input,
  Label,
  Textarea,
  ScrollArea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useRef, useState } from 'react';
import { TermsSheetProps } from '@/types';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Select2 } from '@/components/common/ui/combo-box';
import { useTranslations } from 'next-intl';
import NewTicketCreatedPopup from '../ticket-created-popup';
const ticketSchema = z.object({
  firstname: z.string().nonempty('First Name is required'),
  lastname: z.string().nonempty('Last Name is required'),
  email: z.string().email('Invalid email'),
  phone: z.string().nonempty('Phone is required'),
  requestType: z.string().nonempty('Request Type is required'),
  category: z.string().nonempty('Category is required'),
  companyname: z.string().optional(),
  priority: z.string().nonempty().optional(),
  description: z.string().max(250, 'Max 250 characters').optional(),
  attachment: z
    .any()
    .refine((file) => !file || ['image/png', 'image/jpeg', 'application/pdf'].includes(file.type), {
      message: 'Only PNG, JPG or PDF files are allowed',
    })
    .optional(),
});
export default function AddTicketPopup({}: TermsSheetProps) {
  const t = useTranslations();
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(ticketSchema),
    defaultValues: {
      firstname: '',
      lastname: '',
      email: '',
      phone: '',
      requestType: '',
      category: '',
      companyname: '',
      priority: '',
      description: '',
      attachment: undefined,
    },
  });

  const [newticketcreatedpopup, setNewTicketCreatedPopup] = useState(false);

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const onSubmit = () =>
    //data: any
    {
      // console.log('Form data:', data);
      reset();
    };

  return (
    <>
      <Sheet>
        <SheetTitle></SheetTitle>
        <SheetTrigger>
          <Button className="ticketcard gap-2 items-center flex rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
            <i className="cloud-Add font-normal text-[13px] xl:text-[13px] 3xl:text-[0.733vw] text-BrandSupport1pure"></i>{' '}
            {t('addNewTicket')}
          </Button>
        </SheetTrigger>
        <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
          {/* Header */}
          <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('addNewTicket')}
              </div>

              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <ScrollArea className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-[400px] lg:h-[370px] xl:h-[470px] 2xl:h-[660px] 3xl:h-[84vh]">
              <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('firstName')} <span className="text-[#f12e2e]"> *</span>
                  </Label>
                  <Input
                    {...register('firstname')}
                    type="text"
                    placeholder="Jese"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                  {errors.firstname && (
                    <span className="text-red-500 text-xs">{errors.firstname.message}</span>
                  )}
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="lastname"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('lastName')} <span className="text-[#f12e2e]"> *</span>
                  </Label>
                  <Input
                    {...register('lastname')}
                    type="text"
                    id="lastname"
                    placeholder="Jose"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                  {errors.lastname && (
                    <span className="text-red-500 text-xs">{errors.lastname.message}</span>
                  )}
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="companyname"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('companyName')}
                  </Label>
                  <Input
                    {...register('companyname')}
                    type="text"
                    id="companyname"
                    placeholder="Hexalytics"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="email"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('email')}
                    <span className="text-[#f12e2e]"> *</span>
                  </Label>
                  <Input
                    {...register('email')}
                    type="text"
                    id="email"
                    placeholder="<EMAIL>"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                  {errors.email && (
                    <span className="text-red-500 text-xs">{errors.email.message}</span>
                  )}
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="phone"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('phoneNumber')} <span className="text-[#f12e2e]"> *</span>
                  </Label>
                  <Input
                    {...register('phone')}
                    type="text"
                    id="phone"
                    placeholder="+971 5011 4444"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                  {errors.phone && (
                    <span className="text-red-500 text-xs">{errors.phone.message}</span>
                  )}
                </div>
                <div className="relative ">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('requestType')}
                    <span className="text-[#f12e2e]"> *</span>
                  </Label>
                  <div className="mt-2">
                    <Controller
                      name="requestType"
                      control={control}
                      render={({ field }) => (
                        // <Select onValueChange={field.onChange} defaultValue={field.value}>
                        //   <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        //     <SelectValue placeholder="All" className="" />
                        //   </SelectTrigger>
                        //   <SelectContent>
                        //     <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        //       <SelectItem value="All">All</SelectItem>
                        //       <SelectItem value="india">India</SelectItem>
                        //       <SelectItem value="mea">UAE</SelectItem>
                        //       <SelectItem value="tukry">Turky</SelectItem>
                        //     </SelectGroup>
                        //   </SelectContent>
                        // </Select>
                        <Select2
                          options={[
                            { label: 'All', value: 'All' },
                            { label: 'India', value: 'India' },
                            { label: 'UAE', value: 'UAE' },
                          ]}
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select Request Type"
                        />
                      )}
                    />
                    {errors.firstname && (
                      <span className="text-red-500 text-xs">{errors.firstname.message}</span>
                    )}
                  </div>
                </div>
                <div className="relative ">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('category')} <span className="text-[#f12e2e]"> *</span>
                  </Label>
                  <div className="mt-2">
                    <Controller
                      name="category"
                      control={control}
                      render={({ field }) => (
                        <Select2
                          options={[
                            { label: 'All', value: 'All' },
                            { label: 'India', value: 'India' },
                            { label: 'UAE', value: 'UAE' },
                          ]}
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select Category"
                        />
                      )}
                    />
                    {errors.lastname && (
                      <span className="text-red-500 text-xs">{errors.lastname.message}</span>
                    )}
                  </div>
                </div>
                <div className="relative ">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('priority')}
                  </Label>
                  <div className="mt-2">
                    <Controller
                      name="priority"
                      control={control}
                      render={({ field }) => (
                        <Select2
                          options={[
                            { label: 'All', value: 'All' },
                            { label: 'India', value: 'India' },
                            { label: 'UAE', value: 'UAE' },
                          ]}
                          value={field.value ?? ''}
                          onChange={field.onChange}
                          placeholder="Select Priority"
                        />
                      )}
                    />
                  </div>
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="description"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('descriptionOfIssueRequest')}
                  </Label>
                  <Textarea
                    {...register('description')}
                    id="description"
                    placeholder={t('typeDescription')}
                    className=" placeholder:text-[12px] placeholder:text-InterfaceTextsubtitle h-[160px] xl:h-[160px] 3xl:h-[8.333vw] text-InterfaceTextdefault border border-InterfaceStrokehard rounded-none"
                  />
                  <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                    {t('max250Characters')}{' '}
                  </div>
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="description"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('attachments')}
                  </Label>
                  <div className="bg-InterfaceSurfacecomponentmuted border-dashed border-[2px] border-InterfaceStrokedefault flex items-center justify-center py-[30px] xl:py-[35px] 3xl:py-[2.083vw] mb-[3vw]">
                    <Controller
                      name="attachment"
                      control={control}
                      render={({ field }) => (
                        <>
                          <Button
                            type="button"
                            onClick={() => fileInputRef.current?.click()}
                            className="h-full text-center flex flex-col bg-InterfaceSurfacecomponentmuted"
                          >
                            <i className="cloud-fileupload text-InterfaceTextsubtitle text-[30px] xl:text-[35px] 3xl:text-[2.083vw]"></i>
                            <input
                              type="file"
                              accept=".png,.jpg,.jpeg,.pdf"
                              ref={fileInputRef}
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                field.onChange(file);
                              }}
                              className="hidden"
                            />
                            <div className="text-InterfaceTextdefault text-[12px] font-medium">
                              {t('clickToUploadOrDragAndDrop')}
                            </div>
                            <div className="text-InterfaceTextsubtitle text-[10px] font-normal">
                              {t('fileTypeSize')}
                            </div>
                            {field.value && (
                              <div className="mt-2 text-xs text-InterfaceTextsubtitle">
                                Selected File:{' '}
                                <span className="font-medium">{field.value.name}</span>
                              </div>
                            )}
                          </Button>
                        </>
                      )}
                    />
                    {errors.phone && (
                      <span className="text-red-500 text-xs">{errors.phone.message}</span>
                    )}
                  </div>
                  <div className="relative ">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('requestType')}
                      <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <div className="mt-2">
                      <Controller
                        name="requestType"
                        control={control}
                        render={({ field }) => (
                          // <Select onValueChange={field.onChange} defaultValue={field.value}>
                          //   <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          //     <SelectValue placeholder="All" className="" />
                          //   </SelectTrigger>
                          //   <SelectContent>
                          //     <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          //       <SelectItem value="All">All</SelectItem>
                          //       <SelectItem value="india">India</SelectItem>
                          //       <SelectItem value="mea">UAE</SelectItem>
                          //       <SelectItem value="tukry">Turky</SelectItem>
                          //     </SelectGroup>
                          //   </SelectContent>
                          // </Select>
                          <Select2
                            options={[
                              { label: 'All', value: 'All' },
                              { label: 'India', value: 'India' },
                              { label: 'UAE', value: 'UAE' },
                            ]}
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select Request Type"
                          />
                        )}
                      />
                      {errors.requestType && (
                        <span className="text-red-500 text-xs">{errors.requestType.message}</span>
                      )}
                    </div>
                  </div>
                  <div className="relative ">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('category')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <div className="mt-2">
                      <Controller
                        name="category"
                        control={control}
                        render={({ field }) => (
                          <Select2
                            options={[
                              { label: 'All', value: 'All' },
                              { label: 'India', value: 'India' },
                              { label: 'UAE', value: 'UAE' },
                            ]}
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select Category"
                          />
                        )}
                      />
                      {errors.category && (
                        <span className="text-red-500 text-xs">{errors.category.message}</span>
                      )}
                    </div>
                  </div>
                  <div className="relative ">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('priority')}
                    </Label>
                    <div className="mt-2">
                      <Controller
                        name="priority"
                        control={control}
                        render={({ field }) => (
                          <Select2
                            options={[
                              { label: 'All', value: 'All' },
                              { label: 'India', value: 'India' },
                              { label: 'UAE', value: 'UAE' },
                            ]}
                            value={field.value ?? ''}
                            onChange={field.onChange}
                            placeholder="Select Priority"
                          />
                        )}
                      />
                    </div>
                  </div>
                  <div className="grid gap-1.5">
                    <Label
                      htmlFor="description"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('descriptionOfIssueRequest')}
                    </Label>
                    <Textarea
                      {...register('description')}
                      id="description"
                      placeholder={t('typeDescription')}
                      className=" placeholder:text-[12px] placeholder:text-InterfaceTextsubtitle h-[160px] xl:h-[160px] 3xl:h-[8.333vw] text-InterfaceTextdefault border border-InterfaceStrokehard rounded-none"
                    />
                    <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                      {t('max250Characters')}{' '}
                    </div>
                  </div>
                  <div className="grid gap-1.5">
                    <Label
                      htmlFor="description"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('attachments')}
                    </Label>
                    <div className="bg-InterfaceSurfacecomponentmuted border-dashed border-[2px] border-InterfaceStrokedefault flex items-center justify-center py-[30px] xl:py-[35px] 3xl:py-[2.083vw] mb-[3vw]">
                      <Controller
                        name="attachment"
                        control={control}
                        render={({ field }) => (
                          <>
                            <Button
                              type="button"
                              onClick={() => fileInputRef.current?.click()}
                              className="h-full text-center flex flex-col bg-InterfaceSurfacecomponentmuted"
                            >
                              <i className="cloud-fileupload text-InterfaceTextsubtitle text-[30px] xl:text-[35px] 3xl:text-[2.083vw]"></i>
                              <input
                                type="file"
                                accept=".png,.jpg,.jpeg,.pdf"
                                ref={fileInputRef}
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  field.onChange(file);
                                }}
                                className="hidden"
                              />
                              <div className="text-InterfaceTextdefault text-[12px] font-medium">
                                {t('clickToUploadOrDragAndDrop')}
                              </div>
                              <div className="text-InterfaceTextsubtitle text-[10px] font-normal">
                                {t('fileTypeSize')}
                              </div>
                              {field.value && (
                                <div className="mt-2 text-xs text-InterfaceTextsubtitle">
                                  Selected File:{' '}
                                  <span className="font-medium">{field.value.name}</span>
                                </div>
                              )}
                            </Button>
                          </>
                        )}
                      />
                    </div>
                    {errors.attachment && (
                      <span className="text-red-500 text-xs">
                        {(errors.attachment as { message: string }).message}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </ScrollArea>

            <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
              <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
                <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                  <SheetClose asChild>
                    <Button
                      size="sm"
                      onClick={() => {
                        reset();
                      }}
                      className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                    >
                      <div>
                        <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                      </div>
                      <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                        {t('cancel')}
                      </div>
                    </Button>
                  </SheetClose>
                  <SheetClose asChild>
                    <Button
                      type="submit"
                      size="sm"
                      onClick={() => setNewTicketCreatedPopup(true)}
                      className="applybtn flex gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                    >
                      <div>
                        <i className="cloud-fillcircletick text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                      </div>
                      <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                        {t('submit')}
                      </div>
                    </Button>
                  </SheetClose>
                </div>
              </div>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>

      <NewTicketCreatedPopup
        open={newticketcreatedpopup}
        onClose={() => setNewTicketCreatedPopup(false)}
      />
    </>
  );
}
