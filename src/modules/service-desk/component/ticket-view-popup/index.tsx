'use client';
import { TermsSheetProps } from '@/types';
import {
  <PERSON><PERSON><PERSON>it<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Sheet,
  She<PERSON><PERSON>lose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
// import Image from 'next/image';
import { useTranslations } from 'next-intl';

export default function TicketViewPopup({}: TermsSheetProps) {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="hover:bg-BrandNeutral100 w-full">
        <div className="px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault hover:text-BrandSupport1pure flex gap-2 items-center w-full hover:bg-BrandNeutral100">
          <i className="cloud-readeye1 text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
          {t('view')}
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[580px] 2xl:max-w-[600px] 3xl:max-w-[33.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between items-center">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%] flex gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw] items-center">
              {t('ticket')}: #SR-112233
              <span className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-BrandSupport1pure border border-BrandSupport1300 bg-BrandSupport150 py-1 px-2 leading-tight h-[25px]">
                {t('open')}
              </span>
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid grid-cols-2 gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw]">
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('firstName')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Alex
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('lastName')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Smith
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('companyName')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Hexalytics
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('email')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  <EMAIL>
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('mobileNumber')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  +971 55 111 4450
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('requestType')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Service Request
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('category')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Platform
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('priority')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Medium
                </div>
              </div>
              <div className="col-span-2 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('descriptionOfIssueRequest')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  test Description...
                </div>
              </div>
              <div className="col-span-2 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('resolutionRemarks')}
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Resolution remarks from the solution Architect/ Service Desk Agent
                </div>
              </div>
              <div className="col-span-2 space-y-1 py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('attachments')}
                </div>
                <div className="grid grid-cols-2 gap-[10px] xl:gap-[10px] 2xl:gap-[12px] 3xl:gap-[0.625vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] ">
                  <div className="grid grid-cols-5 border border-border-b border-InterfaceStrokesoft gap-[10px] xl:gap-[10px] 2xl:gap-[12px] 3xl:gap-[0.625vw] p-[10px] xl:p-[10px] 2xl:p-[12px] 3xl:p-[0.625vw] flex items-center">
                    <div className="col-span-1">
                      <Image
                        src={'/images/svg/pdf.svg'}
                        width={36}
                        height={36}
                        alt="pdf"
                        className=" "
                      />
                    </div>
                    <div className="col-span-3  text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      <div className="text-InterfaceTexttitle font-[600]">Document 001.pdf</div>
                      <div className="text-InterfaceTextsubtitle">{t('fileSize')} : 758kb</div>
                    </div>
                    <div className="col-span-1">
                      <i className="text-InterfaceTextsubtitle cloud-document-download1"></i>
                    </div>
                  </div>
                  <div className="grid grid-cols-5 border border-border-b border-InterfaceStrokesoft gap-[10px] xl:gap-[10px] 2xl:gap-[12px] 3xl:gap-[0.625vw] p-[10px] xl:p-[10px] 2xl:p-[12px] 3xl:p-[0.625vw] flex items-center">
                    <div className="col-span-1">
                      <Image
                        src={'/images/svg/pdf.svg'}
                        width={36}
                        height={36}
                        alt="pdf"
                        className=" "
                      />
                    </div>
                    <div className="col-span-3 text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      <div className="text-InterfaceTexttitle font-[600]">Document 001.pdf</div>
                      <div className="text-InterfaceTextsubtitle">{t('fileSize')} : 758kb</div>
                    </div>
                    <div className="col-span-1 justify-end">
                      <i className="text-InterfaceTextsubtitle cloud-document-download1"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
