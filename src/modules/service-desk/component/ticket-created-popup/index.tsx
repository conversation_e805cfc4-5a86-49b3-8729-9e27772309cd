'use client';
import * as React from 'react';
import { Sheet, SheetClose, SheetContent } from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';
import { TermsSheetProps } from '@/types';

interface ChangePasswordProps extends TermsSheetProps {
  userData?: {
    userId?: string;
  };
}
export default function NewTicketCreatedPopup({ open, onClose }: ChangePasswordProps) {
  const t = useTranslations();

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[550px] 3xl:max-w-[31.25vw] p-[0px]">
        <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-info2 text-BrandPrimarypure text-[72px] xl:text-[60px] 3xl:text-[3.75vw]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[30px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                New Ticket Created Successfully!
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
            <div className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-background bg-BrandPrimarypurenew  font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex  items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
              <i className="cloud-Add text-[18px] xl:text-[12px] 3xl:text-[0.938vw]"></i>
              Create Another Ticket
            </div>
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">{t('or')}</p>
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
            </div>
            <SheetClose>
              <div className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                <i className="cloud-back text-[18px] xl:text-[10px] 3xl:text-[0.938vw]"></i>
                Back to Service Desk
              </div>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
