'use client';
import * as React from 'react';
import {
  <PERSON>,
  Sheet<PERSON><PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
// import Link from 'next/link';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types/components';
import { useTranslations } from 'next-intl';
export default function ApplyFilterPopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();
  const [date, setDate] = React.useState<Date>();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault flex items-center gap-2  h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="relative flex items-center">
            <i className="relative cloud-filter text-[15px] xl:text-[16px] 3xl:text-[0.738vw]"></i>
            <div className="absolute z-10 right-0 -top-3 bottom-0 left-2 h-4 w-4 rounded-full bg-blackcolor">
              <div className="text-InterfaceTextwhite text-[10px]">2</div>
            </div>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]  font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[500px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto h-[400px] xl:h-[500px] 3xl:h-[32.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('dateCreated')}
              </Label>
              <div className="grid grid-cols-2 gap-[10px] xl:gap-[10px] 3xl:gap-[0.521vw]">
                <div className="col-span-1">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                          !date && 'text-muted-foreground'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle">{t('selectFromDate')}</span>
                        )}
                        <i className="cloud-canlendar"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-[#FFF]" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="col-span-1">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none',
                          !date && 'text-muted-foreground'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle">{t('selectToDate')}</span>
                        )}
                        <i className="cloud-canlendar"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-[#FFF]" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('ticketCategory')}
              </label>
              <div className="mt-2">
                <Select defaultValue="All">
                  <SelectTrigger className="text-InterfaceTextdefault placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#FFF]">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('selectCreatedBy')}
              </label>
              <div className="mt-2">
                <Select defaultValue="All">
                  <SelectTrigger className="text-InterfaceTextdefault placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#FFF]">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('status')}
              </label>
              <div className="mt-2">
                <Select defaultValue="All">
                  <SelectTrigger className="text-InterfaceTextdefault placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#FFF]">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[24px] xl:p-[20px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
              <SheetClose>
                <div>
                  <Button
                    size="sm"
                    onClick={onClose}
                    className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[16px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[10px] xl:py-[8px] 3xl:py-[0.521vw] px-[16px] xl:px-[12px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-closecircle flex items-center text-InterfaceTextdefault text-[18px] xl:text-[14px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className="text-InterfaceTextdefault text-[16px] xl:text-[14px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                      {t('cancel')}
                    </div>
                  </Button>
                </div>
              </SheetClose>
              <SheetClose>
                <div>
                  <Button
                    size="sm"
                    onClick={onClose}
                    className="flex items-center gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[16px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[10px] xl:py-[8px] 3xl:py-[0.521vw] px-[16px] xl:px-[12px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-filter text-[#EEEEF0] flex items-center text-[18px] xl:text-[14px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className=" text-white font-medium leading-[16px] flex items-center text-[16px] xl:text-[14px] 3xl:text-[1.042vw]">
                      {t('applyFilter')}
                    </div>
                  </Button>
                </div>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
