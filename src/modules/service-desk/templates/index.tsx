'use client';
import * as React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Input,
  Badge,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import AddTicketPopup from '@/modules/service-desk/component/add-ticket-popup';
import ApplyFilterPopup from '@/modules/service-desk/component/apply-filter-popup';
import TicketViewPopup from '@/modules/service-desk/component/ticket-view-popup';
// import { AChart } from '@/components/common/charts/areachart';
// import { SimpleBarChart } from '@/components/common/charts/simplebarchart';
// import { SimpleBarChart2 } from '@/components/common/charts/simplebarchart2';
import {
  Button,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TablePagination } from '@/components/common/pagination';
import Piechart from '@/components/common/charts/piechart';
import ServiceDeskBarchart from '@/components/common/charts/servicedeskbarchart';
import { useTranslations } from 'next-intl';

const frameworks = [
  {
    value: 'Open',
    label: 'Open',
  },
  {
    value: 'InReview',
    label: 'In Review',
  },
  {
    value: 'Closed',
    label: 'Closed',
  },
  {
    value: 'Cancelled',
    label: 'Cancelled',
  },
];

export default function ServiceDeskTemplate() {
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const statusStyles: Record<string, string> = {
    Open: 'text-BrandSupport1pure border border-BrandSupport1300 bg-BrandSupport150',
    'In Review': 'text-BrandHighlightpure border border-BrandPrimary300 bg-BrandPrimary50',
    Closed: 'text-BrandNeutralpure border border-interfacestrokedefaultnew bg-BrandNeutral50',
    Cancelled: 'text-BrandSupport2pure border border-BrandSupport2300 bg-BrandSupport250',
  };
  const [selectedValues, setSelectedValues] = React.useState<string[]>([]);
  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'ticket',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('ticket')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-BrandSupport1pure cursor-pointer">{row.getValue('ticket')}</div>
      ),
    },
    {
      accessorKey: 'datecreated',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('dateCreated')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'ticketcategory',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('ticketCategory')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'desc',
      enableSorting: true,
      size: 1000,
      minSize: 1000,
      maxSize: 1000,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('description')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'createdby',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('createdBy')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-[100px] justify-between bg-transparent border-none pl-0 text-InterfaceTexttitle hover:text-none"
                  >
                    {selectedValues.length > 0
                      ? frameworks
                          .filter((fw) => selectedValues.includes(fw.value))
                          .map((fw) => fw.label)
                          .join(', ')
                      : t('status')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[190px] xl:w-[200px 3xl:w-[10.833vw] p-0 bg-interfacesurfacecomponent">
                  <Command>
                    <CommandInput placeholder="Search here..." className="h-9" />
                    <CommandList>
                      <CommandEmpty>.{t('noFrameworkFound')}</CommandEmpty>
                      <CommandGroup>
                        {frameworks.map((framework) => {
                          const isSelected = selectedValues.includes(framework.value);
                          return (
                            <CommandItem
                              className="border-b border-InterfaceStrokesoft"
                              key={framework.value}
                              onSelect={() => {
                                setSelectedValues((prev) =>
                                  isSelected
                                    ? prev.filter((v) => v !== framework.value)
                                    : [...prev, framework.value]
                                );
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <input type="checkbox" checked={isSelected} readOnly />
                                <span className="text- InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                                  {framework.label}
                                </span>
                              </div>
                            </CommandItem>
                          );
                        })}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                  <div className="flex justify-center items-center mx-[14px] xl:mx-[16px] 3xl:mx-[0.833vw] my-[10px] xl:my-[10px] 3xl:my-[0.521vw]">
                    <div className="w-full m-auto text-InterfaceTextwhite flex justify-center gap-[8px] 3xl:gap-[0.417vw] applybtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none">
                      <i className="cloud-filter"></i>
                      {t('apply')}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const style = statusStyles[status] || 'bg-muted text-muted-foreground';
        return (
          <Badge
            className={`rounded-none py-1 px-2 text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-medium ${style}`}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] p-0 rounded-none w-[150px] xl:w-[150px] 3xl:w-[7.813vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <div className="">
                  <TicketViewPopup open={false} onClose={() => {}} />
                  <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault hover:hover:text-BrandSupport1pure flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                    <i className="cloud-closecircle text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
                    {t('cancel')}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 100,
      minSize: 100,
      maxSize: 100,
    },
  ];

  const data = [
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Renewal',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'Open',
    },
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Order',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'In Review',
    },
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Subscription',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'Closed',
    },
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Invoice',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'Cancelled',
    },
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Renewal',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'Open',
    },
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Account',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'In Review',
    },
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Quotation',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'Closed',
    },
    {
      ticket: 'SR-112233',
      datecreated: '05/05/2024',
      ticketcategory: 'Discount Coupon',
      desc: 'Test description...',
      createdby: 'Jese Leos',
      status: 'Cancelled',
    },
  ];

  type User = {
    ticket: string;
    datecreated: string;
    ticketcategory: string;
    desc: string;
    createdby: string;
  };

  return (
    <div className="p-[20px] pr-[20px] xl:pr-[30px] 2xl:pr-[32px] 3xl:pr-[1.823vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] w-full pl-[32px]">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle">
          {t('serviceDesk')}
        </div>
      </div>
      <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <AddTicketPopup open={false} onClose={() => {}} />
          <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
            <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('download')}
          </Button>
        </div>
        <div className="grid grid-cols-3  gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
          <div className=" rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow  p-[14px] xl:p-[12px] 3xl:p-[0.833vw] grid grid-cols-7">
            <div className="col-span-2 flex flex-col justify-between h-full">
              <div className="">
                <i className="cloud-cards1 text-[25px] xl:text-[20px] 3xl:text-[1.302vw] text-[#7F8488] cloud-cards"></i>
              </div>
              <div className="text-[12px] xl:text-[10px] 3xl:text-[0.733vw] font-medium text-InterfaceTextdefault">
                Total Tickets*
              </div>
              <div className="text-[25px] xl:text-[24px] 3xl:text-[1.458vw] font-semibold text-InterfaceTextdefault">
                501
              </div>
            </div>
            <div className="col-span-5  h-[120px]  xl:h-[100px] 3xl:h-[6.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 0, // distance from the right edge
                  top: 10, // vertically centered
                  itemGap: 4,
                  itemHeight: 8,
                  itemWidth: 8,
                  data: ['Open', 'In-review'],
                  textStyle: {
                    fontSize: 10,
                    color: '#7F8488',
                  },
                }}
                name={'Nightingale Chart'}
                radius={[22, 47]}
                center={['45%', '50%']}
                // center={['20%', '45%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 3,
                  length2: 1,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 400, name: 'Open', itemStyle: { color: '#1570EF' } },
                  { value: 300, name: 'In-review', itemStyle: { color: ' #FACE4F' } },
                ]}
              />
            </div>
          </div>

          <div className="rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow p-[14px] xl:p-[12px] 3xl:p-[0.833vw] grid grid-cols-7">
            <div className="col-span-2 flex flex-col justify-between h-full">
              <div className="">
                <i className="cloud-card1 text-[25px] xl:text-[20px] 3xl:text-[1.302vw] text-[#7F8488] cloud-card1"></i>
              </div>
              <div className="text-[12px] xl:text-[10px] 3xl:text-[0.833vw] font-medium text-InterfaceTextdefault">
                {t('openTickets')}*
              </div>
              <div className="text-[25px] xl:text-[24px] 3xl:text-[1.458vw] font-semibold text-InterfaceTextdefault">
                25
              </div>
            </div>
            <div className="col-span-5  h-[120px] xl:h-[100px] 3xl:h-[6.885vw]">
              <ServiceDeskBarchart
                xaxisdata={['Category1', 'Category2', 'Category3', 'Category4']}
                yaxisdata={['450', '300', '520', '650', '550']}
                color1="#D67309"
              />
            </div>
          </div>
          <div className=" rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow p-[14px] xl:p-[12px] 3xl:p-[0.833vw] grid grid-cols-7">
            <div className="col-span-2  flex flex-col justify-between h-full">
              <div className="">
                <i className="cloud-card-edit1  text-[25px] xl:text-[20px] 3xl:text-[1.302vw] text-[#7F8488] cloud-card-edit"></i>
              </div>

              <div className="text-[12px] xl:text-[10px] 3xl:text-[0.833vw] font-medium text-InterfaceTextdefault ">
                Tickets <br /> in Review*
              </div>
              <div className="text-[25px] xl:text-[24px] 3xl:text-[1.458vw] font-semibold text-InterfaceTextdefault">
                15
              </div>
            </div>
            <div className="col-span-5 h-[120px] xl:h-[100px] 3xl:h-[6.885vw]">
              <ServiceDeskBarchart
                xaxisdata={['Category1', 'Category2', 'Category3', 'Category4']}
                yaxisdata={['450', '300', '520', '650', '550']}
                color1="#1570EF"
              />
            </div>
          </div>
        </div>
        <div className="bg-interfacesurfacecomponent tbl-shadow">
          <div className="flex justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
            <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div className="text-InterfaceTexttitle text-[18px] font-semibold">
                {t('allRecords')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                {t('showingRecords')}
              </div>
            </div>
            <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
              {/* <div className="relative w-[300px] xl:w-[320px] 2xl:w-[400px] 3xl:w-[18.625vw] bg-white border border-InterfaceStrokedefault">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4 " />
                <Input
                  type="text"
                  placeholder="Search"
                  className="pl-[35px] xl:pl-[35px] 2xl:pl-[35px] 3xl:pl-[1.5vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[14px] xl:text-[16px] 3xl:text-[0.833vw]"
                />
              </div> */}
              <div className="relative flex items-center">
                <Input
                  type="text"
                  placeholder={t('search')}
                  className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault flex items-center"
                />
                <i className="cloud-search text-[#777F86] absolute top-[10px] xl:top-[10px] 2xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.883vw] "></i>
              </div>
              <ApplyFilterPopup open={false} onClose={() => {}} />
            </div>
          </div>
          <div className="flex items-center justify-start px-[14px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] gap-2 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
            <div className="group text-InterfaceTextdefault hover:text-InterfaceTexttitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              {t('date')}
              <i className="cloud-closecircle text-ClearAll text-InterfaceTextsubtitle group-hover:text-InterfaceTexttitle"></i>
            </div>
            <div className="group text-InterfaceTextdefault hover:text-InterfaceTexttitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              {t('records')}
              <i className="cloud-closecircle text-ClearAll text-InterfaceTextsubtitle group-hover:text-InterfaceTexttitle"></i>
            </div>
            <div className="text-ClearAll py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              <i className="cloud-closecircle text-ClearAll"></i>
              {t('clearAll')}
            </div>
          </div>
          <DataTable data={data} columns={notificationColumns} withCheckbox={true} />
          <TablePagination />
        </div>
      </div>
    </div>
  );
}
