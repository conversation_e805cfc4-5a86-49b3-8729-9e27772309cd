'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Sheet,
  <PERSON>etClose,
  <PERSON>etContent,
  <PERSON><PERSON><PERSON>rigger,
  SheetFooter,
  Label,
  Input,
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
// 1. Define Zod Schema
const brandSchema = z.object({
  brand: z.string().min(1, 'Brand is required'),
  apnId: z.string().min(1, 'APN ID is required'),
});

type BrandFormData = z.infer<typeof brandSchema>;

export default function BrandSetupPopup() {
  const t = useTranslations();

  // 2. Set up form hook
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<BrandFormData>({
    resolver: zodResolver(brandSchema),
  });

  // 3. Submit Handler
  const onSubmit = () => {
    // console.log('Form Data:', data);
    // Implement your API or state update logic here
  };

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-2 text:InterfaceTextdefault hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="">
            <i className="cloud-export text:InterfaceTextdefault  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTextdefault  ">
            {t('initiate')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col h-full">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('brandSetUp')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[500px] 2xl:h-[550px] 3xl:h-[36.25vw]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="grid gap-1.5">
                <Label
                  htmlFor="email"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('brand')}*
                </Label>
                <Input
                  id="brand"
                  type="text"
                  placeholder={t('brand')}
                  className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                    errors.brand ? 'border-red-500' : ''
                  }`}
                  {...register('brand')}
                />
                {errors.brand && (
                  <p className="text-red-500 text-xs mt-1">{errors.brand.message}</p>
                )}
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="email"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('enterAPNID')}*
                </Label>
                <div className="relative w-full">
                  <Input
                    id="apnId"
                    type="text"
                    placeholder={t('enterAPNID')}
                    className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                      errors.apnId ? 'border-red-500' : ''
                    }`}
                    {...register('apnId')}
                  />

                  <Popover>
                    <PopoverTrigger asChild>
                      <i className="cursor-pointer absolute right-3 top-1/2 transform -translate-y-1/2 cloud-messagebox text-InterfaceTextdefault z-30"></i>
                    </PopoverTrigger>

                    <PopoverContent
                      className=" w-[320px] xl:w-[320px] 3xl:w-[17.188vw] p-0 mr-[30px] xl:mr-[30px] 3xl:mr-[2.125vw] rounded-none z-50 bg-interfacesurfacecomponent cursor-pointer"
                      sideOffset={5}
                    >
                      <div className="p-[12px] xl:p-[12px] 3xl:p-[0.621vw]">
                        <div className="flex justify-between items-center">
                          <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-semibold">
                            {t('info')}
                          </div>
                          <div>
                            <i className="cloud-closecircle text-closecolor font-[500]"></i>
                          </div>
                        </div>
                        <div className="pt-[16px] text-InterfaceTextdefault text-[13px] xl:text-[13px] 3xl:text-[0.677vw]">
                          APN ID is AWS Partner Network ID to be created by every partner to
                          transact with AWS. This is a one-time activity and there is no expiry
                          period. APN ID will provide the access to upload the opportunities,
                          Training & Accreditation
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                  {errors.apnId && (
                    <p className="text-red-500 text-xs mt-1">{errors.apnId.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <Link
                  href="#"
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[0.933vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </Link>
                <Button
                  className="flex applybtn items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  size="md"
                >
                  <div>
                    <i className="cloud-save-2 text-interfacetextinverse flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[0.933vw]">
                    {t('save')}
                  </div>
                </Button>
              </div>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
