'use client';
import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import Link from 'next/link';
import BrandSetupPopup from '../brand-setup-popup';
import { useTranslations } from 'next-intl';

export default function BrandSetupTabel() {
  const t = useTranslations();

  const agreementColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'brand',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brands')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('brands')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => (
        <div className=" text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer hover:font-[500]">
          {row.getValue('brand')}
        </div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'agreementtype',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('resellerId')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('resellerId')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="flex justify-center items-center">{row.getValue('agreementtype')}</div>
      ),

      minSize: 400,
    },

    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('status')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Accepted: {
            colorClass: 'bg-[#EDFFF3] text-[#00953A] border-[#71FFA8]',
          },
          Pending: {
            colorClass: 'bg-[#FDF2C8] text-[#903D10] border-[#FACE4F]',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-[#FDF2C8] text-[#903D10] border-[#F2980E]',
        };

        return (
          <div className="cursor-pointer flex justify-center">
            <div
              className={`inline-block py-[6px] xl:py-[6px] 3xl:py-[0.357vw] px-[10px] rounded-[2px] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-[500] border leading-none  ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 400,
    },

    {
      accessorKey: 'agreementdownload',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('agreement')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('agreement')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({}) => {
        return (
          <div className="flex gap-3 justify-center items-center">
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="">
                  <i className="cloud-document-download1 text-[#00953A] text-[18px] xl:text-[18px] 3xl:text-[0.938vw]" />
                </Link>
              </TooltipTrigger>
              <TooltipContent side="top" className="bg-interfacesurfacecomponent">
                <div className="flex ">
                  <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault">
                    {t('acceptedBy')}:
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-semibold pl-3">
                    {' '}
                    Ganesh Chaudhari
                  </div>
                </div>
                <div className="flex ">
                  <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault">
                    {t('acceptedOn')}:
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-semibold pl-3">
                    {' '}
                    11/3/2025
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[90px] xl:w-[90px] 3xl:w-[4.533vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <BrandSetupPopup />
                {/* <BrandSetupEditPopup /> */}
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    brand: string;
    agreementtype: string;
    agreementdownload: string;
    status: string;
  };

  const Agreementdata: User[] = [
    {
      brand: 'Amazon',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Google Cloud',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Microsoft CSP',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Oracle',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Cisco ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },

    {
      brand: 'Fortinet ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Freshdesk  ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'TrendMicro  ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Vendor Name   ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Vendor Name   ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Vendor Name   ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
    {
      brand: 'Vendor Name   ',
      agreementtype: '-',
      agreementdownload: '',
      status: 'Not Initiated',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('allBrands')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="flex gap-[20px] xl:gap-[20px] 3xl:gap-[1.05vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            <div className=" cursor-pointer flex items-center gap-2 text:InterfaceTexttitle hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
              <div className="">
                <i className="cloud-filtericon text:InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
              </div>
              <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTexttitle font-medium">
                {t('filter')}
              </p>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <TooltipProvider>
            <DataTable data={Agreementdata} columns={agreementColumns} withCheckbox={false} />
          </TooltipProvider>
        </div>
        <TablePagination />
      </div>
    </>
  );
}
