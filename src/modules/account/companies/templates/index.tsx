'use client';
import React from 'react';
import {
  <PERSON>ver<PERSON>ard,
  HoverCardTrigger,
  HoverCardContent,
  Button,
} from '@redington-gulf-fze/cloudquarks-component-library';

import Image from 'next/image';
import UserAccessPopup from '../components/user-access-popup';
import { Roboto } from 'next/font/google';
import BillingProfile from '../components/billing-profiles';
import BillingAddress from '../components/billing-address';
import BillingProfilePopup from '../components/billing-profile-popup';
import Link from 'next/link';
import OnboardNewCompanyPopup from '../components/onboard-new-company';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});
const AllCompanyInformation = () => {
  // const [openCompanyProfile, setOpenCompanyProfile] = React.useState<boolean>(false);
  const [openBillingProfile, setOpenBillingProfile] = React.useState<boolean>(false);
  const [openOnboardNewCompany, setOnboardNewCompany] = React.useState<boolean>(false);
  const t = useTranslations();

  return (
    <>
      <div className="bg-white    p-[24px] xl:p-[24px] 3xl:p-[1.25vw]">
        <div className="flex justify-between items-center flex-wrap pb-[16px] xl:pb-[20px] 3xl:pb-[1.042vw] border-b border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
            {t('companyInformation')}
          </div>
          <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
            <Button
              type="button"
              onClick={() => setOnboardNewCompany(true)}
              className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none applybtn text-InterfaceTextwhite  text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
              size="sm"
            >
              <i className=" cursor-pointer cloud-Add text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              {t('onboardNewCompany')}
            </Button>
          </div>
        </div>
        <OnboardNewCompanyPopup
          open={openOnboardNewCompany}
          onClose={() => setOnboardNewCompany(false)}
        />
        <div className={roboto.className}>
          <div className="h-[420px] lg:h-[450px] xl:h-[550px] 3xl:h-[35.25vw] overflow-y-auto mt-3">
            <div className="p-4 bg-[#f5f6f9]">
              <div className="flex justify-between items-center mb-[14px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                <h2 className="text-[16px] 3xl:text-[0.833vw] font-[500]">
                  {t('company')}: Hexalytics
                </h2>
                <div>
                  <div>
                    <UserAccessPopup />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 xl:grid-cols-3 gap-5">
                {/* Company Info Card */}
                <div className="bg-background  p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] cardShadow text-[12px] 3xl:text-[0.729vw]">
                  <div className="grid grid-cols-12 gap-2 mb-[10px] lg:mb-[20px] xl:mb-[24px] 2xl:mb-[24px] 3xl:mb-[1.25vw]">
                    <div className="col-span-8 flex items-center gap-2">
                      <Image src="/images/briefcase_icon.svg" width={24} height={24} alt="logo" />
                      <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                        {t('companyProfile')}
                      </div>
                    </div>
                    <div className="col-span-4 text-right text-InterfaceTextsubtitle">
                      <HoverCard>
                        <HoverCardTrigger asChild>
                          <div className="flex justify-end items-center h-full">
                            <i className="cloud-info text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          </div>
                        </HoverCardTrigger>
                        <HoverCardContent className="custom-hovercard-left  w-410  bg-background">
                          <div className="flex flex-col items-start gap-4 w-[410px]">
                            <div className="text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.933vw] font-[500] text-InterfaceTexttitle">
                              {t('details')}
                            </div>

                            <div className="flex items-center gap-4 w-full">
                              <div className="flex flex-col items-start gap-[2px] w-1/2">
                                <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                                  {t('admin')}
                                </div>
                                <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                                  {t('email')}
                                </div>
                              </div>

                              <div className="flex flex-col items-start gap-[2px] w-1/2">
                                <div className="text-sm text-InterfaceTextdefault-400">
                                  : Joes Boz
                                </div>
                                <div className="text-sm text-InterfaceTextdefault-400">
                                  : <EMAIL>
                                </div>
                              </div>
                            </div>
                          </div>
                        </HoverCardContent>
                      </HoverCard>
                    </div>
                  </div>

                  <div className="grid grid-cols-5 gap-4 mb-4">
                    <div className="col-span-2">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('companyName')}
                      </div>
                      <div className="font-[500]">Hexalytics</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('country')}
                      </div>
                      <div className="font-[500]">UAE</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('store')}
                      </div>
                      <div className="font-[500]">Dubai</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('role')}
                      </div>
                      <div className="font-[500]">Admin</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-12 gap-4 mb-4">
                    <div className="col-span-9">
                      <div className="flex text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('address')}
                      </div>
                      <div className="font-[500]">Villa 6 Zaglabi Street 99 Al Barsha Turkey</div>
                    </div>

                    <div className="col-span-3">
                      <div className="flex flex-col  justify-end">
                        <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                          {t('status')}
                        </div>

                        <div
                          className="bg-BrandSupport2100 border border-BrandSupport2300 text-BrandSupport2800 font-[500] text-center px-2 xl:px-1 3xl:px-[0.417vw] py-[2px] xl:px-[1px] 3xl:py-[0.104vw] text-[14px] xl:text-[10px] 3xl:text-[0.729vw]"
                          //     className="bg-BrandSupport2100 border border-BrandSupport2300 text-BrandSupport2800 font-[500] text-center px-2 py-1 text-xs leading-[140%]
                          // text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                        >
                          Pending
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 ">
                    <div className="bg-BrandNeutral50 border border-InterfaceStrokedefault p-[10px] 3xl:p-[0.521vw]">
                      <div className="text-InterfaceTextdefault font-semibold flex items-center justify-between mb-1 3xl:mb-2">
                        {t('guestAccount')}
                        <Link href={'/account/companies/account-information'}>
                          <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        </Link>
                      </div>
                      <div className="flex flex-wrap items-center justify-between text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('createdOn')}</div>
                        <div className="font-semibold">01/02/2025</div>
                      </div>
                    </div>

                    <div className="bg-BrandNeutral50 border border-InterfaceStrokedefault p-[10px] 3xl:p-[0.521vw]">
                      <div className="text-InterfaceTextdefault font-semibold flex justify-between items-center mb-1 3xl:mb-2">
                        {t('onboarding')}
                        <Link href={`/account/companies/view/${'123'}`}>
                          <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        </Link>
                      </div>
                      <div className="flex flex-wrap items-center justify-between gap-1 mb-1 text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('submittedOn')}</div>
                        <div className="font-semibold">01/02/2025</div>
                      </div>
                      <div className="flex flex-wrap items-center justify-between gap-1 text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('approvedOn')}</div>
                        <div className="font-semibold">-</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Billing Profile Card */}
                <div
                  className="relative bg-white  p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] cardShadow text-[12px] 3xl:text-[0.729vw] cursor-pointer  "
                  onClick={() => setOpenBillingProfile(true)}
                >
                  <div className="col-span-8 flex items-center gap-2 mb-[10px] lg:mb-[20px] xl:mb-[24px] 2xl:mb-[24px] 3xl:mb-[1.25vw]">
                    <Image src="/images/receipt_icon.svg" width={24} height={24} alt="logo" />
                    <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                      {t('billingProfile')}
                    </div>
                  </div>

                  <BillingProfile />
                </div>

                {/* Billing Address Card */}
                <div className="bg-background p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] cardShadow text-InterfaceTexttitle text-[12px] 3xl:text-[0.729vw]">
                  <div className=" flex items-center gap-2 ">
                    <Image src="/images/routing_icon.svg" width={24} height={24} alt="logo" />
                    <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                      {t('billingAddress')}
                    </div>
                  </div>

                  <>
                    <BillingAddress />
                  </>
                </div>
              </div>
            </div>

            <div className="p-4 bg-[#f5f6f9]">
              <div className="flex justify-between items-center mb-[14px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                <h2 className="text-[16px] 3xl:text-[0.833vw] font-[500]">
                  {t('companyProfile')} 2
                </h2>
                <div>
                  <div>
                    <UserAccessPopup />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-5">
                {/* Company Info Card */}
                <div className="bg-background  p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] cardShadow text-[12px] 3xl:text-[0.729vw]">
                  <div className="grid grid-cols-12 gap-2 mb-4">
                    <div className="col-span-8 flex items-center gap-2">
                      <Image src="/images/briefcase_icon.svg" width={24} height={24} alt="logo" />
                      <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                        {t('companyProfile')}
                      </div>
                    </div>
                    <div className="col-span-4 text-right text-InterfaceTextsubtitle">
                      <HoverCard>
                        <HoverCardTrigger asChild>
                          <div className="flex justify-end items-center h-full">
                            <i className="cloud-info text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          </div>
                        </HoverCardTrigger>
                        <HoverCardContent className="w-410 bg-background">
                          <div className="flex flex-col items-start gap-4 w-[410px] p-4">
                            <div className="text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.933vw] font-[500] text-InterfaceTexttitle">
                              {t('details')}
                            </div>

                            <div className="flex items-center gap-4 w-full">
                              <div className="flex flex-col items-start gap-[2px] w-1/2">
                                <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                                  {t('admin')}
                                </div>
                                <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                                  {t('email')}
                                </div>
                              </div>

                              <div className="flex flex-col items-start gap-[2px] w-1/2">
                                <div className="text-sm text-InterfaceTextdefault-400">
                                  : Joes Boz
                                </div>
                                <div className="text-sm text-InterfaceTextdefault-400">
                                  : <EMAIL>
                                </div>
                              </div>
                            </div>
                          </div>
                        </HoverCardContent>
                      </HoverCard>
                    </div>
                  </div>

                  <div className="grid grid-cols-5 gap-4 mb-4">
                    <div className="col-span-2">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('companyName')}
                      </div>
                      <div className="font-[500]">Hexalytics</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('country')}
                      </div>
                      <div className="font-[500]">UAE</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('store')}
                      </div>
                      <div className="font-[500]">Dubai</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('role')}
                      </div>
                      <div className="font-[500]">Admin</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-12 gap-4 mb-4">
                    <div className="col-span-9">
                      <div className="flex text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('address')}
                      </div>
                      <div className="font-[500]">Villa 6 Zaglabi Street 99 Al Barsha Turkey</div>
                    </div>

                    <div className="col-span-3">
                      <div className="flex flex-col  justify-end">
                        <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                          {t('status')}
                        </div>
                        <span
                          className="bg-BrandSupport2100 border border-BrandSupport2300 text-BrandSupport2800 font-[500] text-center px-2 py-1 text-xs leading-[140%] 
                      text-[14px] xl:text-[10px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                        >
                          Pending
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 ">
                    <div className="bg-BrandNeutral50 border border-InterfaceStrokedefault p-[10px] 3xl:p-[0.521vw]">
                      <div className="text-InterfaceTextdefault font-semibold flex items-center justify-between mb-1 3xl:mb-2">
                        {t('guestAccount')}
                        <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                      </div>
                      <div className="flex flex-wrap items-center justify-between text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('createdOn')}</div>
                        <div className="font-semibold">01/02/2025</div>
                      </div>
                    </div>

                    <div className="bg-BrandNeutral50 border border-InterfaceStrokedefault p-[10px] 3xl:p-[0.521vw]">
                      <div className="text-InterfaceTextdefault font-semibold flex justify-between items-center mb-1 3xl:mb-2">
                        {t('onboarding')}
                        <Link href={`/account/companies/view/${'123'}`}>
                          <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        </Link>
                      </div>
                      <div className="flex flex-wrap items-center justify-between gap-1 mb-1 text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('submittedOn')}</div>
                        <div className="font-semibold">01/02/2025</div>
                      </div>
                      <div className="flex flex-wrap items-center justify-between gap-1 text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('approvedOn')}</div>
                        <div className="font-semibold">-</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Billing Profile Card */}
                <div className="relative bg-white  p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] cardShadow text-[12px] 3xl:text-[0.729vw] ">
                  <div className="col-span-8 flex items-center gap-2 mb-[10px] lg:mb-[20px] xl:mb-[24px] 2xl:mb-[24px] 3xl:mb-[1.25vw]">
                    <Image src="/images/receipt_icon.svg" width={24} height={24} alt="logo" />
                    <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                      {t('billingProfile')}
                    </div>
                  </div>

                  <BillingProfile />
                </div>

                {/* Billing Address Card */}
                <div className="bg-background rounded-lg p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] cardShadow text-InterfaceTexttitle text-[12px] 3xl:text-[0.729vw]">
                  <div className=" flex items-center gap-2">
                    <Image src="/images/routing_icon.svg" width={24} height={24} alt="logo" />
                    <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                      {t('billingAddress')}
                    </div>
                  </div>

                  <>
                    <BillingAddress />
                  </>
                </div>
              </div>
            </div>

            <div className="p-4 bg-[#f5f6f9]">
              <div className="flex justify-between items-center mb-[14px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
                <h2 className="text-[16px] 3xl:text-[0.833vw] font-[500]">
                  {t('companyProfile')} 3
                </h2>
                <div>
                  <div>
                    <UserAccessPopup />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-5">
                {/* Company Info Card */}
                <div className="bg-background  p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] shadow text-[12px] 3xl:text-[0.729vw]">
                  <div className="grid grid-cols-12 gap-2 mb-4">
                    <div className="col-span-8 flex items-center gap-2">
                      <Image src="/images/briefcase_icon.svg" width={24} height={24} alt="logo" />
                      <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                        {t('companyProfile')}
                      </div>
                    </div>
                    <div className="col-span-4 text-right text-InterfaceTextsubtitle">
                      <HoverCard>
                        <HoverCardTrigger asChild>
                          <div className="flex justify-end items-center h-full">
                            <i className="cloud-info text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          </div>
                        </HoverCardTrigger>
                        <HoverCardContent className="w-410 bg-background">
                          <div className="flex flex-col items-start gap-4 w-[410px] p-4">
                            <div className="text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.933vw] font-[500] text-InterfaceTexttitle">
                              {t('details')}
                            </div>

                            <div className="flex items-center gap-4 w-full">
                              <div className="flex flex-col items-start gap-[2px] w-1/2">
                                <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                                  {t('admin')}
                                </div>
                                <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                                  {t('email')}
                                </div>
                              </div>

                              <div className="flex flex-col items-start gap-[2px] w-1/2">
                                <div className="text-sm text-InterfaceTextdefault-400">
                                  : Joes Boz
                                </div>
                                <div className="text-sm text-InterfaceTextdefault-400">
                                  : <EMAIL>
                                </div>
                              </div>
                            </div>
                          </div>
                        </HoverCardContent>
                      </HoverCard>
                    </div>
                  </div>

                  <div className="grid grid-cols-5 gap-4 mb-4">
                    <div className="col-span-2">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('companyName')}
                      </div>
                      <div className="font-[500]">Hexalytics</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('country')}
                      </div>
                      <div className="font-[500]">UAE</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('store')}
                      </div>
                      <div className="font-[500]">Dubai</div>
                    </div>
                    <div className="col-span-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('role')}
                      </div>
                      <div className="font-[500]">Admin</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-12 gap-4 mb-4">
                    <div className="col-span-9">
                      <div className="flex text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('address')}
                      </div>
                      <div className="font-[500]">Villa 6 Zaglabi Street 99 Al Barsha Turkey</div>
                    </div>

                    <div className="col-span-3">
                      <div className="flex flex-col  justify-end">
                        <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                          {t('status')}
                        </div>
                        <span
                          className="bg-BrandSupport2100 border border-BrandSupport2300 text-BrandSupport2800 font-[500] text-center px-2 py-1 text-xs leading-[140%] 
                      text-[14px] xl:text-[10px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                        >
                          Pending
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 ">
                    <div className="bg-BrandNeutral50 border border-InterfaceStrokedefault p-[10px] 3xl:p-[0.521vw]">
                      <div className="text-InterfaceTextdefault font-semibold flex items-center justify-between mb-1 3xl:mb-2">
                        {t('guestAccount')}
                        <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                      </div>
                      <div className="flex flex-wrap items-center justify-between text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('createdOn')}</div>
                        <div className="font-semibold">01/02/2025</div>
                      </div>
                    </div>

                    <div className="bg-BrandNeutral50 border border-InterfaceStrokedefault p-[10px] 3xl:p-[0.521vw]">
                      <div className="text-InterfaceTextdefault font-semibold flex justify-between items-center mb-1 3xl:mb-2">
                        {t('onboarding')}
                        <Link href={`/account/companies/view/${'123'}`}>
                          <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                        </Link>
                      </div>
                      <div className="flex flex-wrap items-center justify-between gap-1 mb-1 text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('submittedOn')}</div>
                        <div className="font-semibold">01/02/2025</div>
                      </div>
                      <div className="flex flex-wrap items-center justify-between gap-1 text-InterfaceTextdefault">
                        <div className="font-[400] ">{t('approvedOn')}</div>
                        <div className="font-semibold">-</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Billing Profile Card */}
                <div className="relative bg-white  p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw] cardShadow text-[12px] 3xl:text-[0.729vw] ">
                  <div className="col-span-8 flex items-center gap-2 mb-[10px] lg:mb-[20px] xl:mb-[24px] 2xl:mb-[24px] 3xl:mb-[1.25vw]">
                    <Image src="/images/receipt_icon.svg" width={24} height={24} alt="logo" />
                    <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                      {t('billingProfile')}
                    </div>
                  </div>

                  <BillingProfile />
                </div>

                {/* Billing Address Card */}
                <div className="bg-background  p-[10px] lg:p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[0.938vw]cardShadow text-InterfaceTexttitle text-[12px] 3xl:text-[0.729vw]">
                  <div className=" flex items-center gap-2">
                    <Image src="/images/routing_icon.svg" width={24} height={24} alt="logo" />
                    <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                      {t('billingAddress')}
                    </div>
                  </div>

                  <>
                    <BillingAddress />
                  </>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <CompanyProfiles open={openCompanyProfile} onClose={() => setOpenCompanyProfile(false)} /> */}
      <BillingProfilePopup open={openBillingProfile} onClose={() => setOpenBillingProfile(false)} />
    </>
  );
};

export default AllCompanyInformation;
