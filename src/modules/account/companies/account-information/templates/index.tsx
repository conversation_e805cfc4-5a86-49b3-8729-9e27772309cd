'use client';
import React from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON><PERSON> } from 'next/font/google';
import { useRouter } from 'next/navigation';
import AccountDetails from '../components/details';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

// Simple validation context

// const steps = [
//   { id: 0, title: 'Company Information', component: CompanyInformation },
//   { id: 1, title: 'Business Information', component: BusinessInformation },
//   { id: 3, title: 'Brand Information', component: BrandInformation },
// ];

export default function AccountInformation() {
  const router = useRouter();
  const t = useTranslations();
  return (
    <div className={`${roboto.className} w-full  w-full h-full`}>
      <div className="relative h-full p-[40px]">
        <div className="grid grid-cols-12 gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw] px-[60px] xl:px-[80px] 3xl:px-[6.25vw] h-full">
          <div className="col-span-12 xl:col-span-3 3xl:col-span-3 w-full h-full">
            <div className="3xl:space-y-[1.25vw] space-y-[20px] p-5 3xl:p-[1.25vw] border border-InterfaceStrokesoft bg-interfacesurfacecomponent ">
              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] cursor-pointer rounded-none border border-Interface-Stroke-soft bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] flex items-center"
                variant="outline"
                size="sm"
                onClick={() => router.back()}
              >
                {' '}
                <i className="cloud-back text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('back')}
              </Button>
              <div className="py-2">
                <div className=" text-InterfaceTexttitle text-[24px] xl:text-[24px] 2xl:text-[26px] 3xl:text-[1.042vw] font-[400]">
                  {t('guestAccountInformation')}
                </div>
                <div className="py-4 text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                  {t(
                    'ReviewyourguestregistrationdetailsbelowanddownloadthePDFofyourregistrationdata'
                  )}
                </div>

                <Button
                  type="button"
                  className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-Interface-Stroke-soft bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                  variant="outline"
                  size="sm"
                  // onClick={() => router.back()}
                >
                  {' '}
                  <i className="cloud-document-download1 text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                  {t('downloadAsPdf')}
                </Button>
              </div>
            </div>
          </div>
          <div className="col-span-12 xl:col-span-9 3xl:col-span-9 h-full">
            <div className="flex flex-col gap-2 p-[20px] xl:p-[30px] 3xl:p-[1.667vw] border border-InterfaceStrokesoft bg-white h-screen">
              <AccountDetails />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
