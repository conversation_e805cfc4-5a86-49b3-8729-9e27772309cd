'use client';
import * as React from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

const userDetails = {
  firstName: '<PERSON>',
  lastName: '<PERSON>',
  Designation: 'CEO',
  mobileNumber: '99999 99999',
  email: '<EMAIL>',
  companyName: 'Test Company Name',
  countryOfBusiness: 'India (India)',
  redingtonReference: 'Yes',
  redingtonReferenceName: '<PERSON>',
};

export default function AccountDetails() {
  const t = useTranslations();
  return (
    <>
      <div>
        <div className="relative bg-interfacesurfacecomponent">
          <div className="flex justify-between items-center flex-wrap border-b border-InterfaceStrokesoft">
            <div className="text-InterfaceTexttitle pb-2 text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
              {t('accountInformation')}
            </div>
          </div>

          <div className="space-y-[20px] xl:space-y-[16px] 3xl:space-y-[0.825vw]">
            <div className="py-6">
              <Image
                width={50}
                className="w-28"
                alt="profile-image"
                height={50}
                src={'/images/profile-circle.png'}
              />
            </div>
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                <h4 className="text-gray-400">{t('firstName')}</h4>
                <p className="text-gray-800">{userDetails.firstName}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                <h4 className="text-gray-400">{t('lastName')}</h4>
                <p className="text-gray-800">{userDetails.lastName}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                <h4 className="text-gray-400">{t('designation')}</h4>
                <p className="text-gray-800">{userDetails.Designation}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                <h4 className="text-gray-400">{t('mobileNumber')}</h4>
                <p className="text-gray-800">{userDetails.mobileNumber}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b  pb-3 border-b-gray-300">
                <h4 className="text-gray-400">{t('email')}</h4>
                <p className="text-gray-800">{userDetails.email}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                <h4 className="text-gray-400">{t('companyName')}</h4>
                <p className="text-gray-800">{userDetails.companyName}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                <h4 className="text-gray-400">{t('countryOfBusiness')}</h4>
                <p className="text-gray-800">{userDetails.countryOfBusiness}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b  pb-3 border-b-gray-300">
                <h4 className="text-gray-400">Redington Reference</h4>
                <p className="text-gray-800">{userDetails.redingtonReference}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                <h4 className="text-gray-400">Redington Reference Name</h4>
                <p className="text-gray-800">{userDetails.redingtonReferenceName}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
