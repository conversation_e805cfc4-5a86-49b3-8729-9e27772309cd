'use client';
import {
  But<PERSON>,
  Input,
  Label,
  Sheet,
  Sheet<PERSON>lose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTit<PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Roboto } from 'next/font/google';
import { OnboardNewCompanyProps } from '@/types/components';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

// 1. Define Zod Schema
const onboardSchema = z.object({
  accountID: z
    .string()
    .min(1, 'Account ID is required')
    .refine((val) => /^\d{12}$/.test(val), {
      message: 'Account ID must be exactly 12 digits',
    }),
});

type OnboardFormData = z.infer<typeof onboardSchema>;
const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
export default function OnboardNewCompanyPopup({ open, onClose }: OnboardNewCompanyProps) {
  const t = useTranslations();
  const [showMoreInfo, setShowMoreInfo] = useState(false);
  const [animateClose, setAnimateClose] = useState(false);
  const [showFieldError, setShowFieldError] = useState(false);

  // 2. Set up form hook
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    trigger,
  } = useForm<OnboardFormData>({
    resolver: zodResolver(onboardSchema),
    mode: 'onChange',
    reValidateMode: 'onSubmit',
  });
  const accountIDValue = watch('accountID');

  useEffect(() => {
    if (accountIDValue === undefined) return;

    const timeout = setTimeout(async () => {
      await trigger('accountID'); // 💡 await ensures sync with errors
      setShowFieldError(true);
    }, 500);

    return () => clearTimeout(timeout);
  }, [accountIDValue, trigger]);

  useEffect(() => {
    if (!open) {
      setShowMoreInfo(false);
      setAnimateClose(false);
      reset(); // Optional: reset form too
    }
  }, [open, reset]);
  const handleCloseSheet = () => {
    setShowMoreInfo(false);
    setAnimateClose(false);
    reset(); // <-- reset form & validation state
    onClose(null); // call parent-provided close handler with null
  };

  // 3. Submit Handler
  const onSubmit = () => {
    // console.log('Form Data:', data);
    // Implement your API or state update logic here
  };
  const handleOpen = () => {
    setAnimateClose(false);
    setShowMoreInfo(true);
  };

  const handleClose = () => {
    setAnimateClose(true);
    // Wait for animation to complete before removing the box
    setTimeout(() => setShowMoreInfo(false), 300); // match duration of animation
  };

  return (
    <Sheet open={open} onOpenChange={handleCloseSheet}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]`}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col h-full">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('cloudquarksOnboarding')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-full">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div>
                <p className="text-InterfaceTextsubtitle text-blackcolor">
                  {t(
                    'ifYouAreRegisteredPartnerWithRedingtonPleaseEnterYourRedingtonAccountNumberAndSelectTheCountry'
                  )}
                </p>
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="email"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('redingtonAccountId')} *
                </Label>
                <Input
                  id="accountID"
                  type="text"
                  placeholder={t('enterAccountId')}
                  className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                    showFieldError && errors.accountID ? 'border-red-500' : ''
                  }`}
                  {...register('accountID')}
                />
                {showFieldError && (
                  <>
                    {errors.accountID && (
                      <p className="text-red-500 text-xs mt-1">{errors.accountID.message}</p>
                    )}
                  </>
                )}
              </div>
              <div className="relative">
                <p
                  className="text-InterfaceTextsubtitle text-[#1570EF] cursor-pointer m-[0px] p-[0px]"
                  onClick={() => handleOpen()}
                >
                  {t('moreInfo')}
                </p>

                {showMoreInfo && (
                  <div
                    className={`absolute z-50 mt-2 left-0 bg-white rounded-md shadow-md border border-gray-200 p-4 w-[420px] 
                                    ${animateClose ? 'animate-slide-out-right' : 'animate-slide-in-left'}`}
                  >
                    <div className="flex justify-between items-start">
                      <h3 className="text-[16px] font-semibold text-[#19212A]">{t('moreInfo')}</h3>
                      <i
                        className="cloud-closecircle text-closecolor text-[18px] cursor-pointer"
                        onClick={handleClose}
                      ></i>
                    </div>
                    <ul className="mt-2 list-disc pl-4 text-sm text-[#3C4146] leading-[1.6]">
                      <li>
                        {t(
                          'redingtonAccountNumberisauniqueidentifierprovidedbyRedingtonforyourcompany'
                        )}
                      </li>
                      <li>
                        {/* Redington Account Number is a 10 digit numeric value
                        <strong> (example **********)</strong> which is tagged with segment{' '}
                        <strong>“Cloud Services”</strong> */}
                        {t(
                          'RedingtonAccountNumberisa10digitnumericvalue(example**********)whichistaggedwithsegmentCloudServices'
                        )}
                      </li>
                      <li>
                        {t(
                          'PleasereachouttoyourRedingtonAccountManagerwhowillassistyouwithyourRedingtonAccountNumber'
                        )}
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <Button
                  type="button"
                  onClick={handleCloseSheet}
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[0.933vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </Button>
                <Button
                  type="submit"
                  className="flex applybtn items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  size="md"
                >
                  <div>
                    <i className="cloud-save-2 text-interfacetextinverse flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[0.933vw]">
                    {t('save')}
                  </div>
                </Button>
              </div>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
