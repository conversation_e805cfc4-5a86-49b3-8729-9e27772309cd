'use client';
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>lose,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON><PERSON> } from 'next/font/google';
import { <PERSON><PERSON> } from '@redington-gulf-fze/cloudquarks-component-library';
import { DataTable } from '@/components/common/datatable';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
// import {
//   HoverCard,
//   HoverCardContent,
//   HoverCardTrigger,
// } from '@redington-gulf-fze/cloudquarks-component-library';
// import Image from 'next/image';
// import Link from 'next/link';
import { CommanSheetProps } from '@/types';
import { TablePagination } from '@/components/common/pagination';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function BillingProfilePopup({ open, onClose }: CommanSheetProps) {
  const t = useTranslations();
  const Purchasesdata = [
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-11096982',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
    {
      redingtonacno: '1010011802',
      companycode: 'CD-1109698',
      status: 'In Active',
    },
  ];

  const PurchasesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'redingtonacno',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('redingtonACNo')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div>{row.getValue('redingtonacno')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'companycode',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('companyCode')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('companycode')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Active: {
            colorClass: 'bg-BrandHighlight100 text-BrandHighlight800 border border-[#ACD69F]',
          },
          'In Active': {
            colorClass:
              'bg-BrandNeutral50 text-InterfaceTextdefault border border-interface-stroke-default-new',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[4px] xl:py-[2px] 3xl:py-[0.208vw] px-[8px] 3xl:px-[0.417vw] rounded-[2px] text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-semibold leading-[140%] ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },
      minSize: 800,
    },
  ];

  type User = {
    redingtonacno: string;
    companycode: string;
    status: string;
  };

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[24px] lg:p-[20px] xl:p-[16px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[20px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('companyInformation')}
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="flex-grow overflow-y-auto space-y-3 px-6 xl:px-4 3xl:px-[1.25vw]">
          <div className="">
            <div className="overflow-x-auto cardShadow">
              <div className="py-3 3xl:py-[0.625vw] px-4 3xl:px-[0.833vw] text-[18px] xl:text-[16px] 3xl:text-[0.938vw] text-InterfaceTexttitle font-semibold">
                {t('billingProfile')}
              </div>
              <DataTable data={Purchasesdata} columns={PurchasesColumns} withCheckbox={false} />
              {/* <div className="flex items-center justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] w-full border-b border-r border-l  border-t-none rounded-bl-[4px] rounded-br-[4px]">
                <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                  1-10{' '}
                  <span className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    Of{' '}
                  </span>
                  <span className=" font-[600] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    100
                  </span>
                </div>
                <Link
                  href=""
                  className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-BrandSupport1pure"
                >
                  Show more...
                </Link>
              </div> */}
              <TablePagination />
            </div>
          </div>
        </div>

        <SheetFooter className="p-6 xl:p-4 3xl:p[1.25vw] border-t border-InterfaceStrokedefault">
          <Button
            type="button"
            className="py-[8px] xl:py-[10px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
            variant="outline"
            size="sm"
            onClick={() => {
              onClose();
            }}
          >
            <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
            {t('close')}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
