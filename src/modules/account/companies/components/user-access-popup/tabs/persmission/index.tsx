import * as React from 'react';
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getExpandedRowModel,
  flexRender,
} from '@tanstack/react-table';
import { useTranslations } from 'next-intl';
import {
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';

export type Task = {
  id: string;
  title: string;
  read: string;
  create: string;
  edit: string;
  delete: string;
  download: string;
  subRows?: Task[];
};

// We need to flatten the data for rendering

export function PermissionTable() {
  const t = useTranslations();

  const tasks: Task[] = [
    {
      id: 'TASK-8782',
      title: t('homePage'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-5582',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-5583',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8783',
      title: t('accountManagement'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8790',
      title: t('brandManagement'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8791',
      title: t('orderManagement'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8791',
      title: t('catalog'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8792',
      title: t('settings'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8793',
      title: t('helpCenter'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8794',
      title: t('notifications'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8795',
      title: t('activityLog'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8796',
      title: t('analytics'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
    {
      id: 'TASK-8798',
      title: t('quickOrder'),
      read: '',
      create: '',
      edit: '',
      delete: '',
      download: '',
      subRows: [
        {
          id: 'TASK-001',
          title: 'Section 1',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
        {
          id: 'TASK-002',
          title: 'Section 2',
          read: '',
          create: '',
          edit: '',
          delete: '',
          download: '',
        },
      ],
    },
  ];
  const [data] = React.useState(() => [...tasks]);
  const [expanded, setExpanded] = React.useState({});

  const columns: ColumnDef<Task>[] = [
    {
      accessorKey: 'title',
      header: t('items'),
      cell: ({ row, getValue }) => (
        <div
          style={{ paddingLeft: `${row.depth * 3}rem` }}
          className="flex items-center gap-3 text-InterfaceTextdefault"
        >
          {row.getCanExpand() && (
            <Button variant="ghost" size="sm" onClick={row.getToggleExpandedHandler()} className="">
              {row.getIsExpanded() ? (
                <span className="font-[600] text-[22px] ">-</span>
              ) : (
                <i className="cloud-Add text-[10px] text-InterfaceTextdefault"></i>
              )}
            </Button>
          )}
          <span>{getValue<string>()}</span>
        </div>
      ),
    },

    {
      accessorKey: 'read',
      header: t('read'),
      cell: ({}) => (
        <div className="flex items-center justify-center text-InterfaceTextdefault">
          <div>
            <Checkbox id="recentpurchases" />
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'create',
      header: t('create'),
      cell: ({}) => (
        <div className="flex items-center justify-center text-InterfaceTextdefault">
          {' '}
          <Checkbox id="recentpurchases" />
        </div>
      ),
    },
    {
      accessorKey: 'edit',
      header: t('edit'),
      cell: ({}) => (
        <div className="flex items-center justify-center text-InterfaceTextdefault">
          {' '}
          <Checkbox id="recentpurchases" />
        </div>
      ),
    },

    {
      accessorKey: 'delete',
      header: t('delete'),
      cell: ({}) => (
        <div className="flex items-center justify-center text-InterfaceTextdefault">
          {' '}
          <Checkbox id="recentpurchases" />
        </div>
      ),
    },

    {
      accessorKey: 'download',
      header: t('downalod'),
      cell: ({}) => (
        <div className="flex items-center justify-center text-InterfaceTextdefault">
          {' '}
          <Checkbox id="recentpurchases" />
        </div>
      ),
    },
  ];

  const table = useReactTable({
    data,
    columns,
    state: {
      expanded,
    },
    onExpandedChange: setExpanded,
    getSubRows: (row) => row.subRows,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  return (
    <div className="h-[500px] xl:h-[500px] 3xl:h-[32.667vw] overflow-auto">
      <div className="border">
        <Table>
          <TableHeader className="bg-InterfaceStrokesoft text-InterfaceTexttitle">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="text-center">
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="text-InterfaceTextdefault font-[400]">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className="hover:bg-InterfaceStrokesoft"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-center">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
