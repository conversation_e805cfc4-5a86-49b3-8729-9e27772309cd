'use client';
import React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

type ProfileItem = {
  acNo: string;
  name: string;
  status: string;
  statusClass: string;
};

const ProfileData: ProfileItem[] = [
  {
    acNo: '1010011802',
    name: 'Hexalytics Ltd',
    status: 'Active',
    statusClass: 'bg-BrandHighlight100 border border-[#ACD69F] text-BrandHighlight800',
  },
  {
    acNo: '1010011803',
    name: 'Hexalytics Pvt Ltd',
    status: 'In Active',
    statusClass:
      'bg-BrandNeutral50 border border-interfacestrokedefaultnew text-InterfaceTextdefault px-2 3xl:px-[0.417vw] py-[2px] 3xl:py-[0.104vw]',
  },
  {
    acNo: '1010011804',
    name: 'Hexalytics Solutions',
    status: 'Active',
    statusClass:
      'bg-BrandHighlight100 border border-[#ACD69F] text-BrandHighlight800 px-2 3xl:px-[0.417vw] py-[2px] 3xl:py-[0.104vw]',
  },
];

const chunkArray = (arr: ProfileItem[], size: number): ProfileItem[][] => {
  const result: ProfileItem[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

const BillingProfile: React.FC = () => {
  const chunks = chunkArray(ProfileData, 2); // 2 items per slide
  const t = useTranslations();
  return (
    <div className="relative">
      <Carousel className="w-full max-w-3xl relative">
        <CarouselContent>
          {chunks.map((group, index) => (
            <CarouselItem key={index} className="basis-full space-y-[18px]">
              <div className="space-y-4">
                {group.map((item, i) => (
                  <div key={i} className={`grid grid-cols-9 gap-4 3xl:gap-[0.833vw] pb-[10px]`}>
                    <div className="col-span-3 space-y-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('redingtonACNo')}
                      </div>
                      <div className="font-[500]">{item.acNo}</div>
                    </div>

                    <div className="col-span-3 space-y-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                        {t('accountName')}
                      </div>
                      <div className="font-[500]">{item.name}</div>
                    </div>

                    <div className="col-span-3 space-y-1">
                      <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                        {t('status')}
                      </div>
                      <div
                        className={`${item.statusClass} font-[500] text-center px-2 3xl:px-[0.417vw] py-[2px] 3xl:py-[0.104vw] text-[14px] xl:text-[11px] 3xl:text-[0.729vw] w-fit`}
                      >
                        {item.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="">
          <div className="flex justify-between items-center mt-2 absolute bottom-[-40px] lg:bottom-[-20px] xl:bottom-[-20px] 3xl:bottom-[-3.142vw] 1440xl:bottom-[-55px] w-full border-t border-InterfaceStrokesoft pb-2 profile_silder ">
            <CarouselPrevious className=" rounded-none border-none  top-5 left-[0px] xl:right-[0px] 3xl:right-[1.042vw]  prevIcon" />
            <CarouselNext className=" rounded-none border-none  top-5 right-[15px] xl:right-[0px] 3xl:right-[1.042vw] nextIcon" />
          </div>
        </div>
      </Carousel>
    </div>
  );
};

export default BillingProfile;
