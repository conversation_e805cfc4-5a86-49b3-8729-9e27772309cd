'use client';
import React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

type AddressItem = {
  isDefault: boolean;
  address: string;
  contact: string;
};

const addressList: AddressItem[] = [
  {
    isDefault: true,
    address:
      'MDX Technology Solutions ME LLC 1501, Al Masood Tower, Hamdan Street Abu Dhabi, 12121 United Arab Emirates',
    contact: '+971 11 55 1142',
  },
  {
    isDefault: false,
    address:
      'Hexalytics Ltd, Level 2, Techno Park, Dubai Silicon Oasis, Dubai, United Arab Emirates',
    contact: '+971 58 123 4567',
  },
];

const BillingAddress: React.FC = () => {
  const t = useTranslations();
  return (
    <div className="relative ">
      <div className="w-full max-w-2xl mx-auto">
        <Carousel>
          <CarouselContent>
            {addressList.map((item, index) => (
              <CarouselItem key={index} className="">
                <div className="space-y-4 3xl:space-y-[0.729vw] py-4">
                  {item.isDefault && (
                    <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded inline-block">
                      {t('default')}
                    </span>
                  )}

                  <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                    <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                      {t('address')}
                    </div>
                    <div className="font-[500] text-InterfaceTexttitle">{item.address}</div>
                  </div>

                  <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                    <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                      {t('contact')}
                    </div>
                    <div className="font-[500] text-InterfaceTexttitle">{item.contact}</div>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>

          <div className="flex justify-between items-center mt-2 absolute bottom-[-40px] lg:bottom-[-20px] xl:bottom-[-20px] 3xl:bottom-[-1.042vw] w-full border-t border-InterfaceStrokesoft pb-2 profile_silder ">
            <CarouselPrevious className=" rounded-none border-none  top-5 left-[0px] xl:right-[0px] 3xl:right-[1.042vw]  prevIcon" />
            <CarouselNext className=" rounded-none border-none  top-5 right-[15px] xl:right-[0px] 3xl:right-[1.042vw] nextIcon" />
          </div>
        </Carousel>
      </div>
    </div>
  );
};

export default BillingAddress;
