'use client';
import React, { useState } from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON><PERSON> } from 'next/font/google';
import { useRouter } from 'next/navigation';
import CompanyDetails from '../components/company-information';
import BusinessDetails from '../components/business-information';
import BrandDetails from '../components/brand-information';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

// Simple validation context

const steps = [
  { id: 0, title: 'Company Information', component: CompanyDetails },
  { id: 1, title: 'Business Information', component: BusinessDetails },
  { id: 3, title: 'Brand Information', component: BrandDetails },
];

export default function CompanyDetials() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);

  const tabs = [
    { id: 0, title: 'Company Information', number: 1 },
    { id: 1, title: 'Business Information', number: 2 },
    { id: 2, title: 'Brand/Brand Category', number: 3 },
  ];

  const CurrentStepComponent = steps[currentStep]?.component || CompanyDetails;

  return (
    <div className={`${roboto.className} p-[40px]`}>
      <div className="relative">
        <div className="">
          <div className="grid grid-cols-12 gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw] px-[60px] xl:px-[80px] 3xl:px-[6.25vw]">
            <div className="col-span-12 xl:col-span-3 3xl:col-span-3 ">
              <div className="3xl:space-y-[1.25vw] space-y-[20px] p-5 3xl:p-[1.25vw] border border-InterfaceStrokesoft bg-interfacesurfacecomponent ">
                <div className="">
                  <Button
                    type="button"
                    className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-Interface-Stroke-soft bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] flex items-center"
                    variant="outline"
                    size="sm"
                    onClick={() => router.back()}
                  >
                    {' '}
                    <i className="cloud-back text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                    Back
                  </Button>
                  <div className="py-6 text-InterfaceTexttitle text-[24px] xl:text-[24px] 2xl:text-[26px] 3xl:text-[1.042vw] font-[400]">
                    {`Onboarding Account Information`}.
                  </div>
                  <div className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                    Review your Onboarding registration details below and download the PDF of your
                    registration data.
                  </div>
                </div>
                <div className="space-y-[8px] xl:space-y-[8px] 3xl:space-y-[0.417vw] ">
                  {tabs.map((tab) => (
                    <div
                      key={tab.id}
                      className={`${
                        currentStep === tab.id
                          ? 'bg-[#42536D] text-[#FFFFFF]'
                          : 'bg-InterfaceSurfacecomponentmuted'
                      }  rounded-none p-[16px] xl:p-[16px] 2xl:p-[0.833vw] 3xl:p-[0.833vw]  text-[#3B4854] cursor-pointer ${
                        tab.id <= currentStep ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'
                      }`}
                      onClick={() => setCurrentStep(tab.id)}
                    >
                      <div className="flex items-center gap-3">
                        <div>
                          <div className="text-sm 3xl:text-[0.833vw] font-[600] leading-[22px] 3xl:leading-[1.146vw]">
                            {tab.title}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <Button
                  type="button"
                  className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-Interface-Stroke-soft bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                  variant="outline"
                  size="sm"
                  // onClick={() => router.back()}
                >
                  {' '}
                  <i className="cloud-document-download1 text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                  Download as PDF
                </Button>
              </div>
            </div>
            <div className="col-span-12 xl:col-span-9 3xl:col-span-9">
              <div className="flex flex-col gap-2 px-8 py-6 border border-InterfaceStrokesoft bg-white">
                <CurrentStepComponent />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
