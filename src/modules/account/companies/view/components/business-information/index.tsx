'use client';
import * as React from 'react';

const businessDetails = {
  userInformation: {
    firstName: 'Jese',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    mobileNumber: '+91 98765 43210',
  },
  director: {
    firstName: 'Je<PERSON>',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    mobileNumber: '+91 98765 43210',
  },
  accountsPerson: {
    firstName: 'Jese',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    mobileNumber: '+91 98765 43210',
  },
  sales: {
    firstName: 'Jese',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    mobileNumber: '+91 98765 43210',
  },
  authorizedSignatory: {
    firstName: 'Jese',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    mobileNumber: '+91 98765 43210',
  },
};

export default function BusinessDetails() {
  return (
    <>
      <div className="relative bg-interfacesurfacecomponent">
        <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
            Business Information
          </div>
        </div>

        <div className="py-3 text-gray-400 text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500]">
          Key Contacts
        </div>

        <div>
          {Object.entries(businessDetails).map(([key, value]) => (
            <div key={key} className="py-2">
              {key == 'userInformation' && (
                <h3 className="text-base font-semibold py-2">User Information</h3>
              )}
              {key == 'director' && (
                <h3 className="text-base font-semibold py-2">Director/Owner</h3>
              )}
              {key == 'accountsPerson' && (
                <h3 className="text-base font-semibold py-2">
                  Accounts Person & Operations Executive
                </h3>
              )}
              {key == 'sales' && <h3 className="text-base  font-semibold py-2">Sales</h3>}
              {key == 'authorizedSignatory' && (
                <h3 className="text-base font-semibold py-2">Authorized Signatory</h3>
              )}

              <div className="grid grid-cols-12 gap-6 py-3">
                <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                  <h4 className="text-gray-400">First Name</h4>
                  <p className="text-gray-600">{value['firstName']}</p>
                </div>
                <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                  <h4 className="text-gray-400">Last Name</h4>
                  <p className="text-gray-600">{value['lastName']}</p>
                </div>
                <div className="col-span-12 lg:col-span-4 border-b  pb-3 border-b-gray-300">
                  <h4 className="text-gray-400">Email ID</h4>
                  <p className="text-gray-600">{value['email']}</p>
                </div>
                <div className="col-span-12 lg:col-span-4 border-b pb-3 border-b-gray-300">
                  <h4 className="text-gray-400">Mobile Number</h4>
                  <p className="text-gray-600">{value['mobileNumber']}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}
