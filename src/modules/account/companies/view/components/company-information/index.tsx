'use client';
import * as React from 'react';
import { ScrollArea } from '@redington-gulf-fze/cloudquarks-component-library';

const companyDetails = {
  redingtonId: '110011234221',
  country: 'India',
  gst: '123232345678',
  companyName: 'Hexalytics LLC',
  address1: '201, Business bay',
  address2: 'Street Adrress 2',
  countryOfBusiness: 'India',
  state: 'Tamil Nadu',
  city: 'Chennai',
  postalCode: '600001',
  website: 'http://hexalytics.com',
};

export default function CompanyDetails() {
  return (
    <>
      <div className="relative bg-interfacesurfacecomponent">
        {/* Company Information */}
        <div className="relative">
          <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
            <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
              Company Information
            </div>
          </div>
          <ScrollArea>
            <div className="py-3 text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
              Details presented from Redington ERP system, please validate to proceed further, if
              you need any assistance, please create a ticket in the platform under Service Desk.
            </div>
            <div className="grid grid-cols-12 gap-6 py-3">
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Redington Account ID</h4>
                <p className="text-gray-600 text-sm">{companyDetails.redingtonId}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Country</h4>
                <p className="text-gray-600 text-sm">{companyDetails.country}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Trade Licence/ GST</h4>
                <p className="text-gray-600 text-sm">{companyDetails.gst}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">
                  Company Name (As per the Trade Licence/ GST)
                </h4>
                <p className="text-gray-600 text-sm">{companyDetails.companyName}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b  pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Street Address 1</h4>
                <p className="text-gray-600 text-sm">{companyDetails.address1}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Street Address 2</h4>
                <p className="text-gray-600 text-sm">{companyDetails.address2}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">
                  Country of Business (As per the Trade Licence/ GST)
                </h4>
                <p className="text-gray-600 text-sm">{companyDetails.countryOfBusiness}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b  pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Province/State/Emirates</h4>
                <p className="text-gray-600 text-sm">{companyDetails.state}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">City</h4>
                <p className="text-gray-600 text-sm">{companyDetails.city}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Zip/Postal Code</h4>
                <p className="text-gray-600 text-sm">{companyDetails.postalCode}</p>
              </div>
              <div className="col-span-12 lg:col-span-4 border-b pb-2 border-b-gray-300">
                <h4 className="text-gray-400 text-sm">Company Website</h4>
                <p className="text-gray-600 text-sm">{companyDetails.website}</p>
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>
    </>
  );
}
