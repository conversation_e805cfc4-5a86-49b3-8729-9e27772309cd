'use client';
import * as React from 'react';

import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';

import { ColumnDef } from '@tanstack/react-table';
import { ScrollArea } from '@redington-gulf-fze/cloudquarks-component-library';
import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/pagination';

type BrandData = {
  vendor: { label: string; value: string };
  brand: { label: string; value: string };
  brandCategory: { label: string; value: string };
};

export default function BrandDetails() {
  const displayTableData: BrandData[] = [
    {
      vendor: { label: 'Vendor 1', value: 'vendor1' },
      brand: { label: 'Brand 1', value: 'brand1' },
      brandCategory: { label: 'Brand Category 1', value: 'brandCategory1' },
    },
    {
      vendor: { label: 'Vendor 2', value: 'vendor2' },
      brand: { label: 'Brand 2', value: 'brand2' },
      brandCategory: { label: 'Brand Category 2', value: 'brandCategory2' },
    },
    {
      vendor: { label: 'Vendor 3', value: 'vendor3' },
      brand: { label: 'Brand 3', value: 'brand3' },
      brandCategory: { label: 'Brand Category 3', value: 'brandCategory3' },
    },
    {
      vendor: { label: 'Vendor 4', value: 'vendor4' },
      brand: { label: 'Brand 4', value: 'brand4' },
      brandCategory: { label: 'Brand Category 4', value: 'brandCategory4' },
    },
    {
      vendor: { label: 'Vendor 5', value: 'vendor5' },
      brand: { label: 'Brand 5', value: 'brand5' },
      brandCategory: { label: 'Brand Category 5', value: 'brandCategory5' },
    },
    {
      vendor: { label: 'Vendor 6', value: 'vendor6' },
      brand: { label: 'Brand 6', value: 'brand6' },
      brandCategory: { label: 'Brand Category 6', value: 'brandCategory6' },
    },
    {
      vendor: { label: 'Vendor 7', value: 'vendor7' },
      brand: { label: 'Brand 7', value: 'brand7' },
      brandCategory: { label: 'Brand Category 7', value: 'brandCategory7' },
    },
    {
      vendor: { label: 'Vendor 8', value: 'vendor8' },
      brand: { label: 'Brand 8', value: 'brand8' },
      brandCategory: { label: 'Brand Category 8', value: 'brandCategory8' },
    },
    {
      vendor: { label: 'Vendor 9', value: 'vendor9' },
      brand: { label: 'Brand 9', value: 'brand9' },
      brandCategory: { label: 'Brand Category 9', value: 'brandCategory9' },
    },
    {
      vendor: { label: 'Vendor 10', value: 'vendor10' },
      brand: { label: 'Brand 10', value: 'brand10' },
      brandCategory: { label: 'Brand Category 10', value: 'brandCategory10' },
    },
  ];
  const brandColumns: ColumnDef<BrandData>[] = [
    {
      accessorKey: 'vendor',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      cell: ({ row }) => row.original.vendor.label,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();

        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Vendor
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <Input
              placeholder="Vendor"
              className="my-2 w-full h-9 rounded-[2px] border border-InterfaceStrokedefault   text-[14px] xl:text-[14px] 3xl:text-[0.729vw] placeholder:font-normal"
              onChange={(e) => column.setFilterValue(e.target.value)}
            /> */}
          </div>
        );
      },
    },
    {
      accessorKey: 'brand',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      cell: ({ row }) => row.original.brand.label,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();

        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Brands
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <Input
              placeholder="Vendor"
              className="my-2 w-full h-9 rounded-[2px] border border-InterfaceStrokedefault   text-[14px] xl:text-[14px] 3xl:text-[0.729vw] placeholder:font-normal"
              onChange={(e) => column.setFilterValue(e.target.value)}
            /> */}
          </div>
        );
      },
    },

    {
      accessorKey: 'brandCategory',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      cell: ({ row }) => row.original.brandCategory.label,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();

        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Brand Category
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <Input
              placeholder="  Brand Category"
              className="my-2 w-full h-9 rounded-[2px] border border-InterfaceStrokedefault  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]  placeholder:font-normal"
              onChange={(e) => column.setFilterValue(e.target.value)}
            /> */}
          </div>
        );
      },
    },
  ];

  return (
    <>
      {/* <div className="px-[105px] xl:px-[260px] lg:px-[260px] 2xl:px-[280px] 3xl:px-[18.75vw] bg-InterfaceSurfacecomponentmuted"> */}
      <div className="relative bg-interfacesurfacecomponent">
        <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-gray-300">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
            Brand/Brand Category
          </div>
        </div>
        <ScrollArea className="h-[520px] overflow-y-auto">
          <div className="py-4">
            <div className=" font-semibold text-gray-500 text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw]">
              List
            </div>
          </div>

          <DataTable data={displayTableData} columns={brandColumns} />
          <TablePagination />
        </ScrollArea>
      </div>
    </>
  );
}
