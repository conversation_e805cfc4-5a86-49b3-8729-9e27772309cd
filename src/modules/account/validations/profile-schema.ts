import { z } from 'zod';

export const profileSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  companyName: z.string().min(1, 'Company name is required'),
  countryCode: z.string().min(1, 'Country is required'),
  email: z.string().email('Invalid email address'),
  mobileNumber: z.string().min(1, 'Mobile number is required'),
  designation: z.string().min(1, 'Designation is required'),
  regionId: z.string().min(0, 'Region is required'),
});

export type ProfileFormValues = z.infer<typeof profileSchema>;
