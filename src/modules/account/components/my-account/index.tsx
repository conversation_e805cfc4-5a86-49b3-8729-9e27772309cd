'use client';
import React from 'react';
import { Label, Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import EditProfileDetails from '../edit-profile-details';
import ChangePassword from '../change-password';
import { ImageUploader } from '@/components/common/image-uploader';
import { IMAGE_UPLOADER_STATE } from '@/lib/enums';
import { countryService, userService } from '@/lib/services/api';
import { useQuery } from '@tanstack/react-query';
import { logger } from '@/lib/utils/logger';
import { CountryType } from '@/types/country';
import { Roboto } from 'next/font/google';
import { useTranslations } from 'next-intl';
const myroboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
const nameRegex = /^[A-Za-z0-9-' ]+$/;
const designationRegex = /^[A-Za-z0-9-/ ]+$/;
const companyNameRegex = /^[A-Za-z0-9-& ]+$/;
const redingtonReferenceRegex = /^[A-Za-z0-9- ]+$/;
const shouldBeDigits = /^\d*$/;
// Zod validation schema
const formSchema = z
  .object({
    first_name: z
      .string()
      .min(1, { message: 'First Name is required' })
      .max(50, { message: 'First Name must be at most 50 characters' })
      .regex(nameRegex, {
        message: 'First Name can only contain letters, numbers, spaces, hyphens, and apostrophes',
      }),

    last_name: z
      .string()
      .min(1, { message: 'Last Name is required' })
      .max(50, { message: 'Last Name must be at most 50 characters' })
      .regex(nameRegex, {
        message: 'Last Name can only contain letters, numbers, spaces, hyphens, and apostrophes',
      }),

    designation: z
      .string()
      .min(1, { message: 'Designation is required' })
      .max(100, { message: 'Designation must be at most 100 characters' })
      .regex(designationRegex, {
        message: 'Designation can only contain letters, numbers, spaces, hyphens, and slashes',
      }),

    mobile_number: z
      .string()
      .regex(shouldBeDigits, {
        message: 'Only digits are allowed!',
      })
      .min(8, { message: 'Mobile number is too short' })
      .max(15, { message: 'Mobile number must be at most 15 characters' }),

    mobile_country_code: z.string().min(1, { message: 'Country code is required' }),

    email: z
      .string()
      .email({ message: 'Invalid email address' })
      .max(100, { message: 'Email must be at most 100 characters' }),

    company_name: z
      .string()
      .min(1, { message: 'Company Name is required' })
      .max(100, { message: 'Company Name must be at most 100 characters' })
      .regex(companyNameRegex, {
        message: 'Company Name can only contain letters, numbers, spaces, hyphens, and ampersands',
      }),

    country_code: z.string().min(1, { message: 'Country of Business is required' }),

    redington_reference: z.boolean(),

    reference_type: z
      .string()
      .max(100)
      .regex(redingtonReferenceRegex, {
        message: 'Reference name can only contain letters, numbers, spaces, and hyphens',
      })
      .optional(),

    profile_pic_url: z
      .string()
      // .url({ message: 'Profile picture must be a valid URL' })
      .optional(),

    accept_terms_condition: z.boolean().refine((val) => val, {
      message: 'You must accept the terms and conditions',
    }),

    recaptcha: z.boolean().refine((val) => val, {
      message: "Please verify you're not a robot",
    }),

    isEmailVerified: z.boolean(),
    isMobileVerified: z.boolean(),
  })
  .superRefine((data, ctx) => {
    // If `redington_reference` is true, ensure `reference_type` is not empty
    if (data.redington_reference && !data.reference_type) {
      ctx.addIssue({
        path: ['reference_type'],
        message: 'Redington Reference is required.',
        code: z.ZodIssueCode.custom,
      });
    }
  });

type FormData = z.infer<typeof formSchema>;

function MyAccount() {
  const t = useTranslations();
  const [editProfileDetails, setEditProfileDetails] = React.useState<boolean>(false);
  const [changePassword, setChangePassword] = React.useState<boolean>(false);
  const [profilePicState, setProfilePicState] = React.useState<
    IMAGE_UPLOADER_STATE.NEW | IMAGE_UPLOADER_STATE.PREVIEW | IMAGE_UPLOADER_STATE.UPLOADED
  >(IMAGE_UPLOADER_STATE.NEW);

  const {
    watch,
    setValue,
    formState: {},
  } = useForm<FormData>({
    mode: 'all',
    resolver: zodResolver(formSchema),
    defaultValues: {
      country_code: '',
      redington_reference: false,
      accept_terms_condition: false,
      recaptcha: false,
      isEmailVerified: false,
      isMobileVerified: false,
      profile_pic_url: '',
      designation: '',
      mobile_country_code: '+91',
    },
  });

  const profileUrl = watch('profile_pic_url');

  const onImageUpload = (file: File) => {
    const fileUrl = URL.createObjectURL(file);
    setValue('profile_pic_url', fileUrl);
    setProfilePicState(IMAGE_UPLOADER_STATE.PREVIEW);
  };

  const { data } = useQuery({
    queryKey: ['user-details'],
    queryFn: () => userService.getUserDetails(),
  });

  const { data: countryData } = useQuery({
    queryKey: ['country-data'],
    queryFn: () => countryService.getCountries(),
  });

  logger.log(data);

  return (
    <>
      <div className="pb-[20px] ">
        <div className=" pb-[14px] xl:pb-[14px] 2xl:pb-[14px] 3xl:pb-[0.729vw]">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
            {t('myProfile')}
          </div>
        </div>
        <div className="relative bg-interfacesurfacecomponent border border-InterfaceSurfacecomponentmuted p-[24px] xl:p-[24px] 3xl:p-[1.25vw] cardShadow">
          <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceSurfacecomponentmuted">
            <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
              {t('personalInformation')}
            </div>

            <div className="flex gap-2 text-background text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-BrandNeutral800 bg-BrandNeutral700 text-background text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                variant="skybluegradient"
                size="sm"
                onClick={() => setChangePassword(true)}
              >
                <i className="cursor-pointer cloud-lock text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('changePassword')}
              </Button>

              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-BrandNeutral800 bg-BrandNeutral700 text-background text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                size="sm"
                onClick={() => setEditProfileDetails(true)}
              >
                <i className="cursor-pointer cloud-card text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('editProfile')}
              </Button>
            </div>
          </div>

          <div
            className={`${myroboto.className} pt-[16px] 3xl:pt-[0.833vw] grid grid-cols-1 xl:grid-cols-7 md:grid-cols-5 gap-[16px] `}
          >
            <div className="flex flex-col gap-1.5">
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                {t('firstName')}
              </div>

              <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                {data && data?.data && data?.data[0]?.first_name}
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                {t('lastName')}
              </div>
              <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                {data && data?.data && data?.data[0]?.last_name}
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                {t('mobileNumber')}{' '}
                <svg
                  className=" inline-flex"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M8.00091 15.1733C7.27425 15.1733 6.55424 14.96 5.98758 14.54L3.12091 12.4C2.36091 11.8333 1.76758 10.6466 1.76758 9.70663V4.74663C1.76758 3.71996 2.52091 2.62663 3.48758 2.26663L6.81425 1.01996C7.47425 0.773294 8.51425 0.773294 9.17425 1.01996L12.5009 2.26663C13.4676 2.62663 14.2209 3.71996 14.2209 4.74663V9.69996C14.2209 10.6466 13.6276 11.8266 12.8676 12.3933L10.0009 14.5333C9.44758 14.96 8.72758 15.1733 8.00091 15.1733ZM7.16758 1.95996L3.84091 3.20663C3.27424 3.41996 2.77424 4.13996 2.77424 4.75329V9.70663C2.77424 10.34 3.22091 11.2266 3.72091 11.6L6.58758 13.74C7.35425 14.3133 8.64758 14.3133 9.42091 13.74L12.2876 11.6C12.7942 11.22 13.2342 10.34 13.2342 9.70663V4.74663C13.2342 4.13996 12.7342 3.41996 12.1676 3.19996L8.84091 1.95329C8.38758 1.79329 7.61425 1.79329 7.16758 1.95996Z"
                    fill="#72B35F"
                  />
                  <path
                    d="M7.10682 9.48707C6.98016 9.48707 6.85349 9.4404 6.75349 9.3404L5.68016 8.26707C5.48682 8.07374 5.48682 7.75374 5.68016 7.5604C5.87349 7.36707 6.19349 7.36707 6.38682 7.5604L7.10682 8.2804L9.62016 5.76707C9.81349 5.57374 10.1335 5.57374 10.3268 5.76707C10.5202 5.9604 10.5202 6.2804 10.3268 6.47374L7.46016 9.3404C7.36016 9.4404 7.23349 9.48707 7.10682 9.48707Z"
                    fill="#72B35F"
                  />
                </svg>
              </div>
              <div className="text-BrandPrimarypure text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                {data && data?.data && data?.data[0]?.mobile_number}
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                {t('emailAddress')}{' '}
                <svg
                  className=" inline-flex"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M8.00091 15.1733C7.27425 15.1733 6.55424 14.96 5.98758 14.54L3.12091 12.4C2.36091 11.8333 1.76758 10.6466 1.76758 9.70663V4.74663C1.76758 3.71996 2.52091 2.62663 3.48758 2.26663L6.81425 1.01996C7.47425 0.773294 8.51425 0.773294 9.17425 1.01996L12.5009 2.26663C13.4676 2.62663 14.2209 3.71996 14.2209 4.74663V9.69996C14.2209 10.6466 13.6276 11.8266 12.8676 12.3933L10.0009 14.5333C9.44758 14.96 8.72758 15.1733 8.00091 15.1733ZM7.16758 1.95996L3.84091 3.20663C3.27424 3.41996 2.77424 4.13996 2.77424 4.75329V9.70663C2.77424 10.34 3.22091 11.2266 3.72091 11.6L6.58758 13.74C7.35425 14.3133 8.64758 14.3133 9.42091 13.74L12.2876 11.6C12.7942 11.22 13.2342 10.34 13.2342 9.70663V4.74663C13.2342 4.13996 12.7342 3.41996 12.1676 3.19996L8.84091 1.95329C8.38758 1.79329 7.61425 1.79329 7.16758 1.95996Z"
                    fill="#72B35F"
                  />
                  <path
                    d="M7.10682 9.48707C6.98016 9.48707 6.85349 9.4404 6.75349 9.3404L5.68016 8.26707C5.48682 8.07374 5.48682 7.75374 5.68016 7.5604C5.87349 7.36707 6.19349 7.36707 6.38682 7.5604L7.10682 8.2804L9.62016 5.76707C9.81349 5.57374 10.1335 5.57374 10.3268 5.76707C10.5202 5.9604 10.5202 6.2804 10.3268 6.47374L7.46016 9.3404C7.36016 9.4404 7.23349 9.48707 7.10682 9.48707Z"
                    fill="#72B35F"
                  />
                </svg>
              </div>
              <div className="text-BrandPrimarypure text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                {data && data?.data && data?.data[0]?.email}
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                {t('designation')}
              </div>

              <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                {data && data?.data && data?.data[0]?.designation?.name}
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                {t('companyName')}
              </div>

              <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                {data && data?.data && data?.data[0]?.company_name}
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <div className="flex items-center gap-[6px] 3xl:gap-[0.36vw]">
                <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
                  {t('countryOfBusiness')}
                </div>
                <i className="cloud-eye text-InterfaceTextprimary" />
              </div>
              <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                {countryData &&
                  countryData?.data &&
                  countryData?.data.find(
                    (country: CountryType) => country.iso_2 === data?.data?.[0]?.iso_2
                  )?.name}
              </div>
            </div>
          </div>
          <div>
            <div className="flex flex-col gap-3 mt-6">
              <Label
                htmlFor="profile"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('profilePicture')}
              </Label>
              <div className="flex items-center justify-between flex-wrap gap-3 3xl:gap-[1.25vw]">
                <div className="flex flex-row items-center gap-4">
                  <img
                    src={profileUrl?.length !== 0 ? profileUrl : '/images/profile-circle.svg'}
                    alt="profile"
                    className="h-[85px]"
                  />

                  {/* When image is selected but not uploaded (PREVIEW state) */}
                  {profilePicState === IMAGE_UPLOADER_STATE.PREVIEW && (
                    <div className="col-span-10 xl:col-span-9 2xl:col-span-10 3xl:col-span-10">
                      <div className="flex items-center flex-wrap gap-[16px]">
                        <Button
                          onClick={() => setProfilePicState(IMAGE_UPLOADER_STATE.UPLOADED)}
                          className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                          variant="outline"
                          size="sm"
                        >
                          <i className="cloud-refresh text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          {t('change')}
                        </Button>

                        <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[300]">
                          {t('or')}
                        </div>

                        <Button
                          onClick={() => {
                            setValue('profile_pic_url', '');
                            setProfilePicState(IMAGE_UPLOADER_STATE.NEW);
                          }}
                          className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                          variant="outline"
                          size="sm"
                        >
                          <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          {t('remove')}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Show uploader when no image selected (NEW) or user clicked "Change" */}
                  {profilePicState === IMAGE_UPLOADER_STATE.NEW && (
                    <ImageUploader onImageUpload={onImageUpload} />
                  )}

                  {/* Uploaded: Show "Change" and "Remove" */}
                  {profilePicState === IMAGE_UPLOADER_STATE.UPLOADED && (
                    <div className="col-span-10 xl:col-span-9 2xl:col-span-10 3xl:col-span-10">
                      <div className="flex items-center flex-wrap gap-[16px]">
                        <Button
                          onClick={() => setProfilePicState(IMAGE_UPLOADER_STATE.NEW)}
                          className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#1570EF] bg-[#1570EF] hover:bg-[#1861DD] hover:text-[#FFF] text-[#FFF] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                          variant="outline"
                          size="sm"
                        >
                          <i className="cloud-refresh text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          {t('Change')}
                        </Button>

                        <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[300]">
                          {t('or')}
                        </div>

                        <Button
                          onClick={() => {
                            setValue('profile_pic_url', '');
                            setProfilePicState(IMAGE_UPLOADER_STATE.NEW);
                          }}
                          className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                          variant="outline"
                          size="sm"
                        >
                          <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                          {t('remove')}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* {data?.data?.[0] && ( */}
      <EditProfileDetails
        open={editProfileDetails}
        onClose={() => setEditProfileDetails(false)}
        firstName={data?.data?.[0]?.first_name ?? ''}
        lastName={data?.data?.[0]?.last_name ?? ''}
        companyName={data?.data?.[0]?.company_name ?? ''}
        designation={data?.data?.[0]?.designation?.id ?? ''}
        mobileNumber={data?.data?.[0]?.mobile_number ?? ''}
        email={data?.data?.[0]?.email ?? ''}
        countryCode={data?.data?.[0]?.iso_2 ?? ''}
      />
      {/* )} */}
      <ChangePassword
        open={changePassword}
        onClose={() => {
          setChangePassword(false);
        }}
      />
    </>
  );
}
export default MyAccount;
