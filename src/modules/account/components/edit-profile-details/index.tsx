'use client';
import React from 'react';
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON><PERSON>,
  Sheet<PERSON>ooter,
  Input,
  Label,
  Button,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TermsSheetProps } from '@/types/components';
import { useQuery, useMutation } from '@tanstack/react-query';
import { userService } from '@/lib/services/api';
import { countryService } from '@/lib/services/api';
import { Select2 } from '@/components/common/ui/combo-box';
import toast from 'react-hot-toast';
import VerificationEmail from '../verify-email';
import VerificationMobile from '../verify-mobile';
import { profileSchema, ProfileFormValues } from '../../validations/profile-schema';
import type { FieldErrors } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import { Roboto } from 'next/font/google';
// import { Select2, Select2Option } from '@/components/common/ui/comboBox';
// import * as React from 'react';
import Image from 'next/image';
import { queryClient } from '@/lib/config/react-query';
import { logger } from '@/lib/utils/logger';
import { localStorageService } from '@/lib/services/storage';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface EditProfileDetailsProps extends TermsSheetProps {
  firstName: string;
  lastName: string;
  companyName: string;
  countryCode?: string;
  designation: string;
  mobileNumber: string;
  email: string;
  isEmailVerified?: boolean;
  isMobileVerified?: boolean;
}

// Add a type for the verification result
interface VerificationResult {
  state: boolean;
  data: boolean | null;
}

export default function EditProfileDetails({
  open,
  onClose,
  firstName = '',
  lastName = '',
  companyName = '',
  countryCode = '',
  designation = '',
  mobileNumber = '',
  email = '',
  isEmailVerified = true,
  isMobileVerified = true,
}: EditProfileDetailsProps) {
  // States for tracking verifications
  const [emailVerified, setEmailVerified] = React.useState(isEmailVerified);
  const [mobileVerified, setMobileVerified] = React.useState(isMobileVerified);
  const [showEmailVerification, setShowEmailVerification] = React.useState(false);
  const [showMobileVerification, setShowMobileVerification] = React.useState(false);
  const [initialEmail, setInitialEmail] = React.useState(email);
  const [initialMobile, setInitialMobile] = React.useState(mobileNumber);
  const t = useTranslations();
  const { data: designations } = useQuery({
    queryKey: ['designations'],
    queryFn: () => userService.getDesignations(),
  });

  const { data: countries } = useQuery({
    queryKey: ['countries'],
    queryFn: () => countryService.getCountries(),
  });

  // const { data: regions } = useQuery({
  //   queryKey: ['regions'],
  //   queryFn: () => regionService.getRegions(),
  // });

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
  } = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName,
      lastName,
      companyName,
      countryCode,
      email,
      mobileNumber,
      designation,
      regionId: '',
    },
  });

  // Watch for changes in email and mobile
  const currentEmail = watch('email');
  const currentMobile = watch('mobileNumber');

  // Check if email or mobile have changed from initial values
  const emailChanged = currentEmail !== initialEmail;
  const mobileChanged = currentMobile !== initialMobile;

  // Reset form when modal opens or props change
  React.useEffect(() => {
    if (open) {
      reset({
        firstName,
        lastName,
        companyName,
        countryCode,
        email,
        mobileNumber,
        designation,
        regionId: '',
      });
      setInitialEmail(email);
      setInitialMobile(mobileNumber);
      setEmailVerified(isEmailVerified);
      setMobileVerified(isMobileVerified);
    }
  }, [
    open,
    firstName,
    lastName,
    companyName,
    countryCode,
    email,
    mobileNumber,
    designation,
    isEmailVerified,
    isMobileVerified,
    reset,
  ]);

  // Reset verification status when values change
  React.useEffect(() => {
    if (emailChanged) {
      setEmailVerified(false);
    }
  }, [currentEmail, emailChanged]);

  React.useEffect(() => {
    if (mobileChanged) {
      setMobileVerified(false);
    }
  }, [currentMobile, mobileChanged]);

  const { mutate: updateProfile, isPending } = useMutation({
    mutationFn: (data: ProfileFormValues) =>
      userService.updateUserDetails({
        first_name: data.firstName,
        last_name: data.lastName,
        company_name: data.companyName,
        country_code: data.countryCode,
        email: data.email,
        mobile_number: data.mobileNumber,
        designation: data.designation,
        region_id: data.regionId,
      }),
    onSuccess: () => {
      reset();
      toast.success('Profile updated successfully');
      queryClient.invalidateQueries({ queryKey: ['user-details'] });
      onClose(false);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update profile');
    },
  });

  const onSubmit = async (data: ProfileFormValues) => {
    // Prevent submission if email or mobile not verified
    if (!emailVerified || !mobileVerified) {
      toast.error('Please verify your email and mobile number before updating your profile');
      return;
    }
    logger.log('Submitting data', data, await localStorageService.get('region'));
    updateProfile({ ...data, regionId: (await localStorageService.get('region')) ?? '' });
  };

  const onError = (errors: FieldErrors<ProfileFormValues>) => {
    logger.error('Form validation errors:', errors);
    toast.error('Please fix the form errors before submitting');
  };

  // Handlers for verification modals
  const handleEmailVerificationClose = (result: VerificationResult) => {
    setShowEmailVerification(false);
    if (result?.data) {
      setEmailVerified(true);
      setInitialEmail(currentEmail);
      toast.success('Email verified successfully');
    }
  };

  const handleMobileVerificationClose = (result: VerificationResult) => {
    setShowMobileVerification(false);
    if (result?.data) {
      setMobileVerified(true);
      setInitialMobile(currentMobile);
      toast.success('Mobile number verified successfully');
    }
  };

  return (
    <>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetTitle></SheetTitle>
        <SheetContent
          className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
          side={'right'}
        >
          {/* Header */}
          <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
                {t('editProfileDetails')}
              </div>

              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>
          <form onSubmit={handleSubmit(onSubmit, onError)} className="flex-grow flex flex-col">
            <div className="flex-grow overflow-auto h-[420px] xl:h-[500px] 2xl:h-[550px] 3xl:h-[36.25vw]">
              <div className="px-[16px] 3xl:px-[0.833vw] pb-[16px] 3xl:pb-[0.833vw] grid grid-cols-1 xl:grid-cols-1 md:grid-cols-1 gap-[20px]">
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="firstName"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('firstName')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="firstName"
                    type="text"
                    placeholder={t('firstName')}
                    {...register('firstName')}
                    className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                      errors.firstName ? 'border-red-500' : 'border-InterfaceStrokehard'
                    }`}
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-xs mt-1">{errors.firstName.message}</p>
                  )}
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="lastName"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('lastName')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="lastName"
                    type="text"
                    placeholder={t('lastName')}
                    {...register('lastName')}
                    className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                      errors.lastName ? 'border-red-500' : 'border-InterfaceStrokehard'
                    }`}
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-xs mt-1">{errors.lastName.message}</p>
                  )}
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="companyName"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('companyName')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="companyName"
                    type="text"
                    placeholder={t('companyName')}
                    {...register('companyName')}
                    className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                      errors.companyName ? 'border-red-500' : 'border-InterfaceStrokehard'
                    }`}
                  />
                  {errors.companyName && (
                    <p className="text-red-500 text-xs mt-1">{errors.companyName.message}</p>
                  )}
                </div>

                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="countryCode"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('countryOfBusiness')} <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="countryCode"
                    render={({ field }) => (
                      <Select2
                        options={
                          countries?.data?.map((country) => ({
                            label: country.name,
                            value: country.iso_2,
                          })) ?? []
                        }
                        value={field.value}
                        onChange={field.onChange}
                      />
                    )}
                  />
                  {errors.countryCode && (
                    <p className="text-red-500 text-xs mt-1">{errors.countryCode.message}</p>
                  )}
                </div>

                {/* <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="countryCode"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Region
                  </Label>
                  <Controller
                    control={control}
                    name="regionId"
                    render={({ field }) => (
                      <Select2
                        options={
                          regions?.data?.map((region) => ({
                            label: region.name,
                            value: region.id,
                          })) ?? []
                        }
                        value={field.value}
                        onChange={field.onChange}
                      />
                    )}
                  />
                  {errors.regionId && (
                    <p className="text-red-500 text-xs mt-1">{errors.regionId.message}</p>
                  )}
                </div> */}

                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="email"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('emailAddress')} <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex w-full">
                    <Input
                      id="email"
                      type="email"
                      placeholder="Email"
                      {...register('email')}
                      className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                        errors.email ? 'border-red-500' : 'border-InterfaceStrokehard'
                      }`}
                    />
                    {emailVerified ? (
                      <div className="flex items-center justify-center bg-green-100 text-green-800 px-3 border border-green-300">
                        <i className="cloud-tick text-green-800 mr-1"></i>
                        <span className="text-sm font-medium">{t('verified')}</span>
                      </div>
                    ) : (
                      <Button
                        disabled={!emailChanged}
                        type="button"
                        variant="verifybtn"
                        size="sm"
                        onClick={() => setShowEmailVerification(true)}
                        className={`px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[4px] xl:py-[4px] 2xl:py-[6px] 3xl:py-[0.36vw] flex items-center justify-center hover:bg-BrandNeutralpure hover:text-InterfaceTextwhite gap-2 cursor-pointer ${
                          emailChanged
                            ? 'bg-BrandNeutralpure text-InterfaceTextwhite'
                            : 'bg-gray-200 text-gray-500'
                        } font-medium text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] rounded-none border border-BrandNeutral700`}
                      >
                        <Image
                          src={'/images/shield-tick.svg'}
                          width={18}
                          height={18}
                          alt="shield"
                        />
                        {' Verify'}
                      </Button>
                    )}
                  </div>
                  {errors.email && (
                    <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
                  )}
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="mobileNumber"
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('mobileNumber')} <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex  w-full">
                    <Input
                      id="mobileNumber"
                      {...register('mobileNumber')}
                      className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                        errors.mobileNumber ? 'border-red-500' : 'border-InterfaceStrokehard'
                      }`}
                    />
                    {mobileVerified ? (
                      <div className="flex items-center justify-center bg-green-100 text-green-800 px-3 border border-green-300">
                        <i className="cloud-tick text-green-800 mr-1"></i>
                        <span className="text-sm font-medium">{t('verified')}</span>
                      </div>
                    ) : (
                      <Button
                        disabled={!mobileChanged}
                        type="button"
                        variant="verifybtn"
                        size="sm"
                        onClick={() => setShowMobileVerification(true)}
                        className={`px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[4px] xl:py-[4px] 2xl:py-[6px] 3xl:py-[0.36vw] flex items-center justify-center hover:bg-BrandNeutralpure hover:text-InterfaceTextwhite gap-2 cursor-pointer ${
                          mobileChanged
                            ? 'bg-BrandNeutralpure text-InterfaceTextwhite'
                            : 'bg-gray-200 text-gray-500'
                        } font-medium text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] rounded-none border border-BrandNeutral700`}
                      >
                        <Image
                          src={'/images/shield-tick.svg'}
                          width={18}
                          height={18}
                          alt="shield"
                        />
                        {'Verify'}
                      </Button>
                    )}
                  </div>
                  {errors.mobileNumber && (
                    <p className="text-red-500 text-xs mt-1">{errors.mobileNumber.message}</p>
                  )}
                </div>

                <div className="flex flex-col gap-1.5">
                  <Label
                    htmlFor="designation"
                    className="bg-interfacesurfacecomponent text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('designation')} <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="designation"
                    render={({ field }) => (
                      <Select2
                        options={
                          designations?.data?.map((designation) => ({
                            label: designation.name,
                            value: designation.id,
                          })) ?? []
                        }
                        value={field.value}
                        onChange={field.onChange}
                      />
                    )}
                  />
                  {errors.designation && (
                    <p className="text-red-500 text-xs mt-1">{errors.designation.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Footer */}
            <SheetFooter className="mr-3 mb-2">
              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                variant="outline"
                size="sm"
                onClick={() => onClose(false)}
              >
                <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('cancel')}
              </Button>
              <Button
                type="submit"
                variant="skybluegradient"
                disabled={isPending || !emailVerified || !mobileVerified}
                className="applybtn text-white lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
              >
                {isPending ? (
                  'Saving...'
                ) : (
                  <>
                    <i className="cloud-profile"></i>
                    {t('save')}
                  </>
                )}
              </Button>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>

      {/* Verification Modals */}
      <VerificationEmail
        open={showEmailVerification}
        onClose={handleEmailVerificationClose}
        input={currentEmail}
      />

      <VerificationMobile
        open={showMobileVerification}
        onClose={handleMobileVerificationClose}
        input={currentMobile}
      />
    </>
  );
}
