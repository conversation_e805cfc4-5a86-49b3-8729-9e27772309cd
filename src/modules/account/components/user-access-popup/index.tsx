import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON><PERSON> } from 'next/font/google';
import * as React from 'react';
import { useState } from 'react';
import BrandTable from './tabs/brands';
import { PermissionTable } from './tabs/persmission';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
const tabs = ['company', 'brands', 'permission'] as const;
type Tab = (typeof tabs)[number];
export default function UserAccessPopup() {
  const [activeTab, setActiveTab] = useState<Tab>('company');

  const handleNext = () => {
    const currentIndex = tabs.indexOf(activeTab);
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1]);
    }
  };

  const handleCancel = () => {
    setActiveTab('company');
  };
  return (
    <Sheet>
      <SheetTrigger className="w-full">
        <Button
          type="button"
          className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-BrandNeutral800 bg-BrandNeutral700 text-background text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[300] leading-[100%]"
          size="sm"
        >
          <i className="cursor-pointer cloud-copy-success1 text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
          My Access and Permissions
        </Button>
      </SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} flex flex-col h-full sm:max-w-[700px] xl:max-w-[800px] 3xl:max-w-[41.667vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              User Access and Permission
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <form className="flex-grow flex flex-col">
          <div className="flex-grow overflow-y-auto space-y-3">
            <div className="px-[15px] xl:px-[22px] 3xl:px-[1.25vw] overflow-y-auto">
              <div className="space-y-6 w-full max-w-3xl mx-auto">
                {/* Tab Navigation */}
                <div className="flex">
                  {tabs.map((tab) => (
                    <button
                      type="button"
                      key={tab}
                      onClick={() => setActiveTab(tab)}
                      className={`px-4 py-2 text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] ${
                        activeTab === tab
                          ? 'bg-[#1F2A37] text-[#fff] py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw]'
                          : 'py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault'
                      }`}
                    >
                      {tab === 'company' ? (
                        <div className="flex gap-[8px] items-center">
                          <i
                            className={`cloud-hexauser text-[16px]
                          ${activeTab === tab ? 'text-[#fff]' : 'text-[#1570EF]'}`}
                          ></i>
                          Company Profiles
                        </div>
                      ) : tab === 'brands' ? (
                        <div className="flex gap-[8px] items-center">
                          <i
                            className={`cloud-shopping text-[16px] ${activeTab === tab ? 'text-[#fff]' : 'text-[#1570EF]'}`}
                          ></i>
                          Brands
                        </div>
                      ) : (
                        <div className="flex gap-[8px] items-center">
                          <i
                            className={`cloud-shield-tickk text-[16px] ${activeTab === tab ? 'text-[#fff]' : 'text-[#1570EF]'}`}
                          ></i>
                          Permission
                        </div>
                      )}
                    </button>
                  ))}
                </div>

                {/* Tab Content */}
                {activeTab === 'company' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-2 gap-[15px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                    <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                      <p className="text-InterfaceTextsubtitle lg:text-[12px] xl:text-[14px] text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        Company Profile
                      </p>
                      <h2 className="text-InterfaceTextdefault lg:text-[12px] xl:text-[14px] text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Hexalytics, UAE (1000123597)
                      </h2>
                    </div>
                    <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                      <p className="text-InterfaceTextsubtitle lg:text-[12px] xl:text-[14px] text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        Role
                      </p>
                      <h2 className="text-InterfaceTextdefault lg:text-[12px] xl:text-[14px] text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Admin
                      </h2>
                    </div>
                  </div>
                )}

                {activeTab === 'brands' && (
                  <div>
                    <BrandTable />
                  </div>
                )}

                {activeTab === 'permission' && (
                  <div>
                    <PermissionTable />
                  </div>
                )}
              </div>
            </div>
          </div>

          <SheetFooter className="mr-3 mb-5 ">
            <div className="flex justify-end w-full gap-2 border-t pt-4">
              <button
                type="button"
                onClick={handleCancel}
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
              >
                <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] mr-2"></i>{' '}
                Cancel
              </button>
              <button
                type="button"
                onClick={handleNext}
                className="bg-BrandPrimarypure border border-BrandPrimarypure text-InterfaceTextwhite text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[140%] rounded-none px-4 py-2  lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
              >
                {activeTab === 'permission' ? 'Submit' : 'Next'}{' '}
                <i
                  className={`cloud-rightarrow1 text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] ml-2`}
                ></i>
              </button>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
