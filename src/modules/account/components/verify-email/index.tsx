'use client';

import * as React from 'react';
import {
  <PERSON><PERSON>,
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { REGEXP_ONLY_DIGITS_AND_CHARS } from 'input-otp';

import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { otpService } from '@/lib/services/api/';
import { NETWORK_STATUS } from '@/lib/enums';
import { logger } from '@/lib/utils/logger';
import { VerificationSheetProps } from '@/types/components';
import { ApiResponse } from '@/types/core/api';
import { SendOtpResponse } from '@/types/otp';
import CountdownTimer from '@/components/common/count-down-timer';
import { isApiResponseError } from '@/lib/utils/util';
import toast from 'react-hot-toast';

const otpRegex = /^\d{6}$/;

const otpSchema = z.object({
  pin: z
    .string()
    .min(6, {
      message: 'Your one-time password must be 6 characters.',
    })
    .regex(otpRegex, {
      message: 'Otp must be a number',
    }),
});

type OTPFormData = z.infer<typeof otpSchema>;

export default function VerificationEmail({ open, onClose, input }: VerificationSheetProps) {
  const {
    setValue,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm<OTPFormData>({
    resolver: zodResolver(otpSchema),
    defaultValues: { pin: '' },
  });

  const [resetCount, setResetCount] = React.useState(0);
  const [isCounting, setIsCounting] = React.useState(open);
  const [timerFinished, setTimerFinished] = React.useState(false);

  const pinValue = watch('pin');

  const handleOpenChange = () => {
    reset();
    setIsCounting(false);
    setTimerFinished(false);
    onClose({ state: false, data: null });
  };

  const resendOtp = async () => {
    try {
      const payload = { email: input };
      const response: ApiResponse<SendOtpResponse> = await otpService.resendEmailOtp(payload);
      toast(response?.message);
    } catch (error) {
      if (isApiResponseError(error)) {
        logger.error('Error:', error?.data?.message);
        toast.error(error?.data?.message);
      }
    }
  };

  const handleResend = async () => {
    if (timerFinished) {
      logger.log('Resending OTP...');
      await resendOtp();
      setResetCount((prev) => prev + 1); // Trigger timer reset
      setTimerFinished(false);
      setIsCounting(true);
    }
  };

  React.useEffect(() => {
    if (open) {
      setResetCount((prev) => prev + 1);
      setIsCounting(true);
    } else {
      setIsCounting(false);
    }
  }, [open]);

  const onSubmit = async (data: OTPFormData) => {
    try {
      const payload = {
        email: input,
        otp_code: data?.pin,
      };
      const response = await otpService.verifyEmailOtp(payload);
      if (response?.data?.is_verified && response?.status == NETWORK_STATUS.SUCCESS) {
        reset();
        toast.success(response?.message);
        onClose({ state: false, data: response?.data?.is_verified });
      }
    } catch (error) {
      if (isApiResponseError(error)) {
        logger.error('Error:', error?.data?.message);
        toast.error(error?.data?.message);
      }
    }
  };

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTitle></SheetTitle>
      <SheetContent className="sm:max-w-[500px] xl:max-w-[600px] 3xl:max-w-[31.25vw] flex items-center justify-center h-full overflow-auto">
        <div>
          <div className="flex justify-center">
            <i className="cloud-info2 text-[20px] lg:text-[30px] xl:text-[50px] 2xl:text-[50px] 3xl:text-[2.563vw] text-[#1570EF]"></i>
          </div>
          <div className="my-[30px] 3xl:my-[1.563vw]">
            <div className="text-InterfaceTexttitle text-[28px] xl:text-[30px] 3xl:text-[1.875vw] font-normal leading-[50.4px] text-center">
              Email Verification
            </div>
            <div className="text-[#7F8488] text-[15px] xl:text-[17px] 3xl:text-[1.042vw] font-normal leading-[28px] text-center mb-[8px]">
              OTP has been sent to below Email Address, <br></br>Please enter OTP to verify.
            </div>
            <div className="text-[#7F8488] text-[15px] xl:text-[17px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
              Email Address : <span className="text-[#1570EF]">{input}</span>
            </div>
            <div className="flex justify-center items-center mt-[13px]">
              <Button
                onClick={handleOpenChange}
                size="md"
                className="py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] flex gap-[8px] bg-[#768FB5] w-fit border border-[#6480AB]"
              >
                <div>
                  <i className="cloud-refresh2 text-[#fff] text-[16px] xl:text-[18px] 3xl:text-[0.938vw] flex items-center"></i>
                </div>
                <div className="text-[#fff] text-[14px] xl:text-[16px] 3xl:text-[0.938vw] font-normal leading-[14px] flex items-center ">
                  Change Email
                </div>
              </Button>
            </div>
          </div>
          <form
            onSubmit={(e) => {
              e.stopPropagation();
              handleSubmit(onSubmit)(e);
            }}
            className="w-full max-w-md mx-auto"
          >
            <div className="mb-[35px]">
              <div className="text-[#7F8488] text-[18px] 3xl:text-[1.042vw] font-normal leading-[25.2px] text-center">
                Enter Email OTP *
              </div>

              <div className="flex justify-center my-[16px]">
                <InputOTP
                  maxLength={6}
                  pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                  value={pinValue}
                  onChange={(val) => setValue('pin', val)}
                >
                  <InputOTPGroup className="flex gap-[10px]">
                    {[0, 1, 2, 3, 4, 5].map((i) => (
                      <InputOTPSlot
                        key={i}
                        index={i}
                        className="border border-[#E5E7EB] xl:w-[45px] xl:h-[45px] 2xl:w-[50px] 2xl:h-[50px] 3xl:h-[3.125vw] 3xl:w-[3.125vw]"
                      />
                    ))}
                  </InputOTPGroup>
                </InputOTP>
              </div>

              {errors.pin && (
                <div className="text-center text-red-500 text-sm mt-[-10px] mb-[10px]">
                  {errors.pin.message}
                </div>
              )}

              <div className="text-[#7F8488] text-[14px] 3xl:text-[0.833vw] font-normal leading-[25.2px] text-center">
                {isCounting ? (
                  <div>
                    Resent OTP Enable in{' '}
                    <CountdownTimer
                      initialSeconds={60}
                      isActive={isCounting}
                      resetKey={resetCount}
                      onComplete={() => {
                        setTimerFinished(true);
                        setIsCounting(false);
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex justify-center mt-4">
                    <span
                      onClick={handleResend}
                      className={`text-[16px] cursor-pointer font-medium transition`}
                    >
                      Resend OTP
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex justify-center">
              <Button
                type="submit"
                className="flex gap-[8px] 3xl:gap-[0.417vw] bg-BrandPrimarypure border border-BrandPrimary800 text-InterfaceTextwhite text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] w-[210px] xl:w-[280px] 3xl:w-[14.583vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                size="md"
              >
                <div>
                  <i className="cloud-tick text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-[#EEEEF0]  font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                  Verify
                </div>
              </Button>
            </div>
          </form>

          <div className="my-[16px] 3xl:my-[0.833vw] flex justify-center items-center gap-[10px] 3xl:gap-[0.521vw]">
            <div className="bg-[#E5E7EB] w-[80px] 3xl:w-[4.167vw] h-[2px] 3xl:h-[0.104vw] flex items-center"></div>
            <div className="text-[#7F8488] text-[12px] 3xl:text-[0.833vw] font-normal leading-[12px]">
              or
            </div>
            <div className="bg-[#E5E7EB] w-[80px] 3xl:w-[4.167vw] h-[2px] 3xl:h-[0.104vw] flex items-center"></div>
          </div>
          <div className="flex justify-center">
            <Button
              type="button"
              className="flex  gap-[8px] bg-trasparent  w-[210px] xl:w-[280px] 3xl:w-[14.583vw] py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none h-fit border border-[#E5E7EB]"
            >
              <div>
                <i className="cloud-closecircle text-[#3C4146] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
              </div>
              <div className="text-[#3C4146]  text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                Cancel
              </div>
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
