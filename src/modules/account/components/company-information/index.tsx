'use client';
import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HoverCardTrigger,
  HoverCardContent,
  Button,
} from '@redington-gulf-fze/cloudquarks-component-library';
import CompanyProfiles from '../company-profile';
import Image from 'next/image';
import UserAccessPopup from '../user-access-popup';

const CompanyInformation = () => {
  const [openCompanyProfile, setOpenCompanyProfile] = React.useState<boolean>(false);
  return (
    <>
      <div className="flex justify-between items-center flex-wrap pb-[16px] xl:pb-[20px] 3xl:pb-[1.042vw] border-b border-InterfaceStrokesoft">
        <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
          Company Information
        </div>
        <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
          <Button
            type="submit"
            className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none bg-BrandPrimarypure text-InterfaceTextwhite  text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
            size="sm"
          >
            <i className=" cursor-pointer cloud-Add text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
            Onboard New Company
          </Button>
        </div>
      </div>
      <div className="h-[420px] overflow-y-auto mt-3">
        <div className="p-4 bg-[#f5f6f9]">
          <div className="flex justify-between items-center mb-[14px] xl:mb-[20px] 2xl:mb-[20px] 3xl:mb-[1.042vw]">
            <h2 className="text-[16px] 3xl:text-[0.833vw] font-[500]">Company: Hexalytics</h2>
            <div>
              <div>
                <UserAccessPopup />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-5">
            {/* Company Info Card */}
            <div className="bg-background rounded-lg p-4 shadow text-[12px] 3xl:text-[0.729vw]">
              <div className="grid grid-cols-12 gap-2 mb-4">
                <div className="col-span-8 flex items-center gap-2">
                  <Image src="/images/briefcase_icon.svg" width={24} height={24} alt="logo" />
                  <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                    Company profile
                  </div>
                </div>
                <div className="col-span-4 text-right text-InterfaceTextsubtitle">
                  <HoverCard>
                    <HoverCardTrigger asChild>
                      <div className="flex justify-end items-center h-full">
                        <i className="cloud-info text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                      </div>
                    </HoverCardTrigger>
                    <HoverCardContent className="w-410 bg-background">
                      <div className="flex flex-col items-start gap-4 w-[410px] p-4">
                        <div className="text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.933vw] font-[500] text-InterfaceTexttitle">
                          Details
                        </div>

                        <div className="flex items-center gap-4 w-full">
                          <div className="flex flex-col items-start gap-[2px] w-1/2">
                            <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                              Admin
                            </div>
                            <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                              Email
                            </div>
                          </div>

                          <div className="flex flex-col items-start gap-[2px] w-1/2">
                            <div className="text-sm text-InterfaceTextdefault-400">: Joes Boz</div>
                            <div className="text-sm text-InterfaceTextdefault-400">
                              : <EMAIL>
                            </div>
                          </div>
                        </div>
                      </div>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </div>

              <div className="grid grid-cols-5 gap-4 mb-4">
                <div className="col-span-2">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Name
                  </div>
                  <div className="font-[500]">Hexalytics</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Country</div>
                  <div className="font-[500]">UAE</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Store</div>
                  <div className="font-[500]">Dubai</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Role</div>
                  <div className="font-[500]">Admin</div>
                </div>
              </div>

              <div className="grid grid-cols-12 gap-4 mb-4">
                <div className="col-span-8">
                  <div className="flex text-InterfaceTextdefault font-[400] leading-[140%]">
                    Address
                  </div>
                  <div className="font-[500]">Villa 6 Zaglabi Street 99 Al Barsha Turkey</div>
                </div>

                <div className="col-span-4">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-yellow-700 bg-yellow-200 rounded px-2 py-1 text-xs">
                    Pending
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 ">
                <div className="bg-InterfaceSurfacecomponentmuted p-[10px] 3xl:p-[0.521vw]">
                  <div className="text-InterfaceTextdefault font-semibold flex items-center justify-between mb-1 3xl:mb-2">
                    User Account &nbsp;{' '}
                    <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                  </div>
                  <div className="flex flex-wrap items-center justify-between">
                    <div className="font-normal">Created On:</div>
                    <div className="font-semibold">01/02/2025</div>
                  </div>
                </div>

                <div className="bg-InterfaceSurfacecomponentmuted p-[10px] 3xl:p-[0.521vw]">
                  <div className="text-InterfaceTextdefault font-semibold flex justify-between items-center mb-1 3xl:mb-2">
                    Onboarding &nbsp;{' '}
                    <i
                      className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"
                      onClick={() => setOpenCompanyProfile(true)}
                    ></i>
                  </div>
                  <div className="flex flex-wrap items-center justify-between gap-1 mb-1">
                    <div className="font-normal">Submitted On:</div>
                    <div className="font-semibold">01/02/2025</div>
                  </div>
                  <div className="flex flex-wrap items-center justify-between gap-1">
                    <div className="font-normal">Approved On:</div>
                    <div className="font-semibold">-</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Billing Profile Card */}
            <div className="relative bg-white rounded-lg p-4 shadow text-[12px] 3xl:text-[0.729vw] h-full">
              <div className="col-span-8 flex items-center gap-2 mb-4">
                <Image src="/images/receipt_icon.svg" width={24} height={24} alt="logo" />
                <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                  Billing Profile (1/6)
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Redington AC No
                  </div>
                  <div className="font-[500]">1010011802</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Code
                  </div>
                  <div className="font-[500]">CD-1109698</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-green-700 bg-green-100 rounded px-2 py-1 text-xs">
                    Active
                  </span>
                </div>
                <hr className="col-span-3 border-gray-200" />
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Redington AC No
                  </div>
                  <div className="font-[500]">1010011802</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Code
                  </div>
                  <div className="font-[500]">CD-1109698</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-red-700 bg-red-100 rounded px-2 py-1 text-xs">
                    In Active
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 text-xs text-blue-600">
                <span>Prev</span>
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                </div>
                <span>Next</span>
              </div>

              {/* <Carousel className="w-full max-w-xs">
          <CarouselContent>
            {Array.from({ length: 5 }).map((_, index) => (
              <CarouselItem key={index}>
                <div className="p-1">
                  <Card>
                    <CardContent className="flex aspect-square items-center justify-center p-6">
                      <span className="text-4xl font-semibold">{index + 1}</span>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel> */}
            </div>

            {/* Billing Address Card */}
            <div className="bg-background rounded-lg p-4 shadow text-InterfaceTexttitle text-[12px] 3xl:text-[0.729vw]">
              <div className=" flex items-center gap-2 mb-4">
                <Image src="/images/routing_icon.svg" width={24} height={24} alt="logo" />
                <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                  Billing Address (1/2)
                </div>
              </div>

              <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded mb-2 inline-block">
                Default
              </span>
              <div className="mb-4">
                <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Address</div>
                <div className="font-[500]">
                  MDX Technology Solutions ME LLC 1501, Al Masood Tower, Hamdan Street Abu Dhabi,
                  12121 United Arab Emirates
                </div>
              </div>
              <div className="">
                <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Contact</div>
                <div className="font-[500]">+971 11 55 1142</div>
              </div>
              <div className="flex items-center justify-between mt-4 text-xs text-blue-600">
                <span>Prev</span>
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                </div>
                <span>Next</span>
              </div>
            </div>
          </div>
        </div>

        <div className="p-4 bg-[#f5f6f9]">
          <h2 className="text-[16px] 3xl:text-[0.833vw] font-[500] mb-4">Company Profile 2</h2>
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-5">
            {/* Company Info Card */}
            <div className="bg-background rounded-lg p-4 shadow text-[12px] 3xl:text-[0.729vw]">
              <div className="grid grid-cols-12 gap-2 mb-4">
                <div className="col-span-8 flex items-center gap-2">
                  <Image src="/images/briefcase_icon.svg" width={24} height={24} alt="logo" />
                  <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                    Company profile
                  </div>
                </div>
                <div className="col-span-4 text-right text-InterfaceTextsubtitle">
                  <HoverCard>
                    <HoverCardTrigger asChild>
                      <div className="flex justify-end items-center h-full">
                        <i className="cloud-info text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                      </div>
                    </HoverCardTrigger>
                    <HoverCardContent className="w-410 bg-background">
                      <div className="flex flex-col items-start gap-4 w-[410px] p-4">
                        <div className="text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.933vw] font-[500] text-InterfaceTexttitle">
                          Details
                        </div>

                        <div className="flex items-center gap-4 w-full">
                          <div className="flex flex-col items-start gap-[2px] w-1/2">
                            <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                              Admin
                            </div>
                            <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                              Email
                            </div>
                          </div>

                          <div className="flex flex-col items-start gap-[2px] w-1/2">
                            <div className="text-sm text-InterfaceTextdefault-400">: Joes Boz</div>
                            <div className="text-sm text-InterfaceTextdefault-400">
                              : <EMAIL>
                            </div>
                          </div>
                        </div>
                      </div>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </div>

              <div className="grid grid-cols-5 gap-4 mb-4">
                <div className="col-span-2">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Name
                  </div>
                  <div className="font-[500]">Hexalytics</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Country</div>
                  <div className="font-[500]">UAE</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Store</div>
                  <div className="font-[500]">Dubai</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Role</div>
                  <div className="font-[500]">Admin</div>
                </div>
              </div>

              <div className="grid grid-cols-12 gap-4 mb-4">
                <div className="col-span-8">
                  <div className="flex text-InterfaceTextdefault font-[400] leading-[140%]">
                    Address
                  </div>
                  <div className="font-[500]">Villa 6 Zaglabi Street 99 Al Barsha Turkey</div>
                </div>

                <div className="col-span-4">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-yellow-700 bg-yellow-200 rounded px-2 py-1 text-xs">
                    Pending
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 ">
                <div className="bg-InterfaceSurfacecomponentmuted p-[10px] 3xl:p-[0.521vw]">
                  <div className="text-InterfaceTextdefault font-semibold flex items-center justify-between mb-1 3xl:mb-2">
                    User Account &nbsp;{' '}
                    <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                  </div>
                  <div className="flex flex-wrap items-center justify-between">
                    <div className="font-normal">Created On:</div>
                    <div className="font-semibold">01/02/2025</div>
                  </div>
                </div>

                <div className="bg-InterfaceSurfacecomponentmuted p-[10px] 3xl:p-[0.521vw]">
                  <div className="text-InterfaceTextdefault font-semibold flex justify-between items-center mb-1 3xl:mb-2">
                    Onboarding &nbsp;{' '}
                    <i
                      className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"
                      onClick={() => setOpenCompanyProfile(true)}
                    ></i>
                  </div>
                  <div className="flex flex-wrap items-center justify-between gap-1 mb-1">
                    <div className="font-normal">Submitted On:</div>
                    <div className="font-semibold">01/02/2025</div>
                  </div>
                  <div className="flex flex-wrap items-center justify-between gap-1">
                    <div className="font-normal">Approved On:</div>
                    <div className="font-semibold">-</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Billing Profile Card */}
            <div className="relative bg-white rounded-lg p-4 shadow text-[12px] 3xl:text-[0.729vw] h-full">
              <div className="col-span-8 flex items-center gap-2 mb-4">
                <Image src="/images/receipt_icon.svg" width={24} height={24} alt="logo" />
                <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                  Billing Profile (1/6)
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Redington AC No
                  </div>
                  <div className="font-[500]">1010011802</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Code
                  </div>
                  <div className="font-[500]">CD-1109698</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-green-700 bg-green-100 rounded px-2 py-1 text-xs">
                    Active
                  </span>
                </div>
                <hr className="col-span-3 border-gray-200" />
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Redington AC No
                  </div>
                  <div className="font-[500]">1010011802</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Code
                  </div>
                  <div className="font-[500]">CD-1109698</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-red-700 bg-red-100 rounded px-2 py-1 text-xs">
                    In Active
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 text-xs text-blue-600">
                <span>Prev</span>
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                </div>
                <span>Next</span>
              </div>

              {/* <Carousel className="w-full max-w-xs">
          <CarouselContent>
            {Array.from({ length: 5 }).map((_, index) => (
              <CarouselItem key={index}>
                <div className="p-1">
                  <Card>
                    <CardContent className="flex aspect-square items-center justify-center p-6">
                      <span className="text-4xl font-semibold">{index + 1}</span>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel> */}
            </div>

            {/* Billing Address Card */}
            <div className="bg-background rounded-lg p-4 shadow text-InterfaceTexttitle text-[12px] 3xl:text-[0.729vw]">
              <div className=" flex items-center gap-2 mb-4">
                <Image src="/images/routing_icon.svg" width={24} height={24} alt="logo" />
                <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                  Billing Address (1/2)
                </div>
              </div>

              <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded mb-2 inline-block">
                Default
              </span>
              <div className="mb-4">
                <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Address</div>
                <div className="font-[500]">
                  MDX Technology Solutions ME LLC 1501, Al Masood Tower, Hamdan Street Abu Dhabi,
                  12121 United Arab Emirates
                </div>
              </div>
              <div className="">
                <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Contact</div>
                <div className="font-[500]">+971 11 55 1142</div>
              </div>
              <div className="flex items-center justify-between mt-4 text-xs text-blue-600">
                <span>Prev</span>
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                </div>
                <span>Next</span>
              </div>
            </div>
          </div>
        </div>

        <div className="p-4 bg-[#f5f6f9]">
          <h2 className="text-[16px] 3xl:text-[0.833vw] font-[500] mb-4">Company Profile 3</h2>
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-5">
            {/* Company Info Card */}
            <div className="bg-background rounded-lg p-4 shadow text-[12px] 3xl:text-[0.729vw]">
              <div className="grid grid-cols-12 gap-2 mb-4">
                <div className="col-span-8 flex items-center gap-2">
                  <Image src="/images/briefcase_icon.svg" width={24} height={24} alt="logo" />
                  <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                    Company profile
                  </div>
                </div>
                <div className="col-span-4 text-right text-InterfaceTextsubtitle">
                  <HoverCard>
                    <HoverCardTrigger asChild>
                      <div className="flex justify-end items-center h-full">
                        <i className="cloud-info text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                      </div>
                    </HoverCardTrigger>
                    <HoverCardContent className="w-410 bg-background">
                      <div className="flex flex-col items-start gap-4 w-[410px] p-4">
                        <div className="text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.933vw] font-[500] text-InterfaceTexttitle">
                          Details
                        </div>

                        <div className="flex items-center gap-4 w-full">
                          <div className="flex flex-col items-start gap-[2px] w-1/2">
                            <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                              Admin
                            </div>
                            <div className="text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] text-sm text-InterfaceTextdefault-500">
                              Email
                            </div>
                          </div>

                          <div className="flex flex-col items-start gap-[2px] w-1/2">
                            <div className="text-sm text-InterfaceTextdefault-400">: Joes Boz</div>
                            <div className="text-sm text-InterfaceTextdefault-400">
                              : <EMAIL>
                            </div>
                          </div>
                        </div>
                      </div>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </div>

              <div className="grid grid-cols-5 gap-4 mb-4">
                <div className="col-span-2">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Name
                  </div>
                  <div className="font-[500]">Hexalytics</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Country</div>
                  <div className="font-[500]">UAE</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Store</div>
                  <div className="font-[500]">Dubai</div>
                </div>
                <div className="col-span-1">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Role</div>
                  <div className="font-[500]">Admin</div>
                </div>
              </div>

              <div className="grid grid-cols-12 gap-4 mb-4">
                <div className="col-span-8">
                  <div className="flex text-InterfaceTextdefault font-[400] leading-[140%]">
                    Address
                  </div>
                  <div className="font-[500]">Villa 6 Zaglabi Street 99 Al Barsha Turkey</div>
                </div>

                <div className="col-span-4">
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-yellow-700 bg-yellow-200 rounded px-2 py-1 text-xs">
                    Pending
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 ">
                <div className="bg-InterfaceSurfacecomponentmuted p-[10px] 3xl:p-[0.521vw]">
                  <div className="text-InterfaceTextdefault font-semibold flex items-center justify-between mb-1 3xl:mb-2">
                    User Account &nbsp;{' '}
                    <i className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                  </div>
                  <div className="flex flex-wrap items-center justify-between">
                    <div className="font-normal">Created On:</div>
                    <div className="font-semibold">01/02/2025</div>
                  </div>
                </div>

                <div className="bg-InterfaceSurfacecomponentmuted p-[10px] 3xl:p-[0.521vw]">
                  <div className="text-InterfaceTextdefault font-semibold flex justify-between items-center mb-1 3xl:mb-2">
                    Onboarding &nbsp;{' '}
                    <i
                      className="cloud-readeye1 text-BrandSupport1pure text-[8px] xl:text-[8px] 2xl:text-[12px] 3xl:text-[0.625vw]"
                      onClick={() => setOpenCompanyProfile(true)}
                    ></i>
                  </div>
                  <div className="flex flex-wrap items-center justify-between gap-1 mb-1">
                    <div className="font-normal">Submitted On:</div>
                    <div className="font-semibold">01/02/2025</div>
                  </div>
                  <div className="flex flex-wrap items-center justify-between gap-1">
                    <div className="font-normal">Approved On:</div>
                    <div className="font-semibold">-</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Billing Profile Card */}
            <div className="relative bg-white rounded-lg p-4 shadow text-[12px] 3xl:text-[0.729vw] h-full">
              <div className="col-span-8 flex items-center gap-2 mb-4">
                <Image src="/images/receipt_icon.svg" width={24} height={24} alt="logo" />
                <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                  Billing Profile (1/6)
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Redington AC No
                  </div>
                  <div className="font-[500]">1010011802</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Code
                  </div>
                  <div className="font-[500]">CD-1109698</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-green-700 bg-green-100 rounded px-2 py-1 text-xs">
                    Active
                  </span>
                </div>
                <hr className="col-span-3 border-gray-200" />
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Redington AC No
                  </div>
                  <div className="font-[500]">1010011802</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%]">
                    Company Code
                  </div>
                  <div className="font-[500]">CD-1109698</div>
                </div>
                <div>
                  <div className="text-InterfaceTextdefault font-[400] leading-[140%] mb-1">
                    Status
                  </div>
                  <span className="text-red-700 bg-red-100 rounded px-2 py-1 text-xs">
                    In Active
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 text-xs text-blue-600">
                <span>Prev</span>
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                </div>
                <span>Next</span>
              </div>

              {/* <Carousel className="w-full max-w-xs">
          <CarouselContent>
            {Array.from({ length: 5 }).map((_, index) => (
              <CarouselItem key={index}>
                <div className="p-1">
                  <Card>
                    <CardContent className="flex aspect-square items-center justify-center p-6">
                      <span className="text-4xl font-semibold">{index + 1}</span>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel> */}
            </div>

            {/* Billing Address Card */}
            <div className="bg-background rounded-lg p-4 shadow text-InterfaceTexttitle text-[12px] 3xl:text-[0.729vw]">
              <div className=" flex items-center gap-2 mb-4">
                <Image src="/images/routing_icon.svg" width={24} height={24} alt="logo" />
                <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[500]">
                  Billing Address (1/2)
                </div>
              </div>

              <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded mb-2 inline-block">
                Default
              </span>
              <div className="mb-4">
                <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Address</div>
                <div className="font-[500]">
                  MDX Technology Solutions ME LLC 1501, Al Masood Tower, Hamdan Street Abu Dhabi,
                  12121 United Arab Emirates
                </div>
              </div>
              <div className="">
                <div className="text-InterfaceTextdefault font-[400] leading-[140%]">Contact</div>
                <div className="font-[500]">+971 11 55 1142</div>
              </div>
              <div className="flex items-center justify-between mt-4 text-xs text-blue-600">
                <span>Prev</span>
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="w-2 h-2 bg-gray-300 rounded-full"></span>
                </div>
                <span>Next</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CompanyProfiles open={openCompanyProfile} onClose={() => setOpenCompanyProfile(false)} />
    </>
  );
};

export default CompanyInformation;
