'use client';
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>lose,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import { TermsSheetProps } from '@/types/components';
import { Input, Label, Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { userService } from '@/lib/services/api';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { passwordSchema, PasswordFormValues } from '../../validations/password-schema';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface ChangePasswordProps extends TermsSheetProps {
  userData?: {
    userId?: string;
    // Add any other user data properties that might be needed
  };
}

export default function ChangePassword({ open, onClose }: ChangePasswordProps) {
  const t = useTranslations();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const { mutate: changePassword, isPending } = useMutation({
    mutationFn: (data: PasswordFormValues) =>
      userService.changePassword({
        oldPassword: data.currentPassword,
        newPassword: data.newPassword,
      }),
  });

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('changePassword')}
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <form
          onSubmit={handleSubmit(async (data) => {
            changePassword(data, {
              onSuccess: () => {
                reset();
                onClose(false);
                toast.success('Password updated successfully');
              },
              onError: (error) => {
                toast.error(error.message);
              },
            });
          })}
          className="flex-grow flex flex-col"
        >
          <div className="flex-grow overflow-y-auto space-y-3">
            <div className=" p-6 pt-[16px] 3xl:pt-[0.833vw] grid grid-cols-1 xl:grid-cols-1 md:grid-cols-1 gap-[20px]">
              <div className="flex flex-col gap-1.5">
                <Label
                  htmlFor="currentPassword"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('currentPassword')}
                </Label>
                <Input
                  id="currentPassword"
                  type="password"
                  {...register('currentPassword')}
                  className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                    errors.currentPassword ? 'border-red-500' : 'border-InterfaceStrokehard'
                  }`}
                />
                {errors.currentPassword && (
                  <p className="text-red-500 text-xs mt-1">{errors.currentPassword.message}</p>
                )}
              </div>
              <div className="flex flex-col gap-1.5">
                <Label
                  htmlFor="newPassword"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('newPassword')}
                </Label>
                <Input
                  id="newPassword"
                  type="password"
                  {...register('newPassword')}
                  className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                    errors.newPassword ? 'border-red-500' : 'border-InterfaceStrokehard'
                  }`}
                />
                {errors.newPassword && (
                  <p className="text-red-500 text-xs mt-1">{errors.newPassword.message}</p>
                )}
              </div>
              <div className="flex flex-col gap-1.5">
                <Label
                  htmlFor="confirmPassword"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('reEnterPassword')}
                </Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  {...register('confirmPassword')}
                  className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
                    errors.confirmPassword ? 'border-red-500' : 'border-InterfaceStrokehard'
                  }`}
                />
                {errors.confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">{errors.confirmPassword.message}</p>
                )}
              </div>
            </div>
          </div>

          <SheetFooter className="mr-3 mb-2">
            <Button
              type="button"
              className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
              variant="outline"
              size="sm"
              onClick={() => {
                reset();
                onClose(false);
              }}
            >
              <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              variant="skybluegradient"
              disabled={isPending}
              className="applybtn text-white lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              {isPending ? (
                'Updating...'
              ) : (
                <>
                  <i className="cloud-tick"></i>
                  {t('update')}
                </>
              )}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
