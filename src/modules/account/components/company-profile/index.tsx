import { TermsSheetProps } from '@/types';
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
  SheetClose,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function CompanyProfiles({ open, onClose }: TermsSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              Company Profiles
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4 text-sm p-4 ">
          <div className="border-b border-b-InterfaceStrokesoft ">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              First Name
            </p>
            <p className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] text-semibold">
              Cameron
            </p>
          </div>
          <div className="border-b border-b-InterfaceStrokesoft ">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Last Name
            </p>
            <p className="text-[#19212A]text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600]">
              Williamson
            </p>
          </div>
          <div className="border-b border-b-InterfaceStrokesoft py-[12px] px-0">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Designation
            </p>
            <p className="text-[#19212A]text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600]">
              CEO
            </p>
          </div>
          <div className="border-b border-b-InterfaceStrokesoft py-[12px] px-0">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Mobile Number
            </p>
            <p className="text-[#19212A]text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600]">
              9876543210
            </p>
          </div>
          <div className="border-b border-b-InterfaceStrokesoft py-[12px] px-0">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Email
            </p>
            <p className="text-[#19212A]text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600]">
              <EMAIL>
            </p>
          </div>
          <div className="border-b border-b-InterfaceStrokesoft py-[12px] px-0">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Company Name
            </p>
            <p className="text-[#19212A]text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600]">
              Test Company Name
            </p>
          </div>
          <div className="border-b border-b-InterfaceStrokesoft py-[12px] px-0">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Country of Business
            </p>
            <p className="text-[#19212A]text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600]">
              Country 1
            </p>
          </div>
          <div className="border-b border-b-InterfaceStrokesoft py-[12px] px-0">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400]">
              Reference
            </p>
            <p className="text-[#19212A]text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600]">
              Jese Leos
            </p>
          </div>

          {/* Profile Picture */}
          <div className="col-span-1">
            <p className="text-InterfaceTextsubtitle text-[16px] xl:text-[14px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400] mb-1">
              Profile Picture
            </p>
            <img
              src="/images/profile-circle.svg"
              alt="Profile"
              className="w-12 h-12 object-cover rounded"
            />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
