'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import Horizantalstackbarchart from '@/components/common/charts/horizantalstackbarchart';
import { useTranslations } from 'next-intl';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function Creditlimit() {
  const t = useTranslations();
  return (
    <div>
      <div className="flex gap-[30px] xl:gap-[30px] 3xl:gap-[1.563vw]">
        <div className="text-interfacetexttitle2 text-[20px] xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
          {t('finance')}
          <span className="text-[#7F8488] px-[10px] 3xl:px-[0.521vw]">/</span>{' '}
          <span className="font-normal">{t('creditLimitUtilization')}</span>
        </div>
        <div className=" border border-[#E5E7EB] text-[#7F8488] flex bg-InterfaceTextwhite w-[170px]">
          <Select>
            <SelectTrigger className="w-full py-1 border-InterfaceTextwhite">
              <span className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                Billing Profile:{' '}
                <span className="text-blackcolor font-[600] text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                  All
                </span>
              </span>
            </SelectTrigger>
            <SelectContent className="bg-InterfaceTextwhite text-[#7F8488]">
              <SelectGroup>
                <SelectItem value="apple">Azure</SelectItem>
                <SelectItem value="banana">Banana</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] pb-[12px]">
        *{t('asOndate')}
      </div>

      <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-[16px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div className="grid grid-cols-6 p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
          <div className="col-span-1 3xl:col-span-2">
            <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
          </div>

          <div className="col-span-7 3xl:col-span-8">
            <div className="w-full h-[100px] xl:h-[100px] 3xl:h-[6vw]">
              <Horizantalstackbarchart
                grid={{
                  top: -30,
                  bottom: 40,
                  left: 170,
                  right: 0,
                }}
                data1={[50]}
                color11={'#FFB5A5'}
                color12={'#FFB5A5'}
                data2={[50]}
                color2={'#72B35F'}
                xaxislabel={{
                  show: false,
                }}
                xaxisline={{
                  show: false,
                }}
                xaxisspliteline={{
                  show: false,
                }}
                yaxisline={{
                  show: false,
                }}
                yaxislabel={{
                  show: false,
                }}
                yaxistick={{
                  show: false,
                }}
              />
            </div>
          </div>

          <div className="absolute left-3 bottom-2">
            <div className="text-[#3C4146] text-[11px] xl:text-[12px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
              {t('totalAssigned')} *
            </div>
            <div className="text-[15px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
              USD 500,000.00
            </div>
          </div>

          <div className="absolute right-3 bottom-2 w-[50%]">
            <div className="grid grid-cols-12">
              <div className="col-span-7 xl:col-span-6 3xl:col-span-5">
                <div className="text-[#3C4146] flex items-center text-[10px] xl:text-[10px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%] gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="w-[9px] xl:w-[9px] 3xl:w-[0.677vw] h-[9px] xl:h-[9px] 3xl:h-[0.677vw] bg-[#72B35F] rounded-[2px]"></div>
                  {t('Available')} :
                </div>
                <div className="text-[#7F8488] flex items-center text-[10px] xl:text-[10px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%] gap-[4px] 3xl:gap-[0.217vw]">
                  <div className="w-[9px] xl:w-[9px] 3xl:w-[0.677vw] h-[9px] xl:h-[9px] 3xl:h-[0.677vw] bg-[#FFB5A5] rounded-[2px]"></div>
                  {t('consumed2')}
                </div>
              </div>
              <div className="col-span-5 xl:col-span-6 3xl:col-span-7">
                <div className="text-[#3C4146] text-[10px] xl:text-[10px] 2xl:text-[14px] 3xl:text-[0.833vw] font-bold leading-[140%] truncate">
                  USD 250,000.00
                </div>
                <div className="text-[#7F8488] text-[10px] xl:text-[10px] 2xl:text-[13px] 3xl:text-[0.833vw] font-normal leading-[140%] truncate">
                  USD 250,000.00
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-1 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="col-span-5 3xl:col-span-4 w-full h-[150px] 3xl:h-[8vw]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 10, // distance from the right edge
                  top: 5, // vertically centered
                  itemGap: 5,
                  itemHeight: 8,
                  itemWidth: 8,
                  data: [t('billed'), t('unbilled')],
                  textStyle: {
                    fontSize: 11,
                    color: '#7F8488',
                  },
                }}
                name={'Nightingale Chart'}
                radius={[25, 50]}
                center={['50%', '50%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 5,
                  length2: 5,
                }}
                label={{
                  position: 'outside',
                  formatter: '{c}',
                }}
                data={[
                  { value: 50000, name: t('billed'), itemStyle: { color: '#73609B' } },
                  { value: 40000, name: t('unbilled'), itemStyle: { color: '#4D9E99' } },
                ]}
              />
            </div>

            <div className="absolute left-3 bottom-2">
              <div className="text-[#3C4146] text-[12px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                {t('totalConsumed')} *
              </div>
              <div className="text-[15px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                USD 100,000.00
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-1 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="col-span-5 3xl:col-span-4 w-full h-[150px] 3xl:h-[8vw]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: -5, // distance from the right edge
                  top: 5, // vertically centered
                  itemGap: 5,
                  itemHeight: 8,
                  itemWidth: 8,
                  data: [t('notDue'), t('outstanding')],
                  textStyle: {
                    fontSize: 11,
                    color: '#7F8488',
                  },
                }}
                name={'Nightingale Chart'}
                radius={[25, 50]}
                center={['50%', '50%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 5,
                  length2: 5,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 50000, name: t('notDue'), itemStyle: { color: '#EB8A44' } },
                  { value: 40000, name: t('outstanding'), itemStyle: { color: '#F8CA16' } },
                ]}
              />
            </div>

            <div className="absolute left-3 bottom-2">
              <div className="text-[#3C4146] text-[12px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                {t('totalBilled')} *
              </div>
              <div className="text-[15px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                USD 100,000.00
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
