'use client';
import * as React from 'react';
import {
  Select,
  Sheet<PERSON><PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
  Input,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';

import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';

const schema = z.object({
  assignedCreditLimit: z.string().min(1, 'Assigned Credit Limit is required'),
  availableCreditLimit: z.string().min(1, 'Available Credit Limit is required'),
  requestedCreditLimit: z.string().min(1, 'Requested Credit Limit is required'),
  requestReason: z
    .string({ required_error: 'Request Reason is required' })
    .min(1, 'Request Reason is required'),
  partnerRemarks: z.string().min(1, 'Remarks are required'),
});

type FormData = z.infer<typeof schema>;

export default function NewRequest() {
  const t = useTranslations();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  const onSubmit = () => {};

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
          <i className="cloud-Add text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
          {t('newRequest')}
        </Button>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col h-full">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('creditLimitRevisionNewRequest')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[408px] xl:h-[490px] 2xl:h-[560px] 3xl:h-[37.25vw] space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw] xl:mb-[74px]">
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('assignedCreditLimitExisting')}
              </Label>
              <Input
                {...register('assignedCreditLimit')}
                placeholder={t('enterTheValue')}
                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
              />
              {errors.assignedCreditLimit && (
                <p className="text-[#f52d2d] text-[12px]">{errors.assignedCreditLimit.message}</p>
              )}
            </div>

            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('availableCreditLimit')}
              </Label>
              <Input
                {...register('availableCreditLimit')}
                placeholder={t('enterTheValue')}
                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
              />
              {errors.availableCreditLimit && (
                <p className="text-[#f52d2d] text-[12px]">{errors.availableCreditLimit.message}</p>
              )}
            </div>

            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('requestedAssignedCreditLimit')}
                <span className="text-[#f52d2d]"> *</span>
              </Label>
              <Input
                {...register('requestedCreditLimit')}
                placeholder={t('enterTheValue')}
                className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
              />
              {errors.requestedCreditLimit && (
                <p className="text-[#f52d2d] text-[12px]">{errors.requestedCreditLimit.message}</p>
              )}
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('requestReason')}
                <span className="text-[#f52d2d]"> *</span>
              </Label>

              <Controller
                name="requestReason"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none">
                      <SelectValue placeholder={t('selectRequestReason')} />
                    </SelectTrigger>
                    <SelectContent className="rounded-none bg-[#FFF] border-none">
                      <SelectGroup className="text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                        <SelectItem value="completed">Reason 1</SelectItem>
                        <SelectItem value="pending">Reason 2</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.requestReason && (
                <p className="text-[#f52d2d] text-[12px]">{errors.requestReason.message}</p>
              )}
            </div>

            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('partnerRemarks')}
              </Label>
              <Textarea
                {...register('partnerRemarks')}
                placeholder={t('enterRemarks')}
                className="border border-InterfaceStrokedefault rounded-none"
              />
              {errors.partnerRemarks && (
                <p className="text-[#f52d2d] text-[12px]">{errors.partnerRemarks.message}</p>
              )}
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
                {t('characterLimit50')}
              </div>
            </div>
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault bg-white">
            <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <SheetClose asChild>
                  <button
                    type="button"
                    className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
              3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                    <span className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                      {t('cancel')}
                    </span>
                  </button>
                </SheetClose>
                <Button
                  type="submit"
                  className="newGreenBtn text-interfacesurfacecomponent rounded-none !border-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
                >
                  <i className="cloud-save-2"></i>
                  {t('submit')}
                </Button>
              </div>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
