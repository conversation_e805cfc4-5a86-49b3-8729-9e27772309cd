import React from 'react';
import Creditlimit from '../component/creditlimit';
import Link from 'next/link';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import CreditlimitTable from '../component/creditlimit-table';
import NewRequest from '../component/new-request';
import { useTranslations } from 'next-intl';
import { Roboto } from 'next/font/google';
const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function CreditlimitTemplate() {
  const t = useTranslations();

  return (
    <div className={roboto.className}>
      <div className=" h-full px-[32px] py-[20px] 3xl:px-[1.667vw] 3xl:py-[1.042vw] bg-[#F6F7F8]">
        <div className="">
          <Creditlimit />
        </div>

        <div className="flex gap-[2px] my-[20px] xl:my-[20px] 3xl:my-[1.042vw]">
          {/* <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
                  <i className="cloud-Add text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  New Request
                </Button> */}
          <NewRequest />

          <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
            <i className="cloud-download text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
            {t('download')}
          </Button>

          <Link href="/account">
            <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
              <i className="cloud-back text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
              {t('back')}
            </Button>
          </Link>
        </div>

        <div>
          <CreditlimitTable />
        </div>
      </div>
    </div>
  );
}
