import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import { Input } from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function PaymentTermsTable() {
  const t = useTranslations();

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'brand',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brand')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('brand')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('brand')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'brandcategory',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brandCategory')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('brandCategory')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('brandcategory')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'paymentterms',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('paymentTerms')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('paymentTerms')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('paymentterms')}</div>,

      minSize: 800,
    },
  ];

  type User = {
    brand: string;
    brandcategory: string;
    paymentterms: string;
  };

  const registrationdata = [
    {
      brand: 'AWS',
      brandcategory: 'AWS',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Microsoft CSP ',
      brandcategory: 'Azure RI',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Microsoft CSP ',
      brandcategory: 'Cisco',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Cisco ',
      brandcategory: 'Cisco',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Microsoft CSP ',
      brandcategory: 'Software Subscription',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Microsoft CSP ',
      brandcategory: 'Software Subscription',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Microsoft CSP ',
      brandcategory: 'Azure Plan',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Azure Plan ',
      brandcategory: 'Google Cloud Platform',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Azure Plan ',
      brandcategory: 'Google Workspace Commitment',
      paymentterms: 'R032 - Advance Payment',
    },
    {
      brand: 'Azure Plan ',
      brandcategory: 'Google Workspace Flexible',
      paymentterms: 'R032 - Advance Payment',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('listOfRecords')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="  flex gap-[24px] xl:gap-[24px] 3xl:gap-[1.25vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            <div className="flex items-center gap-2 cursor-pointer">
              <i className="cloud-filtericon"></i>
              {t('filter')}
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
        </div>
        <TablePagination />
      </div>
    </>
  );
}
