'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import Stackbarchart from '@/components/common/charts/stackbarchart';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function UnbilledChargesCards() {
  const t = useTranslations();
  return (
    <div>
      <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-2 2xl:col-span-2 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="col-span-4 2xl:col-span-4 3xl:col-span-4 w-full h-[110px] 3xl:h-[6.885vw] text-[10px] 2xl:text-[9px] 3xl:text-[0.52vw]">
              <Stackbarchart
                grid={{
                  left: 50,
                  right: 0,
                  top: 8,
                  bottom: 30,
                }}
                legends={{
                  show: true,
                  left: 'center',
                  bottom: -6,
                  itemWidth: 10,
                  itemHeight: 10,
                  textStyle: {
                    fontSize: 10,
                  },
                }}
                data={['Cust.1', 'Cust.2', 'Cust.3', 'Cust.4', 'Cust.5']}
                xaxistick={{
                  show: false,
                }}
                xaxisline={{
                  show: true,
                  lineStyle: {
                    color: '#C9D3DB',
                  },
                }}
                xaxislabel={{
                  color: '#7F8488',
                  fontSize: 8,
                }}
                yaxisname={`${t('value')} (USD)`}
                yaxisnametextstyle={{
                  fontSize: 9,
                  color: '#7F8488',
                  fontWeight: 'normal',
                }}
                yaxislocation={'middle'}
                yaxisnamegap={30}
                min={0}
                max={800}
                yaxislabel={{
                  color: '#7F8488',
                  fontSize: 8,
                }}
                yaxissplitline={{
                  show: true,
                  lineStyle: {
                    color: '#C9D3DB',
                    type: 'dashed',
                  },
                }}
                data1={[550, 220, 200, 280, 580]}
                color1={'#1570EF'}
                color2={'#3696FB'}
                color3={'#F8B720'}
                color4={'#FACE4F'}
                data2={[300, 220, 430, 150, 150]}
                stackname1={t('billed')}
                stackname2={t('unbilled')}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] xl:text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                {t('totalUnbilledUsd')}*
              </div>
              <div className="text-[13px] xl:text-[13px] 2xl:text-[17px] 3xl:text-[0.99vw] font-semibold truncate">
                100,000.00
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-12  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full h-[110px] 3xl:h-[6.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: -10,
                  top: 10,
                  itemGap: 10,
                  itemHeight: 8,
                  itemWidth: 8,
                  data: [t('usageBased'), t('seatBased')],
                  textStyle: {
                    fontSize: 10,
                    color: '#7F8488',
                  },
                }}
                name={'Nightingale Chart'}
                radius={[20, 40]}
                center={['50%', '57%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 3,
                  length2: 3,
                }}
                label={{
                  formatter: '{c}',
                  fontSize: 9,
                }}
                data={[
                  { value: 5000, name: t('usageBased'), itemStyle: { color: '#4F6484' } },
                  { value: 5000, name: t('seatBased'), itemStyle: { color: '#3696FB' } },
                ]}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%] w-[140px] md:w-full lg:w-[128px] xl:w-[110px] 2xl:w-[130px] 3xl:w-[6.729vw]">
                {t('totalUnbilledByChargeTypeUsd')}
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                200,000.00
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-12  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full h-[110px] 3xl:h-[6.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: -10,
                  top: 28,
                  itemGap: 5,
                  itemHeight: 8,
                  itemWidth: 8,
                  data: [t('usageBased'), t('seatBased')],
                  textStyle: {
                    fontSize: 10,
                    color: '#7F8488',
                  },
                }}
                name={'Nightingale Chart'}
                radius={[20, 40]}
                center={['45%', '62%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 2,
                  length2: 2,
                }}
                label={{
                  formatter: '{c}',
                  fontSize: 9,
                }}
                data={[
                  { value: 100000, name: t('usageBased'), itemStyle: { color: '#D67309' } },
                  { value: 100000, name: t('seatBased'), itemStyle: { color: ' #FACE4F' } },
                ]}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%] w-[140px] md:w-full lg:w-[116px] xl:w-[110px] 2xl:w-[145px] 3xl:w-[7.729vw]">
                {t('totalUnbilledByBrandCategoryUsd')} *
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                200,000.00
              </div>
            </div>
            <div className="absolute top-2 right-2">
              <Select>
                <SelectTrigger className="w-[80px] 2xl:w-[5.208vw] 3xl:w-[5.208vw] py-1 border-[#E5E7EB] text-[#7F8488]">
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="bg-[#fff] text-[#7F8488]">
                  <SelectGroup>
                    <SelectItem value="apple">Azure</SelectItem>
                    <SelectItem value="banana">Banana</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
