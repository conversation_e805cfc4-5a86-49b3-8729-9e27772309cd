'use client';
import * as React from 'react';
import { Input, Label } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  She<PERSON><PERSON><PERSON>le,
  SheetHeader,
  Sheet,
  She<PERSON><PERSON>lose,
  She<PERSON><PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function Information() {
  return (
    <form>
      <Sheet>
        <SheetTitle></SheetTitle>
        <SheetTrigger>
          <div className=" flex items-center gap-2 text:InterfaceTexttitle hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
            <div className="">
              <i className="cloud-filtericon text:InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
            </div>
            <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTexttitle font-medium">
              Filter
            </p>
          </div>
        </SheetTrigger>
        <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[900px] xl:max-w-[900px] 3xl:max-w-[46.875vw] p-[0px]">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                Information
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="relative bg-interfacesurfacecomponent">
                <div className="accordian-styles space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw]">
                  <div className=" ">
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="authorized-signatory">
                        <AccordionTrigger className="p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft bg-[#d4d2d2]">
                          <div className="w-full flex justify-between items-center">
                            <div className="text-left">
                              Authorized Signatory*
                              <span className="text-InterfaceTextsubtitle text-[12px] ml-2 font-normal">
                                (Min 1, Max 3 contacts)
                              </span>
                            </div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
                          <div className="3xl:pt-[0.833vw] space-y-[20px]">
                            {[1, 2, 3].map((_, index) => (
                              <div key={`authorized-${index}`} className="relative">
                                <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px] p-4 bg-gray-50 rounded-lg">
                                  <div className="flex flex-col gap-1.5">
                                    <Label>First Name *</Label>
                                    <Input
                                      placeholder="First Name"
                                      disabled
                                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                                    />
                                  </div>
                                  <div className="flex flex-col gap-1.5">
                                    <Label>Last Name *</Label>
                                    <Input
                                      placeholder="Last Name"
                                      disabled
                                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                                    />
                                  </div>
                                  <div className="flex flex-col gap-1.5">
                                    <Label>Email *</Label>
                                    <Input
                                      placeholder="Email"
                                      disabled
                                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                                    />
                                  </div>
                                  <div className="flex flex-col gap-1.5">
                                    <Label>Mobile Number *</Label>
                                    <Input
                                      placeholder="Mobile Number"
                                      disabled
                                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                                    />
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
      {/* <div className="relative bg-interfacesurfacecomponent">
        <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
            Business Information
          </div>
          <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
            * Fields are Mandatory
          </div>
        </div>

        <div className="py-[32px] xl:py-[32px] 2xl:py-[40px] 3xl:py-[1.667vw] text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500]">
          Key Contacts
        </div>

        <div className="accordian-styles space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw]">
          {renderContactSection('Director/Owner', true)}
          {renderContactSection('Sales', false, true)}
          {renderContactSection('Accounts/Operations Executive', false, true)}

          <div className="p-[16px] xl:p-[18px] 2xl:p-[18px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="authorized-signatory">
                <AccordionTrigger>
                  <div className="w-full flex justify-between items-center">
                    <div className="text-left">
                      Authorized Signatory*
                      <span className="text-InterfaceTextsubtitle text-[12px] ml-2 font-normal">
                        (Min 1, Max 3 contacts)
                      </span>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="3xl:pt-[0.833vw] space-y-[20px]">
                    {[1, 2, 3].map((_, index) => (
                      <div key={`authorized-${index}`} className="relative">
                        <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px] p-4 bg-gray-50 rounded-lg">
                          <div className="flex flex-col gap-1.5">
                            <Label>First Name *</Label>
                            <Input
                              placeholder="First Name"
                              disabled
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                            />
                          </div>
                          <div className="flex flex-col gap-1.5">
                            <Label>Last Name *</Label>
                            <Input
                              placeholder="Last Name"
                              disabled
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                            />
                          </div>
                          <div className="flex flex-col gap-1.5">
                            <Label>Email *</Label>
                            <Input
                              placeholder="Email"
                              disabled
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                            />
                          </div>
                          <div className="flex flex-col gap-1.5">
                            <Label>Mobile Number *</Label>
                            <Input
                              placeholder="Mobile Number"
                              disabled
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none bg-gray-100 cursor-not-allowed"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </div> */}
    </form>
  );
}
