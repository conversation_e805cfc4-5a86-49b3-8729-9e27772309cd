'use client';
import {
  <PERSON><PERSON>,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import React from 'react';
import UnbilledChargesTable from '../components/unbilled-charges-table';
import UnbilledChargesCards from '../components/unbilled-charges-cards';
import { useTranslations } from 'next-intl';
import { Roboto } from 'next/font/google';
const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function UnbilledChargesTemplate() {
  const t = useTranslations();
  return (
    <div className={roboto.className}>
      <div className=" h-full">
        <div className="relative h-full px-[32px] py-[20px] 3xl:px-[1.667vw] 3xl:py-[1.042vw] bg-[#F6F7F8]">
          <div className="">
            <div className="flex flex-col pb-[10px] xl:pb-[10px] 3xl:pb-[0.525vw]">
              <div className="flex gap-[30px] xl:gap-[30px] 3xl:gap-[1.563vw]">
                <div className="text-interfacetexttitle2 text-[20px] xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                  {t('finance')} <span className="text-InterfaceTextsubtitle font-[400]">/</span>{' '}
                  <span className="font-[400]">{t('unbilledCharges')}</span>
                </div>
                <div className=" border border-[#E5E7EB] text-[#7F8488] flex bg-InterfaceTextwhite w-[170px]">
                  <Select>
                    <SelectTrigger className="w-full py-1 border-InterfaceTextwhite">
                      <span className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        Billing Profile:{' '}
                        <span className="text-blackcolor font-[600] text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          All
                        </span>
                      </span>
                    </SelectTrigger>
                    <SelectContent className="bg-InterfaceTextwhite text-[#7F8488]">
                      <SelectGroup>
                        <SelectItem value="apple">Azure</SelectItem>
                        <SelectItem value="banana">Banana</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                *{t('asOndate')}
              </div>
            </div>
            <div>
              <UnbilledChargesCards />
            </div>
            <div className="flex gap-[2px] my-[20px] xl:my-[20px] 3xl:my-[1.042vw]">
              <Link href="">
                <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
                  <i className="cloud-download text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  {t('download')}
                </Button>
              </Link>
              <Link href="/account">
                <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
                  <i className="cloud-back text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  {t('back')}
                </Button>
              </Link>
            </div>

            <div className="bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
              <div>
                <UnbilledChargesTable />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
