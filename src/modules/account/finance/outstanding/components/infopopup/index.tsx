'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import { DataTable } from '@/components/common/ui/data-table';
import { useTranslations } from 'next-intl';

export default function Infopopup() {
  const t = useTranslations();
  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'subscriptionname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('subscriptionName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('subscriptionName')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: () => {
        return (
          <>
            <div>
              <div className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[14px] font-medium leading-[140%]">
                Trend micro Worry-free service - worry-free <br />
                service- Monthly Plan
              </div>
              <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[14px] font-normal leading-[140%] ">
                ca55755e-01ac-4939-c788-f050034b72b1
              </div>
            </div>
          </>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'description',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('description')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('description')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: () => {
        return (
          <>
            <div>
              <div className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[14px] font-medium leading-[140%]">
                Commercial Marketplace - Commercial <br />- Commercial - Monthly Subscription Charge
              </div>
              <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[14px] font-normal leading-[140%] ">
                20-4-2025 to 19-4-2025
              </div>
            </div>
          </>
        );
      },

      minSize: 500,
    },
    {
      accessorKey: 'qty',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('qty')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('qty')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('qty')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'amount',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('amount')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('amount')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('amount')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'vat',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('vat')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('vat')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('vat')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'netamt',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('netAmount')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('netAmount')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('netamt')}</div>,

      minSize: 400,
    },
  ];

  type User = {
    subscriptionname: string;
    description: string;
    qty: number;
    amount: string;
    vat: string;
    netamt: string;
  };

  const registrationdata = [
    {
      subscriptionname: '356000023412',
      description: '1122345',
      qty: 2,
      amount: 'USD 515.78',
      vat: '25.79',
      netamt: 'USD 541.57',
    },
  ];
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className="">
          <div className="w-full px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
            <i className="cloud-info text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
            {t('info')}
          </div>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[95%] xl:max-w-[95%] 3xl:max-w-[62vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('information')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[500px] lg:h-[500px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="grid grid-cols-1 xl:grid-cols-3 md:grid-cols-1 gap-[20px]  space-y-3">
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] mt-[12px]">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('invoiceNumber')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    6010123617
                  </div>
                </div>
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('invoiceDate2')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    01/04/2025
                  </div>
                </div>
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('invoiceValue')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    USD 541.57
                  </div>
                </div>
              </div>
              <div className="p-[12px] xl:p-[12px] 3xl:p-[0.625vw] text-InterfaceTexttitle text-[16px] lg:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] mt-[10px] lg:mt-[10px] xl:mt-[10px] 3xl:mt-[0.521vw]">
                {t('commercialMarketplaceTest')}
              </div>
              <div className="overflow-x-auto">
                <DataTable
                  data={registrationdata}
                  columns={registrationColumns}
                  withCheckbox={false}
                />
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
