'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import ActionInfotable from '../action-info-table';
import ViewFilePopup from '../../../../../billing/invoices/components/file-popup';
import InsightsPopup from '../../../../../billing/invoices/components/insights-popup';
import { useTranslations } from 'next-intl';

export default function DocumentNoPopup() {
  const t = useTranslations();
  const selectedId = '6010123110';
  return (
    <form>
      <Sheet>
        <SheetTitle></SheetTitle>
        <SheetTrigger>
          <div className="text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer underline">
            6010123110
          </div>
        </SheetTrigger>
        <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[900px] xl:max-w-[900px] 3xl:max-w-[46.875vw] p-[0px]">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('invoice')} #G088079077
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[670px] lg:h-[685px] xl:h-[800px] 2xl:h-[815px] 3xl:h-[50.25vw]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="grid grid-cols-3 gap-[16px] 3xl:gap-[0.833vw]">
                <div className="bg-BrandNeutral50 border border-InterfaceStrokesoft p-[16px] 3xl:p-[0.833vw] flex flex-col gap-[16px] 3xl:gap-[16px]">
                  <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                    {t('value')}
                  </div>
                  <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                    <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                      AED 61,332.5
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('due')} <span className="font-[400]">10/08/2025</span>
                    </div>
                  </div>
                </div>
                <div className="bg-BrandNeutral50 border border-InterfaceStrokesoft p-[16px] 3xl:p-[0.833vw] flex flex-col gap-[16px] 3xl:gap-[16px]">
                  <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                    {t('billingSummary')}
                  </div>
                  <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('preTaxTotal')} : <span className="font-[400]">AED 58,411.55</span>
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('tax')} <span className="font-[400]">AED 2,920.59</span>
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('grandTotal')} : <span className="font-[400]">AED 61,332.15</span>
                    </div>
                  </div>
                </div>
                <div className="bg-BrandNeutral50 border border-InterfaceStrokesoft p-[16px] 3xl:p-[0.833vw] flex flex-col gap-[16px] 3xl:gap-[16px]">
                  <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                    {t('billingDetails')}
                  </div>
                  <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('invoiceDate2')} <span className="font-[400]">12/05/2025</span>
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('dueDate')} : <span className="font-[400]">10/08/2025</span>
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('paymentTerm')}
                      <span className="font-[400]">R0009-90 Days from Invoice date</span>
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('currency')} : <span className="font-[400]">AED</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div>{t('chargeDetails')}</div>
                <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                  <ViewFilePopup selectedId={selectedId} />
                  <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-transparent bg-[linear-gradient(180deg,_#FEFEFE_0%,_#F6F8FA_100%)] border border-InterfaceStrokesoft flex gap-[8px] 3xl:gap-[0.417vw] rounded-none text-InterfaceTextdefault font-[500]">
                    <i className="cloud-document-download1 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                    {t('downloadCsv')}
                  </Button>
                  <InsightsPopup selectedId={selectedId} />
                </div>
              </div>

              <div>
                <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="grid grid-cols-4 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('vendor')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Microsoft
                      </div>
                    </div>
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('brand')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Microsoft CSP
                      </div>
                    </div>
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('brandCategory')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Azure Plan
                      </div>
                    </div>
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('billType')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Monthly
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('customerName')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Paynow Azure
                      </div>
                    </div>
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('customerId')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        2144b988-db9c-4d56-b34b-36bbec18457d
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('invoiceDescription')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        Test description...
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <ActionInfotable />
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </form>
  );
}
