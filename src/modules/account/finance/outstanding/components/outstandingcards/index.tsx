'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import Barchart from '@/components/common/charts/barchart4';
import { useTranslations } from 'next-intl';

export default function OutstandingCards() {
  const t = useTranslations();
  return (
    <div>
      <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-12   p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full h-[120px] xl:h-[120px]  3xl:h-[6.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 0, // distance from the right edge
                  top: 0, // vertically centered
                  itemGap: 10,
                  itemHeight: 8,
                  itemWidth: 8,
                  data: [t('overdue'), t('notDue')],
                  textStyle: {
                    fontSize: 10,
                    color: '#7F8488',
                  },
                }}
                name={'Nightingale Chart'}
                radius={[20, 40]}
                center={['50%', '50%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 2,
                  length2: 2,
                }}
                label={{
                  formatter: '{c}',
                  fontSize: 10,
                }}
                data={[
                  { value: 100000, name: t('overdue'), itemStyle: { color: '#1570EF' } },
                  { value: 100000, name: t('notDue'), itemStyle: { color: ' #FACE4F' } },
                ]}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                {t('totalReceivablesUsd')}*
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                200,000.00
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-12  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="col-span-8 3xl:col-span-8 w-full h-[120px] 3xl:h-[6.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: -10,
                  top: 28,
                  itemGap: 2,
                  itemHeight: 8,
                  itemWidth: 8,
                  data: [t('aws'), t('microsoft'), t('google'), t('others')],
                  textStyle: {
                    fontSize: 8,
                    color: '#7F8488',
                  },
                }}
                name={'Nightingale Chart'}
                radius={[22, 45]}
                center={['45%', '48%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 8,
                  length2: 8,
                }}
                label={{
                  formatter: '{c}',
                  fontSize: 10,
                }}
                data={[
                  { value: 33000, name: t('aws'), itemStyle: { color: '#73609B' } },
                  { value: 27000, name: t('microsoft'), itemStyle: { color: '#4D9E99' } },
                  { value: 30000, name: t('google'), itemStyle: { color: '#418E4C' } },
                  { value: 26000, name: t('others'), itemStyle: { color: '#4F6484' } },
                ]}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%] w-full lg:w-[122px] xl:lg:w-[122px] 2xl:lg:w-[122px] 3xl:w-[8.354vw]">
                {t('totalOverdueByBrandUsd')}
                {/* <br />
                   (USD)* */}
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                USD 100,000.00
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-3 lg:col-span-2 2xl:col-span-2 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[25px] xl:text-[25px] 3xl:text-[1.51vw]"></i>
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%] w-full lg:w-[120px] xl:w-[118px] 2xl:w-[122px] 3xl:w-[8.354vw]">
                {t('overdueAgeingUsd')}
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                100,000.00
              </div>
            </div>
            <div className="col-span-3 lg:col-span-4 2xl:col-span-4 3xl:col-span-4 w-full h-[120px] 3xl:h-[6.885vw]">
              <Barchart />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
