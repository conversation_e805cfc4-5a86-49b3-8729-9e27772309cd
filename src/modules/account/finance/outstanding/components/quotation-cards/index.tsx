'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import QuotationBarchart from '@/components/common/charts/quotationbarchart';

export default function QuotationCards() {
  return (
    <div>
      <div className="grid grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-1 3xl:col-span-2">
              <i className="cloud-folder text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-5 3xl:col-span-4 w-full h-[110px] 3xl:h-[5.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: 0,
                  top: 23,
                  itemGap: 20,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['Partner', 'Redington', 'Vendor'],
                }}
                name={'Nightingale Chart'}
                radius={[19, 50]}
                center={['35%', '49%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 1,
                  length2: 1,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 65, name: 'Partner', itemStyle: { color: '#73609B' } },
                  { value: 50, name: 'Redington', itemStyle: { color: '#4D9E99' } },
                  { value: 50, name: 'Vendor', itemStyle: { color: '#5BB559' } },
                ]}
              />
            </div>

            <div className="absolute left-3 bottom-2">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                Total Quotes*
              </div>
              <div className="text-[34px] 2xl:text-[34px] 3xl:text-[1.475vw] font-semibold truncate text-InterfaceTextdefault">
                175
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-1 3xl:col-span-2">
              <i className="cloud-note-remove text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-5 3xl:col-span-4 w-full h-[110px] 3xl:h-[5.885vw]">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: -5,
                  top: 23,
                  itemGap: 20,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['Valid', 'Expired'],
                }}
                name={'Nightingale Chart'}
                radius={[18, 48]}
                center={['40%', '45%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 1,
                  length2: 1,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 10, name: 'Valid', itemStyle: { color: '#1570EF' } },
                  { value: 8, name: 'Expired', itemStyle: { color: '#FACE4F' } },
                ]}
              />
            </div>

            <div className="absolute left-3 bottom-2">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                Awaiting Action*
              </div>
              <div className="text-[34px] 2xl:text-[34px] 3xl:text-[1.475vw] font-semibold truncate text-InterfaceTextdefault">
                15
              </div>
            </div>
          </div>
        </div>

        <div className=" gap-[5px] 3xl:gap-[0.625vw] p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)]">
          <div className="w-full h-[90px]">
            <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold">
              Quotes by Status*
            </div>
            <QuotationBarchart />
          </div>
        </div>
      </div>
    </div>
  );
}
