import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import StatementOfAccountFilter from '../statement-account-filter';
import ActionInfo from '../action-info';
import DocumentNoPopup from '../document-no-popup';
import { useTranslations } from 'next-intl';

export default function StatementAccountstable() {
  const t = useTranslations();
  const [fromDate, setFromDate] = React.useState<Date | undefined>(new Date('2025-01-01'));
  const [toDate, setToDate] = React.useState<Date | undefined>(new Date('2025-01-03'));

  const clearAll = () => {
    setFromDate(undefined);
    setToDate(undefined);
  };

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'documentno',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('documentNo')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('documentNo')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: () => {
        return <DocumentNoPopup />;
      },

      minSize: 400,
    },
    {
      accessorKey: 'referenceno',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('referenceNo')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('referenceNo')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('referenceno')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'documentdate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('documentDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder={t('documentDate')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('documentdate')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'duedate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('dueDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('dueDate')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('duedate')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'brandcategory',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brandCategory')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('brandCategory')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('brandcategory')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'customers',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('customers')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('customers')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('customers')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'currency',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('currency')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('currency')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('currency')}</div>,

      minSize: 600,
    },
    {
      accessorKey: 'debt',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('debt')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('debt')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('debt')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'credit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('credit')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('credit')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('credit')}</div>,

      minSize: 600,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('status')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Due: {
            colorClass: 'bg-[#FFFBEB] text-[#D67309] border-[#F8B720]',
          },
          Overdue: {
            colorClass: 'bg-[#FFF3F0] text-[#D42600] border-[#FFB5A5]',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800 border-gray-300',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[6px] xl:py-[6px] 3xl:py-[0.357vw] px-[10px] rounded-[2px] text-[15px] xl:text-[15px] 3xl:text-[0.781vw] font-semibold border  ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title={t('info')}
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[80px] xl:w-[80px] 3xl:w-[3.833vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <ActionInfo />
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    documentno: string;
    referenceno: string;
    documentdate: string;
    duedate: string;
    brandcategory: string;
    customers: string;
    currency: string;
    debt: string;
    credit: string;
    status: string;
  };

  const registrationdata = [
    {
      documentno: '356000023412',
      referenceno: '1122345',
      documentdate: '15/05/2024',
      duedate: '20/02/2025',
      brandcategory: 'CSP',
      customers: 'Customer Name 1',
      currency: 'USD',
      debt: '500',
      credit: '0',
      status: 'Due',
    },
    {
      documentno: '356000023412',
      referenceno: '1122345',
      documentdate: '15/05/2024',
      duedate: '20/02/2025',
      brandcategory: 'Azure RI',
      customers: 'Customer Name 2',
      currency: 'USD',
      debt: '500',
      credit: '0',
      status: 'Overdue',
    },
    {
      documentno: '356000023412',
      referenceno: '1122345',
      documentdate: '15/05/2024',
      duedate: '20/02/2025',
      brandcategory: 'Azure Plan',
      customers: 'Customer Name 3',
      currency: 'USD',
      debt: '500',
      credit: '0',
      status: 'Due',
    },
    {
      documentno: '356000023412',
      referenceno: '1122345',
      documentdate: '15/05/2024',
      duedate: '20/02/2025',
      brandcategory: 'CSP NCE',
      customers: 'Customer Name 4',
      currency: 'USD',
      debt: '0',
      credit: '500',
      status: 'Overdue',
    },
    {
      documentno: '356000023412',
      referenceno: '1122345',
      documentdate: '15/05/2024',
      duedate: '20/02/2025',
      brandcategory: 'AWS',
      customers: 'Customer Name 5',
      currency: 'USD',
      debt: '0',
      credit: '500',
      status: 'Due',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('listOfRecords')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%]">
              {t('showingRecords')}
            </div>
          </div>
          <div className=" flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[10px] xl:top-[10px] 3xl:top-[10px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            {/* <div className="flex items-center gap-2 cursor-pointer">
              <i className="cloud-filtericon"></i>Filter
            </div> */}
            <StatementOfAccountFilter />
          </div>
        </div>

        <div className="overflow-x-auto">
          <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] text-sm font-normal">
            {/* From Date */}
            <div className="flex items-center bg-[#F4F5F7] px-2 py-[6px] rounded-sm">
              <span className="text-[#6B7280]"> {t('fromDate')}:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {fromDate ? format(fromDate, 'dd-MM-yyyy') : 'Select Date'}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={fromDate} onSelect={setFromDate} />
                </PopoverContent>
              </Popover>
              {fromDate && (
                <button onClick={() => setFromDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              )}
            </div>

            {/* To Date */}
            <div className="flex items-center bg-[#F4F5F7] px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.366vw] rounded-sm">
              <span className="text-[#6B7280]"> {t('toDate')} :</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {toDate ? format(toDate, 'dd-MM-yyyy') : 'Select Date'}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={toDate} onSelect={setToDate} />
                </PopoverContent>
              </Popover>
              {toDate && (
                <button onClick={() => setToDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              )}
            </div>

            {/* Clear All */}
            {(fromDate || toDate) && (
              <button
                onClick={clearAll}
                className="flex items-center text-[#8B8B8B] font-[400] hover:text-black"
              >
                <i className="cloud-closecircle mr-[4px]"></i>
                Clear All
              </button>
            )}
          </div>
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
        </div>
        <TablePagination />
      </div>
    </>
  );
}
