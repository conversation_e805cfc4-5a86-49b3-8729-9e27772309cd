'use client';
import { Button, Input } from '@redington-gulf-fze/cloudquarks-component-library';
import React, { useState } from 'react';
import { ProductListingTopPagination } from '../components/product-listing-pagination';
import CategoryCards from '../components/category-cards';
import GridView from '../components/grid-view';
import ListView from '../components/list-view';
import { ProductListingBottomPagination } from '../components/product-listing-pagination/bottom-pagination';
import MarketplaceSidebar from '../components/sidebar';
import { RecentPurchases } from '../components/recent-purchases';
import { Roboto } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function Marketplace() {
  const t = useTranslations();

  const [view, setView] = useState('grid');

  return (
    <div className={roboto.className}>
      <div className="grid grid-cols-11 3xl:grid-cols-12">
        <div className="col-span-11 xl:col-span-2 3xl:col-span-2">
          <MarketplaceSidebar />
        </div>
        <div className="col-span-11 xl:col-span-9 3xl:col-span-10 px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] pt-[10px] xl:pt-[18px] 2xl:pt-[18px] 3xl:pt-[1.042vw] pb-0">
          <RecentPurchases />
          <div className="flex items-center justify-between gap-2 border-b border-InterfaceStrokesoft pb-[18px] 3xl:pb-[1.042vw]">
            <div className="text-InterfaceTextdefault text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%]">
              {t('productListing')} (2,100)
            </div>
            <div className="flex items-center gap-[22px] 3xl:gap-[1.25vw]">
              <div className="relative">
                <Input
                  type="text"
                  placeholder={t('searchProductNameSkuID')}
                  className="w-[400px] xl:w-[400px] 2xl:w-[609px] 3xl:w-[31.719vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
                />
                <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
              </div>
              <div className="flex">
                <div
                  onClick={() => setView('list')}
                  className={`${view === 'list' ? 'text-interfacesurfacecomponent bg-InterfaceSurfacehcinverse' : 'text-InterfaceTexttitle bg-background cardShadow'} w-[40px] h-[40px] 3xl:w-[2.083vw] 3xl:h-[2.083vw]  flex items-center justify-center cursor-pointer`}
                >
                  <i className="cloud-textalign"></i>
                </div>
                <div
                  onClick={() => setView('grid')}
                  className={`${view === 'grid' ? 'text-interfacesurfacecomponent bg-InterfaceSurfacehcinverse' : 'text-InterfaceTexttitle bg-background cardShadow'} w-[40px] h-[40px] 3xl:w-[2.083vw] 3xl:h-[2.083vw]  flex items-center justify-center cursor-pointer`}
                >
                  <i className="cloud-grid"></i>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap items-center justify-between border-b border-b-InterfaceStrokesoft">
            <div className="flex items-center py-[14px] 3xl:py-[0.729vw] gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-lightgraytext text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                {t('appliedFilters')}:
              </div>
              <div className="bg-background px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.208vw] flex items-center gap-2">
                <div className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('brands')}{' '}
                </div>
                <i className="cloud-closecircle text-InterfaceTextsubtitle cursor-pointer"></i>
              </div>
              <div className="bg-background px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.208vw] flex items-center gap-2">
                <div className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('categories')}{' '}
                </div>
                <i className="cloud-closecircle text-InterfaceTextsubtitle cursor-pointer"></i>
              </div>
              <div className="bg-background px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.208vw] flex items-center gap-2">
                <div className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('pricing')}{' '}
                </div>
                <i className="cloud-closecircle text-InterfaceTextsubtitle cursor-pointer"></i>
              </div>
              <Button className="bg-transparent px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.208vw] flex items-center gap-2 cursor-pointer">
                <i className="cloud-closecircle text-InterfaceTextsubtitle cursor-pointer"></i>
                <div className="text-lightgraytext text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('clearAll')}{' '}
                </div>
              </Button>
            </div>
            <ProductListingTopPagination />
          </div>

          <div className="pt-[28px] 2xl:pt-[30px] 3xl:pt-[1.667vw]">
            <div className="text-InterfaceTextdefault text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%] mb-[18px] 3xl:mb-[1.042vw]">
              {t('exploreProductsbyCategories')}
            </div>
            <div className="grid grid-cols-4 xl:grid-cols-5 2xl:grid-cols-8 gap-[16px] 3xl:gap-[0.833vw]">
              <CategoryCards />
            </div>
          </div>
          <div className="pt-[28px] 2xl:pt-[30px] 3xl:pt-[1.667vw] ">
            <div className="text-InterfaceTextdefault text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%] mb-[18px] 3xl:mb-[1.042vw]">
              {t('productivityCollaboration')} (100/2,100)
            </div>
            <div className="h-[700px] 3xl:h-[36.458vw] overflow-y-auto scrollbar-hide">
              {view === 'grid' && (
                <div className="grid grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-[20px] 3xl:gap-[1.24vw]">
                  <GridView />
                </div>
              )}
              {view === 'list' && (
                <div className="space-y-[16px] 3xl:space-y-[0.833vw]">
                  <ListView />
                </div>
              )}
            </div>
          </div>
          <ProductListingBottomPagination />
        </div>
      </div>
    </div>
  );
}
