'use client';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>eader,
  Sheet,
  She<PERSON><PERSON>lose,
  Sheet<PERSON>ontent,
  Input,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import { Search } from 'lucide-react';
import CartPage from './cart-cart';
import { CommanSheetProps } from '@/types';
import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

export default function OpencartPopup({ open, onClose }: CommanSheetProps) {
  const t = useTranslations();
  const endCustomerOptions: Select2Option[] = [
    { label: t('unitedArabEmirates'), value: 'uae' },
    { label: t('india'), value: 'india' },
    { label: t('singapore'), value: 'singapore' },
  ];

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[1400px] xl:max-w-[1536px] 3xl:max-w-[80vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('openCartsSelect')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] relative ">
          <div className="grid grid-cols-3 gap-[28px] lg:gap-[30px] xl:gap-[32px] 2xl:gap-[32px] 3xl:gap-[1.667vw]">
            <div className="col-span-2 relative w-full bg-white border border-InterfaceStrokedefault">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4 " />
              <Input
                type="text"
                placeholder={t('searchByCartIdCartNameCreatedByCustomer')}
                className="pl-[35px] xl:pl-[35px] 2xl:pl-[35px] 3xl:pl-[1.823vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]"
              />
            </div>
            <div className="relative col-span-1 flex items-center gap-2">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium whitespace-nowrap"
              >
                {t('endCustomer')}
              </label>
              <div className="w-full">
                <Select2
                  options={endCustomerOptions}
                  value={''}
                  onChange={() => {}}
                  placeholder={t('select')}
                />
              </div>
            </div>
          </div>
          <div className="my-[30px] lg:my-[30px] xl:my-[32px] 2xl:my-[32px] 3xl:my-[1.667vw] overflow-y-auto max-h-[67vh] pb-8">
            <CartPage />
          </div>
        </div>

        <SheetFooter className="w-full absolute bottom-0 right-0 left-0 gap-[8px] lg:gap-[8px] xl:gap-[8px] 2xl:gap-[8px] 3xl:gap-[0.417vw] px-[24px] lg:px-[24px] xl:px-[24px] 2xl:px-[24px] 3xl:px-[1.25vw] py-[20px] 3xl:py-[1.042vw] bg-white">
          <SheetClose>
            <Button
              href="/marketplace"
              className="applybtn py-[8px] lg:py-[8px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.525vw] px-[14px] lg:px-[14px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] cursor-pointer rounded-none submittbtn text-InterfaceTextwhite text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
            >
              Add & Continue Purchase
            </Button>
          </SheetClose>
          <Link
            href="/order"
            className="hover:bg-[#e6e8e9] py-[8px] lg:py-[8px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.525vw] px-[14px] lg:px-[14px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] bg-LinkBaseDefault border border-InterfaceStrokesoft text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
          >
            Add & Go to Cart
          </Link>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
