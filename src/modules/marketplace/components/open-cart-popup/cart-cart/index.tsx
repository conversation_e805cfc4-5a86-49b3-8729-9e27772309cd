import React, { useState } from 'react';
import { CartThreeDotPopup } from '../../cart-three-dot-popup';
import { useTranslations } from 'next-intl';

interface Cart {
  id: number;
  name: string;
  company: string;
  description: string;
  date: string;
  createdBy: string;
  cartId: string;
  quoteId: string;
  totalItems: number;
  totalValue: string;
  expiresAt: string;
}

interface CartCardProps extends Cart {
  selected: boolean;
  onSelect: (id: number, checked: boolean) => void;
}
//selecting on checkbox
// const CartCard: React.FC<CartCardProps> = ({
//   id,
//   name,
//   company,
//   description,
//   date,
//   createdBy,
//   cartId,
//   quoteId,
//   totalItems,
//   totalValue,
//   expiresAt,
//   selected,
//   onSelect,
// }) => {

//   return (
//     <div
//       className={`p-[14px] lg:p-[16px] border rounded shadow transition-colors duration-300 hover:bg-BrandNeutral100 ${selected ? 'bg-BrandNeutral200' : 'bg-BrandNeutral50'}`}
//     >
//         <div className="bg-interfacesurfacecomponent py-[18px] lg:py-[18px] xl:py-[20px] 2xl:py-[20px] 3xl:py-[1.042vw] px-[20px] lg:px-[22px] xl:px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
//           {' '}
//           <div className="flex items-center justify-between gap-2 text-InterfaceTextsubtitle mb-[10px]">
//             <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
//               {createdBy} on {date}
//             </div>
//             <CartThreeDotPopup />
//           </div>
//           <div className="text-InterfaceTextprimary text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
//             {company}
//           </div>
//           <div className="text-InterfaceTexttitle text-[18px] lg:text-[18px] xl:text-[20px] 2xl:text-[20px] 3xl:text-[1.042vw] font-bold py-1">
//             {name}
//           </div>
//           <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
//             {description}
//           </div>
//           <div className="flex items-center justify-start gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw] mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw] mt-[14px]">
//             <div className="text-InterfaceTextprimary font-medium text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] py-[6px] px-[8px] bg-BrandSupport150 border border-BrandSupport1300">
//               Cart ID: {cartId}
//             </div>
//             <div className="text-InterfaceTextprimary font-medium text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] py-[6px] px-[8px] bg-BrandSupport150 border border-BrandSupport1300">
//               Quote ID: {quoteId}
//             </div>
//           </div>
//         </div>

//         <div className="bg-interfacesurfacecomponent border-t flex justify-between py-[10px] lg:py-[10px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[20px] lg:px-[22px] xl:px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
//           <div className="text-blackcolor text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
//             Total Items:{' '}
//             <span className="font-semibold text-[14px] text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
//               {totalItems}
//             </span>
//           </div>
//           <div className="text-blackcolor text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
//             Total Value:{' '}
//             <span className="font-semibold text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
//               {totalValue}
//             </span>
//           </div>
//         </div>

//         <div className="bg-interfacesurfacecomponent border-t flex justify-between lg:py-[10px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[20px] lg:px-[22px] xl:px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
//           <div className="text-BrandHighlight500 text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] flex items-center gap-[8px]">
//             <i className="cloud-timer"></i>
//             Expires at {expiresAt}
//           </div>
//         </div>

//         <div className="mt-[8px] ">
//           <div className="flex items-center gap-2 py-1">
//             <input
//               type="checkbox"
//               id={`select-${id}`}
//               checked={selected}
//               onChange={(e) => onSelect(id, e.target.checked)}
//               className="custcheck"
//             />
//             <label
//               htmlFor={`select-${id}`}
//               className="text-InterfaceTextdefault font-medium text-[12px]"
//             >
//               {selected ? 'Selected' : 'Select'}
//             </label>
//           </div>
//         </div>
//     </div>
//   );
// };

//selecting on Entire div
const CartCard: React.FC<CartCardProps> = ({
  id,
  name,
  company,
  description,
  date,
  createdBy,
  cartId,
  quoteId,
  totalItems,
  totalValue,
  expiresAt,
  selected,
  onSelect,
}) => {
  const handleCardClick = () => {
    onSelect(id, !selected);
  };

  const stopPropagation = (e: React.MouseEvent) => {
    e.stopPropagation();
  };
  const t = useTranslations();

  return (
    <div
      onClick={handleCardClick}
      className={`cursor-pointer p-[14px] xl:p-[14px] 2xl:p-[16px] 3xl:p-[0.833vw] border rounded shadow transition-colors duration-300 ${
        selected ? 'bg-BrandNeutral200' : 'bg-BrandNeutral50'
      } hover:bg-BrandNeutral100`}
    >
      <div className="bg-interfacesurfacecomponent py-[18px] px-[20px] lg:px-[22px] 2xl:px-[22px] 3xl:px-[1.25vw]">
        <div className="flex items-center justify-between text-InterfaceTextsubtitle mb-[10px]">
          <div className="text-[12px] lg:text-[14px]">
            {createdBy} on {date}
          </div>
          <div onClick={stopPropagation}>
            <CartThreeDotPopup />
          </div>
        </div>
        <div className="text-InterfaceTextprimary text-[12px] lg:text-[14px]">{company}</div>
        <div className="text-InterfaceTexttitle font-bold py-1 text-[18px] xl:text-[20px]">
          {name}
        </div>
        <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[14px]">{description}</div>
        <div className="flex gap-[12px] mt-[14px]">
          <div className="text-InterfaceTextprimary font-medium text-[10px] xl:text-[12px] py-[6px] px-[8px] bg-BrandSupport150 border border-BrandSupport1300">
            {t('cartID')}: {cartId}
          </div>
          <div className="text-InterfaceTextprimary font-medium text-[10px] xl:text-[12px] py-[6px] px-[8px] bg-BrandSupport150 border border-BrandSupport1300">
            {t('quoteId')}: {quoteId}
          </div>
        </div>
      </div>

      <div className="bg-interfacesurfacecomponent border-t flex justify-between gap-1 py-[10px] px-[20px]">
        <div className="text-blackcolor font14">
          {t('totalItems')}: <span className="font-semibold font16">{totalItems}</span>
        </div>
        <div className="text-blackcolor font14">
          {t('totalValue')}: <span className="font-semibold font16">{totalValue}</span>
        </div>
      </div>

      <div className="bg-interfacesurfacecomponent border-t flex justify-between py-[10px] px-[20px]">
        <div className="text-BrandHighlight500 font14 flex items-center gap-[8px]">
          <i className="cloud-timer"></i>
          {t('expiresAt')} {expiresAt}
        </div>
      </div>

      <div className="mt-[8px] px-[20px]">
        <div className="flex items-center gap-2 py-1">
          <input
            type="checkbox"
            checked={selected}
            readOnly
            className="custcheck pointer-events-none"
          />
          <span className="text-InterfaceTextdefault font-medium text-[12px]">
            {selected ? t('select3') : t('select2')}
          </span>
        </div>
      </div>
    </div>
  );
};

const CartPage: React.FC = () => {
  const [selectedCartIds, setSelectedCartIds] = useState<number[]>([]);

  const carts: Cart[] = [
    {
      id: 1,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      id: 2,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      id: 3,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      id: 4,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      id: 5,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      id: 6,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      id: 7,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      id: 8,
      name: 'Cart Name One',
      company: 'Alpha Systems',
      description:
        'A brief description about the products selected by the customer and details of the products',
      date: '11/03/2025',
      createdBy: 'Simmons',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: 5,
      totalValue: 'USD 500.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
  ];

  const handleSelect = (id: number, checked: boolean) => {
    setSelectedCartIds((prev) =>
      checked ? [...prev, id] : prev.filter((cartId) => cartId !== id)
    );
  };

  return (
    <div className="grid grid-cols-3 gap-[28px] lg:gap-[30px] xl:gap-[32px] 2xl:gap-[32px] 3xl:gap-[1.667vw]">
      {carts.map((cart) => (
        <CartCard
          key={cart.id}
          {...cart}
          selected={selectedCartIds.includes(cart.id)}
          onSelect={handleSelect}
        />
      ))}
    </div>
  );
};

export default CartPage;
