'use client';
import * as React from 'react';
import {
  SheetTitle,
  SheetHeader,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

export default function EligibleBaseOffers() {
  const t = useTranslations();

  // Array of offer data
  const offers = [
    {
      title: 'Dynamics 365 Customer Service Enterprise',
      offerId: '39NFJQT206QX-0002-39NFJQT1Q5KK',
      description:
        'User subscription that includes Dynamics 365 Customer Service, Enterprise edition',
    },
    {
      title:
        'Dynamics 365 Customer Service Enterprise Attach To Qualifying Dynamics 365 Base Offer',
      offerId: '39NFJQT206QX-0002-39NFJQT1Q5KK',
      description:
        'User subscription that includes Dynamics 365 Customer Service, Enterprise edition',
    },
    {
      title: 'Dynamics 365 Customer Service Enterprise Device',
      offerId: '39NFJQT206QX-0002-39NFJQT1Q5KK',
      description:
        'User subscription that includes Dynamics 365 Customer Service, Enterprise edition',
    },
    {
      title: 'Dynamics 365 Customer Service Enterprise (Non-Profit Pricing)',
      offerId: '39NFJQT206QX-0002-39NFJQT1Q5KK',
      description:
        'User subscription that includes Dynamics 365 Customer Service, Enterprise edition',
    },
    {
      title: 'Dynamics 365 Customer Service Enterprise (Education Faculty Pricing)',
      offerId: '39NFJQT206QX-0002-39NFJQT1Q5KK',
      description:
        'User subscription that includes Dynamics 365 Customer Service, Enterprise edition',
    },
    {
      title: 'Dynamics 365 Customer Service Enterprise',
      offerId: '39NFJQT206QX-0002-39NFJQT1Q5KK',
      description:
        'User subscription that includes Dynamics 365 Customer Service, Enterprise edition',
    },
  ];

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-[6px] xl:gap-[6px] 3xl:gap-[0.313vw] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
          <i className="cloud-receipt-disscount text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] text-BrandHighlightpure"></i>
          {t('checkEligibleBaseOffer')}
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[660px] xl:max-w-[700px] 2xl:max-w-[730px] 3xl:max-w-[40.854vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('EligibleBaseOffers')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-full">
          <div className="space-y-[16px] xl:space-y-[16px] 3xl:space-y-[0.833vw]">
            {offers.map((offer, idx) => (
              <div
                key={idx}
                className="p-[14px] xl:p-[14px] 3xl:p-[0.729vw] grid grid-cols-11 gap-[13px] xl:gap-[13px] 3xl:gap-[0.677vw] border border-InterfaceStrokesoft cardShadow"
              >
                <div className="h-[80px] xl:h-[80px] 2xl:h-[80px] 3xl:h-[4.767vw] col-span-3 bg-BrandNeutral100 py-[18px] xl:py-[18px] 3xl:py-[0.802vw] px-[12px] xl:px-[12px] 3xl:px-[0.729vw]">
                  <Image
                    src={'/images/microsoft-logo.svg'}
                    width={120}
                    height={45}
                    alt="Logo"
                    className="h-[40px] xl:h-[40px] 2xl:h-[2.744vw] 3xl:h-[3.244vw] w-[130px] xl:w-[130px] 3xl:w-[6.771vw]"
                  />
                </div>
                <div className="col-span-8 leading-[22px] xl:leading-[22px] 3xl:leading-[1.546vw]">
                  <div className="text-InterfaceTexttitle text-[16px] lg:text-[16px] 2xl:text-[17px] 3xl:text-[0.99vw] font-semibold">
                    {offer.title}
                  </div>
                  <div className="text-InterfaceTextdefault text-[13px] lg:text-[13px] 2xl:text-[14px] 3xl:text-[0.781vw] font-normal">
                    {t('offerId')}: {offer.offerId}
                  </div>

                  <div className=" leading-[18px] xl:leading-[18px] 3xl:leading-[0.936vw] text-InterfaceTextsubtitle text-[13px] lg:text-[13px] 2xl:text-[14px] 3xl:text-[0.783vw] font-normal">
                    {offer.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
