'use client';
import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Checkbox,
  Input,
  RadioGroup,
  RadioGroupItem,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Label } from '@redington-gulf-fze/cloudquarks-component-library';

export default function MarketplaceSidebar() {
  return (
    <div className="accordion-container bg-interfacesurfacecomponent py-[18px] xl:py-[20px] 3xl:py-[1.042vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] cardShadow h-full">
      <div className="p-[14px] lg:p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw] border-b border-InterfaceStrokesoft">
        <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] pb-[8px] lg:pb-[8px] xl:pb-[8px] 2xl:pb-[8px] 3xl:pb-[0.417vw]">
          All Filters
        </div>
      </div>
      <Accordion type="single" collapsible className="w-full" defaultValue="item-1">
        <AccordionItem
          value="item-1"
          className="py-[18px] xl:py-[20px] 3xl:py-[1.042vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]"
        >
          <AccordionTrigger className="group text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
            Categories
            <i className="cloud-dropdownarrow text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
          </AccordionTrigger>
          <AccordionContent className="flex flex-col gap-4 text-balance p-0">
            <div className="mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
              <Accordion type="single" collapsible className="w-full" defaultValue="item-1">
                <AccordionItem value="item-11" className="border-none">
                  <AccordionTrigger
                    data-state="closed"
                    className="group accordion-trigger text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                      Infrastructure Services{' '}
                      <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        (27,913)
                      </span>
                    </span>
                    <i className="cloud-Add text-[10px] xl:text-[10px] 2xl:text-[10px] 3xl:text-[0.525vw] transition-transform duration-300 group-data-[state=open]:rotate-90"></i>
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance p-0">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Public Cloud{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (2)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Azure Reservation{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (27911)
                          </span>
                        </label>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-12" className="border-none">
                  <AccordionTrigger className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                      Productivity & Collaboration{' '}
                      <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        (1,489)
                      </span>
                    </span>
                    <i className="cloud-Add text-[10px] xl:text-[10px] 2xl:text-[10px] 3xl:text-[0.525vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance p-0">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Commercial Marketplace{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            {' '}
                            (66)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          CSP New Commerce{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (1136)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Cisco{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (31)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Google Workspace Commitment{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (25)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Google Workspace Flexible{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (6)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Office 365{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (143)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Microsoft 365{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (39)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Windows 365{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (34)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Windows 10{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (9)
                          </span>
                        </label>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-13" className="border-none ">
                  <AccordionTrigger className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                      Business Applications{' '}
                      <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        (49)
                      </span>
                    </span>
                    <i className="cloud-Add text-[10px] xl:text-[10px] 2xl:text-[10px] 3xl:text-[0.525vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance p-0">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Dynamic 365{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            {' '}
                            (0)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Power Platform{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (45)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Cisco{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (31)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Trial Offers{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (0)
                          </span>
                        </label>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-14" className="border-none ">
                  <AccordionTrigger className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                      Software{' '}
                      <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        (111)
                      </span>
                    </span>
                    <i className="cloud-Add text-[10px] xl:text-[10px] 2xl:text-[10px] 3xl:text-[0.525vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance p-0">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Software Subscription{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            {' '}
                            (32)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          CSP Perpetual{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (79)
                          </span>
                        </label>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-15" className="border-none ">
                  <AccordionTrigger className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                      Business Continuity{' '}
                      <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        (1)
                      </span>
                    </span>
                    <i className="cloud-Add text-[10px] xl:text-[10px] 2xl:text-[10px] 3xl:text-[0.525vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance p-0">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Archive{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            {' '}
                            (0)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Backup{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (1)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Disaster Recovery{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (10)
                          </span>
                        </label>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-16" className="border-none ">
                  <AccordionTrigger className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                      Security{' '}
                      <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        (1)
                      </span>
                    </span>
                    <i className="cloud-Add text-[10px] xl:text-[10px] 2xl:text-[10px] 3xl:text-[0.525vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance p-0">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Email Security{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            {' '}
                            (0)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          End Point{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (0)
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Network Security{' '}
                          <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                            (0)
                          </span>
                        </label>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-17" className="border-none ">
                  <AccordionTrigger className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                      Services{' '}
                      <span className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        (1)
                      </span>
                    </span>
                    <i className="cloud-Add text-[10px] xl:text-[10px] 2xl:text-[10px] 3xl:text-[0.525vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance p-0">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Training Services
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Cybersecurity
                        </label>
                      </div>
                      <div className="flex items-center gap-2 py-1">
                        <Checkbox id="terms" className="custcheck" />
                        <label
                          htmlFor="terms"
                          className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                        >
                          Consulting Services
                        </label>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium py-2">
                  <span className="text-InterfaceTextdefault py-[8px] xl:py-[8px] 3xl:py-[0.417vw] text-left">
                    Solution
                  </span>
                </div>
              </Accordion>
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="item-2"
          className="py-[18px] xl:py-[20px] 3xl:py-[1.042vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]"
        >
          <AccordionTrigger className="group text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
            <span className="flex items-center gap-2">
              {' '}
              Filter by Brands
              <span className="bg-BrandPrimarypurenew py-[5px] px-[10px] text-white rounded-full font-medium text-[11px] xl:text-[11px] 3xl:text-[0.573vw]">
                3
              </span>
            </span>
            <i className="cloud-dropdownarrow text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
          </AccordionTrigger>
          <AccordionContent className="flex flex-col gap-0 text-balance p-0">
            <div className="relative w-full bg-white border border-InterfaceStrokehard my-[14px] lg:my-[14px] xl:my-[16px] 2xl:my-[16px] 3xl:my-[0.833vw]">
              <Input
                type="text"
                placeholder="Find a Brand"
                className="py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"
              />
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2 py-1">
                <Checkbox id="terms" className="custcheck" />
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  All
                </label>
              </div>
              <div className="flex items-center gap-2 py-1">
                <Checkbox id="terms" className="custcheck" defaultChecked />
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  AWS
                </label>
              </div>
              <div className="flex items-center gap-2 py-1">
                <Checkbox id="terms" className="custcheck" />
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  Google
                </label>
              </div>
              <div className="flex items-center gap-2 py-1">
                <Checkbox id="terms" className="custcheck" />
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  Acer
                </label>
              </div>
              <div className="flex items-center gap-2 py-1">
                <Checkbox id="terms" className="custcheck" defaultChecked />
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  Dell
                </label>
              </div>
              <div className="flex items-center gap-2 py-1">
                <Checkbox id="terms" className="custcheck" />
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  Oracle
                </label>
              </div>
              <div className="flex items-center gap-2 py-1">
                <Checkbox id="terms" className="custcheck" defaultChecked />
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  Microsoft
                </label>
              </div>
              <div className="flex items-center gap-2 py-1">
                <i className="cloud-readeye1"></i>
                <label
                  htmlFor="terms"
                  className="text-InterfaceTextdefault cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold"
                >
                  Show More
                </label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="item-3"
          className="py-[18px] xl:py-[20px] 3xl:py-[1.042vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]"
        >
          <AccordionTrigger className="group text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
            Order by
            <i className="cloud-dropdownarrow text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
          </AccordionTrigger>
          <AccordionContent className="flex flex-col gap-4 text-balance p-0 mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
            <RadioGroup
              defaultValue="en"
              className="flex flex-col gap-[0px] space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw] custrad"
            >
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c1" id="r1" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  Relevance
                </Label>
              </div>
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c2" id="r2" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  Product Name A to Z
                </Label>
              </div>
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c3" id="r3" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  Product Name Z to A
                </Label>
              </div>
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c4" id="r4" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  Price: Low to High
                </Label>
              </div>
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c5" id="r5" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  Price: High to Low
                </Label>
              </div>
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c6" id="r6" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  Date Added
                </Label>
              </div>
            </RadioGroup>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="item-4"
          className="py-[18px] xl:py-[20px] 3xl:py-[1.042vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]"
        >
          <AccordionTrigger className="group text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
            View by
            <i className="cloud-dropdownarrow text-[6px] xl:text-[6px] 3xl:text-[0.313vw] transition-transform duration-300 group-data-[state=open]:rotate-180"></i>
          </AccordionTrigger>
          <AccordionContent className="flex flex-col gap-4 text-balance p-0 mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
            <RadioGroup
              defaultValue="en"
              className="flex flex-col gap-[0px] space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw] custrad"
            >
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c1" id="r1" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  All
                </Label>
              </div>
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c2" id="r2" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  Favourites
                </Label>
              </div>
              <div className="flex items-center justify-start space-x-2">
                <RadioGroupItem value="c3" id="r3" />
                <Label className="text-InterfaceTextdefault cursor-pointer font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] hover:font-bold">
                  With Discount Coupons
                </Label>
              </div>
            </RadioGroup>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
