import React from 'react';
import { useTranslations } from 'next-intl';

export default function CategoryCards() {
  const t = useTranslations();

  const data = [
    {
      title: t('infrastructureServices'),
      icon: 'cloud-infrasturctrueservices',
    },
    {
      title: t('productivityCollaboration'),
      icon: 'cloud-productivity',
      active: true,
    },
    {
      title: t('businessApplications'),
      icon: 'cloud-application',
    },
    {
      title: t('software'),
      icon: 'cloud-software',
    },
    {
      title: t('businessContinuity'),
      icon: 'cloud-investment',
    },
    {
      title: t('security'),
      icon: 'cloud-security',
    },
    {
      title: t('services'),
      icon: 'cloud-settings',
    },
    {
      title: t('solutions'),
      icon: 'cloud-bulb',
    },
  ];

  return (
    <>
      {data?.map((items, index) => {
        return (
          <div
            key={index}
            className={`${items.active === true ? 'border border-BrandNeutral950' : ''} bg-background p-[16px] 3xl:p-[0.833vw] cardShadow text-center cursor-pointer group hover:bg-BrandNeutral50 transition duration-300 ease-in-out`}
          >
            <i
              className={`${items.icon} ${items.active === true ? 'text-BrandNeutral950' : 'text-InterfaceTextsubtitle'} text-[40px] 3xl:text-[2.083vw]  group-hover:text-BrandNeutralpure`}
            ></i>
            <div
              className={`${items.active === true ? 'text-BrandNeutral950 font-[500]' : 'text-InterfaceTextdefault font-[400]'} text-[13px] 3xl:text-[0.729vw] leading-[140%] group-hover:text-BrandNeutralpure mt-[6px] 3xl:mt-[0.313vw]`}
            >
              {items.title}
            </div>
          </div>
        );
      })}
    </>
  );
}
