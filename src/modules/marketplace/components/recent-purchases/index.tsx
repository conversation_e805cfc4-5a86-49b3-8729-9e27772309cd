'use client';
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import Link from 'next/link';
import { ProductsBanner } from '../products-banner';
import { CardThreeDotPopup } from '../card-three-dot-popup';
import { <PERSON><PERSON> } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

const purchases = [
  {
    id: 1,
    image: '/images/google.svg',
    imageAlt: 'Google Logo',
    productName: 'Commercial Market Place',
    skuId: 'DZH318Z09PGJ.0007',
    vendorSkuId: 'DZH318Z09PGJ.0007',
    price: 'USD 5,000.00',
  },
  {
    id: 2,
    image: '/images/microsoft_logo.svg',
    imageAlt: 'Microsoft Logo',
    productName: 'Commercial Market Place',
    skuId: 'DZH318Z09PGJ.0007',
    vendorSkuId: 'DZH318Z09PGJ.0007',
    price: 'USD 5,000.00',
  },
  {
    id: 3,
    image: '/images/aws.svg',
    imageAlt: 'AWS Logo',
    productName: 'Commercial Market Place',
    skuId: 'DZH318Z09PGJ.0007',
    vendorSkuId: 'DZH318Z09PGJ.0007',
    price: 'USD 5,000.00',
  },
  {
    id: 4,
    image: '/images/microsoft_logo.svg',
    imageAlt: 'Microsoft Logo',
    productName: 'Commercial Market Place',
    skuId: 'DZH318Z09PGJ.0007',
    vendorSkuId: 'DZH318Z09PGJ.0007',
    price: 'USD 5,000.00',
  },
  {
    id: 5,
    image: '/images/google.svg',
    imageAlt: 'Google Logo',
    productName: 'Commercial Market Place',
    skuId: 'DZH318Z09PGJ.0007',
    vendorSkuId: 'DZH318Z09PGJ.0007',
    price: 'USD 5,000.00',
  },
  {
    id: 6,
    image: '/images/microsoft_logo.svg',
    imageAlt: 'Microsoft Logo',
    productName: 'Commercial Market Place',
    skuId: 'DZH318Z09PGJ.0007',
    vendorSkuId: 'DZH318Z09PGJ.0007',
    price: 'USD 5,000.00',
  },
  {
    id: 7,
    image: '/images/aws.svg',
    imageAlt: 'AWS Logo',
    productName: 'Commercial Market Place',
    skuId: 'DZH318Z09PGJ.0007',
    vendorSkuId: 'DZH318Z09PGJ.0007',
    price: 'USD 5,000.00',
  },
];

export function RecentPurchases() {
  const t = useTranslations();

  const [isExpanded, setIsExpanded] = useState(false);

  const displayedItems = isExpanded ? purchases : [];

  return (
    <div className="pb-[30px] 3xl:pb-[30px]">
      <div className="py-[14px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[1px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] bg-BrandNeutral100 border border-InterfaceStrokedefault relative">
        <div className="flex justify-between items-center">
          <div className="text-InterfaceTexttitle text-[16px] lg:text-[15px] xl:text-[16px] 2xl:text-[17px] 3xl:text-[0.938vw] font-[700]">
            {t('yourRecentPurchases')}
          </div>
          <div className="flex items-center gap-[16px] 3xl:gap-[0.833vw]">
            <Link
              href=""
              className={`absolute top-[12px]  ${
                isExpanded
                  ? 'right-[123px] xl:right-[125px] 2xl:right-[150px] 3xl:right-[155px]'
                  : 'right-[62px]'
              }`}
            >
              <Button
                onClick={() => setIsExpanded(!isExpanded)}
                className="py-[4px] xl:py-[4px] 2xl:py-[6px] 3xl:py-[0.36vw] px-[6px] xl:px-[6px] 2xl:px-[8px] 3xl:px-[0.417vw] text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] hover:bg-transparent hover:shadow-none hover:text-InterfaceTextprimary bg-white shadow rounded-none text-InterfaceTextdefault flex items-center gap-[6px]"
              >
                <Image
                  src={isExpanded ? '/images/minus.svg' : '/images/add.svg'}
                  alt={isExpanded ? t('collapse') : t('expand')}
                  width={16}
                  height={16}
                  className="w-[16px] h-[16px] 2xl:w-[16px] 2xl:h-[18px] hover:bg-blue"
                />
                {isExpanded ? t('collapse') : t('expand')}
              </Button>
            </Link>

            <ProductsBanner />
          </div>
        </div>

        {displayedItems.length > 0 ? (
          <Carousel key={isExpanded ? 'expanded' : 'collapsed'}>
            <CarouselContent className="flex items-center gap-2 mt-[18px] lg:mt-[16px] xl:mt-[18px] 2xl:mt-[20px] 3xl:mt-[1.042vw] ">
              {displayedItems.map((item) => (
                <CarouselItem
                  key={item.id}
                  className="group basis-[50%] md:basis-[50%] xl:basis-[39%] 2xl:basis-[35%] 3xl:basis-[30%]"
                >
                  <Card className="flex flex-row items-center p-[16px] shadow-md border rounded-none bg-white border-gray-200 group-hover:border-InterfaceTextprimary transition cursor-pointer">
                    <div className="mr-4 p-[6px]">
                      <Image src={item.image} alt={item.imageAlt} width={65} height={65} />
                    </div>
                    <CardContent className="p-0 flex-1">
                      <div className="flex justify-between items-center">
                        <div
                          className={`${roboto.className} text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%] group-hover:text-InterfaceTextprimary group-hover:underline transition`}
                        >
                          {item.productName}
                        </div>
                        <CardThreeDotPopup />
                      </div>

                      {/* <div
                        className={`${roboto.className} text-InterfaceTextdefault mt-[2px] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%] group-hover:text-[11px] xl:group-hover:text-[11px] 2xl:group-hover:text-[13px] 3xl:group-hover:text-[0.677vw] transition`}
                      >
                        SKU ID :{' '}
                        <span className="text-InterfaceTextsubtitle font-[400] group-hover:font-[400]">
                          {item.skuId}
                        </span>
                      </div>
                      <div
                        className={`${roboto.className} text-InterfaceTextdefault mt-[2px] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%] group-hover:text-[11px] xl:group-hover:text-[11px] 2xl:group-hover:text-[13px] 3xl:group-hover:text-[0.677vw] transition`}
                      >
                        Vendor SKU ID :{' '}
                        <span className="text-InterfaceTextsubtitle font-[400] group-hover:font-[400]">
                          {item.vendorSkuId}
                        </span>
                      </div>
                      <div
                        className={`${roboto.className} text-InterfaceTexttitle text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%] mt-[8px]`}
                      ></div>

                      <div
                        className={`${roboto.className} text-InterfaceTextdefault mt-[2px] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%] group-hover:text-[11px] xl:group-hover:text-[11px] 2xl:group-hover:text-[13px] 3xl:group-hover:text-[0.677vw] transition`}
                      >
                        SKU ID :{' '}
                        <span className="text-InterfaceTextsubtitle font-[400] group-hover:font-[400]">
                          {item.skuId}
                        </span>
                      </div>
                      <div
                        className={`${roboto.className} text-InterfaceTextdefault mt-[2px] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%] group-hover:text-[11px] xl:group-hover:text-[11px] 2xl:group-hover:text-[13px] 3xl:group-hover:text-[0.677vw] transition`}
                      >
                        Vendor SKU ID :{' '}
                        <span className="text-InterfaceTextsubtitle font-[400] group-hover:font-[400]">
                          {item.vendorSkuId}
                        </span>
                      </div> */}
                      <div
                        className={`${roboto.className} text-InterfaceTexttitle text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%] mt-[8px]`}
                      >
                        <div
                          className={`${roboto.className} text-InterfaceTextdefault mt-[2px] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%] group-hover:text-[11px] xl:group-hover:text-[11px] 2xl:group-hover:text-[13px] 3xl:group-hover:text-[0.677vw] transition`}
                        >
                          {t('skuId')}:{' '}
                          <span className="text-InterfaceTextsubtitle font-[400] group-hover:font-[400]">
                            {item.skuId}
                          </span>
                        </div>
                        <div
                          className={`${roboto.className} text-InterfaceTextdefault mt-[2px] text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%] group-hover:text-[11px] xl:group-hover:text-[11px] 2xl:group-hover:text-[13px] 3xl:group-hover:text-[0.677vw] transition`}
                        >
                          {t('vendorSkuId2')} :{' '}
                          <span className="text-InterfaceTextsubtitle font-[400] group-hover:font-[400]">
                            {item.vendorSkuId}
                          </span>
                        </div>
                        <div
                          className={`${roboto.className} text-InterfaceTexttitle text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%] mt-[8px]`}
                        >
                          {item.price}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>

            <CarouselPrevious className="bg-background rounded-none border-none arrowShadow h-[26px] xl:h-[26px] 2xl:h-[32px] 3xl:h-[34px] w-[26px] xl:w-[26px] 2xl:w-[32px] 3xl:w-[34px] right-[72px] xl:right-[72px] 2xl:right-[85px] 3xl:right-[85px] left-auto top-[-18px] xl:top-[-18px] 2xl:top-[-17px] 3xl:top-[-18px] custprevious hover:text-[#4884f5]" />
            <CarouselNext className="bg-background rounded-none border-none arrowShadow h-[26px] xl:h-[26px] 2xl:h-[32px] 3xl:h-[34px] w-[26px] xl:w-[26px] 2xl:w-[32px] 3xl:w-[34px] right-[40px] xl:right-[40px] 2xl:right-[45px] 3xl:right-[45px] top-[-18px] xl:top-[-18px] 2xl:top-[-17px] 3xl:top-[-18px] custnext hover:text-[#4884f5]" />
          </Carousel>
        ) : null}
      </div>
    </div>
  );
}
