'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  SheetClose,
  <PERSON>etContent,
  Button,
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  ScrollArea,
  Label,
  Input,
  Textarea,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { ProductDetailSheetProps } from '@/types';
import EligibleBaseOffers from '../eligible-base-offers';
import OpencartPopup from '../open-cart-popup';
import { useTranslations } from 'next-intl';
import BundledProducts from '../bundled-products-popup';

export default function ProductDetailedView({ open, onClose }: ProductDetailSheetProps) {
  const t = useTranslations();
  const [openCartPopupShow, setOpenCartPopupShow] = React.useState(false);
  const [value, setValue] = React.useState<number>(5000);
  const [clickCount, setClickCount] = React.useState<number>(0);
  const increment = () => {
    if (clickCount >= 2 || value >= 10000) return;

    if (value === 0) setValue(5000);
    else if (value === 5000) setValue(10000);

    setClickCount((prev) => prev + 1);
  };
  const [isNewCart, setIsNewCart] = React.useState(false);

  const handleNewCartClick = () => {
    setIsNewCart(true);
  };
  const decrement = () => {
    if (value === 0) return;

    if (value === 10000) {
      setValue(5000);
      setClickCount((prev) => prev - 1);
    } else if (value === 5000) {
      setValue(0);
      setClickCount(0);
    }
  };
  const [bundledProductsShow, setBundledProductsShow] = React.useState(false);
  return (
    <>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[660px] xl:max-w-[700px] 3xl:max-w-[40.854vw] p-[0px]">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('productDetailedView')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="bg-BrandNeutral50 overflow-auto h-full">
            <div className="grid grid-cols-12">
              <div className="col-span-7 bg-interfacesurfacecomponent border-r-2 border-InterfaceStrokesoft  p-[24px] xl:p-[24px] 3xl:p-[1.354vw]">
                <div className="bg-BrandNeutral100 py-[55px] xl:py-[55px] 3xl:py-[2.865vw] 3xl:pt-[3.125vw] flex justify-center items-center ">
                  <Image
                    src={'/images/microsoft-logo.svg'}
                    width={200}
                    height={55}
                    alt="Logo"
                    className="w-[230px] h-[55px] xl:w-[230px] xl:h-[60px]  3xl:w-[12.079vw] 3xl:h-[3.125vw] "
                  />
                </div>
                <ScrollArea className="h-[320px] lg:h-[400px] xl:h-[460px] 3xl:h-[29.967vw] pt-[20px] xl:pt-[20px] 3xl:pt-[1.042vw]">
                  <div>
                    <div className=" text-InterfaceTexttitle text-[18px] lg:text-[18px] 2xl:text-[21px] 3xl:text-[1.05vw] font-semibold ">
                      Microsoft 365 Apps for Enterprise
                    </div>
                    <div className="font-normal text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 3xl:text-[0.625vw]">
                      {t('description')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      Microsoft 365 Apps for Enterprise is a subscription-based productivity suite
                      designed for large organizations, offering the latest versions of Microsoft
                      Office applications, enhanced security, and cloud-based collaboration
                      features.
                    </div>
                    <div className="mt-[14px]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-normal">
                        {t('price')}
                      </div>
                      <div className="flex gap-[14px] xl:gap-[14px] 3xl:gap-[0.729vw] items-center">
                        <div className="text-[16px] xl:text-[17px] 2xl:text-[18px] 3xl:text-[1.042vw] font-medium">
                          {t('usd')} 5,000.00
                        </div>
                        <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                          <EligibleBaseOffers />
                          <div
                            onClick={() => setBundledProductsShow(true)}
                            className="flex items-center gap-[6px] xl:gap-[6px] 3xl:gap-[0.313vw] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] cursor-pointer"
                          >
                            <i className="cloud-productview text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer" />
                            Bundle
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className=" pt-[17px] xl:pt-[17px] 3xl:pt-[0.885vw] ">
                      <div className="border-t border-InterfaceStrokesoft">
                        <Accordion type="single" collapsible className="w-full">
                          <AccordionItem value="authorized-signatory">
                            <AccordionTrigger className="py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[1.042vw] hover:no-underline focus:no-underline ">
                              <div className="w-full flex justify-between items-center">
                                <div className="text-left text-[15px] xl:text-[15px] 3xl:text-[0.885vw]">
                                  {t('aboutThisItem')}
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="">
                              <div className="">
                                <tbody className="flex flex-col w-full border border-InterfaceStrokesoft">
                                  <tr className="w-full flex ">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%] text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('vendor')}
                                    </td>
                                    <td className=" p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      Microsoft
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%]  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('brand')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      Microsoft CSP
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%] text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('brandCategory')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      Azure RI
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%]  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('category')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      Productivity & Collaboration
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%]  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('segment')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[12px] xl:text-[12px]  3xl:text-[0.677vw] text-InterfaceTextdefault font-normal">
                                      Commercial
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%]  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('vendorSkuId2')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      DZH318Z09PGJ:0007
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%]  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('skuId')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      DZH318Z09PGJ:0007
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%]  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('options')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      Term (2)
                                    </td>
                                  </tr>
                                  <tr className="w-full flex">
                                    <td className="p-1 border border-InterfaceStrokesoft w-[70%]  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-medium">
                                      {t('billType')}
                                    </td>
                                    <td className="p-1 border border-InterfaceStrokesoft w-full  text-[13px] xl:text-[13px] 3xl:text-[0.729vw] text-InterfaceTextdefault font-normal">
                                      Monthly, Yearly
                                    </td>
                                  </tr>
                                </tbody>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      </div>
                      <div className="">
                        <Accordion type="single" collapsible className="w-full">
                          <AccordionItem value="authorized-signatory">
                            <AccordionTrigger className="py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[1.042vw] hover:no-underline focus:no-underline ">
                              <div className="w-full flex justify-between items-center">
                                <div className="text-left text-[15px] xl:text-[15px] 3xl:text-[0.885vw]">
                                  {t('supportingDocument')}
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="space-y-[20px] xl:space-y-[20px] 3xl:space-y-[1.042vw]">
                              <div className=" flex justify-between items-center pt-[20px] xl:pt-[20px] 3xl:pt-[1.042vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] border-b border-InterfaceStrokesoft ">
                                <div className="text-[13px] xl:text-[13px] 3xl:text-[0.725vw] font-normal text-InterfaceTextdefault">
                                  document 1.pdf
                                </div>
                                <div className="flex gap-[30px]">
                                  <i className=" cloud-readeye1 text-BrandSupport1pure text-[14px] xl:text-[14px] 3xl:text-[0.781vw]"></i>
                                  <i className="cloud-fileupload text-BrandSupport1pure text-[14px] xl:text-[14px] 3xl:text-[0.781vw]"></i>
                                </div>
                              </div>
                              <div className=" flex justify-between items-center pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] border-b border-InterfaceStrokesoft ">
                                <div className="text-[13px] xl:text-[13px] 3xl:text-[0.725vw] font-normal text-InterfaceTextdefault">
                                  document 2.pdf
                                </div>
                                <div className="flex gap-[30px]">
                                  <i className=" cloud-readeye1 text-BrandSupport1pure text-[14px] xl:text-[14px] 3xl:text-[0.781vw]"></i>
                                  <i className="cloud-fileupload text-BrandSupport1pure text-[14px] xl:text-[14px] 3xl:text-[0.781vw]"></i>
                                </div>
                              </div>
                              <div className=" flex justify-between items-center pb-[10px] xl:pb-[10px] 3xl:pb-[0.521vw]  ">
                                <div className="text-[13px] xl:text-[13px] 3xl:text-[0.725vw] font-normal text-InterfaceTextdefault">
                                  document 3.pdf
                                </div>
                                <div className="flex gap-[30px]">
                                  <i className=" cloud-readeye1 text-BrandSupport1pure text-[14px] xl:text-[14px] 3xl:text-[0.781vw]"></i>
                                  <i className="cloud-fileupload text-BrandSupport1pure text-[14px] xl:text-[14px] 3xl:text-[0.781vw]"></i>
                                </div>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      </div>
                      <div className="">
                        <Accordion type="single" collapsible className="w-full">
                          <AccordionItem value="authorized-signatory">
                            <AccordionTrigger className="py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[1.042vw] hover:no-underline focus:no-underline ">
                              <div className="w-full flex justify-between items-center">
                                <div className="text-left text-[15px] xl:text-[15px] 3xl:text-[0.885vw]">
                                  {t('promotions')}
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="space-y-[20px] xl:space-y-[20px] 3xl:space-y-[1.042vw]">
                              <div>
                                <div className="border border-BrandNeutralpure px-[12px] py-[6px] bg-BrandNeutralpure text-white flex justify-between">
                                  <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                    15% {t('discount')}
                                  </div>
                                  <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                    {t('endDate')}: 15/05/2025
                                  </div>
                                </div>
                                <div className="border border-InterfaceStrokesoft px-[10px] py-[6px]">
                                  <div className=" leading-[20px] border-b border-InterfaceStrokesoft pb-[10px]">
                                    <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                                      Do more with Microsoft 365 E3 Promo
                                    </div>
                                    <div className="text-[13px] xl:text-[13px] 3xl:text-[0.729vw] font-normal">
                                      {t('promotionId')} : 39NFJQT206QX-0002
                                    </div>
                                  </div>
                                  <div className="pt-[6px] text-InterfaceTextsubtitle grid grid-cols-3">
                                    <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw]">
                                      {t('promotionType')}{' '}
                                    </div>
                                    <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw]">
                                      {t('term')}{' '}
                                    </div>
                                    <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw]">
                                      {t('billType')}
                                    </div>
                                  </div>
                                  <div className="pt-[4px] text-InterfaceTextdefault grid grid-cols-3">
                                    <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] ">
                                      Percent Discount
                                    </div>
                                    <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw]">
                                      1 Year
                                    </div>
                                    <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] ">
                                      Annual
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      </div>
                      <div className="">
                        <Accordion type="single" collapsible className="w-full">
                          <AccordionItem value="authorized-signatory">
                            <AccordionTrigger className="py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[1.042vw] hover:no-underline focus:no-underline ">
                              <div className="w-full flex justify-between items-center">
                                <div className="text-left text-[15px] xl:text-[15px] 3xl:text-[0.885vw]">
                                  {t('discountCoupons')}
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="space-y-[20px] xl:space-y-[20px] 3xl:space-y-[1.042vw]">
                              <div>
                                <div className="border border-BrandNeutralpure px-[12px] xl:px-[12px] 3xl:px-[0.625vw] py-[6px] bg-BrandNeutralpure text-white flex justify-between">
                                  <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                    10% {t('off')}
                                  </div>
                                  <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                    {t('validTill')}: 16/06/2025
                                  </div>
                                </div>
                                <div className="border border-InterfaceStrokesoft px-[10px] xl:px-[10px] 3xl:px-[0.521vw] py-[6px] ">
                                  <div className=" leading-[20px] pb-[10px] xl:pb-[10px] 3xl:pb-[0.521vw]">
                                    <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                                      #1122334455
                                    </div>
                                    <div className="text-[13px] xl:text-[13px] 3xl:text-[0.729vw] font-normal text-InterfaceTextdefault">
                                      Get 10% off on orders above 5 Quantity.
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
              <div className="col-span-5 p-[24px] xl:p-[24px] 3xl:p-[1.354vw]">
                <div>
                  {!isNewCart ? (
                    <>
                      <div className="mb-[22px] xl:mb-[22px] 3xl:mb-[1.198vw] text-[13px] lg:text-[13px] xl:text-[14px] 3xl:text-[0.729vw] font-normal text-InterfaceTextsubtitle">
                        {t('buyingOptions')}
                      </div>
                      <div className="border-b-[0.052vw] border-InterfaceStrokedefault pb-[20px] xl:pb-[20px] 3xl:pb-[1.25vw]">
                        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-blackcolor font-medium">
                          {t('segment')}
                        </div>
                        <div className=" justify-between gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw] pt-[4px] grid grid-cols-2 ">
                          <Button
                            type="button"
                            className=" w-full px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] hover:bg-BrandNeutral950  bg-BrandNeutral950 text-white hover:text-white  text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[100%]"
                            variant="outline"
                            size="sm"
                          >
                            <i className="cloud-fillcircletick text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                            {t('commercial')}
                          </Button>
                          <Button
                            id="addBrands"
                            className=" relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] hover:bg-[#e6e8e9] hover:font-[500] text-[#3C4146] bg-transparent border border-[#E5E7EB] rounded-none text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                          >
                            <i className="cloud-circletick text-InterfaceTextsubtitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                            {t('education')}
                          </Button>
                        </div>
                      </div>
                      <div className="border-b-[0.052vw] border-InterfaceStrokedefault pb-[20px] xl:pb-[20px] 3xl:pb-[1.25vw] pt-[16px] xl:pt-[16px] 3xl:pt-[0.888vw]">
                        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-blackcolor font-medium">
                          {t('billType')}
                        </div>
                        <div className="grid grid-cols-2 justify-between gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw] pt-[4px] ">
                          <Button
                            type="button"
                            className=" w-full py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] hover:bg-BrandNeutral950  bg-BrandNeutral950 text-white hover:text-white  text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[100%]"
                            variant="outline"
                            size="sm"
                          >
                            <i className="cloud-fillcircletick text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                            {t('monthly')}
                          </Button>
                          <Button
                            id="addBrands"
                            className=" relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] hover:bg-[#e6e8e9] hover:font-[500] text-[#3C4146] bg-transparent border border-[#E5E7EB] rounded-none text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                          >
                            <i className="cloud-circletick text-InterfaceTextsubtitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                            {t('annual')}
                          </Button>
                        </div>
                      </div>
                      <div className="border-b-[0.052vw] border-InterfaceStrokedefault pb-[20px] xl:pb-[20px] 3xl:pb-[1.25vw] pt-[16px] xl:pt-[16px] 3xl:pt-[0.888vw]">
                        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-blackcolor font-medium">
                          Term
                        </div>
                        <div className="grid grid-cols-2 justify-between gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw] pt-[4px] ">
                          <Button
                            type="button"
                            className=" w-full py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] hover:bg-BrandNeutral950  bg-BrandNeutral950 text-white hover:text-white  text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[100%]"
                            variant="outline"
                            size="sm"
                          >
                            <i className="cloud-fillcircletick text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                            1 Year
                          </Button>
                          <Button
                            id="addBrands"
                            className=" relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] xl:py-[6px] 2xl:py-[8px] 3xl:py-[0.417vw] hover:bg-[#e6e8e9] hover:font-[500] text-[#3C4146] bg-transparent border border-[#E5E7EB] rounded-none text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                          >
                            <i className="cloud-circletick text-InterfaceTextsubtitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                            {t('flexible')}
                          </Button>
                        </div>
                      </div>
                      <div className="py-[16px] xl:py-[16px] 3xl:py-[0.888vw]">
                        <div className="text-[13px] lg:text-[13px] xl:text-[14px] 3xl:text-[0.729vw] text-blackcolor font-medium">
                          {t('quantity')}
                        </div>
                        <div className="pt-[4px]">
                          <div className="w-full justify-between px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] flex items-center rounded-none border border-[#E5E7EB] bg-white text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]">
                            <div
                              onClick={decrement}
                              className={`cursor-pointer ${value === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                              -
                            </div>
                            <div>{clickCount}</div>
                            <div
                              onClick={increment}
                              className={`cursor-pointer ${clickCount >= 2 || value === 10000 ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                              +
                            </div>
                          </div>

                          <div className=" flex items-center text-center mt-[4px] justify-center w-full relative px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] hover:bg-[#e6e8e9] hover:font-[500] text-InterfaceTextdefault font-semibold bg-transparent border border-[#E5E7EB] rounded-none text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                            <div>{clickCount === 0 ? 'N/A' : `$${value}`}</div>
                          </div>
                        </div>
                        <div className="flex justify-between items-center pt-[16px] xl:pt-[16px] 3xl:pt-[0.986vw] px-[12px] xl:px-[12px] 3xl:px-[0.625vw]">
                          <div className="flex items-center gap-1">
                            <i className="cloud-discount-fillshap text-[#F8B720] text-[12px] lg:text-[12px] xl:text-[12px] 3xl:text-[0.629vw]"></i>
                            <div className="text-InterfaceTextdefault text-[12px] lg:text-[12px] xl:text-[12px] 3xl:text-[0.629vw]">
                              {t('discountCoupon')}
                            </div>
                          </div>
                          <div className="flex items-center gap-1 pt-[4px]">
                            <i className="cloud-promotion-tag text-InterfaceTextprimary text-[12px] lg:text-[12px] xl:text-[12px] 3xl:text-[0.629vw]"></i>
                            <div className="text-InterfaceTextdefault text-[12px] lg:text-[12px] xl:text-[12px] 3xl:text-[0.629vw]">
                              {t('promotions')}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="">
                        <Button
                          onClick={() => {
                            setOpenCartPopupShow(true);
                            onClose();
                          }}
                          className="w-full cursor-pointer bg-BrandPrimarypurenew text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] applybtn  py-[7px] xl:py-[7px] 3xl:py-[0.451vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] text-interfacesurfacecomponent rounded-none"
                        >
                          {t('addToExistingCart')}
                        </Button>

                        <div
                          onClick={handleNewCartClick}
                          className="mt-[6px] w-full cursor-pointer bg- text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokesoft hover:bg-[#e6e8e9] font-[500] text-InterfaceTextdefault bg-interfacesurfacecomponent  rounded-none py-[7px] xl:py-[7px] 3xl:py-[0.451vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] "
                        >
                          {t('addToNewCart')}
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div>
                        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-blackcolor font-semibold">
                          {t('"newCart"')}
                        </div>
                        <div className="flex flex-col gap-1.5 py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                          <Label
                            htmlFor="firstName"
                            className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                          >
                            {t('cartName')}
                            <span className="text-red-500 text-xs mt-1">*</span>
                          </Label>
                          <Input
                            id="firstName"
                            type="text"
                            placeholder="Type"
                            className="placeholder:text-InterfaceTextsubtitle text-blackcolor  border border-InterfaceStrokehard"
                          />
                        </div>
                        <div className="flex flex-col gap-1.5 pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw]">
                          <Label
                            htmlFor="firstName"
                            className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                          >
                            {t('description')}
                          </Label>
                          <Textarea
                            placeholder={t('writeTextHere') + '...'}
                            className="placeholder:text-InterfaceTextsubtitle placeholder:text-[12px] text-blackcolor  border-InterfaceStrokehard rounded-none"
                          />
                        </div>
                        <div className="flex flex-col gap-1.5  pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] ">
                          <Label
                            htmlFor="firstName"
                            className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                          >
                            {t('endCustomer')}
                          </Label>
                          <Select defaultValue="Select End Customer">
                            <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm leading-6 border border-InterfaceStrokehard rounded-none text-[12px] 2xl:text-[14px] xl:text-[13px] 3xl:text-[0.729vw] py-[6px] xl:py-[6px] 2xl:py-[7px] 3xl:py-[0.361vw] px-[12px] xl:px-[14px] 3xl:px-[0.833vw]">
                              <SelectValue
                                placeholder={t('selectEndCustomer')}
                                className="text-[12px] 2xl:text-[14px] xl:text-[12px] 3xl:text-[0.729vw]"
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup className=" text-InterfaceTextsubtitle border-none text-[12px] 2xl:text-[14px] xl:text-[12px] 3xl:text-[0.729vw]  bg-interfacesurfacecomponent">
                                <SelectItem value={t('selectEndCustomer')}>
                                  {t('selectEndCustomer')}
                                </SelectItem>
                                <SelectItem value="india">{t('india')}</SelectItem>
                                <SelectItem value="mea">{t('mea')}</SelectItem>
                                <SelectItem value="turkey">{t('turkey')}</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="">
                        <div className="w-full cursor-pointer bg-BrandPrimarypurenew text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] applybtn  py-[7px] xl:py-[7px] 3xl:py-[0.451vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] text-white">
                          {t('addContinuePurchase')}
                        </div>
                        <div className="mt-[6px] w-full cursor-pointer bg- text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokesoft hover:bg-[#e6e8e9] font-[500] text-InterfaceTextdefault bg-interfacesurfacecomponent   rounded-none py-[7px] xl:py-[7px] 3xl:py-[0.451vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] ">
                          {t('addGoToCart')}
                        </div>

                        <div
                          onClick={() => setIsNewCart(false)}
                          className="mt-[6px] w-full cursor-pointer bg- text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokesoft hover:bg-[#e6e8e9] font-[500] text-InterfaceTextdefault bg-interfacesurfacecomponent   rounded-none py-[7px] xl:py-[7px] 3xl:py-[0.451vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] "
                        >
                          {t('cancel')}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      <OpencartPopup open={openCartPopupShow} onClose={() => setOpenCartPopupShow(false)} />
      <BundledProducts open={bundledProductsShow} onClose={() => setBundledProductsShow(false)} />
    </>
  );
}
