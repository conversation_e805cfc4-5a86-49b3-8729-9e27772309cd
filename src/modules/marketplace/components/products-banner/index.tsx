'use client';
import {
  <PERSON><PERSON>,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { useState } from 'react';
import { Roboto } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function ProductsBanner() {
  const t = useTranslations();

  const [popoverOpen, setPopoverOpen] = useState(false);
  const handleCancel = () => {
    setPopoverOpen(false);
  };
  return (
    <>
      <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
        <PopoverTrigger asChild>
          <Link href="">
            <Button className="text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] px-[12px] lg:px-[11px] xl:px-[12px] 2xl:px-[14px] 3xl:px-[0.729vw] py-[10px] lg:py-[8px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.525vw] font-[500] bg-transparent">
              <i className="cloud-threedot text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]" />
            </Button>
          </Link>
        </PopoverTrigger>
        <PopoverContent className="absolute top-0 right-[-11px] xl:right-[-11px] 2xl:right-[-12px] 3xl:right-[-15px] p-0  w-[300px] xl:w-[325px] 2xl:w-[365px] 3xl:w-[18.91vw] rounded-none bg-[#FFF]">
          <div>
            <div className="border-b border-[#E5E7EB] flex justify-between items-center px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px] xl:py-[10px] 2xl:py-[14px] 3xl:py-[0.729vw]">
              <h4
                className={`${roboto.className} text-[#212325] text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-medium leading-none`}
              >
                {t('productsBanner')}
              </h4>
            </div>
            <div className="p-[14px] xl:p-[14px] 2xl:p-[16px] 3xl:p-[0.833vw]">
              <div>
                <RadioGroup defaultValue="1" className="flex flex-col gap-[12px]">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="1" id="r1" />
                    <Label className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('myRecentPurchases')}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="2" id="r2" />
                    <Label className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('topSellingProductsbyStore')}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="3" id="r3" />
                    <Label className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('topSellingProductsbyAWS')}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="4" id="r4" />
                    <Label className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('topSellingProductsbyMicroSoft')}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="5" id="r5" />
                    <Label className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('topSellingProductsbyGoogle')}
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            <div className="flex justify-end items-center gap-[8px] 3xl:gap-[0.417vw] px-[18px] xl:px-[18px] 2xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[14px] 2xl:py-[16px] 3xl:py-[0.833vw] border-t border-[#BECDE3]">
              <Button
                onClick={handleCancel}
                className="text-InterfaceTextdefault bg-transparent border border-InterfaceStrokesoft px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.36vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500]"
              >
                <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('cancel')}
              </Button>
              <Button
                onClick={handleCancel}
                className="text-InterfaceTextwhite bg-BrandNeutralpure border border-BrandNeutral700 px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.36vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"
              >
                <i className="cloud-copy-success text-InterfaceTextwhite text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('apply')}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
