'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';

export default function AvailableDiscountCoupons({ open, onClose }: CommanSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetTrigger></SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              Available Discount Coupons
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[24px] lg:p-[24px] xl:p-[24px] 3xl:p-[1.25vw]">
          <div className="space-y-[24px] 3xl:space-y-[1.25vw]">
            <div className="border border-InterfaceStrokesoft shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),_0px_1px_2px_-1px_rgba(0,0,0,0.10)]">
              <div className="bg-BrandNeutralpure flex justify-between items-center py-[8px] lg:py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-background text-[22px] lg:text-[22px] xl:text-[22px] 3xl:text-[0.885vw] font-bold leading-[140%] ">
                  10% Off
                </div>
                <div className="text-background text-[15px] lg:text-[15px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] font-medium leading-[140%] ">
                  Valid Till: 18/05/2025
                </div>
              </div>

              <div className="py-[16px] lg:py-[16px] xl:py-[16px] 3xl:py-[0.833vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 3xl:text-[0.885vw] font-semibold leading-[140%]">
                  #1122334455
                </div>

                <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                  Get 10% off on orders above 5 Quantity.
                </div>
              </div>
            </div>

            <div className="border border-InterfaceStrokesoft shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),_0px_1px_2px_-1px_rgba(0,0,0,0.10)]">
              <div className="bg-BrandNeutralpure flex justify-between items-center py-[8px] lg:py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-background text-[22px] lg:text-[22px] xl:text-[22px] 3xl:text-[0.885vw] font-bold leading-[140%] ">
                  15% Off
                </div>
                <div className="text-background text-[15px] lg:text-[15px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.833vw] font-medium leading-[140%] ">
                  Valid Till: 12/05/2025
                </div>
              </div>

              <div className="py-[16px] lg:py-[16px] xl:py-[16px] 3xl:py-[0.833vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 3xl:text-[0.885vw] font-semibold leading-[140%]">
                  #1122334455
                </div>

                <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                  Get 10% off on orders above 5 Quantity.
                </div>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
