'use client';
import { ClonePopup } from '@/modules/home/<USER>/open-carts/clonepopup';
import { DeletePopup } from '@/modules/home/<USER>/open-carts/deletpopup';
import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';

export function CartThreeDotPopup() {
  const [popoverOpen, setPopoverOpen] = useState(false);

  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger asChild className="">
        <Button
          className={`text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] 
                      px-[12px] lg:px-[11px] xl:px-[12px] 2xl:px-[14px] 3xl:px-[0.729vw] 
                      py-[10px] lg:py-[8px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.525vw] 
                      font-[500] rounded-none bg-transparent ${popoverOpen ? 'bg-BrandNeutral100' : ''}`}
        >
          <i
            className="cloud-threedot text-InterfaceTextsubtitle hover:bg-[#e6e8e9] cursor-pointer px-[12px] xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[4px] xl:py-[4px] 2xl:py-[6px] 3xl:py-[0.36vw]
              text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="absolute top-0 right-[-11px] xl:right-[-11px] 2xl:right-[-12px] 3xl:right-[-15px] p-0  w-[145px] xl:w-[145px] 2xl:w-[170px] 3xl:w-[8.91vw] rounded-none bg-[#FFF]">
        <div>
          <DeletePopup />
          <ClonePopup />
        </div>
      </PopoverContent>
    </Popover>
  );
}
