'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import React from 'react';
import ProductDetailedView from '../product-details-view';
import AvailableDiscountCoupons from '../discount-coupons';
import AvailablePromotions from '../promotions';
import { useTranslations } from 'next-intl';
import BundledProducts from '../bundled-products-popup';

export default function GridView() {
  const t = useTranslations();

  const [productDetailedViewShow, setProductDetailedViewShow] = React.useState(false);
  const [availableDiscountCouponsShow, setAvailableDiscountCouponsShow] = React.useState(false);
  const [availablePromotionsShow, setAvailablePromotionsShow] = React.useState(false);
  const [bundledProductsShow, setBundledProductsShow] = React.useState(false);

  const data = [
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['productview', 'fillstar'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['grid', 'fillstar'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['favriote', 'productview'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['fillstar'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['grid', 'favriote'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['fillstar', 'productview'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['grid', 'favriote'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['favriote'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['grid', 'favriote'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['fillstar', 'productview'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['favriote'],
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      icons: ['fillstar'],
    },
  ];

  return (
    <>
      {data.map((items, index) => (
        <div
          key={index}
          className="group border border-InterfaceStrokesoft bg-background hover:shadow-[0px_10px_15px_-3px_rgba(0,0,0,0.10),0px_4px_6px_0px_rgba(0,0,0,0.05)] cursor-pointer"
        >
          <div className="py-[16px] 2xl:py-[16px] 3xl:py-[1.042vw] px-[20px] 2xl:px-[20px] 3xl:px-[1.25vw]">
            <div className="flex justify-between gap-1 mb-[8px] 3xl:mb-[0.417vw]">
              <Image
                width={40}
                height={40}
                className="w-[40px] 3xl:w-[2.083vw] h-[40px] 3xl:h-[2.083vw]"
                src="/images/svg/microsoft_icon.svg"
                alt="microsoft logo"
              />
              <div className="flex gap-[12px] 3xl:gap-[0.625vw]">
                {items.icons?.includes('grid') && (
                  <i className="cloud-grid text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer" />
                )}
                {items.icons?.includes('productview') && (
                  <i
                    onClick={() => setBundledProductsShow(true)}
                    className="cloud-productview text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer"
                  />
                )}
                {items.icons?.includes('fillstar') && (
                  <i className="cloud-fillstar text-BrandSupport2400 text-[14px] 3xl:text-[0.833vw] cursor-pointer" />
                )}
                {items.icons?.includes('favriote') && (
                  <i className="cloud-favriote text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer" />
                )}
              </div>
            </div>

            <div
              onClick={() => setProductDetailedViewShow(true)}
              className="text-InterfaceTextsubtitle text-[11px] 3xl:text-[0.573vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw] uppercase"
            >
              {items.type}
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="group-hover:text-BrandSupport17001 text-InterfaceTexttitle text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%] mb-[12px] 3xl:mb-[0.729vw]">
                    {items.title}
                  </div>
                </TooltipTrigger>
                <TooltipContent className="min-w-[350px] 3xl:min-w-[17.99vw] bg-background rounded-none">
                  <div className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                    {t('description')} :
                  </div>
                  <p className="text-InterfaceTextdefault text-[13px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    The premium Office suite for organizations - <br />
                    including Word, Excel, PowerPoint, Outlook,
                    <br /> OneNote, Access and Skype for Business
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <div
              onClick={() => setProductDetailedViewShow(true)}
              className="space-y-[4px] 3xl:space-y-[0.208vw] mb-[12px] 3xl:mb-[0.729vw]"
            >
              <div className="grid grid-cols-12 gap-1">
                <div className="col-span-3 font-[500]">{t('segment')}</div>
                <div className="col-span-9 text-InterfaceTextdefault">: {items.segment}</div>
              </div>
              <div className="grid grid-cols-12 gap-1">
                <div className="col-span-3 font-[500]">{t('skuId')}</div>
                <div className="col-span-9 text-InterfaceTextsubtitle">: {items.SKUID}</div>
              </div>
              <div className="grid grid-cols-12 gap-1">
                <div className="col-span-3 font-[500]">{t('options')}</div>
                <div className="col-span-9 text-InterfaceTextsubtitle">: {items.options}</div>
              </div>
              <div className="grid grid-cols-12 gap-1">
                <div className="col-span-3 font-[500]">{t('billType')}</div>
                <div className="col-span-9 text-InterfaceTextsubtitle">: {items.billType}</div>
              </div>
            </div>

            <div className="text-InterfaceTexttitle text-[19px] font-[700] mb-[12px]">
              {items.totalValue}
            </div>

            <div className="flex items-center gap-[16px] mb-[12px]">
              <div
                className="flex items-center gap-2"
                onClick={(event) => {
                  event.stopPropagation();
                  setAvailableDiscountCouponsShow(true);
                }}
              >
                <i className="cloud-discount-fillshap text-BrandSupport2400 text-[12px]" />
                <div className="text-InterfaceTextdefault text-[12px]">{t('eligibleOffers')}</div>
              </div>

              <div
                className="flex items-center gap-2"
                onClick={(event) => {
                  event.stopPropagation();
                  setAvailablePromotionsShow(true);
                }}
              >
                <i className="cloud-promotion-tag text-BrandSupport1pure text-[12px]" />
                <div className="text-InterfaceTextdefault text-[12px]">{t('promotions')}</div>
              </div>
            </div>

            <div>
              <Button className="flex justify-center bg-yellowbg px-[13px] py-[8px] rounded-none font-[500] text-InterfaceTexttitle">
                <i className="cloud-cart text-[16px]" />
                <span className="text text-[14px]">{t('addToCart')}</span>
              </Button>
            </div>
          </div>
        </div>
      ))}

      <ProductDetailedView
        open={productDetailedViewShow}
        onClose={() => setProductDetailedViewShow(false)}
      />
      <AvailableDiscountCoupons
        open={availableDiscountCouponsShow}
        onClose={() => setAvailableDiscountCouponsShow(false)}
      />
      <AvailablePromotions
        open={availablePromotionsShow}
        onClose={() => setAvailablePromotionsShow(false)}
      />
      <BundledProducts open={bundledProductsShow} onClose={() => setBundledProductsShow(false)} />
    </>
  );
}
