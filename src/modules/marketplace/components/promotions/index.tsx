'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';

export default function AvailablePromotions({ open, onClose }: CommanSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetTrigger></SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              Available Promotions
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[24px] lg:p-[24px] xl:p-[24px] 3xl:p-[1.25vw]">
          <div className="space-y-[24px]">
            <div className="border border-InterfaceStrokesoft shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),_0px_1px_2px_-1px_rgba(0,0,0,0.10)]">
              <div className="bg-BrandNeutralpure flex justify-between items-center py-[8px] lg:py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-background text-[17px] lg:text-[17px] xl:text-[17px] 3xl:text-[0.885vw] font-bold leading-[140%] ">
                  15% Discount
                </div>
                <div className="text-background text-[24px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium leading-[140%] ">
                  End Date: 19/05/2025
                </div>
              </div>

              <div className="py-[16px] lg:py-[16px] xl:py-[16px] 3xl:py-[0.833vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 3xl:text-[0.885vw] font-semibold leading-[140%]">
                  Do more with Microsoft 365 E3 Promo
                </div>

                <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                  Promotion ID: 39NFJQT206QX-0002-39NFJQT1Q5KK
                </div>

                <div className="grid grid-cols-3 gap-[20px] xl:gap-[20px] 3xl:gap-[1.042vw] pt-[16px] lg:pt-[16px] xl:pt-[16px] 2xl:pt-[16px] 3xl:pt-[0.833vw] mt-[12px] lg:mt-[12px] xl:mt-[12px] 2xl:mt-[12px] 3xl:mt-[0.625vw] border-t border-InterfaceStrokesoft">
                  <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                    <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Promotion Type
                    </div>
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
                      Percent Discount
                    </div>
                  </div>

                  <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                    <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Term
                    </div>
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
                      1 Year
                    </div>
                  </div>

                  <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                    <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Bill Type
                    </div>
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
                      Annual
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="border border-InterfaceStrokesoft shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),_0px_1px_2px_-1px_rgba(0,0,0,0.10)]">
              <div className="bg-BrandNeutralpure flex justify-between items-center py-[8px] lg:py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-background text-[17px] lg:text-[17px] xl:text-[17px] 3xl:text-[0.885vw] font-bold leading-[140%] ">
                  15% Discount
                </div>
                <div className="text-background text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium leading-[140%] ">
                  End Date: 28/05/2025
                </div>
              </div>

              <div className="py-[16px] lg:py-[16px] xl:py-[16px] 3xl:py-[0.833vw] px-[20px] lg:px-[20px] xl:px-[20px] 3xl:px-[1.042vw]">
                <div className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 3xl:text-[0.885vw] font-semibold leading-[140%]">
                  Do more with Microsoft 365 E3 Promo
                </div>

                <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                  Promotion ID: 39NFJQT206QX-0002-39NFJQT1Q5KK
                </div>

                <div className="grid grid-cols-3 gap-[20px] xl:gap-[20px] 3xl:gap-[1.042vw] pt-[16px] lg:pt-[16px] xl:pt-[16px] 2xl:pt-[16px] 3xl:pt-[0.833vw] mt-[12px] lg:mt-[12px] xl:mt-[12px] 2xl:mt-[12px] 3xl:mt-[0.625vw] border-t border-InterfaceStrokesoft">
                  <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                    <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Promotion Type
                    </div>
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
                      Percent Discount
                    </div>
                  </div>

                  <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                    <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Term
                    </div>
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
                      1 Year
                    </div>
                  </div>

                  <div className="space-y-[4px] 3xl:space-y-[0.208vw]">
                    <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Bill Type
                    </div>
                    <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]">
                      Annual
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
