'use client';
import {
  But<PERSON>,
  <PERSON>over,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';
import { useTranslations } from 'next-intl';

export function CardThreeDotPopup() {
  const t = useTranslations();

  const [popoverOpen, setPopoverOpen] = useState(false);

  const handleCancel = () => {
    setPopoverOpen(false);
  };

  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger asChild>
        <Button
          className={`p-0 text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]
                      font-[500] rounded-none bg-transparent ${popoverOpen ? 'bg-BrandNeutral100' : ''}`}
        >
          <i
            className="cloud-threedot text-InterfaceTextsubtitle 
              text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="absolute top-0 right-[-11px] xl:right-[-11px] 2xl:right-[-12px] 3xl:right-[-15px] p-0  w-[145px] xl:w-[145px] 2xl:w-[170px] 3xl:w-[8.91vw] rounded-none bg-[#FFF]">
        <div>
          <div
            onClick={handleCancel}
            className="cursor-pointer hover:bg-BrandNeutral100 hover:text-InterfaceTextprimary px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] border-b border-InterfaceStrokesoft text-InterfaceTextdefault text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          >
            <i className="cloud-receipt mr-[8px] 3xl:mr-[0.417vw] text-InterfaceTextsubtitle" />
            {t('addtoQuote')}
          </div>
          <div
            onClick={handleCancel}
            className="cursor-pointer hover:bg-BrandNeutral100 hover:text-InterfaceTextprimary px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] border-b border-InterfaceStrokesoft text-InterfaceTextdefault text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          >
            <i className="cloud-cart mr-[8px] 3xl:mr-[0.417vw] text-InterfaceTextsubtitle" />
            {t('addToCart')}
          </div>
          <div
            onClick={handleCancel}
            className="cursor-pointer hover:bg-BrandNeutral100 hover:text-InterfaceTextprimary px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] border-b border-InterfaceStrokesoft text-InterfaceTextdefault text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          >
            <i className="cloud-favriote mr-[8px] 3xl:mr-[0.417vw] text-InterfaceTextsubtitle" />
            {t('addtoFavourite')}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
