import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import React from 'react';
import ProductDetailedView from '../product-details-view';
import AvailableDiscountCoupons from '../discount-coupons';
import AvailablePromotions from '../promotions';
import { useTranslations } from 'next-intl';

export default function ListView() {
  const t = useTranslations();

  const [productDetailedViewShow, setProductDetailedViewShow] = React.useState(false);
  const [availableDiscountCouponsShow, setAvailableDiscountCouponsShow] = React.useState(false);
  const [availablePromotionsShow, setAvailablePromotionsShow] = React.useState(false);
  const data = [
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
      favorite: true,
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: t('productivityCollaboration'),
      title: 'Microsoft 365 Apps for Enterprise',
      description:
        'The premium Office suite for organizations - including Word, Excel, PowerPoint, Outlook, OneNote, Access and Skype for Business',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
  ];

  return (
    <>
      {data.map((items, index) => {
        return (
          <div
            key={index}
            onClick={() => setProductDetailedViewShow(true)}
            className="group border border-InterfaceStrokesoft bg-background hover:shadow-[0px_10px_15px_-3px_rgba(0,0,0,0.10),0px_4px_6px_0px_rgba(0,0,0,0.05)] cursor-pointer"
          >
            <div className="py-[16px] 2xl:py-[16px] 3xl:py-[1.042vw] px-[20px] 2xl:px-[20px] 3xl:px-[1.25vw]">
              <div className="grid grid-cols-12 gap-2">
                <div className="col-span-12 xl:col-span-8 3xl:col-span-9">
                  <div className="grid grid-cols-12 items-center gap-[18px] 3xl:gap-[0.938vw]">
                    <div className="w-full col-span-1">
                      <Image
                        width={64}
                        height={64}
                        className="w-[64px] 3xl:w-[3.333vw] h-[64px] 3xl:h-[3.333vw]"
                        src="/images/svg/microsoft_icon.svg"
                        alt="microsoft logo"
                      />
                    </div>
                    <div className="col-span-11">
                      <div className="group-hover:text-BrandSupport17001 text-InterfaceTexttitle text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                        {items.title}
                      </div>
                      <p className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[100%] truncate mb-[8px] 3xl:mb-[0.417vw]">
                        {items.description}
                      </p>
                      <div className="flex flex-wrap items-center">
                        <div className="text-InterfaceTextsubtitle hover:text-BrandSupport17001 hover:font-[500] text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] uppercase pr-[7px] 3xl:pr-[0.365vw] border-r-InterfaceStrokehard border-r-[2px]">
                          {items.type}
                        </div>
                        <div className="flex px-[7px] 3xl:px-[0.365vw] border-r-InterfaceStrokehard border-r-[2px] uppercase">
                          {/* <div className="col-span-3 text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%]">
                            Segment{' '}
                          </div> */}
                          <div className="col-span-9 text-InterfaceTextsubtitle hover:text-BrandSupport17001 hover:font-[500] text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                            {items.segment}
                          </div>
                        </div>
                        <div className="flex px-[7px] 3xl:px-[0.365vw] border-r-InterfaceStrokehard border-r-[2px] uppercase">
                          <div className="col-span-3 text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%]">
                            {t('skuId')}{' '}
                          </div>
                          <div className="col-span-9 text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                            : {items.SKUID}
                          </div>
                        </div>
                        <div className="flex px-[7px] 3xl:px-[0.365vw] border-r-InterfaceStrokehard border-r-[2px] uppercase">
                          <div className="col-span-3 text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%]">
                            {t('options')}
                          </div>
                          <div className="col-span-9 text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                            : {items.options}
                          </div>
                        </div>
                        <div className="flex px-[7px] 3xl:px-[0.365vw] uppercase">
                          <div className="col-span-3 text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[500] leading-[140%]">
                            {t('billType')}
                          </div>
                          <div className="col-span-9 text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                            : {items.billType}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-span-12 xl:col-span-4 3xl:col-span-3">
                  <div className="flex items-center justify-end gap-[16px] 3xl:gap-[0.833vw]">
                    <div className="flex items-center gap-[16px] 3xl:gap-[0.833vw]">
                      <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[1.042vw] font-[700] leading-[140%]">
                        {items.totalValue}
                      </div>
                      <div>
                        <Button className="flex justify-center bg-yellowbg px-[13px] xl:px-[13px] 2xl:px-[13px] 3xl:px-[0.729vw] py-[8px] xl:py-[9px] 2xl:py-[9px] 3xl:py-[0.521vw] rounded-none font-[500] text-InterfaceTexttitle">
                          <i className="cloud-cart  text-[16px] 3xl:text-[0.833vw]"></i>
                          <span className="text text-[14px] xl:text-[16px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                            {t('addToCart')}
                          </span>
                        </Button>
                      </div>
                    </div>
                    {items.favorite ? (
                      <i className="cloud-fillstar text-BrandSupport2400 text-[14px] 3xl:text-[0.833vw] cursor-pointer"></i>
                    ) : (
                      <i className="cloud-favriote text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer"></i>
                    )}
                  </div>

                  <div className="flex items-center justify-end gap-[16px] 3xl:gap-[0.833vw] mt-[18px] 3xl:mt-[1.042vw]">
                    <div
                      onClick={(event) => {
                        event.stopPropagation();
                        setAvailableDiscountCouponsShow(true);
                      }}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      <i className="cloud-discount-fillshap text-BrandSupport2400 text-[12px] 3xl:text-[0.625vw]"></i>
                      <div className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%]">
                        {t('discountCoupon')}
                      </div>
                    </div>
                    <div
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={(event) => {
                        event.stopPropagation();
                        setAvailablePromotionsShow(true);
                      }}
                    >
                      <i className="cloud-promotion-tag text-BrandSupport1pure text-[12px] 3xl:text-[0.625vw]"></i>
                      <div className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%]">
                        {t('promotions')}
                      </div>
                    </div>
                    <i className="invisible cloud-favriote text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      })}
      <ProductDetailedView
        open={productDetailedViewShow}
        onClose={() => {
          setProductDetailedViewShow(false);
        }}
      />
      <AvailableDiscountCoupons
        open={availableDiscountCouponsShow}
        onClose={() => setAvailableDiscountCouponsShow(false)}
      />
      <AvailablePromotions
        open={availablePromotionsShow}
        onClose={() => setAvailablePromotionsShow(false)}
      />
    </>
  );
}
