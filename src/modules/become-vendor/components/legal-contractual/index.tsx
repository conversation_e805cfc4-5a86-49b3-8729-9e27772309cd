'use client';
import React from 'react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Input,
  Label,
  RadioGroup,
  RadioGroupItem,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function LegalContractual() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Legal & Contractual
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw]">
            <div className="grid grid-cols-3 gap-[15px] 3xl:gap-[1.667vw] mt-[20px]">
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Signing Authority Name
                </Label>
                <Input
                  type="text"
                  placeholder="Type"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Signing Authority Title
                </Label>
                <Input
                  type="text"
                  placeholder="Type"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  NDA Executed With REdington?
                </Label>
                <RadioGroup defaultValue="comfortable" className="flex custrad">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="default" id="r1" />
                    <Label htmlFor="r1">Yes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="comfortable" id="r2" />
                    <Label htmlFor="r2">No</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
