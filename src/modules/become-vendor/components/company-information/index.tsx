'use client';
import React from 'react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Input,
  Label,
  PhoneInput,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function CompanyInformation() {
  return (
    <>
      <div className="bg-background mt-[20px] xl:mt-[22px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
        <Accordion type="single" collapsible className="w-full p-0 ">
          <AccordionItem value="item-1" className="border-none">
            <AccordionTrigger className="p-0 hover:no-underline">
              Company Information
            </AccordionTrigger>
            <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw]">
              <div className="grid grid-cols-3 gap-y-[14px] xl:gap-y-[16px] 3xl:gap-y-[0.833vw] gap-x-[22px] xl:gap-x-[24px] 3xl:gap-[1.25vw] mt-[20px]">
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Legal Entity Name *
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter legal Entity Name"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Registered Business Address *
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter Address"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Year Founded
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter Year "
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Company Reg. / VAT / Tax ID
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter Company Reg./VAT/Tax ID"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Website
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter Website URL"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    LinkedIn / Social Handle
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter LinkedIn URL"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Primary Contact Name
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter Name"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Primary Contact Title
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter Title"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5">
                  <Label
                    htmlFor="firstname"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Email *
                  </Label>
                  <Input
                    type="text"
                    placeholder="Enter Email Address"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
                <div className="grid gap-1.5 ">
                  <Label
                    htmlFor="timezone"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Select Time Zone *
                  </Label>
                  <div className=" border border-[#E5E7EB] text-[#7F8488] flex bg-InterfaceTextwhite w-full">
                    <Select>
                      <SelectTrigger className="w-full py-1 border-InterfaceTextwhite">
                        <span className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          Select Timezone
                        </span>
                      </SelectTrigger>
                      <SelectContent className="bg-InterfaceTextwhite text-[#7F8488]">
                        <SelectGroup>
                          <SelectItem value="apple">UAE</SelectItem>
                          <SelectItem value="banana">India</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-1.5">
                  <Label
                    htmlFor="Phone"
                    className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Direct Phone *
                  </Label>
                  <PhoneInput defaultCountry="IN" className="w-full" placeholder="Enter Number" />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </>
  );
}
