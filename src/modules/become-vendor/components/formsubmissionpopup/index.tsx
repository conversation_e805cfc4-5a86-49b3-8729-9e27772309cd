import { Sheet, SheetContent, SheetTitle } from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON><PERSON> } from 'next/font/google';
import * as React from 'react';
import { TermsSheetProps } from '@/types';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function FormSubmissionPopup({ open, onClose }: TermsSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[460px] xl:max-w-[450px] 2xl:max-w-[570px] 3xl:max-w-[29.69vw] p-[0px] flex flex-col h-full`}
        side={'right'}
      >
        <div className="flex-1 overflow-y-auto mb-[30px] xl:mb-0 mt-[40px] xl:mt-[40px] 2xl:mt-[55px] 3xl:mt-[6.29vw] mx-[20px] xl:mx-[40px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[20px] xl:gap-[20px] 2xl:gap-[40px] 3xl:gap-[2.08vw]  my-[60px] xl:my-[60px] 3xl:my-[6.25vw]">
            <div>
              {/* <i className="cloud-notification text-BrandSupport1pure text-[50px] xl:text-[40px] 2xl:text-[60px] 3xl:text-[2.6vw] font-[400]"></i> */}
              <i className="cloud-info text-BrandPrimarypure text-[50px] xl:text-[40px] 2xl:text-[60px] 3xl:text-[2.6vw] font-[400]"></i>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw]">
              <div className="flex flex-col justify-center items-center mx-[20px] xl:mx-[20px] 3xl:mx-[1.042vw]">
                <center className="text-InterfaceTexttitle text-[26px] xl:text-[26px] 2xl:text-[34px] 3xl:text-[1.875vw] font-[400] leading-[140%] text-center">
                  Thank You, Your form has been submitted successfully!
                </center>
              </div>
            </div>

            <div className="text-center ">
              <Link
                href="/home"
                className="  rounded-none signinbtn  px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.525vw] border border-InterfaceStrokesoft text-InterfaceTextdefault  flex gap-[8px] 3xl:gap-[0.417vw] items-center font-medium w-fit text-[15px] 3xl:text-[0.781vw]"
              >
                <i className="cloud-arrowleft text-[13px] 3xl:text-[0.681vw]"></i>
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
