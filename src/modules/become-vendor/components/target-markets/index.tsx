'use client';
import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Checkbox,
  Label,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function TargetMarkets() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Target Markets/Stores
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw]">
            <div className="grid grid-cols-5 gap-y-[14px] xl:gap-y-[16px] 3xl:gap-y-[0.833vw] gap-x-[22px] xl:gap-x-[24px] 3xl:gap-[1.25vw] my-[20px]">
              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  India
                </label>
              </div>
              <div className="flex items-center gap-2  ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Middle East & Africa
                </label>
              </div>
              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Turkey
                </label>
              </div>
              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Global / Other
                </label>
              </div>
            </div>
            <div className="grid col-span-3 gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                Local Subsidiaries (Entity & Country)
              </Label>
              <Textarea
                placeholder="Write text here ..."
                className="w-full border-[#BBC1C7] rounded-none"
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
