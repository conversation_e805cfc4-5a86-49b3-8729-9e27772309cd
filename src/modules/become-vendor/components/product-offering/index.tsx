'use client';
import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Checkbox,
  Input,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function ProductOffering() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Product & Offering Type
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw]">
            <div>
              <div>
                <div className=" mt-[20px] 3xl:mt-[1.042vw] text-[#212325] text-[15px] 3xl:text-[0.781vw] font-medium leading-[140%]">
                  Software/Digital Products
                </div>
              </div>
              <div className="grid grid-cols-3 gap-y-[14px] xl:gap-y-[16px] 3xl:gap-y-[0.833vw] gap-x-[22px] xl:gap-x-[24px] 3xl:gap-[1.25vw] mt-[8px] 3xl:mt-[0.417vw]">
                <div className="grid gap-1.5">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" className="" />
                    <label
                      htmlFor="terms"
                      className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Subscription - Recurring Billing
                    </label>
                  </div>
                  <div className="grid gap-1.5">
                    <Input
                      type="text"
                      placeholder="Approx. SKUs"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
                <div className="grid gap-1.5">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" className="" />
                    <label
                      htmlFor="terms"
                      className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Subscription - One-Time Billing
                    </label>
                  </div>
                  <div className="grid gap-1.5">
                    <Input
                      type="text"
                      placeholder="Approx. SKUs"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
                <div className="grid gap-1.5">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" className="" />
                    <label
                      htmlFor="terms"
                      className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Perpetual Licence
                    </label>
                  </div>
                  <div className="grid gap-1.5">
                    <Input
                      type="text"
                      placeholder="Approx. SKUs"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className=" my-[20px] 3xl:my-[1.042vw]">
              <div>
                <div className=" text-[#212325] text-[15px] 3xl:text-[0.781vw] font-medium leading-[140%]">
                  Services
                </div>
              </div>
              <div className="grid grid-cols-3 gap-[15px] 3xl:gap-[1.667vw] mt-[8px] 3xl:mt-[0.417vw]">
                <div className="grid gap-1.5">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" className="" />
                    <label
                      htmlFor="terms"
                      className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Service - Recurring
                    </label>
                  </div>
                  <div className="grid gap-1.5">
                    <Input
                      type="text"
                      placeholder="Approx. SKUs"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
                <div className="grid gap-1.5">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" className="" />
                    <label
                      htmlFor="terms"
                      className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Service - One-Time/Project-Based
                    </label>
                  </div>
                  <div className="grid gap-1.5">
                    <Input
                      type="text"
                      placeholder="Approx. SKUs"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div>
                <div className=" text-[#212325] text-[15px] 3xl:text-[0.781vw] font-medium leading-[140%]">
                  Lead-Only Listings
                </div>
              </div>
              <div className="grid grid-cols-3 gap-[15px] 3xl:gap-[1.667vw] mt-[8px] 3xl:mt-[0.417vw]">
                <div className="grid gap-1.5">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" className="" />
                    <label
                      htmlFor="terms"
                      className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      Non-orderable - Lead Generation Only
                    </label>
                  </div>
                  <div className="grid gap-1.5">
                    <Input
                      type="text"
                      placeholder="Approx. SKUs"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
