'use client';
import React from 'react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function AdditionalInfo() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Additional Information
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw] pb-0">
            <div className="grid col-span-3 gap-1.5 my-[20px]">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                Marketing Collateral Links, Preferred Go-Live Window or Special Requirements
              </Label>
              <Textarea
                placeholder="Write text here ..."
                className="w-full border-[#BBC1C7] rounded-none"
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
