'use client';
import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Checkbox,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function TechnicalEnablement() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Technical Enablement
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw] pb-0">
            <div>
              <div className=" mt-[20px] 3xl:mt-[1.042vw] text-[#212325] text-[15px] 3xl:text-[0.781vw] font-medium leading-[140%]">
                Provisioning & Integration Method
              </div>
            </div>
            <div className="grid grid-cols-6  mb-[10px] 3xl:mb-[0.521vw]  mt-[10px] 3xl:mt-[0.521vw]">
              <div className="col-span-2 flex items-center gap-2  ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Integrate Own APIs
                </label>
              </div>
              <div className="col-span-2 flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Consume CloudQuarks APIs
                </label>
              </div>
              <div className="flex items-center gap-2">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Manual Fulfilment
                </label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
