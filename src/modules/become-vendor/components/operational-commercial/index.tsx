'use client';
import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Input,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function OperationalCommercial() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Operational & Commercial Details
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw]">
            <div className="grid grid-cols-3 gap-y-[14px] xl:gap-y-[16px] 3xl:gap-y-[0.833vw] gap-x-[22px] xl:gap-x-[24px] 3xl:gap-[1.25vw] mt-[20px]">
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Transaction Currency/Currencies *
                </Label>
                <Input
                  type="text"
                  placeholder="Type"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Preferred Billing Model (Pre-paid / Post-paid)
                </Label>
                <Input
                  type="text"
                  placeholder="Type"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Standard Credit Terms (Days)
                </Label>
                <Input
                  type="text"
                  placeholder="Type "
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Support SLA Tiers Offered
                </Label>
                <Input
                  type="text"
                  placeholder="Type"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Tax & Invoicing Jurisdictions(s)
                </Label>
                <Input
                  type="text"
                  placeholder="Type"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
