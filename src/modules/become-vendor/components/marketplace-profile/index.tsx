'use client';
import React from 'react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Input,
  Label,
  PhoneInput,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function MarketPlaceProfile() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Marketplace Profile
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw]">
            <div className="grid grid-cols-3 gap-y-[14px] xl:gap-y-[16px] 3xl:gap-y-[0.833vw] gap-x-[22px] xl:gap-x-[24px] 3xl:gap-[1.25vw] mt-[20px]">
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Vendor Display Name (Shown to Buyers) *
                </Label>
                <Input
                  type="text"
                  placeholder="Enter Display Number"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Support Email for End-Customers
                </Label>
                <Input
                  type="text"
                  placeholder="Enter Email Address"
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="Phone"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Support Phone for End-Customers
                </Label>
                <PhoneInput defaultCountry="IN" className="w-full" placeholder="Enter Number" />
              </div>
              <div className="grid col-span-3 gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  50-Word Company Blurb
                </Label>
                <Textarea
                  placeholder="Write text here ..."
                  className="w-full border-[#BBC1C7] rounded-none"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
