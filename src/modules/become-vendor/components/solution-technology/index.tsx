'use client';
import React from 'react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Checkbox,
  Input,
  Label,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function SolutionTechnology() {
  return (
    <div className="bg-background mt-[24px] 3xl:mt-[1.25vw] px-[24px] 3xl:px-[1.25vw] pt-[12px] 3xl:pt-[0.625vw] pb-[12px] 3xl:pb-[0.625vw] ">
      <Accordion type="single" collapsible className="w-full p-0 ">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="p-0 hover:no-underline">
            Solution & Technology Areas
          </AccordionTrigger>
          <AccordionContent className="border-t mt-[12px] 3xl:mt-[0.625vw]">
            <div className="grid grid-cols-3 gap-[8px] 3xl:gap-[0.417vw] mt-[20px] 3xl:mt-[1.042vw]">
              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Cloud Iaas / PaaS
                </label>
              </div>
              <div className="flex items-center gap-2  ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Data & Analytics
                </label>
              </div>
              <div className="flex items-center gap-2  ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Networking / Connectivity
                </label>
              </div>

              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Cyber-Security
                </label>
              </div>
              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  AI / ML
                </label>
              </div>
              <div className="flex items-center gap-2">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Storage / Backup / DR
                </label>
              </div>
              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  DevOps / CI-CD
                </label>
              </div>
              <div className="flex items-center gap-2 ">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Productivity & Collaboration
                </label>
              </div>
              <div className="flex items-center gap-2">
                <Checkbox id="terms" className="" />
                <label
                  htmlFor="terms"
                  className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  FinOps / Cost Management
                </label>
              </div>
            </div>
            <div className="grid grid-cols-3 mt-[8px] 3xl:mt-[0.417vw]">
              <div className="grid col-span-1 gap-1.5">
                <div className="flex items-center gap-2">
                  <Checkbox id="terms" className="" />
                  <label
                    htmlFor="terms"
                    className="text-[#3C4146] cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Other
                  </label>
                </div>
                <div className="grid gap-1.5">
                  <Input
                    type="text"
                    placeholder="Enter legal Entity Name"
                    className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  />
                </div>
              </div>
            </div>
            <div className="grid col-span-3 gap-1.5 my-[20px]">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                Supported Platforms / Hyperscalers
              </Label>
              <Textarea
                placeholder="Write text here ..."
                className="w-full border-[#BBC1C7] rounded-none"
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
