'use client';
import React from 'react';
import { <PERSON>o } from 'next/font/google';
import { Button, ScrollArea } from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import CompanyInformation from '../components/company-information';
import MarketPlaceProfile from '../components/marketplace-profile';
import TargetMarkets from '../components/target-markets';
import ProductOffering from '../components/product-offering';
import SolutionTechnology from '../components/solution-technology';
import OperationalCommercial from '../components/operational-commercial';
import TechnicalEnablement from '../components/technical-enablement';
import ComplianceCertification from '../components/compliance-certifications';
import LegalContractual from '../components/legal-contractual';
import AdditionalInfo from '../components/additional-information';
import FormSubmissionPopup from '../components/formsubmissionpopup';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function BecomeVendor() {
  const [openPopup, setOpenPopup] = React.useState(false);
  return (
    <>
      <div className={`${roboto.className} px-[180px] xl:px-[200px] 3xl:px-[11.417vw] relative`}>
        <div>
          <h1 className="text-[#212325] text-[24.22px] 3xl:text-[1.522vw] font-semibold leading-[140%] mt-[20px] 3xl:mt-[1.042vw]">
            Redington CloudQuarks - Become a Vendor
          </h1>
          <h2 className="text-[#7F8488] text-[15px] 3xl:text-[0.781vw] font-normal leading-[140%]">
            * Fields are Mandatory
          </h2>
        </div>
        <ScrollArea className="h-[calc(100vh-300px)] py-[10px] xl:py-[10px] 3xl:py-[0.521vw]">
          <div className="space-y-[8px] ">
            <CompanyInformation />

            <MarketPlaceProfile />

            <TargetMarkets />

            <ProductOffering />

            <SolutionTechnology />

            <OperationalCommercial />

            <TechnicalEnablement />

            <ComplianceCertification />

            <LegalContractual />

            <AdditionalInfo />
          </div>
        </ScrollArea>
        <div className="sticky -bottom-1 pt-[24px] 3xl:pt-[1.25vw] bg-[#f6f8fa] border-InterfaceSurfacepagemuted border-t py-[16px] 3xl:py-[0.833vw] px-[24px] 3xl:px-[1.25vw] flex justify-between">
          <Link
            href="/"
            className="bg-background signinbtn px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.525vw] border border-InterfaceStrokesoft text-InterfaceTextdefault  flex gap-[8px] 3xl:gap-[0.417vw] items-center font-medium w-fit text-[15px] 3xl:text-[0.781vw]"
          >
            <i className="cloud-back text-[15px] 3xl:text-[0.781vw]"></i>
            Back to Home
          </Link>

          <Button
            onClick={() => setOpenPopup(true)}
            className="rounded-none text-InterfaceTextwhite bg-BrandPrimarypurenew hover:bg-[#067532] px-4 lg:px-[20px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] lg:py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.521vw] text-[15px] lg:text-[15px] xl:text-[15px] 2xl:text-[15px] 3xl:text-[0.781vw] "
          >
            <i className="cloud-circletick text-InterfaceTextwhite "></i>
            Submit
          </Button>
        </div>
        <FormSubmissionPopup open={openPopup} onClose={() => setOpenPopup(false)} />
      </div>
    </>
  );
}
