// For the List of Orders page, a single unified page has been designed for both editing and viewing orders.
// This page currently serves as a base and needs to be integrated and customized further according to specific requirements.
'use client';
import * as React from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import ViewTable from '../component/view-table';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function OrdersViewTemplate() {
  const t = useTranslations();

  return (
    <div className="py-[20px] xl:py-[16px] 3xl:py-[1.042vw] px-[32px] xl:px-[24px] 3xl:px-[1.667vw] w-full">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle">
          {t('innovateLimitedOrders')}{' '}
          <span className="text-InterfaceTextsubtitle font-[400]"> / </span>
          <span className="font-normal">{t('order')} 41400000428</span>
        </div>
      </div>
      <div className="space-y-[20px] space-y-[22px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <Button className="ticketcard text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
            <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('printOrder')}
          </Button>
          <Link href="/customer-management/customers/orders">
            <Button className="ticketcard text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
              <i className="cloud-back font-medium text-[14px] xl:text-[15px] 3xl:text-[0.833vw] text-BrandSupport1pure"></i>{' '}
              {t('back')}
            </Button>
          </Link>
        </div>

        <div className="p-[30px] xl:p-[30px] 2xl:p-[32px] 3xl:p-[1.67vw] bg-[#FFF] flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
          <div className="flex justify-between items-center gap-[4px]">
            <div className="flex flex-col gap-[4px]">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600] leading-[100%]">
                {t('order')} 41400000428
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                April 1, 2025 {t('innovateFirstInnovateLast')}
              </div>
            </div>

            <div
              className={`inline-block py-[2px] xl:py-[2px] 3xl:py-[0.1vw] px-[8px] rounded-[2px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold border bg-BrandHighlight100 text-BrandHighlight800 border-BrandHighlight300`}
            >
              <div>{t('complete')}</div>
            </div>
          </div>

          <div className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
            <div className="grid grid-cols-4 gap-[32px] 3xl:gap-[1.67vw]">
              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('billFrom')}{' '}
                </div>
                <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                    {t('redingtonGulfFze')}
                  </div>
                  <div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Plot No. S 30902 South,
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Jabel Ali Free Zone Dubai,
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      United Arab Emirates
                    </div>
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">VAT TRN: </span> ***************
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('billTo')}{' '}
                </div>
                <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                    {t('delphiConsultingLlc')}
                  </div>
                  <div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Suite 1407, Concord Tower, Al Safouh 2 Media city, Dubai. Dubai, Dubai <br />
                      United Arab Emirates
                    </div>
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">TRN: </span> ***************
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Redington Account Number: </span> **********
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('serviceRecipient')}{' '}
                </div>
                <div className="flex flex-col ">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                    {t('delphiConsultingLlc')}
                  </div>
                  <div>
                    <div className="mt-[8px] 3xl:mt-[0.417vw] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      22nd Floor Tameem House Barsha Heights Dubai, Dubai, 24573 <br />
                      United Arab Emirates.
                    </div>
                  </div>
                  <div className="mt-[2px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">TEL: </span> 566869304
                  </div>
                </div>
                <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                    {t('organizationTenant')}
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Tenant ID: </span>{' '}
                    f6e191f8-5aed-4670-86d4-616277ebe4c0
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Tenant Name: </span> thefirstgroup.onmicrosoft.com
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('orderDetails')}{' '}
                </div>
                <div className="flex flex-col gap-[2px]">
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Store: </span> BH-AR (New)
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Vendor: </span> Amazon
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Brand: </span> AWS
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Brand Category: </span> AWS
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Payment Terms: </span> R032-Advance Terms
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">Reference: </span> LPO-543543...{' '}
                    <span className="font-[400] text-InterfaceTextprimary underline cursor-pointer">
                      {t('view')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <ViewTable />
            <div className="flex items-center justify-end mr-[100px]">
              <div className="flex flex-col gap-[6px] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.36vw]">
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('subtotal')}
                </div>
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('estimatedVAT')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
                  {t('grandTotal')}
                </div>
              </div>
              <div className="flex flex-col gap-[6px] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.36vw]">
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  USD 1.00{' '}
                </div>
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  USD 0.00{' '}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
                  USD 1.00{' '}
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
            <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
              {t('disclaimer')}:
            </div>
            <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
              {t('vatCostChangeDisclaimer')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
