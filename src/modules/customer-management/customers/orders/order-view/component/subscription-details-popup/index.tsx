'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function SubscriptionDetailsPopup() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <i className="cloud-doc text-InterfaceTextdefault cursor-pointer flex " title="Doc"></i>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[550px] xl:max-w-[550px] 3xl:max-w-[28.65vw] p-[0px]">
        <div>
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('subscriptionDetails')}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>
          {/* Render in case of Completed */}
          <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div>
                <div className="flex flex-col space-y-[24px]">
                  <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('customerName')}
                    </div>
                    <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('innovateItd')}
                    </div>
                  </div>
                  <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('subscriptionName')}
                    </div>
                    <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('amazonWebServices')}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]  space-y-3 mt-[20px]">
                  <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] mt-[12px]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('subscriptionId')}
                    </div>
                    <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      345678901256
                    </div>
                  </div>
                  <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('quantity')}
                    </div>
                    <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      1
                    </div>
                  </div>
                  <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('startsOn')}
                    </div>
                    <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      01-03-2025
                    </div>
                  </div>
                  <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('expiresOn')}
                    </div>
                    <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      01-03-2026
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="absolute top-[16px] right-[20px]">
              <div className="cursor-pointer ">
                <div
                  className={`inline-block py-[2px] xl:py-[2px] 3xl:py-[0.1vw] px-[10px] rounded-[2px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold border bg-[#E8F4E4] text-[#315229] border-[#ACD69F]`}
                >
                  <div>{t('fulfilled')}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Render in case of Pending */}
        {/* <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-doc-text text-BrandSupport1pure text-[72px] xl:text-[60px] 3xl:text-[3.75vw] font-[100]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[30px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                Subscription Details
              </div>
              <div className="text-InterfaceTextsubtitle text-[20px] lg:text-[20px] xl:text-[16px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                Order is in progress. Awaiting response from Vendor
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
            <SheetClose>
              <div className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-background bg-[#00953A] font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                <i className="cloud-closecircle text-[18px] xl:text-[14px] 3xl:text-[0.938vw]"></i>
                Close
              </div>
            </SheetClose>
          </div>
        </div> */}
      </SheetContent>
    </Sheet>
  );
}
