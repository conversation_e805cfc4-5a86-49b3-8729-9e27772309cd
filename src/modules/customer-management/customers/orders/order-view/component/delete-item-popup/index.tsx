'use client';
import * as React from 'react';
import {
  SheetTitle,
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function DeletelineItemPopup() {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <i className="cloud-trash text-InterfaceTextdefault cursor-pointer flex " title="Doc"></i>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[550px] xl:max-w-[550px] 3xl:max-w-[28.65vw] p-[0px]">
        <div className="h-full overflow-y-auto px-4 flex flex-col justify-center items-center mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-trashlighticon text-BrandSupport1pure text-[72px] xl:text-[60px] 3xl:text-[3.75vw]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[30px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                {t('deleteLineItem')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[20px] lg:text-[20px] xl:text-[16px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                Do you want to delete the line item?
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
            <Link
              href={'/signup'}
              className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-background bg-closecolor font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]"
            >
              <i className="cloud-fillcircletick text-[18px] xl:text-[14px] 3xl:text-[0.938vw]"></i>
              {t('yesDelete')}
            </Link>
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">{t('or')}</p>
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
            </div>
            <SheetClose>
              <div className="text-[16px] md:text-[16px] xl:text-[14px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                <i className="cloud-closecircle text-[18px] xl:text-[14px] 3xl:text-[0.938vw]"></i>
                {t('noLater')}
              </div>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
