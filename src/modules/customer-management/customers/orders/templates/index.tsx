'use client';
import * as React from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Orderstable from '../component/orders-table';
import OrdersCards from '../component/orders-cards';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function CustomersOrdersTemplate() {
  const t = useTranslations();
  return (
    <div className="py-[20px] xl:py-[16px] 3xl:py-[1.042vw] px-[32px] xl:px-[24px] 3xl:px-[1.667vw] w-full">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle">
          {t('innovateLimitedOrders')}{' '}
          <span className="text-InterfaceTextsubtitle font-[400]"> / </span>
          <span className="font-normal">{t('orders')}</span>
        </div>
        <div className="text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] text-InterfaceTextsubtitle italic">
          * {t('asPerTheOrderDateRangeSelected')}
        </div>
      </div>
      <div className="space-y-[16px] xl:space-y-[16px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="">
          <div>
            <OrdersCards />
          </div>
        </div>
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault">
            <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('download')}
          </Button>
          <Link href={'/customer-management/customers'}>
            <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault">
              <i className="cloud-back font-medium text-[16px] xl:text-[14px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
              {t('back')}
            </Button>
          </Link>
        </div>

        <div className="bg-interfacesurfacecomponent tbl-shadow">
          <Orderstable />
        </div>
      </div>
    </div>
  );
}
