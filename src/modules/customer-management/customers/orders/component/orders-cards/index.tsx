'use client';
import Piechart from '@/components/common/charts/piechart';
import React, { useState } from 'react';
import {
  Label,
  RadioGroup,
  RadioGroupItem,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Barchart1 from '../chart/bar-chart1';
import { useTranslations } from 'next-intl';

export default function OrdersCards() {
  const t = useTranslations();
  const [selectedOption, setSelectedOption] = useState('value');
  return (
    <div>
      {/* <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-12 3xl:grid-cols-12 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]"> */}
      <div className="grid grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div className="w-full">
          <div className="grid grid-cols-12 p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[32px] xl:text-[24px] 3xl:text-[1.667vw]"></i>
            </div>

            <div className="col-span-8 3xl:col-span-8 h-[135px] xl:h-[130px] 2xl:h-[135px] 3xl:h-[7.03vw]">
              <RadioGroup
                defaultValue="value"
                value={selectedOption}
                onValueChange={setSelectedOption}
                className="flex justify-end"
              >
                <div className="flex items-center space-x-2 custrediogroup">
                  <RadioGroupItem value="value" id="r1" />
                  <Label
                    htmlFor="r1"
                    className="text-InterfaceTextdefault text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('value')}
                  </Label>
                </div>
                <div className="flex items-center space-x-2 custrediogroup">
                  <RadioGroupItem value="count" id="r2" />
                  <Label
                    htmlFor="r2"
                    className="text-InterfaceTextdefault text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    {t('count')}
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-[14px] xl:bottom-[14px] 3xl:bottom-[14px]">
              <div className="text-InterfaceTextdefault text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                {selectedOption === 'value' ? t('totalItems') + '*' : t('totalItems') + '#'}
              </div>
              <div className="text-InterfaceTextdefault text-[36px] xl:text-[20px] 2xl:text-[28px] 3xl:text-[1.667vw] font-semibold truncate">
                {selectedOption === 'value' ? 'USD 500,000.00' : '501'}
              </div>
            </div>
          </div>
        </div>

        <div className="w-full">
          <div className="grid grid-cols-12  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-4 3xl:col-span-4">
              <i className="cloud-cards1 text-[#7F8488] text-[32px] xl:text-[24px] 3xl:text-[1.667vw]"></i>
            </div>

            <div className="col-span-8 3xl:col-span-8 h-[135px] xl:h-[130px] 2xl:h-[135px] 3xl:h-[7.03vw]">
              <Piechart
                tooltip={{
                  trigger: 'item',
                  position: 'top',
                  confine: true,
                  formatter: () => {
                    return `<div style="padding-top:0px;">Total No Of<br/>Item - 200</div>`;
                  },
                }}
                legends={{
                  orient: 'vertical',
                  bottom: '30%',
                  right: '2%',
                  itemGap: 8,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: [t('fulfilled'), t('pending'), t('failed')],
                  textStyle: {
                    fontSize: 11,
                  },
                }}
                name={'Nightingale Chart'}
                radius={[20, 50]}
                center={['30%', '45%']}
                itemstyle={{
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 1,
                  length2: 1,
                }}
                label={{
                  show: true,
                  position: 'inside',
                  formatter: '{d}%',
                  fontSize: 10,
                  fontWeight: 'normal',
                  color: '#000',
                  backgroundColor: '#F6F7F8',
                  borderColor: '#E5E7EB',
                  borderWidth: 1,
                  borderRadius: 4,
                  padding: [2, 4],
                }}
                data={[
                  { value: 60, name: t('fulfilled'), itemStyle: { color: '#007BFF' } },
                  { value: 20, name: t('pending'), itemStyle: { color: '#5CBFFF' } },
                  { value: 20, name: t('failed'), itemStyle: { color: '#A9D9FF' } },
                ]}
              />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-[14px] xl:bottom-[14px] 3xl:bottom-[14px]">
              <div className="text-InterfaceTextdefault text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                {t('totalItems')}*
              </div>
              <div className="text-InterfaceTextdefault text-[36px] xl:text-[20px] 2xl:text-[28px] 3xl:text-[1.667vw] font-semibold truncate">
                1,000
              </div>
            </div>
          </div>
        </div>

        <div className="w-full">
          <div className="grid grid-cols-5  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[32px] xl:text-[24px] 3xl:text-[1.667vw]"></i>
            </div>

            <div className="col-span-3 h-[135px] xl:h-[130px] 2xl:h-[135px] 3xl:h-[7.03vw]">
              <Barchart1 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-[14px] xl:bottom-[14px] 3xl:bottom-[14px]">
              <div className="text-InterfaceTextdefault truncate text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                {t('orderValueByTop3BrandCategoryUsd')}*
              </div>
              <div className="text-InterfaceTextdefault text-[36px] xl:text-[20px] 2xl:text-[28px] 3xl:text-[1.667vw] font-semibold truncate">
                1,000.00
              </div>
            </div>
          </div>
        </div>

        {/* <div className="w-[100%] xl:w-[27%] 2xl:w-[28%] 3xl:w-[28%]">
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-2 2xl:col-span-2 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[29px]"></i>
            </div>

            <div className="col-span-4 2xl:col-span-4 3xl:col-span-4 h-[135px] xl:h-[140px] 2xl:h-[135px] 3xl:h-[7.03vw]">
              <Barchart2 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-[14px] xl:bottom-[14px] 3xl:bottom-[14px]">
              <div className="text-InterfaceTextdefault truncate text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                Order Value By Top 5<br /> End Customer (USD)*
              </div>
              <div className="text-[13px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                1,000.00
              </div>
            </div>
          </div>
        </div> */}
      </div>
    </div>
  );
}
