'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';

export default function Barchart2() {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      left: '20%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
      itemHeight: 10,
      itemWidth: 10,
      textStyle: {
        fontSize: 11,
      },
    },
    grid: {
      left: '22%',
      right: '0%',
      bottom: '20%',
      top: '8%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        // data: ['<PERSON><PERSON>', '<PERSON>', '<PERSON>' , '<PERSON><PERSON>', '<PERSON><PERSON>'],
        data: ['<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    yAxis: [
      {
        name: 'Order Value',
        type: 'value',
        min: 0,
        max: 800,
        interval: 200,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    series: [
      {
        name: 'Order Value',
        type: 'bar',
        barWidth: '70%',
        data: [450, 300, 520, 450, 300],
        label: {
          show: true,
          position: 'insideTop',
          fontSize: 8,
          color: '#FFF',
          formatter: '{c}',
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#6480AB' }, // Top color
            { offset: 1, color: '#91A5C3' }, // Bottom color
          ]),
          borderRadius: [2, 2, 0, 0],
        },
      },
    ],
  };
  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
