'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  // <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { CommanSheetProps } from '@/types';
import { useTranslations } from 'next-intl';

interface SuccessfullPopupProps extends CommanSheetProps {
  addNewCustomer?: boolean;
  message?: string;
}

export default function AdminRelationShipRequest({ open, onClose }: SuccessfullPopupProps) {
  const t = useTranslations();

  return (
    <Sheet open={open} onOpenChange={onClose}>
      {/* <SheetTrigger>
        <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
          <i className="cloud-Add text-BrandSupport1pure text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.633vw]"></i>
          New Request 
        </Button>
      </SheetTrigger> */}
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[34.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('createanAdminRelationshipRequest')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[400px] xl:h-[480px] 2xl:h-[630px] 3xl:h-[38.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="border-b border-InterfaceStrokesoft pb-[14px] xl:pb-[14px] 3xl:pb-[0.729vw]">
              <div className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                Send the request form below to any of your customers. you may edit the text. but be
                sure to include the URL unedited. Request URLs once accepted, are not reusable
              </div>
              <div className="py-[20px] xl:py-[20px] 3xl:py-[1.042vw] grid grid-cols-2 gap-[20px] xl:gap-[20px] 3xl:gap-[1.042vw]">
                <div className="border-b border-InterfaceStrokesoft pb-[14px] xl:pb-[14px] 3xl:pb-[0.729vw]">
                  <div className="py-1 text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-InterfaceTextsubtitle font-[400]">
                    Admin Relationship Name
                  </div>
                  <div className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    testlink
                  </div>
                </div>
                <div className="border-b border-InterfaceStrokesoft pb-[14px] xl:pb-[14px] 3xl:pb-[0.729vw]">
                  <div className="py-1 text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-InterfaceTextsubtitle font-[400]">
                    Duration in Days
                  </div>
                  <div className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    730
                  </div>
                </div>
              </div>
              <div className="pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] grid grid-cols-2 gap-[20px] xl:gap-[20px] 3xl:gap-[1.042vw]">
                <div className="border-b border-InterfaceStrokesoft pb-[14px] xl:pb-[14px] 3xl:pb-[0.729vw]">
                  <div className="py-1 text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-InterfaceTextsubtitle font-[400]">
                    Auto Extent
                  </div>
                  <div className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Yes
                  </div>
                </div>
                <div className="border-b border-InterfaceStrokesoft pb-[14px] xl:pb-[14px] 3xl:pb-[0.729vw]">
                  <div className="py-1 text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-InterfaceTextsubtitle font-[400]">
                    Auto Extent By
                  </div>
                  <div className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    6 Months
                  </div>
                </div>
              </div>
              <div className="py-1 text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-InterfaceTextsubtitle font-[400]">
                Requested Microsoft Entra roles
              </div>
              <div className="text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-InterfaceTextdefault font-[500]">
                Search Editor, Skype for Business administrator, SharePoint administrator, Teams
                communications support engineer
              </div>
            </div>
            <div>
              <div className="pb-1 text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]  text-InterfaceTextTitle font-[500]">
                Request
              </div>
              <div className="border border-InterfaceStrokedefault p-[14px] xl:p-[14px] 3xl:p-[0.729vw] h-[500px]">
                <div className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  By clicking the included link you will be able to accept the request for us to
                  administer your products using the roles listed below for the specified date
                  range.
                </div>
                <div className=" pt-[20px] text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  Click to review and accept:
                </div>
                <div className=" cursor-pointer w-[90%] text-BrandSupport1pure text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  https://admin.microsoft.com/AdminPortal/Home#/partners/invitation7a0477cd-cbc5-46b6
                  a185-71f696a9812a- 40eba903-1fe1-48bc-ac36-ea6bad38639b
                </div>
                <div className=" pt-[20px] text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  Duration in days: 730
                </div>
                <div>
                  <div className=" pt-[20px] text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    Microsoft Entra admin roles:
                  </div>
                  <div className="flex  ">
                    <div className="h-[4px] w-[6px] bg-[#424242] rounded-full mt-[6px] mx-[6px]"></div>
                    <div className="text-InterfaceTextTitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      Teams communications support engineer :
                      <span className="text-InterfaceTextsubtitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] pl-2">
                        Can troubleshoot communications issues within Teams using advanced tools.
                      </span>
                    </div>
                  </div>
                  <div className="flex   ">
                    <div className="h-[4px] w-[5px] bg-[#424242] rounded-full mt-[6px] mx-[6px]"></div>

                    <div className="text-InterfaceTextTitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      Skype for Business administrator :
                      <span className="text-InterfaceTextsubtitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] pl-2">
                        Can manage all aspects of the Skype for Business product.
                      </span>
                    </div>
                  </div>
                  <div className="flex  ">
                    <div className="h-[4px] w-[4px] bg-[#424242] rounded-full mt-[6px] mx-[6px]"></div>

                    <div className="text-InterfaceTextTitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      SharePoint administrator :
                      <span className="text-InterfaceTextsubtitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] pl-2">
                        Can manage al aspects of the sahrepoint Service.
                      </span>
                    </div>
                  </div>
                  <div className="flex ">
                    <div className="h-[4px] w-[5px] bg-[#424242] rounded-full mt-[6px] mx-[6px]"></div>

                    <div className="text-InterfaceTextTitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      Search Editor :
                      <span className="text-InterfaceTextsubtitle text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] pl-2">
                        Can create and manage the editorial content such as bookmarks, Qand As,
                        Locations, floorplan.
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-[20px]">
              <div className="flex gap-[6px] items-center cursor-pointer">
                <i className="text-BrandSupport1pure cloud-sms text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                <div className="text-BrandSupport1pure text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  Open in email
                </div>
              </div>
              <div className="flex gap-[6px] items-center cursor-pointer">
                <i className="text-BrandSupport1pure cloud-copy text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                <div className="text-BrandSupport1pure text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  Copy to Clipboard
                </div>
              </div>
            </div>
          </div>
        </div>

        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <Link
                onClick={() => {
                  onClose();
                }}
                href="#"
                className="hover:bg-[#d0d1d3] flex items-center gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                </div>
                <div
                  onClick={onClose}
                  className="text-[#3C4146] text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[16px] flex items-center"
                >
                  Cancel
                </div>
              </Link>
              <Button
                className="flex applybtn items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[12px] xl:px-[12px] 3xl:px-[0.729vw] rounded-none"
                size="md"
              >
                <div>
                  <i className="cloud-circletick text-interfacetextinverse flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                </div>
                <div
                  onClick={onClose}
                  className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"
                >
                  Done
                </div>
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
