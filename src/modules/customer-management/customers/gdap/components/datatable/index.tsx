'use client';

import * as React from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  ColumnFiltersState,
  SortingState,
  flexRender,
  ColumnDef,
} from '@tanstack/react-table';
import clsx from 'clsx';

import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '@redington-gulf-fze/cloudquarks-component-library';

import { Checkbox } from '@redington-gulf-fze/cloudquarks-component-library';

type DataTableProps<TData, TValue> = {
  data: TData[];
  withCheckbox?: boolean;
  columns: ColumnDef<TData, TValue>[];
  selectedRowIds?: string[];
  onSelectionChange?: (selectedRowIds: string[]) => void;
};

export function DataTable<TData extends { id: string }, TValue>({
  data,
  columns,
  withCheckbox = false,
  selectedRowIds,
  onSelectionChange,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [internalSelectedRowIds, setInternalSelectedRowIds] = React.useState<string[]>(
    selectedRowIds || []
  );

  // Sync internal state with controlled prop
  React.useEffect(() => {
    if (selectedRowIds) {
      setInternalSelectedRowIds(selectedRowIds);
    }
  }, [selectedRowIds]);

  const handleRowSelectionChange = (rowId: string, selected: boolean) => {
    let updated;
    if (selected) {
      updated = [...internalSelectedRowIds, rowId];
    } else {
      updated = internalSelectedRowIds.filter((id) => id !== rowId);
    }
    setInternalSelectedRowIds(updated);
    if (onSelectionChange) onSelectionChange(updated);
  };

  const checkboxColumn: ColumnDef<TData> = {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => {
          const allIds = table.getRowModel().rows.map((row) => row.original.id);
          let updated: string[];
          if (value) {
            updated = Array.from(new Set([...internalSelectedRowIds, ...allIds]));
          } else {
            updated = internalSelectedRowIds.filter((id) => !allIds.includes(id));
          }
          setInternalSelectedRowIds(updated);
          if (onSelectionChange) onSelectionChange(updated);
        }}
        aria-label="Select all"
        className="mr-[20px] border-[#b6b6b6]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={internalSelectedRowIds.includes(row.original.id)}
        disabled={!row.getCanSelect()}
        onCheckedChange={(value) => handleRowSelectionChange(row.original.id, !!value)}
        aria-label="Select row"
        className="border-[#b6b6b6]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 45,
    minSize: 45,
    maxSize: 45,
  };

  const allColumns = withCheckbox ? [checkboxColumn, ...columns] : columns;

  const table = useReactTable({
    data,
    columns: allColumns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableRowSelection: true,
  });

  return (
    <div className="w-full overflow-x-auto">
      <div className="border">
        <Table>
          <TableHeader className="bg-InterfaceStrokesoft">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    style={{ width: header.getSize() }}
                    className={clsx(
                      'relative px-[12px] xl:px-[12px] 3xl:[0.625vw]',
                      header.column.id === 'select' && 'sticky left-0 z-10 bg-InterfaceStrokesoft',
                      header.column.id === 'action' &&
                        'sticky right-0 bg-InterfaceStrokesoft z-10 sticky-shadow-left '
                    )}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                    {header.column.getCanResize() && (
                      <div
                        onMouseDown={header.getResizeHandler()}
                        onTouchStart={header.getResizeHandler()}
                        className="absolute right-0 top-0 h-full w-1 bg-transparent "
                        style={{ touchAction: 'none' }}
                      />
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                className={`
    hover:bg-BrandNeutral100 transition-colors duration-150

  `}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className={clsx(
                      'text-InterfaceTextdefault px-[12px] xl:px-[12px] 3xl:[0.625vw]',
                      cell.column.id === 'select' && 'sticky left-0 bg-white z-10 ',
                      cell.column.id === 'action' &&
                        'sticky right-0 bg-white z-10 sticky-shadow-left'
                    )}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
