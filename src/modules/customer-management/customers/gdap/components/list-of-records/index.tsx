import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import { Input, Switch, TooltipProvider } from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import GDAPFilter from '../filter-popup';
import ActiveCustomer from '../active-customer-popup';
import TestLinkPopup from '../testlink-popup';
import { useTranslations } from 'next-intl';

export default function ListOfRecords() {
  const t = useTranslations();

  const customerColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'adminname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[18px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('adminRelationshipName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('adminRelationshipName')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <div className="flex gap-3">
            <div className=" text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer hover:font-[500] ">
              {row.getValue('adminname')}
            </div>
            {status === 'Approval Pending' && <TestLinkPopup />}
            {status === 'Active' && <ActiveCustomer />}
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('select2')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Active: {
            colorClass: 'bg-[#E8F4E4] text-[#488138] border-[#ACD69F]',
          },
          Rejected: {
            colorClass: 'bg-[#FDF2C8] text-[#903D10] border-[#FACE4F]',
          },
          Canceled: {
            colorClass: 'bg-[#FFF3F0] text-[#D42600] border-[#FFB5A5]',
          },
          InReview: {
            colorClass: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
          },
          Expired: {
            colorClass: 'bg-[#F5F6F8] text-[#91A5C3] border-[#DBE1EA]',
          },
          Declined: {
            colorClass: 'bg-[#E8F4E4] text-[#2A4423] border-[#E5E7EB]',
          },
          Accepted: {
            colorClass: 'bg-[#E8F4E4] text-[#72B35F] border-[#D2E8CA]',
          },
          New: {
            colorClass: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-[#FFFBEB] text-[#D67309] border-[#FACE4F]',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-flex py-[5px] xl:py-[5px] 3xl:py-[0.257vw] px-[10px] rounded-[2px] text-[15px] xl:text-[15px] 3xl:text-[0.781vw] font-medium border  items-center text-center justify-center ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'startdate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('startDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('startDate')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({}) => {
        return (
          <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] pl-[12px] ">
            05/23/2025
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'enddate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('endDate')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({}) => {
        return (
          <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] pl-[12px] ">
            11/19/2025
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'autoextend',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col items-center justify-center">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] w-full  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw] text-center"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle pl-[40px] 2xl:pl-[55px] 3xl:pl-[3.983vw]">
                {t('autoExtend')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder={t('autoExtend')}
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({}) => {
        return (
          <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] pl-[12px] flex justify-center items-center ">
            <Switch
              defaultChecked
              className="data-[state=unchecked]:bg-[#d1d1d1] data-[state=checked]:bg-InterfaceTextprimary  peer inline-flex h-[23px] w-11 shrink-0 cursor-pointer items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none"
            />
          </div>
        );
      },

      minSize: 400,
    },
  ];

  type User = {
    adminname: string;
    status: string;
  };

  const customerdata: User[] = [
    {
      adminname: 'testlink',
      status: 'Approval Pending',
    },
    {
      adminname: 'Default_REDI-Omnividia_941631808792693 ',
      status: 'Active',
    },
    {
      adminname: 'Default_REDI-Omnividia_941631808792693',
      status: 'Approval Pending',
    },
    {
      adminname: 'Default_REDI-Omnividia_941631808792693',
      status: 'Active',
    },
    {
      adminname: 'Default_REDI-Omnividia_941631808792693',
      status: 'Approval Pending',
    },
    {
      adminname: 'Default_REDI-Omnividia_941631808792693',
      status: 'Approval Pending',
    },
    {
      adminname: 'Default_REDI-Omnividia_941631808792693',
      status: 'Active',
    },
    {
      adminname: 'Default_REDI-Omnividia_941631808792693',
      status: 'Active',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('listOfRecords')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="flex gap-[20px] xl:gap-[20px] 3xl:gap-[1.05vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>

            <div>
              <GDAPFilter />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <TooltipProvider>
            <DataTable data={customerdata} columns={customerColumns} withCheckbox={false} />
          </TooltipProvider>
        </div>
        <TablePagination />
      </div>
    </>
  );
}
