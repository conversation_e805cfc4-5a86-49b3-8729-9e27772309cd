'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  She<PERSON>,
  Sheet<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { CommanSheetProps } from '@/types';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import { ArrowDown, ArrowU<PERSON>, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { TablePagination } from '@/components/common/pagination';
import { TreeTable } from '@/components/common/ui/tree-table';

export default function MicrosoftEntraRoles({ open, onClose }: CommanSheetProps) {
  const customerColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Name
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter placeholder="Name" onChange={(val) => column.setFilterValue(val)} />
          </div>
        );
      },
      cell: ({ row }) => (
        <div className=" text-[13px] xl:text-[13px] 3xl:text-[0.729vw]">{row.getValue('name')}</div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'description',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Description
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Description"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-[13px] xl:text-[13px] 3xl:text-[0.729vw]">
          {row.getValue('description')}
        </div>
      ),

      minSize: 400,
    },
  ];

  type User = {
    id: string;
    name: string;
    description: string;
    subRows?: User[];
  };

  const customerData: User[] = [
    {
      id: '1',
      name: 'Collaboration',
      description: 'Can Manage all aspects of the Dynamics 365 Product',
      subRows: [
        {
          id: '1-1',
          name: 'Search Editor',
          description: 'Can Manage all aspects of the Dynamics 365 Product',
        },
        {
          id: '1-2',
          name: 'Skype for Business administrator',
          description: 'Can Organize, create, manage, and promote topics and knowlwdge',
        },
        {
          id: '1-3',
          name: 'SharePoint administrator',
          description: 'Can Organize, create, manage, and promote topics and knowlwdge',
        },
        {
          id: '1-4',
          name: 'Teams communications support engineer',
          description: 'Can Organize, create, manage, and promote topics and knowlwdge',
        },
        {
          id: '1-5',
          name: 'Dynamic 365 administrator',
          description: 'Can Organize, create, manage, and promote topics and knowlwdge',
        },
        {
          id: '1-6',
          name: 'Dynamic 365 Business Central Administrator',
          description: 'Can Organize, create, manage, and promote topics and knowlwdge',
        },
      ],
    },
    {
      id: '2',
      name: 'Dynamic 365 administrator',
      description: 'Can Manage all aspects of the Dynamics 365 Product',
      subRows: [
        {
          id: '2-1',
          name: 'Dynamic 365 Business Central Administrator',
          description: 'Can Manage all aspects of the Dynamics 365 Product',
        },
      ],
    },
    {
      id: '3',
      name: 'Knowledge Manager',
      description: 'Can Manage all aspects of the Dynamics 365 Product',
      subRows: [
        {
          id: '3-1',
          name: 'Search Editor',
          description: 'Can Manage all aspects of the Dynamics 365 Product',
        },
        {
          id: '3-2',
          name: 'Skype for Business administrator',
          description: 'Can Organize, create, manage, and promote topics and knowlwdge',
        },
        {
          id: '3-3',
          name: 'SharePoint administrator',
          description: 'Can Organize, create, manage, and promote topics and knowlwdge',
        },
      ],
    },
  ];

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[650px] xl:max-w-[650px] 3xl:max-w-[38.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              Create an Admin Relationship Request
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[400px] xl:h-[480px] 2xl:h-[630px] 3xl:h-[38.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="border-b border-InterfaceStrokedefault pb-[24px] xl:pb-[24px] 3xl:pb-[1.354vw]">
              <div className="text-InterfaceTextdefault text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                Select Microsoft Entra roles to include in the relationship.
              </div>
              <div className="flex items-center gap-2 text-InterfaceTextprimary cursor-pointer text-[13px] md:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                Learn more about Microsoft Entra roles
                <i className="cloud-export cursor-pointer text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[13px] 3xl:text-[0.629vw]"></i>
              </div>
            </div>
            <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
              <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
                <div className="flex items-center gap-2 ">
                  <div className="text-InterfaceTexttitle text-[16px] lg:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                    Request list
                  </div>
                  <div className="text-InterfaceTextsubtitle text-[13px] xl:text-[13px]  3xl:text-[0.629vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
                    Showing 10/100 Records
                  </div>
                </div>
              </div>

              <div>
                <TreeTable<User, unknown>
                  data={customerData}
                  columns={customerColumns}
                  withCheckbox={true}
                />
              </div>
              <TablePagination />
            </div>
          </div>
        </div>

        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <Link
                onClick={() => {
                  onClose();
                }}
                href="#"
                className="hover:bg-[#d0d1d3] flex items-center gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[16px] flex items-center">
                  Cancel
                </div>
              </Link>
              <Button
                className="flex applybtn items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[12px] xl:px-[12px] 3xl:px-[0.729vw] rounded-none"
                size="md"
              >
                <div>
                  <i className="cloud-circletick text-interfacetextinverse flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                </div>
                <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  Save
                </div>
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
