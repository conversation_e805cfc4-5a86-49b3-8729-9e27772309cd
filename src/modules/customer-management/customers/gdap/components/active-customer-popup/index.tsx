'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
  Switch,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { DataTable } from '@/components/common/ui/data-table';
import { TooltipProvider } from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowU<PERSON>, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { MicrosoftEntraRoleInfo } from '../entra-role-info-popup';
import { useTranslations } from 'next-intl';

export default function ActiveCustomer() {
  const t = useTranslations();

  const customerColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('name')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="flex gap-3">
          <div className="underline text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer hover:font-[500] ">
            {row.getValue('name')}
          </div>
        </div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const statusConfig = {
          Active: {
            colorClass: 'bg-[#E8F4E4] text-[#488138] border-[#ACD69F]',
          },
          Rejected: {
            colorClass: 'bg-[#FDF2C8] text-[#903D10] border-[#FACE4F]',
          },
          Canceled: {
            colorClass: 'bg-[#FFF3F0] text-[#D42600] border-[#FFB5A5]',
          },
          InReview: {
            colorClass: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
          },
          Expired: {
            colorClass: 'bg-[#F5F6F8] text-[#91A5C3] border-[#DBE1EA]',
          },
          Declined: {
            colorClass: 'bg-[#E8F4E4] text-[#2A4423] border-[#E5E7EB]',
          },
          Accepted: {
            colorClass: 'bg-[#E8F4E4] text-[#72B35F] border-[#D2E8CA]',
          },
          New: {
            colorClass: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-[#FFFBEB] text-[#D67309] border-[#FACE4F]',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-flex py-[6px] xl:py-[6px] 3xl:py-[0.357vw] px-[10px] rounded-[2px] text-[15px] xl:text-[15px] 3xl:text-[0.781vw] font-semibold border w-fit items-center text-center justify-center ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 100,
    },
  ];

  type User = {
    name: string;
    status: string;
  };

  const customerdata: User[] = [
    {
      name: 'Admin Agents',
      status: 'Active',
    },
    {
      name: 'Desk Agents',
      status: 'Active',
    },
  ];

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div>
          <i className="cloud-dangerlight text-Interfacefeedbackneutral700 cursor-pointer"></i>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[34.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between items-center gap-4">
            <div className="flex justify-start items-center gap-2">
              <div className="text-InterfaceTexttitle overflow-hidden text-ellipsis text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                Default_REDI-Omnividia_9416318087...
              </div>
              <div className="bg-[#E8F4E4] text-[#488138] border border-[#ACD69F] text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-medium py-[2px] px-[8px]">
                Active
              </div>
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-full">
          <div className="grid grid-cols-2 gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw] pb-[14px] xl:pb-[16px] 3xl:pb-[0.833vw]">
            <div className="text-InterfaceTextprimary flex items-center gap-[6px] 3xl:gap-[0.333vw] text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] ">
              <i className="cloud-document-download1"></i>Request for new Relationship
            </div>
            <div className="text-closecolor flex items-center gap-[6px] 3xl:gap-[0.333vw] text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] ">
              <i className="cloud-trash"></i>Terminate Relationship
            </div>
          </div>
          <div className="grid grid-cols-2 gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
            <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                {t('startDate')}
              </div>
              <div className="text-InterfaceTextdefault text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                07/26/2023, 3:39:42 AM GMT+4
              </div>
            </div>
            <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                {t('endDate')}
              </div>
              <div className="text-InterfaceTextdefault text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                07/26/2025, 3:39:42 AM GMT+4
              </div>
            </div>
            <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                {t('autoExtend')}
              </div>
              <div className="text-InterfaceTextdefault text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium flex items-center gap-1">
                <Switch
                  defaultChecked
                  className="data-[state=unchecked]:bg-[#d1d1d1] data-[state=checked]:bg-InterfaceTextprimary  peer inline-flex h-[23px] w-11 shrink-0 cursor-pointer items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none"
                />
                Enabled
              </div>
            </div>
            <div className="py-[12px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                {t('autoExtendBy')}
              </div>
              <div className="text-InterfaceTextdefault text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                6 Months
              </div>
            </div>
          </div>

          <div className="border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] my-[14px] xl:my-[16px] 3xl:my-[0.833vw] space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw]">
            <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] flex items-center gap-2">
              Microsoft Entra roles <MicrosoftEntraRoleInfo />
            </div>
            <div className="flex flex-wrap gap-[6px]">
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                Directory Reader
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                Directory writes
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                Global reader
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                Helpdesk administrator
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                License administrator
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                Privileged authentication administrator
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                User administrator
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                Privileged role administrator
              </div>
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] bg-BrandNeutral50 px-[6px] py-[2px]">
                Service support administrator
              </div>
            </div>
          </div>

          <div className="space-y-[8px] xl:space-y-[8px] 2xl:space-y-[8px] 3xl:space-y-[0.417vw] pt-[12px] xl:pt-[12px] 3xl:pt-[0.625vw]">
            <div className="text-InterfaceTextsubtitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
              Security Groups
            </div>
            <div className="text-InterfaceTestdefault text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
              Select a security group to modify role assignments available for members of the group.
              Role assignments can be modified only for security groups that are present in
              Microsoft Entra ID and have no pending role assignment Updates. <br />
              <span className="text-InterfaceTextprimary">Learn more about security groups</span>
            </div>
          </div>
          <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw] py-[14px] xl:py-[16px] 3xl:py-[0.833vw]">
            <div className="text-InterfaceTextprimary flex items-center gap-[6px] 3xl:gap-[0.333vw] text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] ">
              <i className="cloud-Add"></i>
              {t('addSecurityGroup')}
            </div>
            <div className="text-closecolor flex items-center gap-[6px] 3xl:gap-[0.333vw] text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] ">
              <i className="cloud-trash"></i>
              {t('removeSecuritygroups')}
            </div>
          </div>
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="overflow-x-auto">
              <TooltipProvider>
                <DataTable data={customerdata} columns={customerColumns} withCheckbox={true} />
              </TooltipProvider>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
