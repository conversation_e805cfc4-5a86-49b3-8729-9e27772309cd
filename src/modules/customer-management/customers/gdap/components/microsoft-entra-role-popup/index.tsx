'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';
import { Roboto } from 'next/font/google';
import ListOfRecords from '../microsoft-roles-list';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface AddNewEndCustomerPopupProps extends CommanSheetProps {
  isEdit?: boolean;
  titleName?: string;
  onRolesSelected?: (roles: { id: string; name: string; description: string }[]) => void;
  value?: { id: string; name: string; description: string }[];
}

export function MicrosoftEntrRolesList({
  open,
  onClose,
  titleName,
  onRolesSelected,
  value,
}: AddNewEndCustomerPopupProps) {
  function handleClose(open: boolean): void {
    if (!open && onClose) {
      onClose();
    }
  }

  // Handler to receive selected roles from ListOfRecords and pass up
  const handleRolesSelection = (
    selectedRoles: { id: string; name: string; description: string }[]
  ) => {
    if (onRolesSelected) {
      onRolesSelected(selectedRoles);
    }
    // Do NOT close the popup here!
  };

  return (
    <div>
      <Sheet open={open} onOpenChange={handleClose}>
        <SheetContent
          className={`${roboto.className} hideclose flex flex-col h-full gap-0 sm:max-w-[900px] xl:max-w-[900px] 3xl:max-w-[46.25vw] p-[0px]`}
        >
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                <i
                  className="cloud-back cursor-pointer text-BrandSupport1pure py-[8px] px-[8px] text-[px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[1.00vw]"
                  onClick={() => handleClose(false)}
                ></i>
                {titleName}
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="bg-InterfaceSurfacecomponent overflow-x-auto h-[400px] xl:h-[480px] 2xl:h-[620px] 3xl:h-[35.25vw] p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            {/* Section Header and Description */}
            <div className="mb-4">
              <div className="text-[14px] text-InterfaceTextsubtitle flex items-center gap-1">
                Select Microsoft Entra roles to include in the relationship.&nbsp;
              </div>
              <div className="text-[14px] text-InterfaceTextsubtitle flex items-center gap-1 pb-2">
                <a
                  href="https://learn.microsoft.com/en-us/entra/roles/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[#1570EF]  flex items-center"
                >
                  Learn more about Microsoft Entra roles &nbsp;{' '}
                  <i className="cloud-export text-[10px] " />
                  <i className="cloud-external-link ml-1 text-[#1570EF] text-[16px] align-middle"></i>
                </a>
              </div>
            </div>
            {/* Roles List */}
            <ListOfRecords onSelectionChange={handleRolesSelection} value={value} />
          </div>
          <SheetFooter className="absolute bottom-0 right-0 p-[16px] 3xl:p-[0.833vw] bg-[#FFF] w-full border-t border-InterfaceStrokesoft">
            <Button
              type="button"
              className="cancelbtn py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
              variant="outline"
              size="sm"
              onClick={() => handleClose(false)}
            >
              <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              Cancel
            </Button>
            <Button
              onClick={() => handleClose(false)}
              type="submit"
              className="bg-BrandPrimarypure border border-BrandPrimary800 text-interfacesurfacecomponent py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
            >
              <i className="cloud-circletick text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              Save
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
}
