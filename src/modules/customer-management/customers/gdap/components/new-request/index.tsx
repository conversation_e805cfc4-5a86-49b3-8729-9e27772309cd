'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  She<PERSON>,
  <PERSON>et<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetFooter,
  Label,
  Input,
  RadioGroup,
  RadioGroupItem,
} from '@redington-gulf-fze/cloudquarks-component-library';
import AdminRelationShipRequest from '../admin-relationship-request';
import { MicrosoftEntrRolesList } from '../microsoft-entra-role-popup';
import { CommanSheetProps } from '@/types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTranslations } from 'next-intl';

// Define validation schema
const formSchema = z.object({
  relationshipName: z.string().min(1, 'Admin Relationship Name is required'),
  duration: z.string().min(1, 'Duration is required').regex(/^\d+$/, 'Duration must be a number'),
  microsoftRoles: z.string().min(1, 'Microsoft Roles are required'),
  autoExtend: z.enum(['yes', 'no'], {
    required_error: 'Auto Extend selection is required',
  }),
});

type FormValues = z.infer<typeof formSchema>;

export default function NewRequest({ open, onClose }: CommanSheetProps) {
  const t = useTranslations();

  const [SuccessPopupOpen, setSuccessPopupOpen] = React.useState(false);
  const [rolesPopupOpen, setRolesPopupOpen] = React.useState(false);
  const [selectedRoles, setSelectedRoles] = React.useState<
    { id: string; name: string; description: string }[]
  >([]);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      relationshipName: '',
      duration: '',
      microsoftRoles: '',
      autoExtend: 'no',
    },
  });

  const inputClasses =
    'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none focus:border-InterfaceStrokehard focus:ring-0 text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]';

  const handleClose = () => {
    reset();
    onClose();
  };

  const onSubmit = () => {
    // console.log('Form submitted:', data);
    setSuccessPopupOpen(true);
    onClose();
    reset();
  };

  // Callback to handle roles selected from popup
  const handleRolesSelected = (roles: { id: string; name: string; description: string }[]) => {
    const safeRoles = Array.isArray(roles) ? roles : [];
    setSelectedRoles(safeRoles);
    const roleNames = safeRoles.map((r) => r.name);
    setValue('microsoftRoles', roleNames.length > 0 ? roleNames.join(', ') : '', {
      shouldValidate: true,
    });
    // Do NOT close the popup here!
  };

  return (
    <Sheet open={open} onOpenChange={handleClose}>
      <SheetTitle></SheetTitle>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[34.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('createanAdminRelationshipRequest')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[400px] xl:h-[480px] 2xl:h-[630px] 3xl:h-[38.25vw]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="border-b border-InterfaceStrokedefault pb-[24px] xl:pb-[24px] 3xl:pb-[1.354vw]">
                <div className="text-InterfaceTextdefault text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  To request an admin relationship with a customer, fill out the form below, copy
                  and paste the following text, including the URL, into an email. Edit the text if
                  necessary and send the email to your customer. Your customer will be able to see
                  the admin relationship name in the M365 admin centre.
                </div>
                <div className="text-InterfaceTextdefault text-[13px] md:text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                  Please note this will not establish a reseller relationship. Go to the{' '}
                  <span className="text-InterfaceTextprimary cursor-pointer">
                    {t('customerList')}{' '}
                  </span>{' '}
                  page to request a{' '}
                  <span className="text-InterfaceTextprimary cursor-pointer">
                    Reseller relationship.
                  </span>
                </div>
              </div>

              <div className="flex flex-col gap-1.5">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  {t('adminRelationshipName')} <span className="text-red-600"> *</span>
                </Label>
                <div className="relative w-full">
                  <Input
                    placeholder={t('adminRelationshipName')}
                    className={`${inputClasses} pr-36 ${errors.relationshipName ? 'border-red-500' : ''}`}
                    {...register('relationshipName')}
                  />
                  {/* Show "Link is Available" only if value is entered and no error */}
                  {watch('relationshipName') && !errors.relationshipName && (
                    <div className="absolute inset-y-0 right-3 flex gap-2 items-center">
                      <i className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] cloud-tickcircle"></i>
                      <div className="text-BrandHighlightpure text-[12px] xl:text-[12px] 3xl:text-[0.625vw]">
                        Link is Available
                      </div>
                    </div>
                  )}
                </div>
                {errors.relationshipName && (
                  <p className="text-red-500 text-xs">{errors.relationshipName.message}</p>
                )}
                <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextsubtitle">
                  The admin relationship name is visible to customers.
                </div>
              </div>

              <div className="flex flex-col gap-1.5">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  Duration in days<span className="text-red-600"> *</span>
                </Label>
                <Input
                  placeholder="Enter in numbers"
                  className={`${inputClasses} ${errors.duration ? 'border-red-500' : ''}`}
                  {...register('duration')}
                />
                {errors.duration && (
                  <p className="text-red-500 text-xs">{errors.duration.message}</p>
                )}
              </div>

              <div className="flex flex-col gap-1.5">
                <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                  Requested Microsoft Roles<span className="text-red-600"> *</span>
                </Label>
                <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextsubtitle">
                  Identify the Microsoft Entra roles you want to assume for your Customer.
                </div>
                {/* Custom roles box UI */}
                <div
                  className={`border border-InterfaceStrokedefault rounded-none min-h-[60px] p-4 flex flex-wrap gap-3 bg-white transition-all duration-150 ${errors.microsoftRoles ? 'border-red-500' : ''}`}
                  style={{ cursor: 'pointer' }}
                  tabIndex={0}
                  onClick={() => setRolesPopupOpen(true)}
                  onFocus={() => setRolesPopupOpen(true)}
                >
                  {selectedRoles && selectedRoles.length > 0 ? (
                    selectedRoles.map((role) => (
                      <span
                        key={role.id}
                        className="inline-flex items-center bg-gray-100 border border-gray-300 rounded px-1 py-1 text-sm text-gray-800 font-medium"
                      >
                        {role.name}
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-400 text-sm">No Microsoft Entra Role selected</span>
                  )}
                </div>
                {errors.microsoftRoles && (
                  <p className="text-red-500 text-xs mt-1">{errors.microsoftRoles.message}</p>
                )}
                <div
                  className="border border-InterfaceStrokedefault p-[10px] xl:p-[10px] 3xl:p-[0.525vw] mt-[-8px] bg-InterfaceSurfacecomponentmuted"
                  onClick={() => setRolesPopupOpen(true)}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="py-[11px] xl:py-[11px] 3xl:py-[0.573vw] px-[12px] xl:px-[12px] 3xl:px-[0.625vw] bg-BrandNeutralpure border border-BrandNeutral700  gap-2 items-center inline-flex ">
                    <i className="cloud-new text-interfacesurfacecomponent text-[12px] xl:text-[12px] 3xl:text-[0.625vw]"></i>
                    <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] text-interfacesurfacecomponent">
                      Select Microsoft Extra Roles
                    </div>
                  </div>
                </div>
                {/* Render MicrosoftEntrRolesList popup */}
                {rolesPopupOpen && (
                  <MicrosoftEntrRolesList
                    titleName="Microsoft Entra Roles List"
                    open={rolesPopupOpen}
                    onClose={() => setRolesPopupOpen(false)}
                    onRolesSelected={handleRolesSelected}
                    value={selectedRoles}
                  />
                )}
              </div>

              <div>
                <div className="text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold text-InterfaceTexttitle">
                  {t('autoExtend')}
                </div>
                <div className="pt-[12px] xl:pt-[12px] 3xl:pt-[0.625vw] pb-[100px] xl:pb-[100px] 3xl:pb-[5.208vw]">
                  <RadioGroup
                    value={watch('autoExtend')}
                    onValueChange={(value: 'yes' | 'no') => setValue('autoExtend', value)}
                    className="flex"
                  >
                    <div className="flex items-center space-x-2 custrediogroup">
                      <RadioGroupItem value="yes" id="r1" />
                      <Label htmlFor="r1" className="font-medium">
                        {t('yes')}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 custrediogroup">
                      <RadioGroupItem value="no" id="r2" />
                      <Label htmlFor="r2" className="font-normal">
                        {t('no')}
                      </Label>
                    </div>
                  </RadioGroup>
                  {errors.autoExtend && (
                    <p className="text-red-500 text-xs">{errors.autoExtend.message}</p>
                  )}
                  {watch('autoExtend') === 'yes' && (
                    <div className="flex gap-2 py-[20px] xl:py-[20px] 3xl:py-[1.042vw]">
                      <i className="cloud-info text-InterfaceTextsubtitle"></i>
                      <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextsubtitle">
                        Auto Extended By 6 Months
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <Button
                  type="button"
                  onClick={handleClose}
                  className="hover:bg-[#d0d1d3] flex items-center gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[16px] flex items-center">
                    Cancel
                  </div>
                </Button>
                <Button
                  type="submit"
                  className="flex applybtn items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[12px] xl:px-[12px] 3xl:px-[0.729vw] rounded-none"
                  size="md"
                >
                  <div>
                    <i className="cloud-circletick text-interfacetextinverse flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                  </div>
                  <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    Finalize request
                  </div>
                </Button>
              </div>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
      <AdminRelationShipRequest
        open={SuccessPopupOpen}
        onClose={() => setSuccessPopupOpen(false)}
      />
    </Sheet>
  );
}
