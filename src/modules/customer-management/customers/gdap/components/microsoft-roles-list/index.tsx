import { DataTable } from '../datatable';
import { TablePagination } from '@/components/common/ui/pagination';
import { TooltipProvider } from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';

type Role = {
  id: string;
  name: string;
  description: string;
};

interface MicrosoftRolesListProps {
  onSelectionChange?: (selectedRoles: Role[]) => void;
  value?: Role[];
}

export default function MicrosoftRolesList({ onSelectionChange, value }: MicrosoftRolesListProps) {
  const columns: ColumnDef<Role>[] = [
    {
      accessorKey: 'name',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-2 font-medium justify-between pt-2"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <span className="text-[14px] text-InterfaceTexttitle">Name</span>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-3 w-3" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-3 w-3" />
              ) : (
                <ArrowUpDown className="h-3 w-3" />
              )}
            </button>
            <ColumnHeaderFilter placeholder="Name" onChange={(val) => column.setFilterValue(val)} />
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-[14px] text-InterfaceTexttitle">{row.getValue('name')}</div>
      ),
      minSize: 200,
    },
    {
      accessorKey: 'description',
      enableSorting: false,
      header: () => (
        <div className="flex flex-col">
          <span className="text-[14px] text-InterfaceTexttitle">Description</span>
          <ColumnHeaderFilter placeholder="Description" onChange={() => {}} />
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-[14px] text-InterfaceTextsubtitle truncate max-w-[320px]">
          {row.getValue('description')}
        </div>
      ),
      minSize: 300,
    },
  ];

  const roles: Role[] = [
    {
      id: '1',
      name: 'Collaboration',
      description: 'Can Manage all aspects of the Dynamics 365 Prod...',
    },
    {
      id: '2',
      name: 'Search Editor',
      description: 'Can Organize, create, manage, and promote topics...',
    },
    {
      id: '3',
      name: 'Skype for Business administrator',
      description: 'Can Organize, create, manage, and promote topics...',
    },
    {
      id: '4',
      name: 'SharePoint administrator',
      description: 'Can Organize, create, manage, and promote topics...',
    },
    {
      id: '5',
      name: 'Teams communications support engineer',
      description: 'Can Organize, create, manage, and promote topics...',
    },
    {
      id: '6',
      name: 'Dynamic 365 administrator',
      description: 'Can Access Dynamics 365 central environments a...',
    },
    {
      id: '7',
      name: 'Dynamic 365 Business Central Administrator',
      description: 'Can manage all aspects of the Exchange products',
    },
    {
      id: '8',
      name: 'Exchange administrator',
      description: 'Can manage all aspects of the Exchange products',
    },
    {
      id: '9',
      name: 'Exchange recipient administrator',
      description: 'Manage access using Microsoft entra ID for identit...',
    },
    {
      id: '10',
      name: 'Identity governance administrator',
      description: 'Can configure Knowledge, learning and other intelli...',
    },
  ];

  // Maintain selectedRowIds state, controlled by value prop if provided
  const [selectedRowIds, setSelectedRowIds] = React.useState<string[]>([]);

  // Sync selectedRowIds with value prop if provided
  React.useEffect(() => {
    if (value) {
      setSelectedRowIds(value.map((role) => role.id));
    }
  }, [value]);

  // Handler for row selection
  const handleSelectionChange = (selectedIds: string[]) => {
    setSelectedRowIds(selectedIds);
    if (onSelectionChange) {
      const selectedRoles = roles.filter((role) => selectedIds.includes(role.id));
      onSelectionChange(selectedRoles);
    }
  };

  return (
    <>
      {/* Table Card */}
      <div className="bg-white border border-BrandNeutral100 rounded shadow-md">
        {/* Table Title Row */}
        <div className="flex items-center justify-between border-b border-InterfaceStrokesoft px-4 py-3">
          <div className="flex items-center gap-2">
            <span className="text-[16px] font-semibold text-InterfaceTexttitle">Request list</span>
            <span className="text-[13px] text-InterfaceTextsubtitle">Showing 10/100 Records</span>
          </div>
        </div>
        {/* Table */}
        <div className="overflow-x-auto ">
          <TooltipProvider>
            <DataTable
              data={roles}
              columns={columns}
              withCheckbox={true}
              selectedRowIds={selectedRowIds}
              onSelectionChange={handleSelectionChange}
            />
            <TablePagination />
          </TooltipProvider>
        </div>
        {/* Pagination */}
      </div>
    </>
  );
}
