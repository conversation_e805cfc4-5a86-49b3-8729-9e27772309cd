'use client';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';

export function InvitationLinkInfo() {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger>
          <i className="cloud-info text-InterfaceTextdefault text-[16px] xl:text-[12px] 3xl:text-[0.833vw]"></i>
        </PopoverTrigger>

        <PopoverContent
          side="top"
          align="center"
          className="p-0 w-[280px] xl:w-[340px] 2xl:w-[320px] 3xl:w-[320px] rounded-none bg-[#FFF] z-[999]"
        >
          <div className="p-[16px] 3xl:p-[0.833vw] flex flex-col gap-[20px] xl:gap-[16px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
            <div className="flex justify-between items-center">
              <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                Info
              </div>
              <div onClick={() => setOpen(false)}>
                <i className="cloud-closecircle text-closecolor text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
              </div>
            </div>
            <div>
              <p className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] leading-[1.4] font-[400]">
                Send this invitation link to your customer if you have not already. By clicking it,
                the customer will be able to accept your request to administrator their products
                using the roles listed below for the specified duration
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
