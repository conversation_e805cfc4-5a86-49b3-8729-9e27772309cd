'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>onte<PERSON>,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { InvitationLinkInfo } from '../invitation-link-info-popup';
import { MicrosoftEntraRoleInfo } from '../entra-role-info-popup';
import { useTranslations } from 'next-intl';

export default function TestLinkPopup() {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <i
          className="cloud-dangerlight text-Interfacefeedbackneutral700 cursor-pointer flex "
          title="Doc"
        ></i>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[550px] xl:max-w-[550px] 3xl:max-w-[28.65vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                testlink
              </div>
              <button className="py-[2px] 3xl:py-[0.104vw] px-[8px] 3xl:px-[0.417vw] bg-BrandSupport250 border border-BrandSupport2300 text-Interfacefeedbackneutral700 text-[16px] xl:text-[0.625vw] font-[500] leading-[1.4]">
                Approval Pending
              </button>
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
          <div className="flex gap-[24px] 3xl:gap-[1.25vw]">
            <h3 className="text-InterfaceTextprimary text-[16px] xl:text-[14px] 3xl:text-[0.833vw] font-[400]">
              <i className="cloud-doc-forward mr-[6px] mr-[2px] 3xl:mr-[0.313vw]"></i>
              {t('requestfornewRelationship')}
            </h3>
            <h3 className="text-closecolor text-[16px] xl:text-[14px] 3xl:text-[0.833vw] font-[400]">
              <i className="cloud-trash mr-[6px] mr-[2px] 3xl:mr-[0.313vw]"></i>
              {t('terminateRelationship')}
            </h3>
          </div>

          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="flex flex-col space-y-[24px] mt-[24px] 3xl:mt-[1.25vw]">
                <div className=" space-y-1 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="flex items-center gap-[8px] xl:gap-[4px] 3xl:gap-[0.417vw]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      {t('invitationLink')}
                    </div>
                    <InvitationLinkInfo />
                  </div>

                  <div
                    className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] break-words
                  "
                  >
                    https://admin.microsoft.com/adminportal/home#/partners/invitation/granularadminrelationships/7a0477cd-cdc5-46b6-a185-71f69666a98121-40eba903-
                    1fe1-48bc-ac36-ea6bad38639b{' '}
                    <i className="cloud-copy text-InterfaceTextprimary"></i>
                  </div>
                </div>
                <div className=" space-y-1 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="flex items-center gap-[8px] xl:gap-[4px] 3xl:gap-[0.417vw]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                      {t('microsoftEntraroles')}
                    </div>
                    <MicrosoftEntraRoleInfo />
                  </div>

                  <div className="flex flex-wrap gap-[6px] 3xl:gap-[0.313vw]">
                    <div className="bg-BrandNeutral50 py-[2px] px-[6px] text-InterfaceTexttitle text-[14px] 3xl:text-[0.729vw] font-[400]">
                      Search Editor
                    </div>
                    <div className="bg-BrandNeutral50 py-[2px] px-[6px] text-InterfaceTexttitle text-[14px] 3xl:text-[0.729vw] font-[400]">
                      Skype for Business administrator
                    </div>
                    <div className="bg-BrandNeutral50 py-[2px] px-[6px] text-InterfaceTexttitle text-[14px] 3xl:text-[0.729vw] font-[400]">
                      SharePoint administrator
                    </div>
                    <div className="bg-BrandNeutral50 py-[2px] px-[6px] text-InterfaceTexttitle text-[14px] 3xl:text-[0.729vw] font-[400]">
                      Teams communications support engineer
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]  space-y-3 mt-[20px]">
                <div className=" space-y-1 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    {t('durationinDays')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    730
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
