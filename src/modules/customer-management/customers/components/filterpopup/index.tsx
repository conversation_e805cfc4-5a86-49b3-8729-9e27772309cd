'use client';
import * as React from 'react';
import {
  Select,
  Sheet<PERSON><PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  // Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { cn } from '@/lib/utils/util';

// import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function CustomerFilter() {
  const t = useTranslations();

  const [brand, setBrand] = React.useState('');
  const [endCustomer, setEndCustomer] = React.useState('');
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-2 text-InterfaceTextdefault hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className=" text">
            <i className="cloud-filtericon  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]  font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[500px] 2xl:h-[550px] 3xl:h-[36.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('customerName')}
              </Label>
              <Select value={endCustomer} onValueChange={setEndCustomer}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    endCustomer
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none ">
                  <SelectGroup className="px-0 text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="india" className="px-2">
                      Wade
                    </SelectItem>
                    <SelectItem value="ind" className="px-2">
                      Darrell
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('customerStatus')}
              </Label>
              <Select value={brand} onValueChange={setBrand}>
                <SelectTrigger
                  className={cn(
                    'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                    'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                    endCustomer
                      ? 'text-blackcolor' // option text color
                      : 'text-InterfaceTextsubtitle' // placeholder color
                  )}
                >
                  <SelectValue placeholder={t('select2')} />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none ">
                  <SelectGroup className="px-0 text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="pending" className="px-2">
                      Pending
                    </SelectItem>
                    <SelectItem value="completed" className="px-2">
                      Completed
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose
                className="flex items-center gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[16px] flex items-center">
                  {t('cancel')}
                </div>
              </SheetClose>
              <SheetClose className="w-full flex gap-2 newGreenBtn text-interfacesurfacecomponent rounded-none !border-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]">
                <div>
                  <i className="cloud-save-2"></i>{' '}
                </div>
                <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('applyFilter')}
                </div>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
