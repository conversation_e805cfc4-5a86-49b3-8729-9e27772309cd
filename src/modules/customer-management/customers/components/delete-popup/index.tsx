'use client';

import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function DeletePopup() {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTrigger className="hover:bg-BrandNeutral100 w-full">
        <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
          <i className="cloud-trash text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
          {t('delete')}
        </div>
      </SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[520px] xl:max-w-[520px] 3xl:max-w-[30.646vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[35px] xl:gap-[35px] 3xl:gap-[1.863vw]">
            <i className="cloud-trash text-closecolor text-[58px]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="mx-[40px] text-InterfaceTexttitle text-[28px] lg:text-[28px] xl:text-[28px] 2xl:text-[28px] 3xl:text-[1.85vw] font-normal  text-center">
                Would you like Delete <br /> customer, please <br />
                confirm?
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
            <Link
              href={''}
              className="text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw] text-background bg-closecolor hover:bg-BrandPrimarypurenew font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]"
            >
              <i className="cloud-trash2 text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw]"></i>
              {t('yesDelete')}
            </Link>
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">or</p>
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
            </div>
            <SheetClose>
              <div className="text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                <i className="cloud-closecircle text-[15px] md:text-[15px] xl:text-[15px] 3xl:text-[0.781vw]"></i>
                {t('noLater')}
              </div>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
