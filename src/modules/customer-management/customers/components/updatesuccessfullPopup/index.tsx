'use client';
import { CommanSheetProps } from '@/types';
import {
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SuccessfullPopupProps extends CommanSheetProps {
  addNewCustomer?: boolean;
  message?: string;
  backlink?: string;
}

export function UpdateSuccessfullPopup({
  open,
  onClose,
  message,
  // backlink = '/customer-management/customers',
}: SuccessfullPopupProps) {
  return (
    <div>
      <Sheet open={open} onOpenChange={onClose}>
        <SheetTitle></SheetTitle>
        <SheetContent
          className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw]  p-[0px] hideclose`}
          side={'right'}
        >
          <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
            <div className="flex flex-col justify-center items-center gap-[35px] xl:gap-[35px] 3xl:gap-[1.863vw] mt-2">
              <i className="cloud-info2 text-BrandPrimary7001 text-[58px]"></i>
              <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="mx-[40px] text-InterfaceTexttitle text-[28px] lg:text-[28px] xl:text-[28px] 2xl:text-[28px] 3xl:text-[1.85vw] font-normal  text-center">
                  {message}
                </div>
              </div>
            </div>
            <div className="flex  justify-center text-center">
              <SheetClose>
                <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
                  <Button
                    type="submit"
                    className="applybtn font-[400] text-interfacesurfacecomponent rounded-none !border-none px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[9px] md:py-[9px] xl:py-[9px] 3xl:py-[0.501vw] text-[14px] md:text-[14px] xl:text-[14px] 3xl:text-[0.721vw]"
                  >
                    <i className="cloud-addprofile"></i>
                    Add Another Customer
                  </Button>
                  <div className="flex items-center gap-[10px]">
                    <div className="bg-InterfaceStrokesoft w-[50px] h-[1px]"></div>
                    <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal">
                      or
                    </div>
                    <div className="bg-InterfaceStrokesoft w-[50px] h-[1px]"></div>
                  </div>
                  <SheetClose>
                    {/* <div className="text-[14px] md:text-[14px] xl:text-[14px] 3xl:text-[0.721vw] text-InterfaceTextdefault cancelbtn font-[400] rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[9px] md:py-[9px] xl:py-[9px] 3xl:py-[0.501vw] flex justify-center items-center gap-2 w-[220px] 3xl:w-[10.8vw]">
                  <i className="cloud-arrowleft text-[11px] md:text-[11px] xl:text-[11px] 3xl:text-[0.512vw]"></i>
                 Go to Cart
                </div> */}

                    <Button
                      className="text-[#3C4146] bg-transparent border border-[#E5E7EB] px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.833vw] py-[10px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]  rounded-none text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.833vw] w-[220px] 3xl:w-[10.8vw] flex justify-center items-cen
                  "
                    >
                      <i className="cloud-arrowleft text-[14px] xl:text-[14px] 2xl:text-[15px] 3xl:text-[0.781vw]"></i>
                      Go to Cart
                    </Button>
                  </SheetClose>
                </div>
              </SheetClose>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
