'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  <PERSON>etClose,
  <PERSON>etContent,
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  Input,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  Select,
  SheetFooter,
  Button,
  SheetOverlay,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CommanSheetProps } from '@/types';
import { Roboto } from 'next/font/google';
import { cn } from '@/lib/utils/util';
import { PhoneInput } from '@/components/common/phone-input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { UpdateSuccessfullPopup } from '../updatesuccessfullPopup';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
interface AddNewEndCustomerPopupProps extends CommanSheetProps {
  isEdit?: boolean;
  titleName?: string;
  backlink?: string;
}

// Define validation schema
const formSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  streetAddress: z.string().min(1, 'Street address is required'),
  streetAddress2: z.string().optional(),
  country: z.string().min(1, 'Country is required'),
  state: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
  mobileNumber: z
    .string()
    .min(8, 'Mobile number must be at least 8 digits')
    .max(15, 'Mobile number must be at most 15 digits'),
  customerVertical: z.string().optional(),
  segment: z.string().optional(),
  sector: z.string().optional(),
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  department: z.string().min(1, 'Department is required'),
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  companyTaxId: z
    .string()
    .optional()
    .refine((val) => !val || (val.length >= 5 && val.length <= 20), {
      message: 'Tax ID must be between 5 and 20 characters',
    }),
});

type FormValues = z.infer<typeof formSchema>;

export default function AddNewEndCustomerPopup({
  open,
  onClose,
  isEdit,
  backlink,
  titleName,
}: AddNewEndCustomerPopupProps) {
  const [SuccessPopupOpen, setSuccessPopupOpen] = React.useState(false);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      companyName: '',
      streetAddress: '',
      streetAddress2: '',
      country: '',
      state: '',
      city: '',
      postalCode: '',
      mobileNumber: '',
      customerVertical: '',
      segment: '',
      sector: '',
      firstName: '',
      middleName: '',
      lastName: '',
      department: '',
      email: '',
      companyTaxId: '',
    },
  });

  const inputClasses =
    'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none focus:border-InterfaceStrokehard focus:ring-0 text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]';

  const handleClose = () => {
    reset(); // Reset form values
    onClose(); // Close the sheet
  };

  const onSubmit = () => {
    setSuccessPopupOpen(true);
    reset(); // Reset form after successful submission
  };

  const handlePhoneChange = (value: string) => {
    setValue('mobileNumber', value, { shouldValidate: true });
  };

  const t = useTranslations();

  return (
    <div>
      <Sheet open={open} onOpenChange={handleClose}>
        <SheetOverlay className="bg-black/20" /> {/* Remove double darkening */}
        <SheetContent
          className={`${roboto.className} hideclose flex flex-col h-full min-h-0 gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]`}
        >
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {titleName} Customer
              </div>
              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col flex-1 min-h-0">
            <div className="bg-InterfaceSurfacecomponent overflow-auto flex-1 p-[20px] lg:p-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
              <div className="flex flex-col gap-[20px] xl:gap-[20px] 3xl:gap-[1.042vw]">
                <Accordion
                  type="multiple"
                  defaultValue={['authorized-signatory']}
                  className="w-full"
                >
                  <AccordionItem value="authorized-signatory">
                    <AccordionTrigger className="px-[14px] lg:px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] lg:py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] border border-InterfaceStrokesoft bg-InterfaceSurfacecomponent hover:no-underline">
                      <div className="w-full flex justify-between items-center">
                        <div className="text-left text-[16px] lg:text-[16px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] text-blackcolor font-semibold leading-[140%]">
                          {t('companyInformation')}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="p-[16px] xl:p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft">
                      <div className="space-y-[22px] lg:space-y-[22px] xl:space-y-[22px] 2xl:space-y-[22px] 3xl:space-y-[1.042vw]">
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('companyName')} <span className="text-red-600">*</span>
                          </Label>
                          <Input
                            placeholder={t('companyName')}
                            className={`${inputClasses} ${errors.companyName ? 'border-red-500' : ''}`}
                            {...register('companyName')}
                          />
                          {errors.companyName && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors.companyName.message}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('streetAddress')} <span className="text-red-600">*</span>
                          </Label>
                          <Input
                            placeholder={t('streetAddress')}
                            className={`${inputClasses} ${errors.streetAddress ? 'border-red-500' : ''}`}
                            {...register('streetAddress')}
                          />
                          {errors.streetAddress && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors.streetAddress.message}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('streetAddress2')}
                          </Label>
                          <Input
                            placeholder={t('streetAddress2')}
                            className={`${inputClasses}`}
                            {...register('streetAddress2')}
                          />
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('country')} <span className="text-red-600">*</span>
                          </Label>
                          <Select
                            value={watch('country')}
                            onValueChange={(value) => {
                              setValue('country', value, { shouldValidate: true });
                            }}
                          >
                            <SelectTrigger
                              className={cn(
                                inputClasses,
                                watch('country') ? 'text-blackcolor' : 'text-InterfaceTextsubtitle',
                                errors.country ? 'border-red-500' : ''
                              )}
                            >
                              <SelectValue placeholder="Select Country" />
                            </SelectTrigger>
                            <SelectContent className="rounded-none bg-[#FFF] border-none">
                              <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                                <SelectItem value="Kuwait">Kuwait</SelectItem>
                                <SelectItem value="India">India</SelectItem>
                                <SelectItem value="MEA">MEA</SelectItem>
                                <SelectItem value="UAE">UAE</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                          {errors.country && (
                            <p className="text-red-500 text-xs mt-1">{errors.country.message}</p>
                          )}
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('regionStateOrProvince')}
                          </Label>
                          <Select
                            value={watch('state')}
                            onValueChange={(value) => setValue('state', value)}
                          >
                            <SelectTrigger
                              className={cn(
                                inputClasses,
                                watch('state') ? 'text-blackcolor' : 'text-InterfaceTextsubtitle'
                              )}
                            >
                              <SelectValue placeholder="Select Region, State, or Province" />
                            </SelectTrigger>
                            <SelectContent className="rounded-none bg-[#FFF] border-none">
                              <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                                <SelectItem value="Kuwait">Kuwait</SelectItem>
                                <SelectItem value="India">India</SelectItem>
                                <SelectItem value="MEA">MEA</SelectItem>
                                <SelectItem value="UAE">UAE</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('city')} <span className="text-red-600">*</span>
                          </Label>
                          <Input
                            placeholder={t('enterCity')}
                            className={`${inputClasses} ${errors.city ? 'border-red-500' : ''}`}
                            {...register('city')}
                          />
                          {errors.city && (
                            <p className="text-red-500 text-xs mt-1">{errors.city.message}</p>
                          )}
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('zipPostalCode')} <span className="text-red-600">*</span>
                          </Label>
                          <Input
                            placeholder={t('enterZipPostalCode')}
                            className={`${inputClasses} ${errors.postalCode ? 'border-red-500' : ''}`}
                            {...register('postalCode')}
                          />
                          {errors.postalCode && (
                            <p className="text-red-500 text-xs mt-1">{errors.postalCode.message}</p>
                          )}
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('mobileNumber')} <span className="text-red-600">*</span>
                          </Label>
                          <PhoneInput
                            value={watch('mobileNumber')}
                            onChange={handlePhoneChange}
                            onCountryChange={() => {}}
                            placeholder={t('enterMobileNumber')}
                            defaultCountry="IN"
                            className={`w-full ${errors.mobileNumber ? 'border-red-500' : ''}`}
                          />
                          {errors.mobileNumber && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors.mobileNumber.message}
                            </p>
                          )}
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="authorized-signatory">
                    <AccordionTrigger className="px-[14px] lg:px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] lg:py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] border border-InterfaceStrokesoft bg-InterfaceSurfacecomponent hover:no-underline">
                      <div className="w-full flex justify-between items-center">
                        <div className="text-left text-[16px] lg:text-[16px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] text-blackcolor font-semibold leading-[140%]">
                          {t('Company Profile')}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="p-[16px] xl:p-[16px] 3xl:p-[0.833vw] border border-InterfaceStrokesoft">
                      <div className="space-y-[22px] lg:space-y-[22px] xl:space-y-[22px] 2xl:space-y-[22px] 3xl:space-y-[1.042vw]">
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('customerVertical')}
                          </Label>
                          <Select
                            value={watch('customerVertical')}
                            onValueChange={(value) => setValue('customerVertical', value)}
                          >
                            <SelectTrigger
                              className={cn(
                                inputClasses,
                                watch('customerVertical')
                                  ? 'text-blackcolor'
                                  : 'text-InterfaceTextsubtitle'
                              )}
                            >
                              <SelectValue placeholder={t('selectCustomerVertical')} />
                            </SelectTrigger>
                            <SelectContent className="rounded-none bg-[#FFF] border-none">
                              <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                                <SelectItem value="Kuwait">Kuwait</SelectItem>
                                <SelectItem value="India">India</SelectItem>
                                <SelectItem value="MEA">MEA</SelectItem>
                                <SelectItem value="UAE">UAE</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('segment')}
                          </Label>
                          <Select
                            value={watch('segment')}
                            onValueChange={(value) => setValue('segment', value)}
                          >
                            <SelectTrigger
                              className={cn(
                                inputClasses,
                                watch('segment') ? 'text-blackcolor' : 'text-InterfaceTextsubtitle'
                              )}
                            >
                              <SelectValue placeholder={t('selectSegment')} />
                            </SelectTrigger>
                            <SelectContent className="rounded-none bg-[#FFF] border-none">
                              <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                                <SelectItem value="Kuwait">Kuwait</SelectItem>
                                <SelectItem value="India">India</SelectItem>
                                <SelectItem value="MEA">MEA</SelectItem>
                                <SelectItem value="UAE">UAE</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex flex-col gap-1.5">
                          <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('sector')}
                          </Label>
                          <Select
                            value={watch('sector')}
                            onValueChange={(value) => setValue('sector', value)}
                          >
                            <SelectTrigger
                              className={cn(
                                inputClasses,
                                watch('sector') ? 'text-blackcolor' : 'text-InterfaceTextsubtitle'
                              )}
                            >
                              <SelectValue placeholder={t('selectSector')} />
                            </SelectTrigger>
                            <SelectContent className="rounded-none bg-[#FFF] border-none">
                              <SelectGroup className="text-InterfaceTexttitle  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                                <SelectItem value="Kuwait">Kuwait</SelectItem>
                                <SelectItem value="India">India</SelectItem>
                                <SelectItem value="MEA">MEA</SelectItem>
                                <SelectItem value="UAE">UAE</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            </div>
            <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft bg-white">
              <SheetClose asChild>
                <Button
                  type="button"
                  className="cancelbtn py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                  variant="outline"
                  size="sm"
                >
                  <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                  {t('cancel')}
                </Button>
              </SheetClose>
              <Link href="">
                <Button
                  type="submit"
                  onClick={onSubmit}
                  className="applybtn text-interfacesurfacecomponent rounded-none !border-none px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
                >
                  <i className="cloud-save-2"></i>
                  {t('submit')}
                </Button>
              </Link>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>

      <UpdateSuccessfullPopup
        open={SuccessPopupOpen}
        onClose={() => setSuccessPopupOpen(false)}
        message={`${isEdit === true ? 'Customer Details Updated Successfully!' : 'New Customer Added Successfully!'}`}
        addNewCustomer={isEdit === true ? false : true}
        backlink={backlink}
      />
    </div>
  );
}
