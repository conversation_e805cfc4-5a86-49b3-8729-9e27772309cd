import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  TooltipProvider,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
// import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import CustomerFilter from '../filterpopup';
import { DeletePopup } from '../delete-popup';
import Link from 'next/link';
import AddNewEndCustomerPopup from '../add-new-end-customer-popup';
import { useTranslations } from 'next-intl';

export default function CustomersTable() {
  const t = useTranslations();

  const [addNewEndCustomerPopupOpen, setAddNewEndCustomerPopupOpen] = React.useState(false);
  const customerColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'companyname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('companyName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder={t('companyName')}
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({}) => (
        <Link href="/customer-management/customers/details">
          <div className=" text-[#1570EF] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer hover:font-[500] ">
            Innovate Limited
          </div>
        </Link>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'firstname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('firstName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder={t('firstName')}
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="flex">{row.getValue('firstname')}</div>,

      minSize: 400,
    },

    {
      accessorKey: 'lastname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('lastName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder={t('lastName')}
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => {
        return <div className="">{row.getValue('lastname')}</div>;
      },

      minSize: 400,
    },
    {
      accessorKey: 'city',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('city')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter placeholder={t('city')} onChange={(val) => column.setFilterValue(val)} /> */}
          </div>
        );
      },
      cell: ({}) => {
        return (
          <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] pl-[12px] ">
            Dubai
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'country',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('country')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder={t('country')}
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({}) => {
        return (
          <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] pl-[12px] ">
            UAE
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            {/* <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[90px] xl:w-[90px] 3xl:w-[4.533vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <Link href="/customer-management/customers/details">
                  <div className="  px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                    <div className="">
                      <i className="cloud-readeye1 text:InterfaceTextdefault  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
                    </div>
                    <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTextdefault  ">
                      View
                    </p>
                  </div>
                </Link>

                <DeletePopup />
                <div
                  onClick={() => setAddNewEndCustomerPopupOpen(true)}
                  className="  px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100"
                >
                  <div className="">
                    <i className="cloud-edit text:InterfaceTextdefault  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
                  </div>
                  <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTextdefault  ">
                    Edit
                  </p>
                </div>
              </PopoverContent>
            </Popover> */}
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title={t('info')}
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[100px] xl:w-[110px] 2xl:w-[120px] 3xl:w-[5.833vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <Link href="/customer-management/customers/details">
                  <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
                    <i className="cloud-readeye1 text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
                    {t('view')}
                  </div>
                </Link>
                <div className="border-b border-InterfaceStrokesoft ">
                  <DeletePopup />
                </div>
                <div
                  onClick={() => setAddNewEndCustomerPopupOpen(true)}
                  className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft"
                >
                  <i className="cloud-edit text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
                  {t('edit')}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    firstname: string;
    lastname: string;
  };

  const customerdata: User[] = [
    {
      firstname: 'Wade',
      lastname: 'Warren',
    },
    {
      firstname: 'Darrell ',
      lastname: 'Steward',
    },
    {
      firstname: 'Jerome ',
      lastname: 'Bell',
    },
    {
      firstname: 'Robert ',
      lastname: 'Fox',
    },
    {
      firstname: 'Kathryn ',
      lastname: 'Murphy',
    },
    {
      firstname: 'Marvin',
      lastname: 'McKinney',
    },
    {
      firstname: 'Floyd ',
      lastname: 'Miles',
    },
    {
      firstname: 'Annette ',
      lastname: 'Black',
    },
    {
      firstname: 'Cameron',
      lastname: 'Williamson',
    },
    {
      firstname: 'Devon ',
      lastname: 'Lane',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('listOfCustomer')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="flex gap-[20px] xl:gap-[20px] 3xl:gap-[1.05vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            {/* <div className=" cursor-pointer flex items-center gap-2 text:InterfaceTexttitle hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
              <div className="">
                <i className="cloud-filtericon text:InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
              </div>
              <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text:InterfaceTexttitle font-medium">
                Filter
              </p>
            </div> */}
            <div>
              <CustomerFilter />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <TooltipProvider>
            <DataTable data={customerdata} columns={customerColumns} withCheckbox={false} />
          </TooltipProvider>
        </div>
        <TablePagination />

        {addNewEndCustomerPopupOpen && (
          <AddNewEndCustomerPopup
            titleName={t('edit')}
            open={addNewEndCustomerPopupOpen}
            isEdit={true}
            onClose={() => setAddNewEndCustomerPopupOpen(false)}
          />
        )}
      </div>
    </>
  );
}
