import { useTranslations } from 'next-intl';
import React from 'react';

export default function Information() {
  const t = useTranslations();
  return (
    <div className="space-y-[16px] xl:space-y-[16px] 2xl:xl:space-y-[16px] 3xl:space-y-[0.833vw]">
      <div className="bg-white border border-InterfaceStrokesoft p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[1.25vw]">
        <div className="text-[#19212A] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] pb-[14px] xl:pb-[14px] 2xl:pb-[14px] 3xl:pb-[0.833vw] font-semibold leading-[140%] border-b border-InterfaceStrokesoft">
          {t('companyInformation')}
        </div>
        <div className="mt-[20px] xl:mt-[15px] 2xl:mt-[15px] 3xl:mt-[1.042vw]">
          <div className="">
            <div className="grid grid-cols-5 gap-[22px] xl:gap-[20px] 2xl:xl:gap-[20px] 3xl:gap-[1.25vw]">
              <div className="flex flex-col gap-[1px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('companyName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Google UAE
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('address')}1
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Test Address
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('address')}2
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Test Address
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('country')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  UAE
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('regionstateprovince')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Ajman
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('city')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Dubai
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('zipPostalCode')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  112025
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('phoneNumber')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  +971 121145544
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('customerVertical')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  BPO
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('segment')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  SMB
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('sector')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Education
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('companyTaxRegistrationID')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  123456
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white border border-InterfaceStrokesoft p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[1.25vw]">
        <div className="text-[#19212A] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] pb-[14px] xl:pb-[14px] 2xl:pb-[14px] 3xl:pb-[0.833vw] font-semibold leading-[140%] border-b border-InterfaceStrokesoft">
          {t('primaryContactInformation')}
        </div>
        <div className="mt-[20px] xl:mt-[15px] 2xl:mt-[15px] 3xl:mt-[1.042vw]">
          <div className="">
            <div className="grid grid-cols-5 gap-[22px] xl:gap-[20px] 2xl:xl:gap-[20px] 3xl:gap-[1.25vw]">
              <div className="flex flex-col gap-[1px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('firstName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Alex
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('middleName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  William
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('lastName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Henry
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('department2')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  Finance
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('email')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%]">
                  <EMAIL>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
