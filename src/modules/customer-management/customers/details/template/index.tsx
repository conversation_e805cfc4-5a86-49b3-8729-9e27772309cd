import React from 'react';
import Link from 'next/link';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import Information from '../components/information';
import AddNewEndCustomerPopup from '../../components/add-new-end-customer-popup';
import { useTranslations } from 'next-intl';

export default function DetailsTemplate() {
  const t = useTranslations();

  const [addNewEndCustomerPopupOpen, setAddNewEndCustomerPopupOpen] = React.useState(false);

  return (
    <>
      <div className="w-full p-[20px] pr-[20px] xl:pr-[30px] 2xl:pr-[32px] 3xl:pr-[1.823vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw]">
        <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-semibold leading-[140%]">
          {t('innovateLimitedOrders')}
          <span className="text-[#7F8488] px-[10px] 3xl:px-[0.521vw]">/</span>{' '}
          <span className="font-normal">{t('details')}</span>
        </div>

        <div className="flex gap-[2px] my-[20px] xl:my-[20px] 3xl:my-[1.042vw]">
          <Link href="">
            <Button
              onClick={() => setAddNewEndCustomerPopupOpen(true)}
              className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal"
            >
              <Image src="/images/blueedit.svg" width={20} height={20} alt="" />
              {t('edit')}
            </Button>
          </Link>

          <Link href="/customer-management/customers">
            <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
              <i className="cloud-back text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
              {t('back')}
            </Button>
          </Link>
        </div>

        <div>
          <Information />
        </div>

        {addNewEndCustomerPopupOpen && (
          <AddNewEndCustomerPopup
            open={addNewEndCustomerPopupOpen}
            onClose={() => setAddNewEndCustomerPopupOpen(false)}
            titleName={t('edit')}
          />
        )}
      </div>
    </>
  );
}
