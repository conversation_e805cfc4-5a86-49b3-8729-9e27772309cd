import React from 'react';
import { useTranslations } from 'next-intl';

export default function OrgInformation() {
  const t = useTranslations();

  return (
    <div className="space-y-[16px] xl:space-y-[16px] 2xl:xl:space-y-[16px] 3xl:space-y-[0.833vw]">
      <div className="bg-white border border-InterfaceStrokesoft p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[1.25vw]">
        <div className="text-[#19212A] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] pb-[14px] xl:pb-[14px] 2xl:pb-[14px] 3xl:pb-[0.833vw] font-semibold leading-[140%] border-b border-InterfaceStrokesoft">
          {t('customerDetails')}
        </div>
        <div className="mt-[20px] xl:mt-[15px] 2xl:mt-[15px] 3xl:mt-[1.042vw]">
          <div className="">
            <div className="grid grid-cols-5 gap-[22px] xl:gap-[20px] 2xl:xl:gap-[20px] 3xl:gap-[1.25vw]">
              <div className="flex flex-col gap-[1px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('customerId')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  CQ122321
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('customerName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Omnividia Technologies LLC
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('address')} 1
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  4, BlockB
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('address')} 2
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Diamond Views, 1JVC
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('city')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Dubai
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%] ">
                  {t('state')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  DU
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft ">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('zipPostalCode')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  191987
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('firstName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Jay
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('lastName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Menon
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  Customer email address
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate ">
                  <EMAIL>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white border border-InterfaceStrokesoft p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[1.25vw]">
        <div className="flex justify-between items-center border-b border-InterfaceStrokesoft pb-[14px] xl:pb-[14px] 2xl:pb-[16px] 3xl:pb-[0.833vw] ">
          <div className="text-[#19212A] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] font-semibold leading-[140%] ">
            {t('organizationTenant')} {t('details')}
          </div>
          <div className="bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 cursor-pointer py-[7px] px-[14px] xl:py-[7px] xl:px-[14px] 2xl:py-[7px] 2xl:px-[14px] 3xl:py-[0.521vw] 3xl:px-[0.833vw] flex gap-[8px] items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] border border-InterfaceStrokesoft">
            <i className="cloud-refresh"></i>
            {t('details')}
          </div>
        </div>
        <div className="mt-[20px] xl:mt-[15px] 2xl:mt-[15px] 3xl:mt-[1.042vw]">
          <div className="">
            <div className="grid grid-cols-4 gap-[22px] xl:gap-[20px] 2xl:xl:gap-[20px] 3xl:gap-[1.25vw]">
              <div className="flex flex-col gap-[1px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('tenantID')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  d2e5866c-c9f5-4b36-a8d4-f2ed7142e5f8
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('tenantName')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Omnividia2025.onmicrosoft.com
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('tenantName')}
                </div>
                <div className="text-[#067532] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Approved
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  Special Qualification
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  None
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  Organization Name
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Omnividia
                </div>
              </div>
              <div className="flex flex-col gap-[1px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('address')} 1
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Villa 4, Block B, Diamond Views 1, JVC, Dubai
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('address')} 2
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  -
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('city')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Dubai
                </div>
              </div>
              <div className="flex flex-col gap-[1px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('state')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Dubai
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  {t('zipPostalCode')}
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  --
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  Primary Contact First Name
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Jay
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw]  border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  Primary Contact Last Name
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  Menon
                </div>
              </div>
              <div className="flex flex-col gap-[1px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  Primary Contact Email
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  <EMAIL>
                </div>
              </div>
              <div className="flex flex-col gap-[1px]   py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft ">
                <div className="text-InterfaceTextsubtitle text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400] leading-[140%]">
                  Primary Contact Phone
                </div>
                <div className="text-[#19212A] text-[12px]  xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-semibold leading-[140%] truncate">
                  504592620
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white border border-InterfaceStrokesoft p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[1.25vw]">
        <div className="flex justify-between items-center border-b border-InterfaceStrokesoft pb-[14px] xl:pb-[14px] 2xl:pb-[16px] 3xl:pb-[0.833vw] ">
          <div className="text-[#19212A] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] font-semibold leading-[140%] ">
            Customer Agreement Details
          </div>
          <div className="flex gap-[12px]">
            <div className="bg-ButtonBaseDefault py-[7px] px-[14px] xl:py-[7px] xl:px-[14px] 2xl:py-[7px] 2xl:px-[14px] 3xl:py-[0.521vw] 3xl:px-[0.833vw] flex gap-[8px] items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] border border-InterfaceStrokesoft">
              <i className="cloud-refresh"></i>
              Refresh
            </div>

            <div className="bg-ButtonBaseDefault py-[7px] px-[14px] xl:py-[7px] xl:px-[14px] 2xl:py-[7px] 2xl:px-[14px] 3xl:py-[0.521vw] 3xl:px-[0.833vw] flex gap-[8px] items-center text-[15px] xl:text-[15px] 2xl:text-[16px] 3xl:text-[0.833vw] border border-InterfaceStrokesoft">
              <i className="cloud-edit"></i>
              {t('edit')}
            </div>
          </div>
        </div>
        <div className="my-[20px] xl:my-[15px] 2xl:my-[15px] 3xl:my-[1.042vw] px-[16px] py-[8px] xl:px-[16px] xl:py-[8px] 2xl:px-[16px] 2xl:py-[8px] 3xl:px-[0.833vw] 3xl:py-[0.417vw] bg-InterfaceSurfacecomponentmuted">
          <div className="flex gap-[15px] xl:gap-[15px] 2xl:gap-[15px] 3xl:gap-[0.781vw]">
            <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal leading-[140%]">
              Direct Customer Agreement Acceptance{' '}
              <span className="text-BrandSupport1pure underline">(Learn More)</span>
            </div>
            <div className=" flex text-InterfaceTextdefault font-medium leading-[140%]">
              <div className="px-[6px] xl:px-[6px] 2xl:px-[6px] 3xl:px-[0.313vw]">:</div>Provided
            </div>
          </div>

          <div className="mt-[6px] xl:mt-[6px] 3xl:mt-[0.313vw] flex gap-[15px] xl:gap-[15px] 2xl:gap-[15px] 3xl:gap-[0.781vw]">
            <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal leading-[140%]">
              Direct Customer Agreement Acceptance{' '}
              <span className="text-BrandSupport1pure underline">(Learn More)</span>
            </div>

            <div className=" flex text-InterfaceTextdefault font-medium leading-[140%]">
              <div className="px-[6px] xl:px-[6px] 2xl:px-[6px] 3xl:px-[0.313vw]">:</div>Provided
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-[20px] xl:gap-[20px] 2xl:gap-[20px] 3xl:gap-[1.042vw]">
          <div className="border border-InterfaceStrokesoft py-[6px] px-[8px] xl:py-[6px] xl:px-[8px] 2xl:py-[6px] 2xl:px-[8px] 3xl:py-[0.313vw] 3xl:px-[0.417vw]">
            <div className="pb-[6px] xl:pb-[6px] 2xl:pb-[6px] 3xl:pb-[0.313vw] border-b border-InterfaceStrokesoft text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium leading-[140%]">
              {t('details')} 01
            </div>
            <div className=" grid grid-cols-11 mt-[8px] xl:mt-[8px] 2xl:mt-[8px] 3xl:mt-[0.417vw]">
              <div className="col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                {`${t('agreement')} ${t('date')} `}
              </div>
              <div className="col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                05/23/2025
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                {t('firstName')}
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                Jay
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                {t('lastName')}
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                Menon
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                Customer Email Address
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                <EMAIL>
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                Customer Phone Number
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                504592620
              </div>
            </div>
          </div>

          <div className="border border-InterfaceStrokesoft py-[6px] px-[8px] xl:py-[6px] xl:px-[8px] 2xl:py-[6px] 2xl:px-[8px] 3xl:py-[0.313vw] 3xl:px-[0.417vw]">
            <div className="pb-[6px] xl:pb-[6px] 2xl:pb-[6px] 3xl:pb-[0.313vw] border-b border-InterfaceStrokesoft text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium leading-[140%]">
              {t('details')} 02
            </div>
            <div className=" grid grid-cols-11 mt-[8px] xl:mt-[8px] 2xl:mt-[8px] 3xl:mt-[0.417vw]">
              <div className="col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                {`${t('agreement')} ${t('date')} `}
              </div>
              <div className="col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                05/23/2025
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                {t('firstName')}
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                Susheet
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                {t('lastName')}
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                Sinha
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                Customer Email Address
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                <EMAIL>
              </div>

              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-4 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-medium">
                Customer Phone Number
              </div>
              <div className="mt-[2px] xl:mt-[2px] 2xl:mt-[2px] 3xl:mt-[0.208vw] col-span-6 text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal">
                <span className="text-InterfaceTextdefault font-medium  px-[4px] xl:px-[4px] 2xl:px-[4px] 3xl:px-[0.208vw]">
                  :
                </span>
                504592620
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
