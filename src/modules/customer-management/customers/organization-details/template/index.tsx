import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@redington-gulf-fze/cloudquarks-component-library';
import OrgInformation from '../components/organization-info';
import { Roboto } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function OrgDetailsTemplate() {
  const t = useTranslations();

  return (
    <>
      <div className={roboto.className}>
        <div className="w-full  pt-[20px] xl:pt-[20px] 2xl:pt-[20px] 3xl:pt-[1.042vw] px-[20px] xl:px-[30px] 2xl:px-[32px] 3xl:px-[1.823vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw]">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-semibold leading-[140%]">
            Innovate Limited <span className="text-[#7F8488] px-[10px] 3xl:px-[0.521vw]">/</span>{' '}
            <span className="font-normal">Omnividia2025.onmicrosoft.com</span>
            <span className="text-[#7F8488] px-[10px] 3xl:px-[0.521vw]">/</span>{' '}
            <span className="font-normal">{t('details')}</span>
          </div>

          <div className="flex gap-[2px] my-[20px] xl:my-[20px] 3xl:my-[1.042vw]">
            <Link href="/customer-management/customers/organization-tenants">
              <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] hover:bg-buttonbasedefaulthover2 bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
                <i className="cloud-back text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                {t('back')}
              </Button>
            </Link>
          </div>

          <div>
            <OrgInformation />
          </div>
        </div>
      </div>
    </>
  );
}
