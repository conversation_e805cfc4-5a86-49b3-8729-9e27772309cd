'use client';
import * as React from 'react';
import {
  Select,
  SheetTitle,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types/components';

export default function ApplyFilterPopup({ onClose }: TermsSheetProps) {
  const [date, setDate] = React.useState<Date>();
  const [statuss, setStatus] = React.useState('All');
  const [brand, setBrand] = React.useState('All');
  // const [ordertype, setOrdertype] = React.useState('All');
  const [endcustmer, setEndcustmer] = React.useState('All');
  // const [orderplacedby, setOrderplacedby] = React.useState('All');

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-2 hover:bg-BrandNeutral100  h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="relative flex items-center">
            <i className="cloud-filtericon text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-medium">
            Filter
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              Apply Filter
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[450px] xl:h-[450px] 2xl:h-[580px] 3xl:h-[35.25vw]">
          <div className="space-y-[16px] xl:space-y-[16px] 3xl:space-y-[0.833vw]">
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                End Customer
              </label>
              <div className="mt-2">
                <Select value={endcustmer} onValueChange={setEndcustmer}>
                  <SelectTrigger
                    className={cn(
                      'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                      'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                      endcustmer
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                Brand
              </label>
              <div className="mt-2">
                <Select value={endcustmer} onValueChange={setEndcustmer}>
                  <SelectTrigger
                    className={cn(
                      'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                      'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                      endcustmer
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                Brand Category
              </label>
              <div className="mt-2">
                <Select value={brand} onValueChange={setBrand}>
                  <SelectTrigger
                    className={cn(
                      'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                      'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                      brand
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]"
              >
                Products
              </label>
              <div className="mt-2">
                <Select value={statuss} onValueChange={setStatus}>
                  <SelectTrigger
                    className={cn(
                      'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                      'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                      statuss
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]"
              >
                Terms
              </label>
              <div className="mt-2">
                <Select value={statuss} onValueChange={setStatus}>
                  <SelectTrigger
                    className={cn(
                      'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                      'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                      statuss
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]"
              >
                Bill Type
              </label>
              <div className="mt-2">
                <Select value={statuss} onValueChange={setStatus}>
                  <SelectTrigger
                    className={cn(
                      'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                      'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                      statuss
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium leading-[140%]"
              >
                Contract Status
              </label>
              <div className="mt-2">
                <Select value={statuss} onValueChange={setStatus}>
                  <SelectTrigger
                    className={cn(
                      'border border-InterfaceStrokehard rounded-none px-[10px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[7px] 2xl:py-[8px] 3xl:py-[0.417vw]',
                      'text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-normal',
                      statuss
                        ? 'text-blackcolor' // option text color
                        : 'text-InterfaceTextsubtitle' // placeholder color
                    )}
                  >
                    <SelectValue placeholder="Select Country" className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Contract End date
              </Label>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                <div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-2 text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ',
                          !date ? ' text-InterfaceTextsubtitle ' : 'text-blackcolor'
                        )}
                      >
                        {date ? format(date, 'PPP') : <span className="">From Date</span>}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-2 text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ',
                          !date ? ' text-InterfaceTextsubtitle ' : 'text-blackcolor'
                        )}
                      >
                        {date ? format(date, 'PPP') : <span>To Date</span>}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite " align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
              <SheetClose>
                <Button
                  size="sm"
                  onClick={onClose}
                  className="bg-[#FFF] hover:bg-InterfaceStrokesoft cursor-pointer border border-InterfaceStrokesoft flex gap-[8px] 3xl:gap-[0.417vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    Cancel
                  </div>
                </Button>
              </SheetClose>
              <Link
                href=""
                className="flex items-center applybtn gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-filtericon text-interfacetextinverse flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                  Apply Filter
                </div>
              </Link>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
