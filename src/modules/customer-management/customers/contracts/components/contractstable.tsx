import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import ApplyFilterPopup from './contractsfilter';
import Informationpopup from './infopopup';
import ContractSchedulePopup from './contractschedule';
import { useTranslations } from 'next-intl';

export default function Contractstable() {
  const [fromDate, setFromDate] = React.useState<Date | undefined>(new Date('2025-01-01'));
  const [toDate, setToDate] = React.useState<Date | undefined>(new Date('2025-03-01'));

  const clearAll = () => {
    setFromDate(undefined);
    setToDate(undefined);
  };

  const t = useTranslations();
  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                End Customer
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="End Customer"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('endcustomer')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'contractid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Contract id
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Contract id"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('contractid')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'subscriptionid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Subscription ID
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder="Subscription ID"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('subscriptionid')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'brand',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Brand
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder="Brand"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('brand')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'productname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Product Name
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            <ColumnHeaderFilter
              placeholder="Product Name"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('productname')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'startdate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Start Date
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Start Date"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('startdate')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'endDate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                End Date
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="End Date"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('endDate')}</div>,

      minSize: 600,
    },
    {
      accessorKey: 'quantity',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Quantity
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Quantity"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('quantity')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'currency',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Currency
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Currency"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('currency')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'value',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Value
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Value"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('value')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'term',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Term
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter placeholder="Term" onChange={(val) => column.setFilterValue(val)} />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('term')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'billtype',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Bill Type
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Bill Type"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('billtype')}</div>,

      minSize: 400,
    },

    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          Action
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot hover:bg-[#e6e8e9] px-[12px] xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[4px] xl:py-[4px] 2xl:py-[6px] 3xl:py-[0.36vw] text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[180px] xl:w-[180px] 2xl:w-[180px] 3xl:w-[9.833vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <div className="">
                  <Informationpopup />
                  <ContractSchedulePopup />

                  {/* <CloneOrderPopup /> */}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    endcustomer: string;
    contractid: string;
    subscriptionid: string;
    brand: string;
    productname: string;
    startdate: string;
    endDate: string;
    quantity: string;
    currency: string;
    value: string;
    term: string;
    billtype: string;
  };

  const registrationdata = [
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
    {
      endcustomer: 'Customer 1',
      contractid: '12345678',
      subscriptionid: 'DZH318Z0BPS6:0008',
      brand: 'Brand 1',
      productname: 'Azure Plan',
      startdate: '02/05/2025',
      endDate: '02/05/2025',
      quantity: '1',
      currency: 'USD',
      value: '1,500.00',
      term: 'Term 1',
      billtype: 'Bill Type 1',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-[12px] ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              List of Contracts
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
              Showing 10/100 Records
            </div>
          </div>
          <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search"
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            <div className=" hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault flex items-center gap-2  h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
              <div className="relative flex items-center">
                <i className="relative cloud-filter text-[15px] xl:text-[16px] 3xl:text-[0.738vw]"></i>
                <div className="absolute z-10 right-0 -top-3 bottom-0 left-2 h-4 w-4 rounded-full bg-blackcolor">
                  <div className="text-InterfaceTextwhite text-[10px]">2</div>
                </div>
              </div>
              <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]  font-medium">
                {t('filter')}
              </p>
            </div>
            <ApplyFilterPopup open={false} onClose={() => {}} />
          </div>
        </div>

        <div className="overflow-x-auto">
          <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] text-sm font-normal">
            {/* From Date */}
            <div className="flex items-center bg-[#F4F5F7] px-2 py-[6px] rounded-sm">
              <span className="text-[#6B7280]">From Date :</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {fromDate ? format(fromDate, 'dd-MM-yyyy') : 'Select Date'}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={fromDate} onSelect={setFromDate} />
                </PopoverContent>
              </Popover>
              {fromDate && (
                <button onClick={() => setFromDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              )}
            </div>

            {/* To Date */}
            <div className="flex items-center bg-[#F4F5F7] px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.366vw] rounded-sm">
              <span className="text-[#6B7280]">To Date :</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {toDate ? format(toDate, 'dd-MM-yyyy') : 'Select Date'}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={toDate} onSelect={setToDate} />
                </PopoverContent>
              </Popover>
              {toDate && (
                <button onClick={() => setToDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              )}
            </div>
            <div className="flex items-center bg-[#F4F5F7] px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.366vw] rounded-sm">
              <span className="text-[#6B7280]">Status :</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    Status
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]"></PopoverContent>
              </Popover>

              <button onClick={() => setToDate(undefined)} className="ml-1">
                <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
              </button>
            </div>

            {/* Clear All */}
            {(fromDate || toDate) && (
              <button
                onClick={clearAll}
                className="flex items-center text-[#8B8B8B] font-[400] hover:text-black"
              >
                <i className="cloud-closecircle mr-[4px]"></i>
                Clear All
              </button>
            )}
          </div>
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
        </div>
        <TablePagination />
      </div>
    </>
  );
}
