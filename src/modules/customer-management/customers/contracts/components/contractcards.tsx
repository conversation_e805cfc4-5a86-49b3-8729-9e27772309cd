'use client';
import React from 'react';
import Barchart2 from '@/components/common/charts/barchart2';
import Barchart3 from '@/components/common/charts/barchart3';
import HorizontalBarChart from '@/components/common/charts/horizontalbarchart';

export default function ContractCards() {
  return (
    <div>
      <div className="grid grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-2 2xl:col-span-2 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw]"></i>
            </div>

            <div className="col-span-4 2xl:col-span-4 3xl:col-span-4 w-full h-[110px] 3xl:h-[5.885vw]">
              <Barchart2 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] xl:text-[12px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                Total Active <br />
                Contracts-Count*
              </div>
              <div className="text-[13px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                100
              </div>
            </div>
          </div>
        </div>
        <div>
          <div className="grid grid-cols-6  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-2 2xl:col-span-2 3xl:col-span-2">
              <i className="cloud-cards1 text-[#7F8488] text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw]"></i>
            </div>

            <div className="col-span-4 2xl:col-span-4 3xl:col-span-4 w-full h-[110px] 3xl:h-[5.885vw]">
              <Barchart3 />
            </div>

            <div className="absolute left-3 3xl:left-4 bottom-2 3xl:bottom-3">
              <div className="text-[#3C4146] text-[11px] xl:text-[12px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                Total Contracts <br />
                Value *
              </div>
              <div className="text-[13px] xl:text-[16px] 2xl:text-[19px] 3xl:text-[0.99vw] font-semibold truncate">
                USD 500K
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="  p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="flex items-center gap-[10px]">
              <i className="cloud-cards1 text-[#7F8488] text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw]"></i>
              <div className="">
                <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium leading-[140%]">
                  Contracts Expiring *
                </div>
              </div>
            </div>

            <div className=" w-full h-[86px] 3xl:h-[5.885vw]">
              <HorizontalBarChart
                legend={{
                  show: false,
                  left: '2%',
                  bottom: '-6%',
                  itemHeight: 10,
                  itemWidth: 10,
                  textStyle: {
                    color: '#2C363F',
                    fontSize: 10,
                  },
                }}
                grid={{
                  top: '5%',
                  left: '2%',
                  right: '2%',
                  bottom: '-2%',
                  containLabel: true,
                }}
                min={0}
                max={50}
                xAxisLabel={{ show: true, fontSize: 9 }}
                xAxisSplitLine={{
                  show: false,
                  lineStyle: {
                    type: 'dashed',
                    color: '#C8CBD0',
                  },
                }}
                yAxisLabel={{
                  color: '#7F8488',
                  fontSize: 9,
                  padding: [0, 0, 0, 0],
                  textAlign: 'left',
                  fontWeight: 400,
                }}
                yAxisTick={{ show: false }}
                yAxisLine={{
                  show: false,
                  lineStyle: {
                    color: '#E4E7EC',
                  },
                }}
                yAxisLine2={{
                  show: false,
                  lineStyle: {
                    color: '#E4E7EC',
                  },
                }}
                yAxisLabel2={{
                  inside: false,
                  position: 'top',
                  zIndex: 1,
                  color: '#7F8488',
                  fontSize: 9,
                  fontWeight: 400,
                }}
                name={'# Quotes'}
                showBackground={true}
                backgroundStyle={{
                  color: '#EDEEF1',
                  borderRadius: [0, 4, 4, 0],
                }}
                label={{
                  show: false,
                  position: 'outside',
                  color: '#344054',
                  formatter: '{c}',
                  fontSize: 12,
                }}
                itemStyle={{
                  color: '#6480AB',
                  borderRadius: [2, 2, 2, 2],
                }}
                yAxisdata={['> 91 days', '61-90 days', '31-61 days', '0-30 days']}
                yAxisdata2={['40', '35', '40', '40']}
                data={[40, 40, 35, 40]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
