'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function SentEmailPopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className="py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] signinbtn text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 3xl:text-[0.833vw] border border-Interface-Stroke-soft">
          {t('sendEmail')}
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%] flex gap-2 items-center">
              <SheetClose>
                <i
                  onClick={onClose}
                  className="cloud-back border border-InterfaceTextprimary text-InterfaceTextprimary p-1.5 rounded-lg text-sm "
                ></i>
              </SheetClose>
              {t('sentEmail')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto h-[450px] xl:h-[550px] 2xl:h-[550px] 3xl:h-[32.25vw] ">
          <div className="space-y-[14px] xl:space-y-[16px] 3xl:space-y-[0.833vw]">
            <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
              {t('cspInvitationSentMessage')}
            </div>
            <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
              {t('reverifyAfterCustomerAcceptance')}
              <Link href="/customer-management/customers/details ">
                {' '}
                <span>
                  <i className="cloud-refresh text-BrandSupport1pure text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[16px] 3xl:text-[0.833vw]" />
                </span>{' '}
              </Link>{' '}
              {t('saveTenantInstruction')}
            </div>
            <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
              {t('resendInvitationInfo')}
              <Link href="/customer-management/customers/details ">
                {' '}
                <span>
                  <i className="cloud-sms   text-BrandSupport1pure text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[16px] 3xl:text-[0.833vw]" />
                </span>{' '}
              </Link>
            </div>
            <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
              {t('attention')} :<br />
              {t('shareMcaConfirmation')}
            </div>
            <div className="text-BrandSupport1pure text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
              {t('viewAgreementLinkText')}
            </div>
            <div className="text-BrandSupport1pure font-medium text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] flex gap-2 py-2 items-center"></div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
