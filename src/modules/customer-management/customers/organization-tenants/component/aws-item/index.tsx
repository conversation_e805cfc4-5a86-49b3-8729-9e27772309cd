'use client';
import {
  Input,
  But<PERSON>,
  Popover,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import { useState } from 'react';
import { useTranslations } from 'next-intl';

export default function AWSItem() {
  const t = useTranslations();

  const [inputValue, setInputValue] = useState('');
  const [isActive, setIsActive] = useState(false);
  const [open, setOpen] = useState(false);
  const [showDiv, setShowDiv] = useState(false);

  const handleFocus = () => {
    const newValue = '123456789';
    setInputValue(newValue);
    setIsActive(true);
  };

  const handleClick = () => {
    setShowDiv(true); // or use setShowDiv(!showDiv) to toggle
  };

  return (
    <div className="">
      <div className="flex items-center w-full  gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw] mb-[20px] xl:mb-[22px] 3xl:mb-[1.25vw]">
        <div className="flex w-full">
          <Input
            type="text"
            value={inputValue}
            onFocus={handleFocus}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={t('enterAWSAccountID')}
            className="flex-grow placeholder:text-InterfaceTextsubtitle text-blackcolor border border-r-0 border-InterfaceStrokehard rounded-l"
            aria-label="Microsoft domain"
          />
          <Button
            type="submit"
            onClick={handleClick}
            className={`border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none ${
              isActive
                ? 'bg-[#00953A] text-500 text-white' // active state
                : 'bg-BrandNeutral400 text-interfacetextinverse'
            }`}
          >
            {t('verify')}
          </Button>
        </div>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <i className="cloud-messagebox text-text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
            <div className="flex justify-between items-center">
              <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                {' '}
                {t('info')}
              </div>
              {/* Close Button */}
              <button
                onClick={() => setOpen(false)}
                className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                aria-label="Close"
              ></button>
            </div>
            <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
              {' '}
              {/* mt-4 to avoid overlap with close button */}
              <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400]">
                {t('awsAccountIdInfo')}
                <span className="text-[#488138]"> https://console.aws.amazon.com/iam/.</span>
                {t('signedInAccountInfo')}
                Click <span className="text-[#488138]"> here </span> for more details.
              </p>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {showDiv && (
        <div className="bg-BrandPrimary50new text-BrandPrimary600 py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-BrandPrimary300new flex items-center gap-2">
          <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
          <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
            {' '}
            {t('clickLinkButton')}
          </div>
        </div>
      )}
    </div>
  );
}
