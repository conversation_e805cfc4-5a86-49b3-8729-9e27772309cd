'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { CustomerTenantSuccessPopup } from '../customer-tenant-success-popup';
import { useTranslations } from 'next-intl';

export default function ExistingCustomerTenantDetailsPopup() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button
          type="submit"
          className="bg-BrandNeutral400 text-interfacetextinverse border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none"
          //  className="bg-BrandNeutralpure hover:bg-BrandNeutral400 text-interfacetextinverse border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none"
        >
          {t('checkAvailability')}
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('existingCustomerTenantDetails')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto h-[450px] xl:h-[550px] 2xl:h-[550px] 3xl:h-[32.25vw]">
          <div className="text-InterfaceTextsubtitle text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
            {t('belowAreTheCustomerDetails')}
          </div>
          <div className="bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokedefault mt-[20px] xl:mt-[22px] 3xl:mt-[1.25vw]">
            <div className="border-b border-InterfaceStrokesoft py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
              {t('details')}
            </div>
            <div className="grid grid-cols-3 ">
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('channelPartnerID')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  C02a7qnez
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('customerCloudIdentityID')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  C02r2qhzh
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('domain')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  goog-test.rilcsp.in.18Dec2023-6.com
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('customerType')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('domain')}
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('customerCompanyName')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  Google UAT Demo Itd
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('firstName')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  Google UAT Demo Itd
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('lastName')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  Google UAT Demo Itd
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('primaryEmail')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  <EMAIL>
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('alternativeEmail')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  <EMAIL>
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('regionCode')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  IN
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('address')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  <EMAIL>
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('postalCode')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  121212
                </div>
              </div>
              <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('phoneNumber')}
                </div>
              </div>
              <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  +911212121212
                </div>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="w-full mt-[26px] lg:mt-[28px] xl:mt-[30px] 2xl:mt-[32px] 3xl:mt-[1.667vw]">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-center ">
            <CustomerTenantSuccessPopup />
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
