'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  She<PERSON>,
  <PERSON><PERSON><PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Label,
  Input,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types/components';
import { useTranslations } from 'next-intl';

export default function NickNamePopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className='hover:bg-BrandNeutral100"'>
        <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[12px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-1 items-center border-t w-full border-InterfaceStrokesoft ">
          <i className="cloud-edit text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.833vw]"></i>
          {`${t('update')} `} {t('nickName')}
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {`${t('update')} `} {t('nickName')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto h-[400px] xl:h-[500px] 3xl:h-[32.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('nickName')} <span className="text-[#f12e2e]"> *</span>
              </Label>
              <Input
                type="text"
                placeholder={t('enterNickName')}
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
              />
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
              <SheetClose asChild>
                <Button
                  size="sm"
                  onClick={onClose}
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </Button>
              </SheetClose>
              <SheetClose>
                <Button
                  size="sm"
                  onClick={onClose}
                  className="flex items-center gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-save-2 text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    {t('update')}
                  </div>
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
