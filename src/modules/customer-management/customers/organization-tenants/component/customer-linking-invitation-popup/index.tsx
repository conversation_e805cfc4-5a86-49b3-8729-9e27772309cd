'use client';
import * as React from 'react';
import {
  Sheet<PERSON>it<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types';
import { useTranslations } from 'next-intl';

export default function CustomerLinkingInvitationPopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className="py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] signinbtn text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 3xl:text-[0.833vw] border border-Interface-Stroke-soft">
          {t('displayInvitationUrl')}
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%] flex gap-2 items-center">
              <SheetClose>
                <i
                  onClick={onClose}
                  className="cloud-back border border-InterfaceTextprimary text-InterfaceTextprimary p-1.5 rounded-lg text-sm "
                ></i>
              </SheetClose>
              {t('customerLinkingInvitationUrl')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto h-[450px] xl:h-[550px] 2xl:h-[550px] 3xl:h-[32.25vw]">
          <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[14px] xl:py-[16px] 3xl:py-[0.833vw]">
            {t('invitationUrlDescription')}
          </div>
          <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
            {t('link')}:
            <span className="text-BrandSupport1pure break-all">
              {' '}
              https://admin.microsoft.com/Adminportal/Home?invType=IndirectResellerRelationship&partnerId=d1716a0f-835d-4210-94ea-c26b8c3f677b&msppld=6363276&indirectCSPId=&DAP=true#/BillingAccounts/partner-invitation
            </span>
          </div>
          <div className="text-BrandSupport1pure font-medium text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] flex gap-2 py-2 items-center">
            <i className="cloud-link"></i>
            {t('copyLink')}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
