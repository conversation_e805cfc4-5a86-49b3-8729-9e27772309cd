'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Sheet,
  Sheet<PERSON>lose,
  <PERSON>et<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON>Footer,
  Input,
  Label,
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
  PhoneInput,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { cn } from '@/lib/utils/util';
import { format } from 'date-fns';
import { TermsSheetProps } from '@/types';
import { NewTenantSuccessPopup } from '../new-tenant-success-popup';
import { useTranslations } from 'next-intl';

export default function ConfirmCustomerAgreementPopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();

  const [date, setDate] = React.useState<Date | undefined>(undefined);

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button
          type="submit"
          className="border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none bg-BrandNeutral400 text-interfacetextinverse"
        >
          {t('submit')}
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('organizationTenantConfiguration')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[450px] xl:h-[480px] 2xl:h-[620px] 3xl:h-[36.25vw]">
          <div className="text-InterfaceTexttitle font-semibold text-[16px] lg:text-[16px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw]">
            {t('confirmMcaAcceptance')}
          </div>
          <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[14px] xl:py-[16px] 3xl:py-[0.833vw]">
            {t('provideInfoAndConfirmAcceptance')}
            <span className="text-BrandSupport1pure">{t('microsoftCustomerAgreement')}</span>.
            {t('documentAcceptanceInstruction')}
          </div>
          <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
            {t('viewDownloadAgreement')}:
            <span className="text-BrandSupport1pure"> https://aka.ms/customeragreement</span>
          </div>
          <div className=" w-full grid grid-cols-1 gap-[22px] xl:gap-[24px] 3xl:gap-[1.25vw] my-[22px] xl:my-[24px] 3xl:my-[1.25vw]">
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('firstName')}
                <span className="text-[#f12e2e]"> *</span>
              </Label>
              <Input
                type="text"
                placeholder="Karthik"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
              />
              <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                {t('endCustomerFirstName')}
              </div>
            </div>
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('lastName')} <span className="text-[#f12e2e]"> *</span>
              </Label>
              <Input
                type="text"
                placeholder="test"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
              />
              <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                {t('endCustomerLastName')}
              </div>
            </div>
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('emailLabel')}
                <span className="text-[#f12e2e]"> *</span>
              </Label>
              <Input
                type="text"
                placeholder="<EMAIL>"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
              />
              <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                {t('endCustomerEmail')}
              </div>
            </div>
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('phoneNumber')} <span className="text-[#f12e2e]"> *</span>
              </Label>
              {/* <Input
                type="text"
                placeholder="<EMAIL>"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
              /> */}
              <PhoneInput defaultCountry="IN" className="w-full" placeholder="Enter Number" />
              <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                {t('phoneFormatInfo')}
              </div>
            </div>
            <div className="grid gap-1.5">
              <Label
                htmlFor="firstname"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('dateOfAcceptance')} <span className="text-[#f12e2e]"> *</span>
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={'outline'}
                    className={cn(
                      'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-2 text-[12px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] ',
                      !date ? ' text-InterfaceTextsubtitle ' : 'text-blackcolor'
                    )}
                  >
                    {date ? format(date, 'PPP') : <span className="">23/02/2025</span>}
                    <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                  <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                </PopoverContent>
              </Popover>
              <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                {t('dateFormatInfo')}
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
            <SheetClose>
              <Button
                size="sm"
                onClick={onClose}
                className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                  {t('cancel')}
                </div>
              </Button>
            </SheetClose>
            <NewTenantSuccessPopup open={false} onClose={() => {}} />
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
