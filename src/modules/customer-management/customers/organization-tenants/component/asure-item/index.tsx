'use client';
import {
  Button,
  RadioGroup,
  RadioGroupItem,
  Label,
  Input,
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import { useState } from 'react';
import CustomerLinkingInvitationPopup from '../customer-linking-invitation-popup';
import SentEmailPopup from '../sent-email-popup';
import { useTranslations } from 'next-intl';

export default function AsureItem() {
  const t = useTranslations();

  const [inputValue, setInputValue] = useState('');
  const [isActive, setIsActive] = useState(false);
  const [isActive2, setIsActive2] = useState(false);
  const [open, setOpen] = useState(false);
  const [openVerify, setOpenVerify] = useState(false);
  const [openCheck, setOpenCheck] = useState(false);
  const [showDiv, setShowDiv] = useState(false);
  const [showDiv2, setShowDiv2] = useState(false);
  const [selectedOption, setSelectedOption] = useState('newtenant'); // default

  const handleFocus = () => {
    const verifyValue = 'redingtongulddxd.onmicrosoft.com';
    setInputValue(verifyValue);
    setIsActive(true);
  };
  const handleFocus2 = () => {
    const verifyValue = '6f98a371-2bbf3-46a8-b20d-29b7bcce4b01';
    setInputValue(verifyValue);
    setIsActive2(true);
  };

  const handleClick = () => {
    setShowDiv(true); // or use setShowDiv(!showDiv) to toggle
  };
  const handleClick2 = () => {
    setShowDiv2(true); // or use setShowDiv(!showDiv) to toggle
  };

  return (
    <div className="">
      <RadioGroup
        onValueChange={(value) => setSelectedOption(value)}
        defaultValue="newtenant"
        className="flex my-[20px] lg:my-[22px] xl:my-[22px] 2xl:my-[24px] 3xl:my-[1.25vw] "
      >
        <div className="flex items-center gap-3">
          <RadioGroupItem value="newtenant" id="r1" />
          <Label htmlFor="r1">{t('createNewTenant')}</Label>
        </div>
        <div className="flex items-center gap-3">
          <RadioGroupItem value="existingtenant" id="r2" />
          <Label htmlFor="r2">{t('linkExistingTenant')}</Label>
        </div>
      </RadioGroup>

      {/* Conditional rendering based on selected radio */}
      {selectedOption === 'newtenant' && (
        <>
          <div className="flex items-center w-full  gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw] mb-[22px] xl:mb-[24px] 3xl:mb-[1.25vw]">
            <div className="flex  w-full gap-0">
              <Input
                type="text"
                value={inputValue}
                onFocus={handleFocus}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder=".onmicrosoft.com"
                className="flex-grow placeholder:text-InterfaceTextsubtitle placeholder:text-right text-right text-blackcolor border border-r-0 border-InterfaceStrokehard rounded-l"
                aria-label="Microsoft domain"
              />
              <Button
                type="submit"
                onClick={handleClick}
                className={`border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none ${
                  isActive
                    ? 'bg-BrandNeutral500 hover:bg-BrandNeutral400 text-interfacetextinverse' // active state
                    : 'bg-BrandNeutral400 text-interfacetextinverse'
                }`}
              >
                {t('checkAvailability')}
              </Button>
            </div>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <i className="cloud-messagebox text-text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                <div className="flex justify-between items-center">
                  <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    {' '}
                    {t('info')}
                  </div>
                  {/* Close Button */}
                  <button
                    onClick={() => setOpen(false)}
                    className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                    aria-label="Close"
                  ></button>
                </div>
                <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                  <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                    {t('accountCreationInstructions')}
                  </p>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {showDiv && (
            <div className="bg-BrandPrimary50new text-BrandPrimary600 py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-BrandPrimary300new flex items-start gap-2">
              <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
              <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                {t('tenantCreationMessage')}
              </div>
            </div>
          )}
        </>
      )}

      {selectedOption === 'existingtenant' && (
        <>
          <div className="space-y-[22px] xl:space-y-[24px] 3xl:space-y-[1.25vw]">
            <div className="flex justify-between items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div className="flex w-full gap-0">
                <Input
                  type="text"
                  value={inputValue}
                  onFocus={handleFocus2}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="Enter your 36 Digit Tenant ID"
                  className="flex-grow placeholder:text-InterfaceTextsubtitle text-blackcolor border border-r-0 border-InterfaceStrokehard rounded-l"
                  aria-label="Microsoft domain"
                />
                <Button
                  type="submit"
                  onClick={handleClick}
                  className={`border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none ${
                    isActive2
                      ? 'bg-BrandNeutral500 hover:bg-BrandNeutral400 text-white' // active state
                      : 'bg-BrandNeutral400 text-interfacetextinverse'
                  }`}
                >
                  {t('verify')}
                </Button>
              </div>
              <Popover open={openVerify} onOpenChange={setOpenVerify}>
                <PopoverTrigger asChild>
                  <i className="cloud-messagebox text-text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
                </PopoverTrigger>
                <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                  <div className="flex justify-between items-center">
                    <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      {' '}
                      {t('info')}
                    </div>
                    {/* Close Button */}
                    <button
                      onClick={() => setOpenVerify(false)}
                      className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                    <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      {t('tenantIdInfo')}{' '}
                      <span className="text-InterfaceTextprimary underline">{t('clickHere')}</span>{' '}
                      for the instructions to locate your customers tenant ID.
                    </p>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {showDiv && (
              <>
                <div className="bg-BrandPrimary50new text-BrandPrimary600 py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-BrandPrimary300new flex items-start gap-2">
                  <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
                  <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                    {t('signedInAccountInfo')}
                  </div>
                </div>

                <div className="flex justify-end gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                  <CustomerLinkingInvitationPopup open={false} onClose={() => {}} />
                  <SentEmailPopup open={false} onClose={() => {}} />
                </div>
              </>
            )}

            <div className="flex items-center justify-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">
                {t('findId')}
              </p>
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
            </div>
            <div>
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('findTenantId')}
              </label>
              <div className="flex justify-between items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <div className="flex w-full">
                  <Input
                    type="text"
                    placeholder=".onmicrosoft.com"
                    className="flex-grow placeholder:text-InterfaceTextsubtitle placeholder:text-right  text-right text-blackcolor border border-r-0 border-InterfaceStrokehard rounded-l"
                  />
                  <Button
                    type="submit"
                    onClick={handleClick2}
                    className={`border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none ${
                      isActive
                        ? 'bg-BrandNeutral500 hover:bg-BrandNeutral400 text-white' // active state
                        : 'bg-BrandNeutral400 text-interfacetextinverse'
                    }`}
                  >
                    {t('check')}
                  </Button>
                </div>
                <Popover open={openCheck} onOpenChange={setOpenCheck}>
                  <PopoverTrigger asChild>
                    <i className="cloud-messagebox text-text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
                  </PopoverTrigger>
                  <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                    <div className="flex justify-between items-center">
                      <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                        {' '}
                        {t('info')}
                      </div>
                      {/* Close Button */}
                      <button
                        onClick={() => setOpenCheck(false)}
                        className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                        aria-label="Close"
                      ></button>
                    </div>
                    <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                      <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                        {t('findTenantIdUsingCustomer')}
                        <span className="text-InterfaceTextprimary underline">
                          Onmicrosoft.Com
                        </span>{' '}
                        Domain{' '}
                      </p>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {showDiv2 && (
              <>
                <div className="bg-[#FFF7F5] text-closecolor py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-closecolor flex items-start gap-2">
                  <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
                  <div className="text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[13px] 3xl:text-[0.710vw]">
                    “Tenant ID is not Found. Please ensure you enter correct .onmicrosoft.com
                    domain”
                  </div>
                </div>
              </>
            )}
            {/* {showDiv3 && (
              <>
                <div className="bg-BrandPrimary50new text-BrandPrimary600 py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-BrandPrimary300new ">
                  <div className='flex items-start justify-between w-full gap-2'>
                    <div className='flex items-start justify-start gap-2'>
                      <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
                      <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                        Tenant ID Found: 6f98a371-2bbf3-46a8-b20d-29b7bcce4b01
                      </div>
                    </div>
                    <div className='flex items-center justify-between whitespace-nowrap gap-2' >
                      <i className="cloud-files1 text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
                      <div className='text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]'>Copy ID</div>
                    </div>
                  </div>
                </div>
              </>
            )} */}
          </div>
        </>
      )}
    </div>
  );
}
