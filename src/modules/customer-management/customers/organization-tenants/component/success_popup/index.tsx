'use client';

import { Button, Input, SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function SuccessPopup() {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTrigger>
        <Button
          size="sm"
          className="flex gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
        >
          <div>
            <i className="cloud-link text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
          </div>
          <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
            {t('link')}
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-info2 text-BrandPrimarypurenew text-[65px] lg:text-[65px] xl:text-[70px] 2xl:text-[70px] 3xl:text-[3.75vw] font-light"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[30px] lg:text-[34px] xl:text-[36px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                {t('success')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                {t('requestSubmitted')} <br /> {t('successfully')}
              </div>
            </div>
            <div className="w-[428px] 3xl:w-[22.292vw] bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokedefault py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('nickName')}
              </label>
              <div className="flex w-full gap-[10px] 3xl:gap-[0.521vw]">
                <Input
                  type="text"
                  placeholder={t('enterSubscriptionNickName')}
                  className="flex-grow placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  aria-label="Microsoft domain"
                />
                <Link href="/customer-management/customers/organization-tenants">
                  <Button
                    type="submit"
                    className="bg-[#FFF] hover:bg-InterfaceStrokesoft cursor-pointer border border-InterfaceStrokesoft rounded-none"
                    // className={`border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none ${isActive
                    //   ? 'bg-green-500 text-white' // active state
                    //   : 'bg-BrandNeutral400 text-interfacetextinverse'
                    //   }`}
                  >
                    <i className="cloud-save"></i>
                    {t('save')}
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] text-center">
              <SheetClose>
                <div className="bg-[#00953A] hover:bg-[#067532] text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-background cursor-pointer border border-InterfaceStrokesoft rounded-none font-medium leading-[100%] w-[200px] xl:w-[240px] 3xl:w-[12.5vw] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2">
                  <i className="cloud-closecircle text-[18px]"></i>
                  {t('close')}
                </div>
              </SheetClose>
              <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
                <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">
                  {t('or')}
                </p>
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              </div>
              <Link href="/customer-management/customers/organization-tenants">
                <div className="bg-[#FFF] hover:bg-InterfaceStrokesoft text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault cursor-pointer border border-InterfaceStrokesoft rounded-none font-medium leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2">
                  <i className="cloud-arrowleft text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                  {t('backtoOrg&TenantList')}
                </div>
              </Link>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
