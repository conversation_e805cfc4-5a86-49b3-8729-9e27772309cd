'use client';
import {
  Input,
  // Button,
  Popover,
  <PERSON>overContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import { useState } from 'react';
import CreateNewDomainPopup from '../create-new-domain-popup';
import ExistingCustomerTenantDetailsPopup from '../existing-customer-tenant-details-popup';
import { useTranslations } from 'next-intl';

export default function GoogleCloudPlatformItem() {
  const t = useTranslations();
  const [inputValue, setInputValue] = useState('');
  // const [isActive, setIsActive] = useState(false);
  const [openDomain, setOpenDomain] = useState(false);
  const [open, setOpen] = useState(false);
  const [openTeam, setOpenTeam] = useState(false);
  // const [showDiv, setShowDiv] = useState(false);
  // const [showDiv2, setShowDiv2] = useState(false);

  const handleFocus = () => {
    const newValue = 'goog-test-rilcsp.in.17March2025-5.com';
    setInputValue(newValue);
    // setIsActive(true);
  };

  // const handleClick = () => {
  //   setShowDiv(true); // or use setShowDiv(!showDiv) to toggle
  // };

  const [selectedOption, setSelectedOption] = useState('comfortable'); // default
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const toggle = () => {
    setIsOpen((prev) => !prev);
  };

  return (
    <div className="">
      <RadioGroup
        defaultValue=""
        className="my-[20px] lg:my-[22px] xl:my-[22px] 2xl:my-[24px] 3xl:my-[1.25vw] space-y-[12px] xl:space-y-[14px] 3xl:space-y-[0.729vw]"
        onValueChange={(value) => setSelectedOption(value)}
      >
        <div className="flex items-center gap-3">
          <RadioGroupItem value="newcustomer" id="r1" />
          <Label htmlFor="r11">{t('googleCloudPlatform')}</Label>
        </div>
        <div className="flex items-center gap-3">
          <RadioGroupItem value="existingcustomer" id="r2" />
          <Label htmlFor="r22">{t('linkorTransferExistingCustomerTenant/Domain')}</Label>
        </div>
      </RadioGroup>

      {/* Conditional rendering based on selected radio */}
      {selectedOption === 'newcustomer' && (
        <div className="">
          <label className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium flex items-center justify-between gap-[8px] 3xl:gap-[0.417vw]">
            {t('selectCustomerTenantType')}
          </label>

          <RadioGroup
            defaultValue=""
            className="flex my-[20px] lg:my-[22px] xl:my-[22px] 2xl:my-[24px] 3xl:my-[1.25vw] gap-[20px] lg:gap-[22px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]"
          >
            <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div className="flex items-center gap-3">
                <RadioGroupItem onClick={toggle} value="domaincustomer" id="r1" />
                <Label
                  htmlFor="r1"
                  className="text-[12px] xl:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                >
                  {t('domainCustomer(recommended)')}
                </Label>
              </div>
              <Popover open={openDomain} onOpenChange={setOpenDomain}>
                <PopoverTrigger asChild>
                  <i className="cloud-messagebox text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
                </PopoverTrigger>
                <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                  <div className="flex justify-between items-center">
                    <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      Info
                    </div>
                    <button
                      onClick={() => setOpenDomain(false)}
                      className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                    <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      For Google Workspace or Google Cloud Subscriptions
                    </p>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div className="flex items-center gap-3">
                <RadioGroupItem value="teamcustomer" id="r2" />
                <Label
                  htmlFor="r2"
                  className="text-[12px] xl:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                >
                  {t('teamCustomer')}
                </Label>
              </div>
              <Popover open={openTeam} onOpenChange={setOpenTeam}>
                <PopoverTrigger asChild>
                  <i className="cloud-messagebox text-text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
                </PopoverTrigger>
                <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                  <div className="flex justify-between items-center">
                    <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      {t('info')}
                    </div>
                    <button
                      onClick={() => setOpenTeam(false)}
                      className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                    <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      For Essentials or Enterprise Essentials, the customer will verify their
                      account by their email address (not by domain). Select Team Customer only if
                      you&apos;re ordering Essentials or Enterprise Essentials, and the customer
                      will verify their account by email address (not by domain). A customer with
                      email-verified accounts cannot purchase other Google Workspace services, such
                      as Google Vault, unless they verify their domain.{' '}
                    </p>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </RadioGroup>

          <div className="">
            {isOpen && (
              <div className=" w-full   ">
                <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw] mb-[20px] xl:mb-[22px] 3xl:mb-[1.25vw]">
                  <div className="flex w-full">
                    <Input
                      type="text"
                      value={inputValue}
                      onFocus={handleFocus}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder={t('entertheDomainName')}
                      className="flex-grow placeholder:text-InterfaceTextsubtitle placeholder:text-right text-right text-blackcolor border border-r-0 border-InterfaceStrokehard rounded-l"
                      aria-label="Microsoft domain"
                    />
                    <CreateNewDomainPopup />
                  </div>
                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <i className="cloud-messagebox text-text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
                    </PopoverTrigger>
                    <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                      <div className="flex justify-between items-center">
                        <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                          {' '}
                          Info
                        </div>
                        {/* Close Button */}
                        <button
                          onClick={() => setOpen(false)}
                          className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                          aria-label="Close"
                        ></button>
                      </div>
                      <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                        {' '}
                        <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                          his is the primary internet address that the customer&apos;s organization
                          uses for its online presence. It is the part of the email addresses after
                          the &quot;@&quot; symbol (e.g.,{' '}
                          <span className="text-InterfaceTextprimary">example.com</span> in &quot;
                          <span className="text-InterfaceTextprimary"><EMAIL></span>
                          &quot;). When you enter the domain name while creating a new customer
                          account in Google services, it allows Google to set up services, user
                          accounts, and email addresses associated with that specific domain.
                        </p>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* this is shows on click of Check Availability button */}
                {/* {showDiv2 && (
                  <div className="bg-[#FFF7F5] text-closecolor py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-closecolor flex items-start gap-2">
                    <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
                    <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      The domain you provided is already registered with another Google Customer. We
                      cannot create a tenant using this domain. Please submit a different domain
                      name.
                    </div>
                  </div>
                )} */}
              </div>
            )}
          </div>
        </div>
      )}

      {selectedOption === 'existingcustomer' && (
        <div className="">
          <div className="flex items-center w-full  gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw] mb-[20px] xl:mb-[22px] 3xl:mb-[1.25vw]">
            <div className="flex w-full ">
              <Input
                type="text"
                value={inputValue}
                onFocus={handleFocus}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={t('entertheDomainName')}
                className="flex-grow placeholder:text-InterfaceTextsubtitle placeholder:text-right text-right text-blackcolor border border-r-0 border-InterfaceStrokehard rounded-l"
                aria-label="Microsoft domain"
              />
              {/* <Button
                type="submit"
                onClick={handleClick}
                className={`border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none ${
                  isActive
                    ? 'bg-[#768FB5] text-white' // active state
                    : 'bg-BrandNeutral400 text-interfacetextinverse'
                }`}
              >
                Check Availability
              </Button> */}
              <ExistingCustomerTenantDetailsPopup />
            </div>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <i className="cloud-messagebox text-text-InterfaceTextdefault text-[22px] lg:text-[22px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] cursor-pointer"></i>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                <div className="flex justify-between items-center">
                  <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                    {' '}
                    Info
                  </div>
                  {/* Close Button */}
                  <button
                    onClick={() => setOpen(false)}
                    className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                    aria-label="Close"
                  ></button>
                </div>
                <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                  {' '}
                  <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                    For Essentials or Enterprise Essentials, the customer will verify their account
                    by their email address (not by domain). Select Team Customer only if you&#39;re
                    ordering Essentials or Enterprise Essentials, and the customer will verify their
                    account by email address (not by domain). A customer with email-verified
                    accounts cannot purchase other Google Workspace services, such as Google Vault,
                    unless they verify their domain.
                  </p>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* this is shows on click of Check Availability button */}
          {/* {showDiv && (
            <div className="bg-BrandPrimary50new text-BrandPrimary600 py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-BrandPrimary300new flex items-start gap-2">
              <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
              <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                Your Tenant ID &quot;goog-test.rilcsp.in.118Dec2024-6.com&quot; is linked to
                REDINGTON. Please click Save to Continue.
              </div>
            </div>
          )} */}
        </div>
      )}
    </div>
  );
}
