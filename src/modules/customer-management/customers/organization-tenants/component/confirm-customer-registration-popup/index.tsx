'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  Input,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types';
import ConfirmCustomerAgreementPopup from '../confirm-customer-agreement-popup';
import { useTranslations } from 'next-intl';

export default function ConfirmCustomerRegistrationPopup({ onClose }: TermsSheetProps) {
  const t = useTranslations();

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button
          size="sm"
          className="flex gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
        >
          <div>
            <i className="cloud-save-2 text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
          </div>
          <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
            {t('save')}
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('organizationTenantConfiguration')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto h-[450px] xl:h-[550px] 2xl:h-[550px] 3xl:h-[32.25vw]">
          <div className="text-InterfaceTexttitle font-semibold text-[16px] lg:text-[16px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw]">
            {t('confirmCustomerTaxId')}
          </div>
          <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[14px] xl:py-[16px] 3xl:py-[0.833vw]">
            {t('taxRegistrationRequirement')}
          </div>
          <div className="text-InterfaceTextdefault text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
            {t('microsoftAnnouncements')}:
            <span className="text-BrandSupport1pure">
              {' '}
              https://docs.microsoft.com/en-us/partner-center/announcement/2021-march#15
            </span>
          </div>
          <div className="flex  w-full gap-0 my-[22px] xl:my-[24px] 3xl:my-[1.25vw]">
            <Input
              type="text"
              placeholder={t('taxIdentificationNumber')}
              className="flex-grow placeholder:text-InterfaceTextsubtitle text-blackcolor border border-r-0 border-InterfaceStrokehard rounded-l"
              aria-label="Microsoft domain"
            />
            <ConfirmCustomerAgreementPopup open={false} onClose={() => {}} />
          </div>

          <div className="bg-InterfaceSurfacecomponentmuted text-InterfaceTextsubtitle py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-InterfaceStrokedefault flex items-start gap-2">
            <i className="cloud-info text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
            <div className="space-y-[10px] xl:space-y-[10px] 3xl:space-y-[0.525vw]">
              <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                *{t('taxIdValidationMessage')} <br />
              </div>
              <div className="text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                *{t('confirmCompanyTaxId')}.
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <SheetClose>
              <Button
                size="sm"
                onClick={onClose}
                className="hover:bg-InterfaceStrokesoft border border-InterfaceStrokesoft bg-interfacesurfacecomponent flex gap-[8px] 3xl:gap-[0.417vw] text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
              >
                <div>
                  <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                </div>
                <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                  {t('cancel')}
                </div>
              </Button>
            </SheetClose>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
