'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  Label,
  Input,
  RadioGroupItem,
  RadioGroup,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Popover,
  PopoverTrigger,
  PopoverContent,
  ScrollArea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';
import { GoogleSuccessPopup } from '../google-success-popup';
import { useTranslations } from 'next-intl';

export default function CreateNewDomainPopup() {
  const t = useTranslations();
  const [selectedOption, setSelectedOption] = useState(''); // default
  const [open, setOpen] = useState(false);
  const [openEmail, setOpenEmail] = useState(false);

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button
          type="submit"
          className="bg-BrandNeutral400 text-interfacetextinverse border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none"
          // className="bg-BrandNeutralpure hover:bg-BrandNeutral400 text-interfacetextinverse border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none"
        >
          {t('checkAvailability')}
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('createNewDomainCustomer')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <ScrollArea className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto">
          <div className="pb-[100px]">
            <Label
              htmlFor="firstname"
              className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
            >
              {t('selectTheCustomerSegmentType')}
            </Label>
            <RadioGroup
              defaultValue=""
              onValueChange={(value) => setSelectedOption(value)}
              className="flex my-[20px] lg:my-[22px] xl:my-[22px] 2xl:my-[24px] 3xl:my-[1.25vw] gap-[20px] lg:gap-[22px] xl:gap-[22px] 2xl:gap-[24px] 3xl:gap-[1.25vw]"
            >
              <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <div className="flex items-center gap-3">
                  <RadioGroupItem value="commercial" id="r1" />
                  <Label
                    htmlFor="r1"
                    className="text-[12px] xl:text-[14px] 1xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                  >
                    {t('commercial')}
                  </Label>
                </div>
              </div>
              <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <div className="flex items-center gap-3">
                  <RadioGroupItem value="educational" id="r2" />
                  <Label
                    htmlFor="r2"
                    className="text-[12px] xl:text-[14px] 1xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"
                  >
                    {t('educationalInstitution')}
                  </Label>
                </div>
              </div>
            </RadioGroup>

            {selectedOption === 'commercial' && (
              <>
                <div className="grid grid-cols-2 gap-[20px] lg:gap-[22px] xl:gap-[24px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
                  <div className="col-span-2">
                    <div className="text-InterfaceTexttitle font-medium text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('companyInformation')}
                    </div>
                    <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 1xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      {t('thecompanyInformationIsUsedToCreate')}
                    </div>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('organizationName')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo itd"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('domain')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="google-test.rllcsp.in.17March2025-6.com"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('channelPartnerID')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="C02a7qnez"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('CRMID')}
                    </Label>
                    <Input
                      type="text"
                      placeholder="CRM ID"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('phoneNumber')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="0129875345"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="col-span-2">
                    <div className="text-InterfaceTexttitle font-medium text-[12px] xl:text-[14px] 1xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('addressInformation')}
                    </div>
                    <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 1xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      The address is used to create the initial administrator account for Google
                      Workspace and Google Cloud.
                    </div>
                  </div>
                  <div className="relative ">
                    <label
                      htmlFor="required-email"
                      className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                    >
                      {t('country')} <span className="text-[#f12e2e]"> *</span>
                    </label>
                    <Select defaultValue="Azure">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder="Select a Brand Category" className="" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#fff]">
                        <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectItem value="Azure">Azure</SelectItem>
                          <SelectItem value="AWS">AWS</SelectItem>
                          <SelectItem value="Google Cloud Platform">
                            Google Cloud Platform
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('streetAddress')} 1 <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo ltd add"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('streetAddress2')}
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo ltd add 2"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('streetAddress')} 3
                    </Label>
                    <Input
                      type="text"
                      placeholder="Street Address 3"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="relative ">
                    <label
                      htmlFor="required-email"
                      className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                    >
                      {t('state')} <span className="text-[#f12e2e]"> *</span>
                    </label>
                    <Select defaultValue="--Select--">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder="Select a Brand Category" className="" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#fff]">
                        <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectItem value="--Select--">Azure</SelectItem>
                          <SelectItem value="AWS">AWS</SelectItem>
                          <SelectItem value="Google Cloud Platform">
                            Google Cloud Platform
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('city')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Gurgaon"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('pincode')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="121212"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="col-span-2">
                    <div className="text-InterfaceTexttitle font-medium text-[12px] xl:text-[14px] 1xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('contactInformation')}
                    </div>
                    <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 1xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      {t('theNameAndEmailAddress')}
                    </div>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('firstName')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('lastName')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo ltd"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] flex items-center gap-3"
                    >
                      {t('email')}
                      <span className="text-[#f12e2e]">*</span>
                      <Popover open={openEmail} onOpenChange={setOpenEmail}>
                        <PopoverTrigger asChild>
                          <i className="cloud-messagebox text-text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i>
                        </PopoverTrigger>
                        <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                          <div className="flex justify-between items-center">
                            <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                              {t('info')}
                            </div>
                            <button
                              onClick={() => setOpenEmail(false)}
                              className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                              aria-label="Close"
                            ></button>
                          </div>
                          <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                            <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                              Super Administrator for their Google Workspace or Cloud Identity
                              Account
                            </p>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </Label>
                    <Input
                      type="text"
                      placeholder="@googletest.rlksp.in.17march2025-6.com"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] flex items-center gap-3"
                    >
                      Alternative Email <span className="text-[#f12e2e]"> *</span>
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <i className="cloud-messagebox text-text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i>
                        </PopoverTrigger>
                        <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                          <div className="flex justify-between items-center">
                            <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                              Info
                            </div>
                            <button
                              onClick={() => setOpen(false)}
                              className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                              aria-label="Close"
                            ></button>
                          </div>
                          <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                            <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                              Entry any email that doesn’t support the customer’s primary domain.
                              <span className="text-BrandSupport1500"> Learn More </span>
                            </p>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </Label>
                    <Input
                      type="text"
                      placeholder="<EMAIL>"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
              </>
            )}
            {selectedOption === 'educational' && (
              <>
                <div className="grid grid-cols-2 gap-[20px] lg:gap-[22px] xl:gap-[24px] 2xl:gap-[24px] 3xl:gap-[1.25vw]">
                  <div className="col-span-2">
                    <div className="text-InterfaceTexttitle font-medium text-[12px] xl:text-[14px] 1xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('companyInformation')}
                    </div>
                    <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 1xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      {t('thecompanyInformationIsUsedToCreate')}
                    </div>
                  </div>
                  <div className="relative ">
                    <label
                      htmlFor="required-email"
                      className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                    >
                      {t('educationalInstituteType')} <span className="text-[#f12e2e]"> *</span>
                    </label>
                    <Select defaultValue="Please Select the Educational Institute Type">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder={t('pleaseSelectInstituteType')} className="" />
                      </SelectTrigger>
                      <SelectContent className="bg-white">
                        <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectItem value="Please Select the Educational Institute Type">
                            {t('pleaseSelectInstituteType')}
                          </SelectItem>
                          <SelectItem value="AWS">AWS</SelectItem>
                          <SelectItem value="Google Cloud Platform">
                            Google Cloud Platform
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="relative ">
                    <label
                      htmlFor="required-email"
                      className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                    >
                      {t('instituteSize')} <span className="text-[#f12e2e]"> *</span>
                    </label>
                    <Select defaultValue="Please Select the Institute Size">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder={t('pleaseSelectInstituteSize')} className="" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectItem value="Please Select Institute Size">
                            {t('pleaseSelectInstituteSize')}
                          </SelectItem>
                          <SelectItem value="AWS">AWS</SelectItem>
                          <SelectItem value="Google Cloud Platform">
                            Google Cloud Platform
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('organizationName')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo itd"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('domain')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="google-test.rllcsp.in.17March2025-6.com"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('channelPartnerID')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="C02a7qnez"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('CRMID')}
                    </Label>
                    <Input
                      type="text"
                      placeholder={t('CRMID')}
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('phoneNumber')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="0129875345"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('website')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="--Type Here--"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="col-span-2">
                    <div className="text-InterfaceTexttitle font-medium text-[12px] xl:text-[14px] 1xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('addressInformation')}
                    </div>
                    <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 1xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      The address is used to create the initial administrator account for Google
                      Workspace and Google Cloud.
                    </div>
                  </div>
                  <div className="relative ">
                    <label
                      htmlFor="required-email"
                      className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                    >
                      Country <span className="text-[#f12e2e]"> *</span>
                    </label>
                    <Select defaultValue="Azure">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder="Select a Brand Category" className="" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#fff]">
                        <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectItem value="Azure">Azure</SelectItem>
                          <SelectItem value="AWS">AWS</SelectItem>
                          <SelectItem value="Google Cloud Platform">
                            Google Cloud Platform
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('streetAddress')} 1 <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo ltd add"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('streetAddress2')}
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo ltd add 2"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('streetAddress')} 3
                    </Label>
                    <Input
                      type="text"
                      placeholder="Street Address 3"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="relative ">
                    <label
                      htmlFor="required-email"
                      className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                    >
                      {t('state')} <span className="text-[#f12e2e]"> *</span>
                    </label>
                    <Select defaultValue="--Select--">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder="Select a Brand Category" className="" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectItem value="--Select--">Azure</SelectItem>
                          <SelectItem value="AWS">AWS</SelectItem>
                          <SelectItem value="Google Cloud Platform">
                            Google Cloud Platform
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('city')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Gurgaon"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('Pincode')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="121212"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="col-span-2">
                    <div className="text-InterfaceTexttitle font-medium text-[12px] xl:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('contactInformation')}
                    </div>
                    <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                      The name and email address are used to create the initial administrator
                      account for Google Workspace and Google Cloud.
                    </div>
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('firstName')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                    >
                      {t('lastName')} <span className="text-[#f12e2e]"> *</span>
                    </Label>
                    <Input
                      type="text"
                      placeholder="Google UAT Demo ltd"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] flex items-center gap-3"
                    >
                      {t('email')}
                      <span className="text-[#f12e2e]">*</span>
                      <Popover open={openEmail} onOpenChange={setOpenEmail}>
                        <PopoverTrigger asChild>
                          <i className="cloud-messagebox text-text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i>
                        </PopoverTrigger>
                        <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                          <div className="flex justify-between items-center">
                            <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                              {t('info')}
                            </div>
                            <button
                              onClick={() => setOpenEmail(false)}
                              className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                              aria-label="Close"
                            ></button>
                          </div>
                          <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                            <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                              Super Administrator for their Google Workspace or Cloud Identity
                              Account
                            </p>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </Label>
                    <Input
                      type="text"
                      placeholder="@googletest.rlksp.in.17march2025-6.com"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                  <div className="">
                    <Label
                      htmlFor="firstname"
                      className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] flex items-center gap-3"
                    >
                      {t('alternativeEmail')}
                      <span className="text-[#f12e2e]">*</span>
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <i className="cloud-messagebox text-text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i>
                        </PopoverTrigger>
                        <PopoverContent className="w-[300px] lg:w-[325px] xl:w-[325px] 2xl:w-[335px] 3xl:w-[17.448vw] cardShadow bg-white relative p-[14px] lg:p-[14px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[0.833vw]">
                          <div className="flex justify-between items-center">
                            <div className="text-InterfaceTexttitle text-[12px] lg:text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                              {t('info')}
                            </div>
                            <button
                              onClick={() => setOpen(false)}
                              className="absolute top-3 right-2 cloud-closecircle text-closecolor text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] cursor-pointer"
                              aria-label="Close"
                            ></button>
                          </div>
                          <div className="mt-[14px] lg:mt-[14px] xl:mt-[16px] 2xl:mt-[16px] 3xl:mt-[0.833vw]">
                            <p className="text-InterfaceTextdefault text-[10px] lg:text-[10px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                              Entry any email that doesn’t support the customer’s primary domain.
                              <span className="text-BrandSupport1500"> Learn More </span>
                            </p>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </Label>
                    <Input
                      type="text"
                      placeholder="<EMAIL>"
                      className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </ScrollArea>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
              <SheetClose>
                <Button
                  size="sm"
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </Button>
              </SheetClose>
              <GoogleSuccessPopup />
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
