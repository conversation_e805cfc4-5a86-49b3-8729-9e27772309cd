'use client';
import { Button, Input, SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { CommanSheetProps } from '@/types';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SuccessfullPopupProps extends CommanSheetProps {
  backlink?: string;
}

export function NewTenantSuccessPopup({
  backlink = '/customer-management/customers/organization-tenants',
}: SuccessfullPopupProps) {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTrigger>
        <Button
          size="sm"
          className="flex gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
        >
          <div>
            <i className="cloud-save-2 text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
          </div>
          <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
            {t('submit')}
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-4 py-[100px] lg:py-[100px] xl:py-[100px] 2xl:py-[100px] 3xl:py-[5.208vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-info2 text-BrandPrimarypurenew text-[65px] lg:text-[65px] xl:text-[70px] 2xl:text-[70px] 3xl:text-[3.75vw] font-light"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[30px] lg:text-[34px] xl:text-[36px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                Your new tenant has been <br /> created successfully
              </div>
              <div className="text-InterfaceTextsubtitle text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                Please make note of the details and the Admin <br /> credentials to access this
                tenant.
              </div>
            </div>

            <div className="bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokedefault mt-[20px] xl:mt-[22px] 3xl:mt-[1.25vw]">
              <div className="border-b border-InterfaceStrokesoft py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                {t('tenantDetails')}
              </div>
              <div className="grid grid-cols-3 ">
                <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                  <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    {t('customerDomain')}
                  </div>
                </div>
                <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                  <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    redingtongulf.onmicrosoft.com
                  </div>
                </div>
                <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                  <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    {t('tenantID')}
                  </div>
                </div>
                <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                  <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    3bd4cf97-7a98-4a3e-8585-3da0aa7ab12b
                  </div>
                </div>
                <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                  <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    {t('username')}
                  </div>
                </div>
                <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                  <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    <EMAIL>
                  </div>
                </div>
                <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                  <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    {t('temporaryPassword')}
                  </div>
                </div>
                <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                  <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                    MeiZGZo7
                  </div>
                </div>
              </div>
            </div>

            <div className="w-[428px] 3xl:w-[22.292vw]bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokedefault py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('nickName')}
              </label>
              <div className="flex w-full gap-[10px] 3xl:gap-[0.521vw]">
                <Input
                  type="text"
                  placeholder={t('enterSubscriptionNickName')}
                  className="flex-grow placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                  aria-label="Microsoft domain"
                />
                <Link
                  href="/customer-management/customers"
                  className="signinbtn border border-InterfaceStrokesoft rounded-none"
                  // className={`border border-l-0 border-BrandNeutral500 py-2 px-4 rounded-none ${isActive
                  //   ? 'bg-green-500 text-white' // active state
                  //   : 'bg-BrandNeutral400 text-interfacetextinverse'
                  //   }`}
                >
                  <i className="cloud-save"></i>
                  {t('save')}
                </Link>
              </div>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] text-center">
              <SheetClose>
                <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-background applybtn font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] xl:w-[240px] 3xl:w-[12.5vw]">
                  <i className="cloud-user text-[18px]"></i>
                  {t('addSubscription')}
                </div>
              </SheetClose>
              <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
                <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">or</p>
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              </div>
              <Link href={backlink}>
                <SheetClose>
                  <div className="text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault signinbtn hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2">
                    <i className="cloud-arrowleft text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                    {t('backtoOrg&TenantList')}
                  </div>
                </SheetClose>
              </Link>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
