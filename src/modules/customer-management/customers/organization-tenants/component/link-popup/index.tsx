'use client';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON><PERSON>ger,
  She<PERSON>Footer,
} from '@redington-gulf-fze/cloudquarks-component-library';
import React from 'react';
import { TermsSheetProps } from '@/types';
import AsureItem from '../asure-item';
import AWSItem from '../aws-item';
import GoogleCloudPlatformItem from '../google-cloud-platform-item';
import { SuccessPopup } from '../success_popup';
import { useTranslations } from 'next-intl';
import { ExistingTenantSuccessPopup } from '../existing-tenant';

export default function LinkPopup({ open, onClose }: TermsSheetProps) {
  const t = useTranslations('');
  const [selectedDiv, setSelectedDiv] = React.useState<string>('');

  const handleChange = (value: string) => {
    setSelectedDiv(value);
  };

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button
          size="sm"
          className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure"
        >
          <i className="cloud-link font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
          {t('link')}
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose custrad">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('organizationTenantConfiguration')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw]  overflow-auto h-[400px] xl:h-[450px] 2xl:h-[600px] 3xl:h-[32.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="relative ">
              <label
                htmlFor="required-email"
                className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
              >
                {t('selectaBrandCategory')}
              </label>
              <div className="">
                <Select defaultValue="Azure" onValueChange={handleChange} value={selectedDiv}>
                  <SelectTrigger className="text-InterfaceTextsubtitle placeholder:text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                    <SelectValue placeholder={t('selectaBrandCategory')} className="" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none bg-[#FFF] border-none">
                    <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                      <SelectItem value="Azure">{t('azure')}</SelectItem>
                      <SelectItem value="AWS">{t('aws')}</SelectItem>
                      <SelectItem value="Google Cloud Platform">
                        {t('googleCloudPlatform')}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              {selectedDiv === 'Azure' && (
                <div className="">
                  <AsureItem />
                </div>
              )}
              {selectedDiv === 'AWS' && (
                <div className=" ">
                  <AWSItem />
                </div>
              )}
              {selectedDiv === 'Google Cloud Platform' && (
                <div className="">
                  <GoogleCloudPlatformItem />
                </div>
              )}
            </div>
          </div>
        </div>
        {selectedDiv === 'Azure' && (
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <SheetClose>
                  <Button
                    size="sm"
                    // onClick={onClose}
                    className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                      {t('cancel')}
                    </div>
                  </Button>
                </SheetClose>
                <ExistingTenantSuccessPopup open={false} onClose={() => {}} />
                {/* <ConfirmCustomerRegistrationPopup open={false} onClose={() => {}} /> */}
              </div>
            </div>
          </SheetFooter>
        )}
        {selectedDiv === 'AWS' && (
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <SheetClose
                  // size="sm"
                  // onClick={onClose}
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </SheetClose>
                <SuccessPopup />
              </div>
            </div>
          </SheetFooter>
        )}
        {selectedDiv === 'Google Cloud Platform' && (
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                <SheetClose>
                  <Button
                    size="sm"
                    onClick={onClose}
                    className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                      {t('cancel')}
                    </div>
                  </Button>
                </SheetClose>
                <Button
                  size="sm"
                  className="flex gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-save-2 text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    {t('create')}
                  </div>
                </Button>
              </div>
            </div>
          </SheetFooter>
        )}
      </SheetContent>
    </Sheet>
  );
}
