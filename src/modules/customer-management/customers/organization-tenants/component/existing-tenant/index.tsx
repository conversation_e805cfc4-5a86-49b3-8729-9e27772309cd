'use client';
import { Button, Input } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON><PERSON> } from 'next/font/google';
import Link from 'next/link';
import { CommanSheetProps } from '@/types';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SuccessfullPopupProps extends CommanSheetProps {
  backlink?: string;
}

export function ExistingTenantSuccessPopup({}: SuccessfullPopupProps) {
  return (
    <Sheet>
      <SheetTrigger>
        <Button
          size="sm"
          className="flex gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
        >
          <div>
            <i className="cloud-save-2 text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
          </div>
          <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
            Save
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-4 py-[100px] lg:py-[100px] xl:py-[100px] 2xl:py-[100px] 3xl:py-[5.208vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-info2 text-BrandPrimarypurenew text-[65px] lg:text-[65px] xl:text-[70px] 2xl:text-[70px] 3xl:text-[3.75vw] font-light"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[30px] lg:text-[34px] xl:text-[36px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                Your existing tenant has been <br /> linked successfully
              </div>
              <div className="text-InterfaceTextsubtitle text-[16px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                Please make note of the details and the Admin <br /> credentials to access this
                tenant.
              </div>
            </div>

            <div className="flex flex-col gap-[20px] 3xl:gap-[1.042vw]">
              <div className="bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokedefault">
                <div className="border-b border-InterfaceStrokesoft py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                  Tenant Details
                </div>
                <div className="grid grid-cols-3 ">
                  <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      Customer Domain
                    </div>
                  </div>
                  <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                    <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      redingtongulf.onmicrosoft.com
                    </div>
                  </div>
                  <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      Tenant ID
                    </div>
                  </div>
                  <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                    <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      3bd4cf97-7a98-4a3e-8585-3da0aa7ab12b
                    </div>
                  </div>
                  <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      Username
                    </div>
                  </div>
                  <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border-b border-InterfaceStrokesoft">
                    <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      <EMAIL>
                    </div>
                  </div>
                  <div className="col-span-1 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                    <div className="text-InterfaceTextdefault font-medium text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      Temporary Password
                    </div>
                  </div>
                  <div className="col-span-2 bg-interfacesurfacecomponent py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                    <div className=" text-InterfaceTextdefault font-normal text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                      MeiZGZo7
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-full bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokedefault py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
                <label
                  htmlFor="required-email"
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Nick Name
                </label>
                <div className="flex w-full gap-[10px] 3xl:gap-[0.521vw]">
                  <Input
                    type="text"
                    placeholder="Enter Subscription Nick Name"
                    className="flex-grow placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                    aria-label="Microsoft domain"
                  />
                  <Link href="/customer-management/customers/organization-tenants">
                    <Button
                      type="submit"
                      className="bg-[#FFF] hover:bg-InterfaceStrokesoft cursor-pointer border border-InterfaceStrokesoft rounded-none"
                    >
                      <i className="cloud-save"></i>
                      Save
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] text-center">
              <Link href="/marketplace">
                <div className="bg-[#00953A] hover:bg-[#067532] text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-background cursor-pointer border border-InterfaceStrokesoft rounded-none font-medium leading-[100%] w-[200px] xl:w-[240px] 3xl:w-[12.5vw] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2">
                  <i className="cloud-user text-[18px]"></i>
                  Add Subscription
                </div>
              </Link>
              <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
                <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">or</p>
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              </div>
              <Link href="/customer-management/customers/organization-tenants">
                <div className="bg-[#FFF] hover:bg-InterfaceStrokesoft text-[14px] lg:text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault cursor-pointer border border-InterfaceStrokesoft rounded-none font-medium leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2">
                  <i className="cloud-arrowleft text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
                  Back to Org & Tenant List
                </div>
              </Link>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
