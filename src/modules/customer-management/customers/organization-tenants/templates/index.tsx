'use client';
import * as React from 'react';
import { Search } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Input,
  Badge,
  Button,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { TablePagination } from '@/components/common/ui/pagination';
import LinkPopup from '../component/link-popup';
import NickNamePopup from '../component/nick-name-popup';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function OrganizationTenantsTemplate() {
  const t = useTranslations();

  const statusStyles: Record<string, string> = {
    Approved: 'text-BrandHighlight800 border border-BrandHighlight400 bg-BrandHighlight100',
    Pending: 'text-BrandSupport2800 border border-[#F2980E] bg-BrandSupport2100',
  };
  const typeStyles: Record<string, string> = {
    New: 'text-BrandSupport1pure border border-BrandSupport1300 bg-BrandSupport150',
    Existing: 'text-InterfaceTextdefault border border-BrandNeutral300 bg-BrandNeutral50',
  };
  const orgColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'brandcategory',
      enableSorting: true,
      minSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between "
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brandCategory')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <Link href="/customer-management/customers/organization-tenants/details">
          <div className="text-BrandSupport1pure cursor-pointer">
            {row.getValue('brandcategory')}
          </div>
        </Link>
      ),
    },
    {
      accessorKey: 'nickname',
      enableSorting: true,
      minSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('nickName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'domain',
      enableSorting: true,
      minSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('domain')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'customerid',
      enableSorting: true,
      minSize: 1000,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('customerIdorganizationId')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      enableSorting: true,
      minSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('type')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        const type = row.getValue('type') as string;
        const style = typeStyles[type] || 'bg-muted text-muted-foreground';
        return (
          <Badge
            className={`rounded-none py-1 px-2 text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-medium ${style}`}
          >
            {type}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      minSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              {t('status')}
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const style = statusStyles[status] || 'bg-muted text-muted-foreground';
        return (
          <Badge
            className={`rounded-none py-1 px-2 text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-medium ${style}`}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot hover:bg-[#e6e8e9] px-[12px] xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[4px] xl:py-[4px] 2xl:py-[6px] 3xl:py-[0.36vw] text-InterfaceTextsubtitle cursor-pointer flex "
                  title={t('info')}
                ></i>
              </PopoverTrigger>
              <PopoverContent className="p-0 mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-[180px] xl:w-[180px] 3xl:w-[9.375vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <div className="">
                  <NickNamePopup open={false} onClose={() => {}} />
                  <Link href="/customer-management/customers/organization-tenants/details">
                    <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                      <i className="cloud-info text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
                      {t('info')}
                    </div>
                  </Link>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      minSize: 100,
    },
  ];

  const data = [
    {
      brandcategory: 'Azure ',
      nickname: 'Nick Name 1',
      domain: 'turkeycustomer.onmicrosoft.com ',
      customerid: '830b9c37-c21c-4985-b494-873a39daf982',
      type: 'New',
      status: 'Pending',
    },
    {
      brandcategory: 'Google Cloud Platform  ',
      nickname: 'Nick Name 2',
      domain: 'goog-test.redingtonscm.com.Auto-20250516173857436.com ',
      customerid: 'C032quozv',
      type: 'Existing',
      status: 'Approved',
    },
    {
      brandcategory: 'AWS',
      nickname: 'Nick Name 3',
      domain: 'N/A ',
      customerid: '272102456789',
      type: 'New',
      status: 'Pending',
    },
    {
      brandcategory: 'AWS',
      nickname: 'Nick Name 4',
      domain: 'N/A ',
      customerid: '272102456789',
      type: 'New',
      status: 'Pending',
    },
    {
      brandcategory: 'Azure RI',
      nickname: 'Nick Name 5',
      domain: 'N/A ',
      customerid: '272102456789',
      type: 'Existing',
      status: 'Approved',
    },
    {
      brandcategory: 'Innovate Limited',
      nickname: 'Nick Name 6',
      domain: 'd1716a0f-835d-4210-94ea-c26b8c3f677b ',
      customerid: 'TestID0101',
      type: 'New',
      status: 'Approved',
    },
  ];

  type User = {
    brandcategory: string;
    nickname: string;
    domain: string;
    customerid: string;
    type: string;
  };

  const [linkinkpopup, setLinkpopup] = React.useState(false);

  return (
    <div className="p-[20px] pr-[20px] xl:pr-[30px] 2xl:pr-[32px] 3xl:pr-[1.823vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] w-full">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle mt-[16px] xl:mt-[18px] 3xl:mt-[1.042vw]">
          Innovate Limited
          <span className="font-normal px-[10px] 3xl:px-[0.521vw]">/</span>
          <span className="font-normal"> {t('organizationTenant')}</span>
        </div>
      </div>
      <div className="space-y-[20px] space-y-[22px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <Button
            onClick={() => setLinkpopup(true)}
            size="sm"
            className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure"
          >
            <i className="cloud-link font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('link')}
          </Button>
          <Link
            href="/customer-management/customers"
            className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure"
          >
            <i className="cloud-back font-medium text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-BrandSupport1pure"></i>{' '}
            {t('back')}
          </Link>
        </div>

        <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100">
          <div className="flex justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
            <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div className="text-InterfaceTexttitle text-[18px] font-semibold">
                {t('allBrands')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                {t('showingRecords')}
              </div>
            </div>
            <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
              <div className="relative w-full max-w-md bg-white border border-InterfaceStrokedefault">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4 " />
                <Input
                  type="text"
                  placeholder={t('search')}
                  className="pl-[35px] xl:pl-[35px] 2xl:pl-[35px] 3xl:pl-[1.5vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[14px] xl:text-[16px] 3xl:text-[0.833vw]"
                />
              </div>
              <div className=" flex items-center gap-2  h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
                <div className="relative flex items-center">
                  <i className="cloud-filtericon text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw]"></i>
                </div>
                <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-medium">
                  {t('filter')}
                </p>
              </div>
            </div>
          </div>
          <div className="overflow-x-auto">
            <DataTable data={data} columns={orgColumns} withCheckbox={false} />
            <TablePagination />
          </div>
        </div>
      </div>
      {linkinkpopup && <LinkPopup open={linkinkpopup} onClose={() => setLinkpopup(false)} />}
    </div>
  );
}
