'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import CustomerBarChart from '../charts/customer';
import { Select2 } from '@/components/common/ui/combo-box';
import { logger } from '@/lib/utils/logger';

export default function Info() {
  const handleCurrecySelect = (value: string) => {
    logger.log(value);
  };

  return (
    <div>
      <div className="grid grid-cols-3 gap-[16px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div className=" p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
          <div className="">
            <i className="cloud-folder text-[#7F8488] text-[26px]"></i>
          </div>
          <div className="absolute z-50 right-2 top-2 ">
            <Select2
              value={''}
              placeholder="Currency"
              onChange={(value) => handleCurrecySelect(value as string)}
              options={[
                { value: 'usd', label: 'USD' },
                { value: 'aed', label: 'AED' },
              ]}
              classNames={{
                trigger:
                  ' px-[6px] py-[4px] xl:py-[6px] text-xs border border-gray-300 justify-between',
                item: 'text-xs px-[6px] py-[4px] 2xl:py-[6px]',
                itemSelectedIcon: 'text-primary',
              }}
              styles={{
                trigger: {
                  backgroundColor: '#fff',
                  fontWeight: 400,
                },
              }}
            />
          </div>

          <div className="grid grid-cols-6">
            <div className="col-span-2 flex flex-col items-start justify-end">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium text-md leading-[140%]">
                Total Invoice (USD)*
              </div>
              <div className="text-[25px] xl:text-[25px] 2xl:text-[30px] 3xl:text-[1.475vw] font-semibold truncate text-InterfaceTextdefault">
                100,000.00
              </div>
            </div>

            <div className=" w-full h-[140px] xl:h-[140px] 2xl:h-[140px] 3xl:h-[5.885vw] col-span-4">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: 0,
                  top: 30,
                  itemGap: 10,
                  itemHeight: 8,
                  itemWidth: 8,
                  bottom: 0,
                  textStyle: {
                    fontSize: 8,
                  },
                  data: ['Amazon', 'Microsoft CSP', 'Google Cloud'],
                  selectedMode: false,
                }}
                name={'Nightingale Chart'}
                radius={[18, 42]}
                center={['36%', '32%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 5,
                  length2: 10,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 500000, name: 'Amazon', itemStyle: { color: '#73609B' } },
                  { value: 500000, name: 'Microsoft CSP', itemStyle: { color: '#4D9E99' } },
                  { value: 500000, name: 'Google Cloud', itemStyle: { color: '#FACE4F' } },
                ]}
              />
            </div>
          </div>
        </div>

        <div className="min-h-[20vh] gap-[5px] 3xl:gap-[0.625vw] p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)]">
          <div className="w-full h-[120px]">
            <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold">
              Invoices Value by Bill Type
            </div>
            <div className=" w-full h-[140px] xl:h-[145px] 2xl:h-[145px] 3xl:h-[6.085vw] ">
              <CustomerBarChart />
            </div>
          </div>
        </div>

        <div className=" p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
          <div className="">
            <i className="cloud-folder text-[#7F8488] text-[26px]"></i>
          </div>
          <div className="absolute z-50 right-2 top-2 ">
            <Select2
              value={''}
              placeholder="Currency"
              onChange={(value) => handleCurrecySelect(value as string)}
              options={[
                { value: 'usd', label: 'USD' },
                { value: 'aed', label: 'AED' },
              ]}
              classNames={{
                trigger:
                  ' px-[6px] py-[4px] xl:py-[6px] text-xs border border-gray-300 justify-between',
                item: 'text-xs px-[6px] py-[4px] 2xl:py-[6px]',
                itemSelectedIcon: 'text-primary',
              }}
              styles={{
                trigger: {
                  backgroundColor: '#fff',
                  fontWeight: 400,
                },
              }}
            />
          </div>

          <div className="grid grid-cols-6">
            <div className="col-span-2 flex flex-col items-start justify-end">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium text-md leading-[140%]">
                Invoices Value (USD)
              </div>
              <div className="text-[25px] xl:text-[25px] 2xl:text-[30px] 3xl:text-[1.475vw] font-semibold truncate text-InterfaceTextdefault">
                100,000.00
              </div>
            </div>

            <div className=" w-full h-[140px] xl:h-[140px] 2xl:h-[140px] 3xl:h-[5.885vw] col-span-4">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: 0,
                  top: 30,
                  itemGap: 10,
                  itemHeight: 8,
                  itemWidth: 8,
                  bottom: 0,
                  textStyle: {
                    fontSize: 10,
                  },
                  data: ['Due', 'Over Due'],
                  selectedMode: false,
                }}
                name={'Nightingale Chart'}
                radius={[18, 42]}
                center={['36%', '32%']}
                // rosetype={'area'}
                itemstyle={{
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 5,
                  length2: 10,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 800000, name: 'Due', itemStyle: { color: '#1570EF' } },
                  { value: 300000, name: 'Over Due', itemStyle: { color: '#FACE4F' } },
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
