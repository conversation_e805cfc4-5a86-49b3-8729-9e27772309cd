'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  Sheet,
  Sheet<PERSON>lose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import PDFViewer from '@/components/common/pdf-viewer';

export default function ViewFilePopup({ selectedId }: { selectedId: string }) {
  const fileUrl = '/dummy-pdf.pdf';
  return (
    <form>
      <Sheet>
        <SheetTitle></SheetTitle>
        <SheetTrigger>
          <div className="">
            <div className="py-[10px] text-sm 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-transparent bg-[linear-gradient(180deg,_#FEFEFE_0%,_#F6F8FA_100%)] border border-InterfaceStrokesoft flex gap-[8px] 3xl:gap-[0.417vw] rounded-none text-InterfaceTextdefault font-[500] items-center">
              <i className="cloud-readeye1 text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
              View PDF
            </div>
          </div>
        </SheetTrigger>
        <SheetContent className="hideclose flex flex-col gap-0 sm:max-w-[400px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
          <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
            <SheetTitle className="flex justify-between">
              <div className="flex items-center gap-4">
                <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                  View PDF Document
                </div>
              </div>

              <SheetClose>
                <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
              </SheetClose>
            </SheetTitle>
          </SheetHeader>

          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto ">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="">
                <div className="bg-BrandNeutral50 border border-InterfaceStrokesoft ">
                  <div className="bg-InterfaceTextwhite py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[12px] xl:px-[16px] 3xl:px-[0.833vw] flex justify-between">
                    <div className="text-[16px]  xl:text-[16px] 3xl:text-[0.779vw] font-[600]">
                      {selectedId}
                    </div>
                    <div>
                      <Link
                        href={fileUrl}
                        target="_donwload"
                        download={true}
                        className="flex gap-2 items-center text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  text-BrandSupport1pure"
                      >
                        <i className="cloud-document-download1 text-[16px]  xl:text-[16px] 3xl:text-[0.779vw]"></i>{' '}
                        Download as PDF
                      </Link>
                    </div>
                  </div>
                  <div className=" custompdf">
                    <PDFViewer fileUrl={fileUrl + '#toolbar=0&navpanes=0&scrollbar=0'} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </form>
  );
}
