import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React, { useState } from 'react';
import { Column, ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import CreditLimitFilter from '../filter';
import InvoiceDetailsPopup from '../details-popup';

export default function Table() {
  const [openView, setOpenView] = useState(false);
  const [selectedId, setSelectedId] = useState<string>('');

  const handleView = (id: string) => {
    setSelectedId(id);
    setOpenView(true);
  };

  type Invoice = {
    endCustomer: string;
    invoiceNumber: string;
    invoiceDate: string;
    brand: string;
    brandCategory: string;
    currency: string;
    amount: string;
    dueDate: string;
  };

  const createSortableHeader = (
    label: string,
    column: Column<Invoice, unknown>,
    placeholder: string
  ) => {
    const isSorted = column.getIsSorted();
    return (
      <div className="flex flex-col">
        <button
          type="button"
          className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
            {label}
          </div>
          {isSorted === 'asc' ? (
            <ArrowUp className="h-[12px] w-[12px]" />
          ) : isSorted === 'desc' ? (
            <ArrowDown className="h-[12px] w-[12px]" />
          ) : (
            <ArrowUpDown className="h-[12px] w-[12px]" />
          )}
        </button>
        <ColumnHeaderFilter
          placeholder={placeholder}
          onChange={(val) => column.setFilterValue(val)}
        />
      </div>
    );
  };

  const invoiceColumns: ColumnDef<Invoice>[] = [
    {
      accessorKey: 'endCustomer',
      header: ({ column }) => createSortableHeader('End Customer', column, 'End Customer'),
      cell: ({ row }) => <div>{row.getValue('endCustomer')}</div>,
    },
    {
      accessorKey: 'invoiceNumber',
      header: ({ column }) => createSortableHeader('Invoice Number', column, 'Invoice Number'),
      cell: ({ row }) => (
        <a href="#" className="text-blue-600 hover:underline">
          {row.getValue('invoiceNumber')}
        </a>
      ),
    },
    {
      accessorKey: 'invoiceDate',
      header: ({ column }) => createSortableHeader('Invoice Date', column, 'Invoice Date'),
      cell: ({ row }) => <div>{row.getValue('invoiceDate')}</div>,
    },
    {
      accessorKey: 'brand',
      header: ({ column }) => createSortableHeader('Brand', column, 'Brand'),
      cell: ({ row }) => <div>{row.getValue('brand')}</div>,
    },
    {
      accessorKey: 'brandCategory',
      header: ({ column }) => createSortableHeader('Brand Category', column, 'Brand Category'),
      cell: ({ row }) => <div>{row.getValue('brandCategory')}</div>,
    },
    {
      accessorKey: 'currency',
      header: ({ column }) => createSortableHeader('Currency', column, 'Currency'),
      cell: ({ row }) => <div>{row.getValue('currency')}</div>,
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => createSortableHeader('Amount', column, 'Amount'),
      cell: ({ row }) => <div>{row.getValue('amount')}</div>,
    },
    {
      accessorKey: 'dueDate',
      header: ({ column }) => createSortableHeader('Due Date', column, 'Due Date'),
      cell: ({ row }) => <div>{row.getValue('dueDate')}</div>,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          Action
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2 justify-center">
          <Popover>
            <PopoverTrigger asChild>
              <i
                className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex"
                title="Details"
              ></i>
            </PopoverTrigger>
            <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[200px] z-20 bg-interfacesurfacecomponent cursor-pointer">
              <div onClick={() => handleView(row.getValue('invoiceNumber'))} className="">
                <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
                  <i className="cloud-folder text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw]"></i>
                  Invoice Details
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      ),
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0 z-10',
        cellClassName: 'sticky right-0 z-10',
      },
    },
  ];

  const invoiceData = [
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000151845',
      invoiceDate: '2/2/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '24,500.00',
      dueDate: '5/5/2025',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158453',
      invoiceDate: '12/2/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '-49,850.00',
      dueDate: '5/5/2025',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158452',
      invoiceDate: '15/2/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '5,000.00',
      dueDate: '5/5/2025',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158412',
      invoiceDate: '16/2/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '10,000.00',
      dueDate: '5/5/2025',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158413',
      invoiceDate: '12/3/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '25,500.00',
      dueDate: '5/5/2025',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158414',
      invoiceDate: '13/3/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '26,500.00',
      dueDate: '5/5/2025',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158415',
      invoiceDate: '14/3/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '32,500.00',
      dueDate: '5/5/2025',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158416',
      invoiceDate: '15/3/2025',
      brand: 'Google',
      brandCategory: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '45,500.00',
      dueDate: '5/5/2025',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">Invoice Details</div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
              Showing 10/100 Records
            </div>
          </div>
          <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search"
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-InterfaceTextsubtitle absolute top-[12px] lg:top-[11px] xl:top-[11px] 2xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            {/* <div className="flex items-center gap-2 cursor-pointer">
              <i className="cloud-filtericon"></i>Filter
            </div> */}

            <CreditLimitFilter />
          </div>
        </div>
        <div className="flex items-center justify-start px-[14px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] gap-2 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <div className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <span className="text-InterfaceTextsubtitle">Start Date :</span> 01-01-2025
            <i className="cloud-closecircle text-ClearAll"></i>
          </div>
          <div className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <span className="text-InterfaceTextsubtitle">End Date :</span> 01-06-2025
            <i className="cloud-closecircle text-ClearAll"></i>
          </div>
          <div className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <span className="text-InterfaceTextsubtitle">End Customer :</span> All
            <i className="cloud-closecircle text-ClearAll"></i>
          </div>
          <div className="text-ClearAll  py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <i className="cloud-closecircle text-ClearAll"></i> Clear All
          </div>
        </div>
        <div className="overflow-x-auto">
          <DataTable data={invoiceData} columns={invoiceColumns} withCheckbox={false} />
        </div>
        <TablePagination />
      </div>
      <InvoiceDetailsPopup
        selectedId={selectedId}
        open={openView}
        onClose={() => setOpenView(false)}
      />
    </>
  );
}
