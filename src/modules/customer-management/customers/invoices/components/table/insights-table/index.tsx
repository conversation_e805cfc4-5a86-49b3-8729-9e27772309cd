import { DataTable } from '@/components/common/ui/data-table';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import { TablePagination } from '@/components/common/ui/pagination';

export default function InsightsTable() {
  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'category',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-2"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className="text-[14px] text-InterfaceTexttitle">Category</div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-3 w-3" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-3 w-3" />
              ) : (
                <ArrowUpDown className="h-3 w-3" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Category"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div>{row.getValue('category')}</div>,
      minSize: 150,
    },
    {
      accessorKey: 'subCategory',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-2"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className="text-[14px] text-InterfaceTexttitle">Sub Category</div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-3 w-3" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-3 w-3" />
              ) : (
                <ArrowUpDown className="h-3 w-3" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Sub Category"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div>{row.getValue('subCategory')}</div>,
      minSize: 150,
    },
    {
      accessorKey: 'resourceName',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-2"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className="text-[14px] text-InterfaceTexttitle">Resource Name</div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-3 w-3" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-3 w-3" />
              ) : (
                <ArrowUpDown className="h-3 w-3" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Resource Name"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div>{row.getValue('resourceName')}</div>,
      minSize: 250,
    },
    {
      accessorKey: 'sumOfQuantityUsed',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-2"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className="text-[14px] text-InterfaceTexttitle">Sum of Quantity Used</div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-3 w-3" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-3 w-3" />
              ) : (
                <ArrowUpDown className="h-3 w-3" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Sum of Quantity Used"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => {
        const quantity: string = row.getValue('sumOfQuantityUsed');
        const unit = row.original.unit;
        return (
          <div>
            {quantity} {unit}
          </div>
        );
      },
      minSize: 150,
    },
    {
      accessorKey: 'unit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col ">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-2 "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className="text-[14px] text-InterfaceTexttitle ">Unit</div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-3 w-3" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-3 w-3" />
              ) : (
                <ArrowUpDown className="h-3 w-3" />
              )}
            </button>
            <ColumnHeaderFilter placeholder="Unit" onChange={(val) => column.setFilterValue(val)} />
          </div>
        );
      },
      cell: ({ row }) => <div>{row.getValue('unit')}</div>,
      minSize: 150,
    },
    {
      accessorKey: 'sumOfTotalCost',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-2"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className="text-[14px] text-InterfaceTexttitle">Sum of Total Cost</div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-3 w-3" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-3 w-3" />
              ) : (
                <ArrowUpDown className="h-3 w-3" />
              )}
            </button>
            <ColumnHeaderFilter
              placeholder="Sum of Total Cost"
              onChange={(val) => column.setFilterValue(val)}
            />
          </div>
        );
      },
      cell: ({ row }) => <div>${row.getValue('sumOfTotalCost')}</div>,
      minSize: 150,
    },
  ];

  type User = {
    category: string;
    subCategory: string;
    resourceName: string;
    sumOfQuantityUsed: string;
    unit: string;
    sumOfTotalCost: string;
  };

  const registrationdata: User[] = [
    {
      category: 'Compute',
      subCategory: 'Virtual Machines',
      resourceName: 'VM Standard D2s v3',
      sumOfQuantityUsed: '120',
      unit: '1GB',
      sumOfTotalCost: 'INR 1686.68',
    },
    {
      category: 'Storage',
      subCategory: 'Blob Storage',
      resourceName: 'Hot Tier Blob Storage',
      sumOfQuantityUsed: '350',
      unit: '1GB',
      sumOfTotalCost: 'INR 15959.31',
    },
    {
      category: 'Compute',
      subCategory: 'Virtual Machines',
      resourceName: 'VM Standard D2s v3',
      sumOfQuantityUsed: '120',
      unit: '1/Month',
      sumOfTotalCost: 'INR 11806.72',
    },
    {
      category: 'Storage',
      subCategory: 'Blob Storage',
      resourceName: 'Hot Tier Blob Storage',
      sumOfQuantityUsed: '350',
      unit: '1GB',
      sumOfTotalCost: 'INR 11806.72',
    },
    {
      category: 'Compute',
      subCategory: 'Virtual Machines',
      resourceName: 'VM Standard D2s v3',
      sumOfQuantityUsed: '120',
      unit: '1/Month',
      sumOfTotalCost: '450.75',
    },
    {
      category: 'Storage',
      subCategory: 'Blob Storage',
      resourceName: 'Hot Tier Blob Storage',
      sumOfQuantityUsed: '350',
      unit: '1/Month',
      sumOfTotalCost: 'INR 1883.01',
    },
    {
      category: 'Networking',
      subCategory: 'Bandwidth',
      resourceName: 'Outbound Data Transfer',
      sumOfQuantityUsed: '200',
      unit: '1/Month',
      sumOfTotalCost: 'INR 1883.01',
    },
    {
      category: 'Database',
      subCategory: 'SQL Database',
      resourceName: 'Standard S3',
      sumOfQuantityUsed: '30',
      unit: '1GB',
      sumOfTotalCost: 'INR 14626.51',
    },
    {
      category: 'Compute',
      subCategory: 'Virtual Machines Licenses',
      resourceName: 'Windows Server License',
      sumOfQuantityUsed: '120',
      unit: '1/Month',
      sumOfTotalCost: 'INR 480.00',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="overflow-x-auto ">
          <div className=" border-y-2 border-BrandNeutral200 bg-InterfaceStrokesoft flex justify-between items-center px-[90px] lg:px-[100px] xl:px-[150px] 3xl:px-[5.908vw] py-2">
            <div className="text-[13px] xl:text-[13px] 3xl:text-[0.719vw] font-semibold text-InterfaceTexttitle px-[200px]">
              Sum of Total Cost Category
            </div>
            <div className="text-[13px] xl:text-[13px] 3xl:text-[0.719vw] font-semibold text-InterfaceTexttitle">
              {' '}
              Total
            </div>
          </div>
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={false} />
        </div>
        <div className=" flex justify-end items-center px-[45px] xl:px-[45px] 3xl:px-[1.563vw] py-[4px] ">
          <div className="text-[14px] text-InterfaceTexttitle font-bold pr-[30px] xl:pr-[30px] 3xl:pr-[1.563vw]">
            Grand Total
          </div>
          <div>
            <span className="text-[14px] text-InterfaceTexttitle font-bold">INR 556,286.68</span>
          </div>
        </div>
        <TablePagination />
      </div>
    </>
  );
}
