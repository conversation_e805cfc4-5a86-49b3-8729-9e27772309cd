'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { EChartsOption, AxisPointerComponentOption, TooltipComponentOption } from 'echarts';

export default function CustomerBarChart() {
  const option: EChartsOption = {
    grid: {
      top: 15,
      right: 10,
      bottom: 8,
      left: 20,
      containLabel: true,
    },

    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      } as AxisPointerComponentOption,
      formatter: () => 'Customer Name', // Static text for all bars
      backgroundColor: '#fff',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#111827',
        fontSize: 12,
      },
    } as TooltipComponentOption,

    xAxis: {
      type: 'category',
      data: ['Annually', 'Monthly', 'One Time', 'Qtr.', 'Half Yearly'],
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisLabel: {
        color: '#7F8488',
        fontSize: 10,
        interval: 0,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      name: 'Invoice value (USD)',
      nameLocation: 'middle',
      nameTextStyle: {
        fontSize: 10,
        color: '#7F8488',
      },
      nameGap: 32,
      min: 0,
      max: 80,
      interval: 20,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E5E7EB',
          type: 'dashed',
        },
      },
      axisLabel: {
        color: '#7F8488',
        fontSize: 10,
        formatter: (value: number) => `${value}k`,
      },
    },
    series: [
      {
        data: [{ value: 75 }, { value: 60 }, { value: 45 }, { value: 60 }, { value: 25 }],
        type: 'bar',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#007BFF' },
            { offset: 1, color: '#3399FF' },
          ]),
        },
        barWidth: '50%',
      },
    ],
  };

  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
