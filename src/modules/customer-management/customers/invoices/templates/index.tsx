'use client';
import React from 'react';
import Link from 'next/link';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Info from '../components/info';
import Table from '../components/table';

export default function InvoicesTemplate() {
  return (
    <>
      <div className="py-[20px] xl:py-[16px] 3xl:py-[1.042vw] px-[32px] xl:px-[24px] 3xl:px-[1.667vw] w-full">
        <div className="">
          <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle">
            Innovate Limited <span className="text-InterfaceTextsubtitle font-[400]"> / </span>
            <span className="font-normal">Invoice</span>
          </div>
        </div>
        <div className="space-y-[16px] xl:space-y-[16px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
          <div className="">
            <div>
              <Info />
            </div>
          </div>
          <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
            <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault">
              <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
              Download
            </Button>
            <Link href={'/customer-management/customers'}>
              <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault">
                <i className="cloud-back font-medium text-[16px] xl:text-[14px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
                Back
              </Button>
            </Link>
          </div>

          <div className="bg-interfacesurfacecomponent tbl-shadow">
            <Table />
          </div>
        </div>
      </div>
    </>
  );
}
