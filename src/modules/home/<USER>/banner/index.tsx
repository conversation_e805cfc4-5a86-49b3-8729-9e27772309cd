import React from 'react';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
} from '@redington-gulf-fze/cloudquarks-component-library';
import VideoPopup from '../video-popup';
import { useTranslations } from 'next-intl';
import { localStorageService } from '@/lib/services/storage';
import { useEffect, useState } from 'react';

export default function DashboardBanner() {
  const t = useTranslations();
  const [selectedLang, setSelectedLang] = useState<'en' | 'ar' | 'tr'>('en');
  useEffect(() => {
    (async () => {
      const storedLocale = await localStorageService.get<string>('locale');
      if (storedLocale === 'ar') {
        setSelectedLang('ar');
      } else if (storedLocale === 'tr') {
        setSelectedLang('tr');
      } else {
        setSelectedLang('en');
      }
    })();
  }, []);
  const data = [
    {
      icon: 'cloud-world',
      step: t('stepOne'),
      companyName: t('companyInformation'),
      description: t('accountActiveMessage1') + ' ' + t('accountActiveMessage2'),
    },
    {
      icon: 'cloud-brifcase',
      step: t('stepTwo'),
      companyName: t('businessInformation'),
      description: t('shareholdingDetails'),
    },
    {
      icon: 'cloud-folder',
      step: t('stepThree'),
      companyName: t('shareDocuments'),
      description: t('shareDocumentsDescription'),
    },
    {
      icon: 'cloud-clipboard',
      step: t('stepFour'),
      companyName: t('checkCompliance'),
      description: t('checkComplianceDescription'),
    },
    {
      icon: 'cloud-crown',
      step: t('stepFive'),
      companyName: t('brandCategory'),
      description: t('brandCategoryDescription'),
    },
  ];

  return (
    <div className="bg-background pb-[28px] 2xl:pb-[30px] 3xl:pb-[1.667vw]">
      <div className="dashboard_bg py-[40px] xl:py-[85px] 3xl:py-[4.427vw] px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] ">
        <div className="grid grid-cols-12 items-center gap-5">
          <div className="col-span-12 xl:col-span-7 flex flex-col items-start">
            <div
              className="text-InterfaceTexttitle text-[34px] md:text-[34px] lg:text-[34px] xl:text-[34px] 2xl:text-[34px] 3xl:text-[2.083vw] leading-[140%] font-normal"
              dir={selectedLang === 'ar' ? 'rtl' : 'ltr'}
            >
              {selectedLang === 'en' ? (
                <>
                  {t('welcomeTitle1')}{' '}
                  <span className="text-BrandSupport1pure font-semibold">
                    {'"'}
                    {t('welcomeTitle2')}
                    {'"'}
                  </span>
                </>
              ) : selectedLang === 'ar' ? (
                <>
                  <span className="text-BrandSupport1pure font-semibold">
                    {'"'}
                    {t('welcomeTitle2')}
                    {'"'}
                  </span>{' '}
                  {t('welcomeTitle1')}
                </>
              ) : (
                <>
                  <span className="text-BrandSupport1pure font-semibold">
                    {'"'}
                    {t('welcomeTitle2')}
                    {'"'}
                  </span>{' '}
                  {t('welcomeTitle1')}
                </>
              )}
              {/* Welcome to{' '}
              <span className="text-BrandSupport1pure font-semibold">
                {'"'}Redington CloudQuarks{'"'}
              </span> */}
            </div>

            <div className="text-center uppercase bg-BrandPrimary100 text-BrandPrimary7001 text-[22px] md:text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[1.25vw] leading-[140%] font-normal py-[6px] 3xl:py-[0.313vw] px-[40px] ">
              {t('platformMessage')}
            </div>
          </div>
          <div className="col-span-12 xl:col-span-5">
            <div className="flex flex-wrap items-center gap-[16px] 3xl:gap-[0.833vw]">
              <div>
                <div className="flex -space-x-3">
                  <Avatar className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]">
                    <AvatarImage src="/images/user1.png" alt="user1" />
                    <AvatarFallback>CN</AvatarFallback>
                  </Avatar>
                  <Avatar className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]">
                    <AvatarImage src="/images/user2.png" alt="user2" />
                    <AvatarFallback>VC</AvatarFallback>
                  </Avatar>
                  <Avatar className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]">
                    <AvatarImage src="/images/user3.png" alt="user3" />
                    <AvatarFallback>NJ</AvatarFallback>
                  </Avatar>
                  <Avatar className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]">
                    <AvatarImage src="/images/user4.png" alt="user4" />
                    <AvatarFallback>NJ</AvatarFallback>
                  </Avatar>
                  <Avatar className="w-[32px] h-[32px] 3xl:w-[1.667vw] 3xl:h-[1.667vw]">
                    <AvatarImage src="/images/user5.png" alt="user5" />
                    <AvatarFallback>NJ</AvatarFallback>
                  </Avatar>
                </div>
              </div>
              <div>
                <div className="text-InterfaceTexttitle text-[18px] md:text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] leading-[140%] font-normal">
                  {t('callToAction')} {t('callToActionLine2')}
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-[10px] md:gap-[22px] 2xl:gap-[22px] 3xl:gap-[1.24vw] mt-[22px] 2xl:mt-[22px] 3xl:mt-[1.24vw]">
              <div className="">
                <VideoPopup />
              </div>
              <div className="">
                <Button
                  variant="skybluegradient"
                  className="greenBtnnew text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] 2xl:py-[13px] 3xl:py-[0.681vw]"
                >
                  <i className="cloud-rightarrow1"></i> {t('getOnboardedNow')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] ">
        <div className="text-InterfaceTextsubtitle text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal leading-[140%] pb-[16px] 3xl:pb-[0.833vw]">
          {t('nextStep')}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-[16px] 3xl:gap-[0.833vw]">
          {data?.map((items, index) => {
            return (
              <div
                key={index}
                className="relative bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokedefault p-[16px] xl:p-[16px] 2xl:p-[16px] 3xl:p-[1.042vw]"
              >
                {index === data.length - 1 ? null : (
                  <div className="absolute z-10 max-md:rotate-90 max-md:left-[40%] max-md:-bottom-[28px] max-md: md:-right-[28px] md:top-[85px] 3xl:top-[3.646vw] w-[40px] 3xl:w-[2.083vw] h-[40px] 3xl:h-[2.083vw] bg-BrandPrimarypure border-[4px] border-borderwhite rounded-full flex justify-center items-center">
                    <i className="cloud-rightarrow1 text-InterfaceTextwhite text-[12px] md:text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw]"></i>
                  </div>
                )}
                <i
                  className={`${items.icon} text-BrandSupport1500 text-[24px] 3xl:text-[1.24vw]`}
                ></i>
                <div className="text-InterfaceTextsubtitle text-[12px] md:text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-medium leading-[140%] pb-[4px] 3xl:pb-[0.200vw] uppercase">
                  {items.step}
                </div>
                <div className="text-InterfaceTexttitle text-[16px] md:text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[140%] pb-[16px] 3xl:pb-[0.833vw]">
                  {items.companyName}
                </div>
                <div className="text-InterfaceTextsubtitle text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw] font-normal leading-[140%] pb-[4px] 3xl:pb-[0.200vw]">
                  {items.description}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
