import React, { useState } from 'react';
import {
  Button,
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>Down, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import Link from 'next/link';
import { ImformationWidgetOverlay } from './imformation-widget';
import { QuickOrderPopup } from '../quick-order';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import ProductDetailedView from '@/modules/marketplace/components/product-details-view';

export default function RecentPurchase() {
  const t = useTranslations();
  const [tab, setTab] = useState(0);
  const [quickOrderPopupShow, setQuickOrderPopupShow] = useState(false);
  const [productdetailedpopup, setProductdetailedpopup] = useState(false);

  const PurchasesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('productName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-BrandSupport1pure cursor-pointer">
          <HoverCard>
            <HoverCardTrigger asChild>
              <div onClick={() => setProductdetailedpopup(true)}>{row.getValue('productname')}</div>
            </HoverCardTrigger>
            <HoverCardContent className="w-[320px] 3xl:w-[16.667vw] bg-background ml-[150px]">
              <div
                onClick={() => setQuickOrderPopupShow(true)}
                className="flex items-center gap-[16px] 3xl:gap-[0.833vw] w-full"
              >
                <div>
                  <div className="bg-BrandPrimary100 w-[64px] 3xl:w-[3.333vw] h-[64px] 3xl:h-[3.333vw] flex items-center justify-center">
                    <Image
                      width={30}
                      height={54}
                      className="w-[30px] 3xl:w-[1.563vw] h-[54px] 3xl:h-[2.813vw]"
                      src="/images/svg/redinton_logo.svg"
                      alt="redinton logo"
                    />
                  </div>
                </div>
                <div>
                  <div className="text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] text-InterfaceTexttitle text-wrap text-left">
                    Reserved VM Instance, Cool_GRS_Data_Stored_10
                  </div>
                  <div className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[600] text-InterfaceTexttitle text-wrap text-left">
                    SKU ID: <span className="font-[400]">DZH318Z08T98:00SB</span>
                  </div>
                </div>
              </div>
            </HoverCardContent>
          </HoverCard>
        </div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'quality',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('quality')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('quality')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'currency',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('currency')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('currency')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'salepriceunit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('salePricePerUnit')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="text-end">{row.getValue('salepriceunit')}</div>,

      minSize: 200,
    },
    {
      accessorKey: 'dateofpurchase',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('dateOfPurchase')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('dateofpurchase')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endCustomer')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>{row.getValue('endcustomer')}</div>
              </TooltipTrigger>
              <TooltipContent className="min-w-[150px] 3xl:min-w-[7.813vw] bg-background">
                <p className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  End Customer Name
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),

      minSize: 500,
    },
  ];

  type User = {
    productname: string;
    quality: string;
    currency: string;
    salepriceunit: string;
    dateofpurchase: string;
    endcustomer: string;
  };

  const Purchasesdata = [
    {
      productname: 'Product 1',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      dateofpurchase: '10/10/2024',
      endcustomer: 'End Customer 1',
    },
    {
      productname: 'Product 2',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      dateofpurchase: '10/10/2024',
      endcustomer: 'End Customer 1',
    },
    {
      productname: 'Product 3',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      dateofpurchase: '10/10/2024',
      endcustomer: 'End Customer 1',
    },
    {
      productname: 'Product 4',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      dateofpurchase: '10/10/2024',
      endcustomer: 'End Customer 1',
    },
    {
      productname: 'Product 5',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      dateofpurchase: '10/10/2024',
      endcustomer: 'End Customer 1',
    },
    {
      productname: 'Product 6',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      dateofpurchase: '10/10/2024',
      endcustomer: 'End Customer 1',
    },
  ];

  return (
    <div className="bg-background">
      <div className="flex justify-between gap-1 px-[16px] 3xl:px-[0.833vw] py-[12px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokedefault">
        <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] font-[700] leading-[140%]">
          {t('recentPurchases')}
        </div>
        <div className="flex items-center gap-3">
          <ImformationWidgetOverlay />
        </div>
      </div>
      <div className="flex items-center ml-[16px] 3xl:ml-[0.833vw]">
        <Button
          onClick={() => setTab(0)}
          className={`${tab === 0 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none rounded-bl-[6px] 3xl:rounded-bl-[0.333vw]`}
        >
          {t('last7Days')}
        </Button>
        <Button
          onClick={() => setTab(1)}
          className={`${tab === 1 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none`}
        >
          {t('last15Days')}
        </Button>
        <Button
          onClick={() => setTab(2)}
          className={`${tab === 2 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none`}
        >
          {t('last30Days')}
        </Button>
        <Button
          onClick={() => setTab(3)}
          className={`${tab === 3 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none rounded-br-[6px] 3xl:rounded-br-[0.333vw]`}
        >
          {t('last60Days')}
        </Button>
      </div>
      <div className="p-[16px] 3xl:p-[0.833vw]">
        <div className="overflow-x-auto">
          <DataTable data={Purchasesdata} columns={PurchasesColumns} withCheckbox={false} />
        </div>
        <div className="flex items-center justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] w-full border-b border-r border-l  border-t-none rounded-bl-[4px] rounded-br-[4px]">
          <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
            1-10{' '}
            <span className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
              Of{' '}
            </span>
            <span className=" font-[600] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
              100
            </span>
          </div>
          <Link
            href="/sales/orders"
            className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-BrandSupport1pure"
          >
            {t('showMore')}
          </Link>
        </div>
      </div>
      {quickOrderPopupShow && (
        <QuickOrderPopup open={quickOrderPopupShow} onClose={() => setQuickOrderPopupShow(false)} />
      )}
      <ProductDetailedView
        open={productdetailedpopup}
        onClose={() => setProductdetailedpopup(false)}
      />
    </div>
  );
}
