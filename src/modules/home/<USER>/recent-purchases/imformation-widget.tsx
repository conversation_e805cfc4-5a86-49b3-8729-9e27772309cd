'use client';
import {
  <PERSON><PERSON>,
  Checkbox,
  <PERSON>over,
  <PERSON>overContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';

export function ImformationWidgetOverlay() {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative" onClick={() => setOpen(true)}>
            {/* <i className="cloud-threedot text-BrandNeutralpure text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i> */}
            <i
              className={`rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[8px] xl:px-[8px] 2xl:px-[10px] 3xl:px-[0.6vw] cloud-threedot text-[14px] 3xl:text-[0.729vw] cursor-pointer 
    ${open ? 'bg-BrandNeutral100 text-BrandNeutralpure' : 'text-BrandNeutralpure hover:bg-BrandNeutral100'}`}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 mt-[-10px] w-[360px] xl:w-[360px] 2xl:w-[360px] 3xl:w-[18.75vw] rounded-none bg-background">
          <div>
            <div className="border-b border-InterfaceStrokedefault px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw]">
              <h4 className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%]">
                Information Widget
              </h4>
              <h4 className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[150%]">
                Select any 1
              </h4>
            </div>

            <div className="px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[20px] 3xl:py-[1.042vw]">
              <div className="space-y-[12px] 3xl:space-y-[0.625vw]">
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="recentpurchases" className="col-span-1" />
                  <label
                    htmlFor="recentpurchases"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Recent Purchases
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="mytop5products" className="col-span-1" />
                  <label
                    htmlFor="mytop5products"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    My Top 5 Products
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="productswithdiscountcoupon" className="col-span-1" />
                  <label
                    htmlFor="productswithdiscountcoupon"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Products with Discount Coupon
                  </label>
                </div>
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="creditmanagement" className="col-span-1" />
                  <label
                    htmlFor="creditmanagement"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Credit Management
                  </label>
                </div>
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="quotemanagement" className="col-span-1" />
                  <label
                    htmlFor="quotemanagement"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Quote Management
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="invoicemanagement" className="col-span-1" />
                  <label
                    htmlFor="invoicemanagement"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Invoice Management
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="mytop5customer" className="col-span-1" />
                  <label
                    htmlFor="mytop5customer"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    My Top 5 Customer
                  </label>
                </div>
              </div>
            </div>

            <div className="px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] flex justify-end gap-[12px] 3xl:gap-[0.625vw] border-t border-InterfaceStrokesoft">
              <Button
                type="submit"
                onClick={() => setOpen(false)}
                className="cancelbtn text-InterfaceTextdefault rounded-none px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-closecircle"></i>
                Cancel
              </Button>

              <Button
                type="submit"
                onClick={() => setOpen(false)}
                className="border border-[#3798FA] rounded-none text-interfacesurfacecomponent bg-BrandNeutralpure px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-copy-success"></i>
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
