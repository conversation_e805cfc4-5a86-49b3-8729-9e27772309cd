import Link from 'next/link';
import React from 'react';
import { QuickLinksOverlay } from './quick-links';
import { useTranslations } from 'next-intl';

export default function QuickLinks() {
  const t = useTranslations();
  return (
    <div className="bg-background px-[16px] 3xl:px-[0.833vw] py-[10px] 3xl:py-[0.521vw] mt-[18px] 3xl:mt-[0.938vw] cardShadow">
      <div className="flex justify-between gap-1 mb-[16px] 3xl:mb-[0.833vw]">
        <div className="text-InterfaceTextdefault text-[18px] 3xl:text-[0.938vw] font-[700] leading-[140%]">
          {t('quickLinks')}
        </div>
        <QuickLinksOverlay />
      </div>

      <div className="grid grid-cols-3 gap-2">
        <div className="space-y-2 3xl:space-y-[0.417vw]">
          <Link
            href=""
            className="block text-BrandSupport1pure text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]"
          >
            My Orders
          </Link>
          <Link
            href=""
            className="block text-BrandSupport1pure text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]"
          >
            Invoice
          </Link>
        </div>
        <div className="space-y-2 3xl:space-y-[0.417vw]">
          <Link
            href=""
            className="block text-BrandSupport1pure text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]"
          >
            Contracts
          </Link>
          <Link
            href=""
            className="block text-BrandSupport1pure text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]"
          >
            Reports
          </Link>
        </div>
        <div className="space-y-2 3xl:space-y-[0.417vw]">
          <Link
            href=""
            className="block text-BrandSupport1pure text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]"
          >
            User Management
          </Link>
          <Link
            href=""
            className="block text-BrandSupport1pure text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]"
          >
            Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}
