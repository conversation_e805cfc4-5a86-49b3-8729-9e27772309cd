'use client';
import {
  <PERSON><PERSON>,
  Checkbox,
  Popover,
  <PERSON>overContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';
import { useTranslations } from 'next-intl';

export function QuickLinksOverlay() {
  const [open, setOpen] = useState(false);
  const t = useTranslations();
  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative" onClick={() => setOpen(true)}>
            {/* <i className="cloud-threedot text-BrandNeutralpure text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i> */}
            <i
              className={`rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[8px] xl:px-[8px] 2xl:px-[10px] 3xl:px-[0.6vw] cloud-threedot text-[14px] 3xl:text-[0.729vw] cursor-pointer 
    ${open ? 'bg-InterfaceSurfacehcinverse text-[#FFF]' : 'text-BrandNeutralpure hover:bg-InterfaceSurfacehcinverse hover:text-[#FFF]'}`}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 mt-[-10px] w-[360px] xl:w-[360px] 2xl:w-[360px] 3xl:w-[18.75vw] rounded-none bg-background">
          <div>
            <div className="border-b border-InterfaceStrokedefault px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw]">
              <h4 className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%]">
                {t('quickLinks')}
              </h4>
              <h4 className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[150%]">
                {t('selectAny5')}
              </h4>
            </div>

            <div className="p-[14px] xl:p-[14px] 2xl:p-[16px] 3xl:p-[0.833vw]">
              <div className="">
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="myorders" className="col-span-1" />
                  <label
                    htmlFor="myorders"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    My Orders
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="invoice" className="col-span-1" />
                  <label
                    htmlFor="invoice"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Invoice
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="contracts" className="col-span-1" />
                  <label
                    htmlFor="contracts"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Contracts
                  </label>
                </div>
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="reports" className="col-span-1" />
                  <label
                    htmlFor="reports"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Reports
                  </label>
                </div>
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="usermanagement" className="col-span-1" />
                  <label
                    htmlFor="usermanagement"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    User Management
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="dashboard" className="col-span-1" />
                  <label
                    htmlFor="dashboard"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Dashboard
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="quotation" className="col-span-1" />
                  <label
                    htmlFor="quotation"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Quotation
                  </label>
                </div>
              </div>
            </div>

            <div className="px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] flex justify-end gap-[12px] 3xl:gap-[0.625vw] border-t border-InterfaceStrokesoft">
              <Button
                type="submit"
                onClick={() => setOpen(false)}
                className="cancelbtn text-InterfaceTextdefault rounded-none px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-closecircle"></i>
                {t('cancel')}
              </Button>

              <Button
                type="submit"
                onClick={() => setOpen(false)}
                className="border border-[#3798FA] rounded-none text-interfacesurfacecomponent bg-BrandNeutralpure px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-copy-success"></i>
                {t('apply')}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
