'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>over,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';

export function MetricScoreCardsOverlay() {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative " onClick={() => setOpen(true)}>
            {/* <i className="hover:bg-InterfaceSurfacehcinverse hover:text-[#FFF] rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[8px] xl:px-[8px] 2xl:px-[10px] 3xl:px-[0.6vw] cloud-threedot text-BrandNeutralpure text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i> */}
            <i
              className={`rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[8px] xl:px-[8px] 2xl:px-[10px] 3xl:px-[0.6vw] cloud-threedot text-[14px] 3xl:text-[0.729vw] cursor-pointer 
    ${open ? 'bg-InterfaceSurfacehcinverse text-[#FFF]' : 'text-BrandNeutralpure hover:bg-InterfaceSurfacehcinverse hover:text-[#FFF]'}`}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 mt-[-10px] w-[360px] xl:w-[360px] 2xl:w-[360px] 3xl:w-[18.75vw] rounded-none bg-background">
          <div>
            <div className="border-b border-InterfaceStrokedefault px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw]">
              <h4 className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%]">
                Metric Score Cards
              </h4>
              <h4 className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[150%]">
                (Select any 5)
              </h4>
            </div>

            <div className="px-[28px] xl:px-[28px] 2xl:px-[30px] 3xl:px-[1.67vw] py-[20px] xl:py-[20px] 2xl:py-[20px] 3xl:py-[1.042vw]">
              <div className="space-y-[12px] 3xl:space-y-[0.625vw]">
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="TotalCustomers" className="col-span-1" />
                  <label
                    htmlFor="TotalCustomers"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Total Customers
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="Subscription1" className="col-span-1" />
                  <label
                    htmlFor="Subscription1"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Expiring Subscription 7 Days
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="Subscription2" className="col-span-1" />
                  <label
                    htmlFor="Subscription2"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Expiring Subscription 15 Days
                  </label>
                </div>
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="opencarts" className="col-span-1" />
                  <label
                    htmlFor="opencarts"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Open Carts
                  </label>
                </div>
                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="overdueinvoices" className="col-span-1" />
                  <label
                    htmlFor="overdueinvoices"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Overdue Invoices
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="creditavailable" className="col-span-1" />
                  <label
                    htmlFor="creditavailable"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Credit Available
                  </label>
                </div>

                <div className="grid grid-cols-12 custcheckbox">
                  <Checkbox id="unbilledusage" className="col-span-1" />
                  <label
                    htmlFor="unbilledusage"
                    className="text-InterfaceTextdefault col-span-10 cursor-pointer text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                  >
                    Unbilled Usage
                  </label>
                </div>
              </div>
            </div>

            <div className="px-[16px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 2xl:py-[12px] 3xl:py-[0.625vw] flex justify-end gap-[12px] 3xl:gap-[0.625vw] border-t border-InterfaceStrokesoft">
              <Button
                type="submit"
                onClick={() => setOpen(false)}
                className="cancelbtn text-InterfaceTextdefault rounded-none px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-closecircle"></i>
                Cancel
              </Button>

              <Button
                type="submit"
                onClick={() => setOpen(false)}
                className="border border-[#3798FA] rounded-none text-interfacesurfacecomponent bg-BrandNeutralpure px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[10px xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw]"
              >
                <i className="cloud-copy-success"></i>
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
