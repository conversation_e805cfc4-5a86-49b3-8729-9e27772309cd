import React from 'react';
import { MetricScoreCardsOverlay } from './metric-score-cards';
import { useTranslations } from 'next-intl';

export default function Cards() {
  const t = useTranslations();
  const data = [
    {
      title: t('totalCustomers'),
      icon: 'cloud-doubleuser',
      number: '25',
    },
    {
      title: t('expiringSubscriptionsDays'),
      icon: 'cloud-calendar',
      number: '10',
    },
    {
      title: t('expiringSubscriptionsDays2'),
      icon: 'cloud-canlendar',
      number: '25',
    },
    {
      title: t('openCarts'),
      icon: 'cloud-cart',
      number: '5',
    },
    {
      title: t('overdueInvoices'),
      icon: 'cloud-receipt',
      number: '5',
    },
  ];

  return (
    <div className="grid grid-cols-5 gap-[16px] 3xl:gap-[0.833vw] mb-[20px] 3xl:mb-[1.042vw]">
      {data.map((items, index) => {
        return (
          <div
            key={index}
            className="bg-background px-[16px] 3xl:px-[0.833vw] py-[10px] 3xl:py-[0.521vw] cardShadow rounded-[6px] 3xl:rounded-[0.313vw]"
          >
            <div className="flex justify-between gap-1 mb-[16px] 3xl:mb-[0.833vw] min-h-[40px] 3xl:min-h-[2.083vw]">
              <div className="text-InterfaceTextdefault text-[13px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                {items.title}
              </div>
              <MetricScoreCardsOverlay />
            </div>
            <div className="flex items-center gap-2">
              <i
                className={`${items.icon} text-BrandNeutralpure text-[30px] 3xl:text-[1.563vw] leading-none`}
              ></i>
              <div className="text-InterfaceTextdefault text-[42px] 3xl:text-[2.292vw] font-normal leading-none">
                {items.number}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
