import Link from 'next/link';
import React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Cards from './cards';
import QuickLinks from './quicklinks';
import { useTranslations } from 'next-intl';
import { localStorageService } from '@/lib/services/storage';
import { useEffect, useState } from 'react';

export default function Announcements() {
  const t = useTranslations();
  const [selectedLang, setSelectedLang] = useState<'en' | 'ar' | 'tr'>('en');
  useEffect(() => {
    (async () => {
      const storedLocale = await localStorageService.get<string>('locale');
      if (storedLocale === 'ar') {
        setSelectedLang('ar');
      } else if (storedLocale === 'tr') {
        setSelectedLang('tr');
      } else {
        setSelectedLang('en');
      }
    })();
  }, []);
  return (
    <div className="grid grid-cols-10 gap-[16px] 3xl:gap-[0.833vw] my-[18px] 3xl:my-[1.042vw] ">
      <div className="col-span-12 xl:col-span-7">
        <Cards />
        <Carousel>
          <CarouselContent className="flex items-center gap-2">
            <CarouselItem className="">
              <div className="relative introBanner ">
                <video
                  autoPlay
                  loop
                  muted
                  playsInline
                  className="w-full -mt-1 h-[300px] 3xl:h-[17.604vw]"
                >
                  <source src="/images/intro_video.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>

                <div className="absolute top-0 left-0 right-0 bottom-0 carosal_bg">
                  <div className="flex flex-col justify-center h-full pl-[100px] 3xl:pl-[5.031vw]">
                    {selectedLang === 'en' ? (
                      <>
                        <div className="text-[30px] 2xl:text-[30px] 3xl:text-[1.875vw] text-InterfaceTextwhite font-[500] leading-[120%] mb-[6px]">
                          Introducing Red.AI
                          <p>The Future of Innovation With</p>
                        </div>

                        <div className="text-[30px] 2xl:text-[30px] 3xl:text-[1.875vw] text-darkgreencolor font-[350] leading-[100%]">
                          Generative AI Excellence
                        </div>
                      </>
                    ) : (
                      <div className="text-[30px] 2xl:text-[30px] 3xl:text-[1.875vw] text-InterfaceTextwhite font-[500] leading-[120%] mb-[6px]">
                        {t('redAlIntro')}
                      </div>
                    )}
                    <div className="flex mt-[25px] 3xl:mt-[1.458vw]">
                      <Link
                        href={''}
                        className=" px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.525vw] border border-background text-InterfaceTextwhite block"
                      >
                        {t('knowMore')}
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </CarouselItem>
            <CarouselItem className="">
              <div className="relative introBanner ">
                <video
                  autoPlay
                  loop
                  muted
                  playsInline
                  className="w-full -mt-1 h-[300px] 3xl:h-[17.604vw]"
                >
                  <source src="/images/intro_video.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>

                <div className="absolute top-0 left-0 right-0 bottom-0 carosal_bg">
                  <div className="flex flex-col justify-center h-full pl-[100px] 3xl:pl-[5.031vw]">
                    {selectedLang === 'ar' || selectedLang === 'tr' ? (
                      <div className="text-[30px] 2xl:text-[30px] 3xl:text-[1.875vw] text-InterfaceTextwhite font-[500] leading-[120%] mb-[6px]">
                        {t('redAlIntro')}
                      </div>
                    ) : (
                      <>
                        <div className="text-[30px] 2xl:text-[30px] 3xl:text-[1.875vw] text-InterfaceTextwhite font-[500] leading-[120%] mb-[6px]">
                          Introducing Red.AI
                          <p>The Future of Innovation With</p>
                        </div>

                        <div className="text-[30px] 2xl:text-[30px] 3xl:text-[1.875vw] text-darkgreencolor font-[350] leading-[100%]">
                          Generative AI Excellence
                        </div>
                      </>
                    )}
                    <div className="flex mt-[25px] 3xl:mt-[1.458vw]">
                      <Link
                        href={''}
                        className=" px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.525vw] border border-background text-InterfaceTextwhite block"
                      >
                        {t('knowMore')}
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </CarouselItem>
          </CarouselContent>

          <CarouselPrevious className="left-[20px] text-InterfaceTextwhite hover:text-blackcolor hover:bg-background bg-transparent  " />
          <CarouselNext className="right-[20px] text-InterfaceTextwhite hover:text-blackcolor hover:bg-background bg-transparent" />
        </Carousel>
      </div>
      <div className="col-span-12 xl:col-span-3">
        <div className="bg-background p-[16px] xl:p-[16px] 3xl:p-[0.833vw] cardShadow">
          <h1 className="text-InterfaceTexttitle text-[18px] md:text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[700] mb-[10px] 3xl:mb-[0.512vw]">
            {t('announcements')}
          </h1>
          <div className="space-y-[6px] 3xl:scale-y-[0.417vw]">
            <Link
              href={''}
              className="text-InterfaceTextdefault block hover:text-BrandSupport1pure hover:underline text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400] border-b border-b-InterfaceStrokesoft pb-[8px] 3xl:py-[0.417vw]"
            >
              One Platform to manage your multi-cloud infrastructure for all partners, vendors and
              customers
            </Link>
            <Link
              href={''}
              className="text-InterfaceTextdefault block hover:text-BrandSupport1pure hover:underline text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400] border-b border-b-InterfaceStrokesoft pb-[8px] 3xl:py-[0.417vw]"
            >
              The only Self-Service platform with managed services capabilities.
            </Link>
            <Link
              href={''}
              className="text-InterfaceTextdefault block hover:text-BrandSupport1pure hover:underline text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400] border-b border-b-InterfaceStrokesoft pb-[8px] 3xl:py-[0.417vw]"
            >
              Do it yourself or get it done by an Expert!
            </Link>
            <Link
              href={''}
              className="text-InterfaceTextdefault block hover:text-BrandSupport1pure hover:underline text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400] border-b border-b-InterfaceStrokesoft pb-[8px] 3xl:py-[0.417vw]"
            >
              One Platform to manage your multi-cloud infrastructure for all partners, vendors and
              customers
            </Link>
            <Link
              href={''}
              className="text-InterfaceTextdefault block hover:text-BrandSupport1pure hover:underline text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400] pb-[8px] 3xl:py-[0.417vw]"
            >
              Do it yourself or get it done by an Expert!
            </Link>
          </div>
          <div className="flex justify-end items-center 3xl:mt-[0.417vw]">
            <Link
              href={''}
              className="text-BrandSupport1pure block hover:text-BrandSupport1pure hover:underline text-[14px] md:text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] leading-[140%] font-[400]"
            >
              {t('showMore')}
            </Link>
          </div>
        </div>
        <QuickLinks />
      </div>
    </div>
  );
}
