import HorizontalBarChart from '@/components/common/charts/horizontalbarchart';
import React from 'react';
import { useTranslations } from 'next-intl';

export default function QuoteManagement() {
  const t = useTranslations();
  return (
    <div className="bg-background">
      <div className="flex justify-between gap-1 px-[16px] 3xl:px-[0.833vw] py-[12px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokedefault">
        <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] font-[700] leading-[140%]">
          {t('quoteManagement')}
        </div>
        <div className="flex items-center gap-3">
          <i className="cloud-threedot text-BrandNeutralpure text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i>
        </div>
      </div>
      <div className="">
        <div className="h-[385px] 3xl:h-[20.041vw] w-full p-[14px] 3xl:p-[0.729vw]">
          <HorizontalBarChart
            legend={{
              show: false,
              left: '2%',
              bottom: '-1%',
              itemHeight: 10,
              itemWidth: 10,
              textStyle: {
                color: '#2C363F',
                fontSize: 11,
              },
            }}
            grid={{
              top: '0%',
              left: '2%',
              right: '2%',
              bottom: '2%',
              containLabel: true,
            }}
            xAxisLabel={{ show: false }}
            xAxisSplitLine={{
              show: false,
              lineStyle: {
                type: 'dashed',
                color: '#C8CBD0',
              },
            }}
            yAxisLabel={{
              color: '#24262D',
              fontSize: 13,
              padding: [0, 0, 0, 0],
              textAlign: 'left',
              fontWeight: 600,
            }}
            yAxisTick={{ show: false }}
            yAxisLine={{
              show: false,
              lineStyle: {
                color: '#E4E7EC',
              },
            }}
            yAxisLine2={{
              show: false,
              lineStyle: {
                color: '#E4E7EC',
              },
            }}
            yAxisLabel2={{
              color: '#24262D',
              fontSize: 14,
              fontWeight: 600,
            }}
            name={'# Quotes'}
            showBackground={true}
            backgroundStyle={{
              color: '#EDEEF1',
              borderRadius: [0, 4, 4, 0],
            }}
            label={{
              show: false,
              position: 'outside',
              color: '#344054',
              formatter: '{c}',
              fontSize: 12,
            }}
            itemStyle={{
              color: '#6480AB',
              borderRadius: [4, 4, 4, 4],
            }}
            yAxisdata={[
              t('expired'),
              t('cancelled'),
              t('redeemed'),
              t('declined'),
              t('accepted'),
              t('rejected'),
              t('approved'),
              t('inReview'),
              t('new'),
              t('totalQuotes'),
            ]}
            yAxisdata2={[
              '10 (10%)',
              '56 (10%)',
              '50 (10%)',
              '60 (10%)',
              '65 (10%)',
              '40 (10%)',
              '91 (10%)',
              '76 (10%)',
              '88 (10%)',
              '100',
            ]}
            data={[60, 50, 40, 50, 20, 60, 50, 40, 50, 20]}
          />
        </div>
        <div className="p-[16px] 3xl:p-[0.833vw] flex items-center gap-[6px] border-t border-InterfaceStrokedefault">
          <div className="h-[10px] w-[10px] bg-BrandNeutral700 rounded-[3px]"></div>
          <div className="text-InterfaceTextdefault text-[10px] 3xl:text-[0.625vw] font-[400]">
            # Quotes{' '}
          </div>
        </div>
      </div>
    </div>
  );
}
