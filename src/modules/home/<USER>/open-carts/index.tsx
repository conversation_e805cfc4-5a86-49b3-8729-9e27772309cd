import React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  Popover,
  <PERSON>overContent,
  PopoverTrigger,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { DeletePopup } from './deletpopup';
import { ClonePopup } from './clonepopup';
import { useTranslations } from 'next-intl';

export default function OpenCarts() {
  const t = useTranslations();
  const data = [
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
      duplicate: true,
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
      duplicate: true,
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
  ];
  const [openPopoverIndex, setOpenPopoverIndex] = React.useState<number | null>(null);
  return (
    <div className="bg-background my-[18px] 3xl:my-[1.042vw] pt-[18px] 3xl:pt-[1.042vw] px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] pb-[28px] 2xl:pb-[30px] 3xl:pb-[1.667vw]">
      <div className="text-blackcolor text-[18px] md:text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[700]">
        {t('openCarts')}
      </div>

      <div className="pt-[23px] 3xl:pt-[1.198vw]">
        <Carousel>
          <CarouselContent className="flex items-center">
            {data.map((items, index) => {
              return (
                <CarouselItem
                  key={index}
                  className="basis-1/1 md:basis-1/2 xl:basis-1/4 2xl:basis-1/4 3xl:basis-1/5"
                >
                  <div className="border border-InterfaceStrokesoft bg-background openCardShadow">
                    <div className="py-[18px] 2xl:py-[20px] 3xl:py-[1.042vw] px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
                      <div className="flex justify-between gap-1 mb-[8px] 3xl:mb-[0.417vw]">
                        <div className="text-InterfaceTextsubtitle text-[13px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                          {t('simmonsOn')} {items.simmonsOn}
                        </div>
                        <div className="flex items-center gap-3">
                          {items.duplicate === true && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <i className="cloud-danger text-closecolor text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i>
                                </TooltipTrigger>
                                <TooltipContent
                                  side="bottom"
                                  align="center"
                                  className="min-w-[100px] 3xl:min-w-[5.208vw] bg-background"
                                >
                                  <p className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] text-center">
                                    Duplicate <br />
                                    Cart ID: CT1001
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}

                          <Popover
                            open={openPopoverIndex === index}
                            onOpenChange={(open) => setOpenPopoverIndex(open ? index : null)}
                          >
                            <PopoverTrigger asChild>
                              {/* <i className="cloud-threedot text-BrandNeutralpure text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i> */}
                              <i
                                className={`rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[6px] xl:px-[6px] 2xl:px-[8px] 3xl:px-[0.417vw] cloud-threedot text-[14px] 3xl:text-[0.729vw] cursor-pointer
        ${
          openPopoverIndex === index
            ? 'bg-BrandNeutral100 text-BrandNeutralpure'
            : 'text-BrandNeutralpure hover:bg-BrandNeutral100'
        }`}
                              />
                            </PopoverTrigger>
                            <PopoverContent className=" mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-fit z-20 bg-interfacesurfacecomponent cursor-pointer p-0">
                              <div className="">
                                <DeletePopup />
                                <ClonePopup />
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                      <div className="text-InterfaceTextprimary text-[13px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                        {items.systems}
                      </div>
                      <div className="text-InterfaceTexttitle text-[20px] 3xl:text-[1.042vw] font-[700] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                        {items.title}
                      </div>

                      <div className="text-InterfaceTextsubtitle text-[13px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {items.description}
                      </div>
                      <div className="flex items-center gap-[12px] 3xl:gap-[0.625vw] mt-[16px] 3xl:mt-[0.833vw]">
                        <div>
                          <div className="border border-BrandSupport1300 bg-BrandSupport150 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.313vw]">
                            <div className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] font-[500] leading-[100%]">
                              {t('cartId')}: {items.cartId}
                            </div>
                          </div>
                        </div>
                        <div>
                          <div className="border border-BrandSupport1300 bg-BrandSupport150 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.313vw]">
                            <div className="text-InterfaceTextprimary text-[12px] 3xl:text-[0.625vw] font-[500] leading-[100%]">
                              {t('quoteId')}: {items.quoteId}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="py-[13px] 2xl:py-[13px] 3xl:py-[0.677vw] px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw] border-b border-t border-InterfaceStrokesoft">
                      <div className="flex justify-between gap-1">
                        <div className="text-blackcolor text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-[400] leading-[140%] flex items-center gap-1">
                          {t('totalItems')}:{' '}
                          <span className="text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                            5
                          </span>
                        </div>
                        <div className="text-blackcolor text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-[400] leading-[140%] flex items-center gap-1">
                          {t('totalValue')}:{' '}
                          <span className="text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600]">
                            USD 50,000.00
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="py-[13px] 2xl:py-[13px] 3xl:py-[0.677vw] px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
                      <div className="flex items-center gap-1">
                        <i className="cloud-clock11 text-Interfacefeedbackneutral700 text-[16px] 3xl:text-[0.833vw]"></i>
                        <div className="text-Interfacefeedbackneutral700 text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                          {t('expiresAt')} {items.expiresAt}
                        </div>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              );
            })}
          </CarouselContent>

          <CarouselPrevious className="bg-background rounded-none border-none arrowShadow right-[40px] left-auto -top-[35px] prevIcon" />
          <CarouselNext className="bg-background rounded-none border-none arrowShadow right-0 -top-[35px] nextIcon" />
        </Carousel>
      </div>
    </div>
  );
}
