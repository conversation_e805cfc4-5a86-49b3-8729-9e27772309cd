'use client';

import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { useRouter } from 'next/navigation';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function DeletePopup() {
  const router = useRouter();
  return (
    <Sheet>
      <SheetTrigger className="hover:bg-BrandNeutral100 w-full">
        <div className="hover:text-InterfaceTextprimary px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
          <i className="cloud-trash hover:text-InterfaceTextdefault text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
          Delete
        </div>
      </SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full flex flex-col justify-center items-center">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-dangerlight text-closecolor text-[72px]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[36px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                Delete cart(s)?
              </div>
              <div className="text-InterfaceTextsubtitle text-[20px] lg:text-[20px] xl:text-[20px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                Would you like to delete the selected cart(s), <br />
                your action can not be reversed,
                <br /> please confirm
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center border-[#D42600]">
            <SheetClose>
              <div
                onClick={() => router.push('/open-carts')}
                className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-background bg-closecolor  hover:bg-[#00953A] font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]"
              >
                <i className="cloud-save-2 text-[18px]"></i>
                Yes, Delete
              </div>
            </SheetClose>
            <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">or</p>
              <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
            </div>

            <SheetClose>
              <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                <i className="cloud-closecircle text-[18px]"></i>
                No, Later
              </div>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
