'use client';
import { QuickOrderSheetProps } from '@/types';
import {
  Sheet,
  SheetContent,
  SheetTitle,
  Label,
  RadioGroup,
  RadioGroupItem,
  SheetClose,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import Link from 'next/link';

export function QuickOrderPopup({ open, onClose }: QuickOrderSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={` sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto relative">
          <div className="px-4 mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
            <div className="flex flex-col justify-center items-center gap-[20px] 3xl:gap-[2.083vw]">
              <i className="cloud-info2 text-BrandSupport1pure text-[72px] xl:text-[65px] 3xl:text-[3.75vw]"></i>
              <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[36px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                  Quick Order
                </div>
              </div>
              <div className="border border-InterfaceStrokesoft py-[12px] 3xl:py-[0.625vw] px-[16px] 3xl:px-[0.833vw]">
                <div className="py-[20px] 3xl:py-[1.042vw] flex gap-[24px] 3xl:gap-[1.25vw] border-b border-InterfaceStrokesoft">
                  <Image
                    width={80}
                    height={80}
                    className="w-[80px] 3xl:w-[4.167vw] h-[80px] 3xl:h-[4.167vw]"
                    src="/images/svg/redinton_logo.svg"
                    alt="redinton logo"
                  />
                  <div>
                    <p className="text-InterfaceTextdefault text-[20px] 3xl:text-[1.042vw] font-normal">
                      Reserved VM Instance, Cool_GRS_Data_Stored_10
                    </p>
                    <p className="text-InterfaceTexttitle text-[20px] 3xl:text-[1.042vw] font-semibold">
                      USD 302.25
                    </p>
                  </div>
                </div>
                <div className="py-[20px] 3xl:py-[1.042vw] flex flex-col gap-[12px] 3xl:gap-[0.625vw] border-b border-InterfaceStrokesoft">
                  <Label
                    htmlFor="email"
                    className="text-[16px] 3xl:text-[0.833vw] font-medium text-[#000]"
                  >
                    Term{' '}
                  </Label>
                  <RadioGroup defaultValue="year" className="flex gap-6">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="month" id="r1" />
                      <Label htmlFor="r1" className="text-InterfaceTextdefault">
                        1 Month
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="year" id="r2" />
                      <Label htmlFor="r2" className="text-InterfaceTextdefault">
                        1 Year{' '}
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
                <div className="py-[20px] 3xl:py-[1.042vw] flex flex-col gap-[12px] 3xl:gap-[0.625vw]">
                  <Label
                    htmlFor="email"
                    className="text-[16px] 3xl:text-[0.833vw] font-medium text-[#000]"
                  >
                    Bill Type
                  </Label>
                  <RadioGroup defaultValue="month" className="flex gap-6">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="month" id="r1" />
                      <Label htmlFor="r1" className="text-InterfaceTextdefault">
                        Monthly
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="year" id="r2" />
                      <Label htmlFor="r2" className="text-InterfaceTextdefault">
                        Annual
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 right-0 left-0 flex justify-end items-center gap-[12px] 3xl:gap-[0.625vw] mt-[40px] 3xl:mt-[2.083vw] text-center p-6 border-t border-InterfaceStrokedefault">
            <SheetClose>
              <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2">
                <i className="cloud-closecircle text-[18px] text-InterfaceTextdefault"></i>
                Cancel
              </div>
            </SheetClose>

            <Link
              href={'#'}
              className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-background bg-[#00953A] font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2"
            >
              <i className="cloud-fillcircletick text-[18px]"></i>
              Select
            </Link>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
