import React, { useState } from 'react';
import {
  Hover<PERSON>ard,
  HoverCardContent,
  HoverCardTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import Link from 'next/link';
import { QuickOrderPopup } from '../quick-order';
import Image from 'next/image';
import ProductDetailedView from '@/modules/marketplace/components/product-details-view';
import { useTranslations } from 'next-intl';

export default function ProductDiscountCoupon() {
  const [quickOrderPopupShow, setQuickOrderPopupShow] = useState(false);
  const [productdetailedpopup, setProductdetailedpopup] = useState(false);
  const t = useTranslations();

  const PurchasesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('productName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-BrandSupport1pure cursor-pointer">
          <HoverCard>
            <HoverCardTrigger asChild>
              <div onClick={() => setProductdetailedpopup(true)}>{row.getValue('productname')}</div>
            </HoverCardTrigger>
            <HoverCardContent className="w-[320px] 3xl:w-[16.667vw] bg-background ml-[150px]">
              <div
                onClick={() => setQuickOrderPopupShow(true)}
                className="flex items-center gap-[16px] 3xl:gap-[0.833vw] w-full"
              >
                <div>
                  <div className="bg-BrandPrimary100 w-[64px] 3xl:w-[3.333vw] h-[64px] 3xl:h-[3.333vw] flex items-center justify-center">
                    <Image
                      width={30}
                      height={54}
                      className="w-[30px] 3xl:w-[1.563vw] h-[54px] 3xl:h-[2.813vw]"
                      src="/images/svg/redinton_logo.svg"
                      alt="redinton logo"
                    />
                  </div>
                </div>
                <div>
                  <div className="text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] text-InterfaceTexttitle text-wrap text-left">
                    Reserved VM Instance, Cool_GRS_Data_Stored_10
                  </div>
                  <div className="text-[12px] xl:text-[12px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[600] text-InterfaceTexttitle text-wrap text-left">
                    SKU ID: <span className="font-[400]">DZH318Z08T98:00SB</span>
                  </div>
                </div>
              </div>
            </HoverCardContent>
          </HoverCard>
        </div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'quality',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('minQuality')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('quality')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'currency',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('currency')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('currency')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'salepriceunit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('listPriceUnit')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="text-end">{row.getValue('salepriceunit')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'discountedpriceunit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('discountedPriceUnit')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="text-end">{row.getValue('discountedpriceunit')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('validUntil')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('endcustomer')}</div>,

      minSize: 500,
    },
  ];

  type User = {
    productname: string;
    quality: string;
    currency: string;
    salepriceunit: string;
    discountedpriceunit: string;
    endcustomer: string;
  };

  const Purchasesdata = [
    {
      productname: 'Product 1',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      discountedpriceunit: '50.00',
      endcustomer: '10/10/2024',
    },
    {
      productname: 'Product 2',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      discountedpriceunit: '50.00',
      endcustomer: '10/10/2024',
    },
    {
      productname: 'Product 3',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      discountedpriceunit: '50.00',
      endcustomer: '10/10/2024',
    },
    {
      productname: 'Product 4',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      discountedpriceunit: '50.00',
      endcustomer: '10/10/2024',
    },
    {
      productname: 'Product 5',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      discountedpriceunit: '50.00',
      endcustomer: '10/10/2024',
    },
    {
      productname: 'Product 6',
      quality: '12',
      currency: 'USD',
      salepriceunit: '150.00',
      discountedpriceunit: '50.00',
      endcustomer: '10/10/2024',
    },
  ];

  return (
    <div className="bg-background">
      <div className="flex justify-between gap-1 px-[16px] 3xl:px-[0.833vw] py-[12px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokedefault">
        <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] font-[700] leading-[140%]">
          {t('productsWithDiscountCoupon')}
        </div>
        <div className="flex items-center gap-3">
          <i className="cloud-threedot text-BrandNeutralpure hover:bg-BrandNeutral100 rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[8px] xl:px-[8px] 2xl:px-[10px] 3xl:px-[0.6vw] text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i>
        </div>
      </div>
      <div className="p-[16px] 3xl:p-[0.833vw]">
        <div className="overflow-x-auto">
          <DataTable data={Purchasesdata} columns={PurchasesColumns} withCheckbox={false} />
        </div>
        <div className="flex items-center justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] w-full border-b border-r border-l  border-t-none rounded-bl-[4px] rounded-br-[4px]">
          <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
            1-10{' '}
            <span className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
              Of{' '}
            </span>
            <span className=" font-[600] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
              100
            </span>
          </div>
          <Link
            href="/sales/discount-coupons"
            className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-BrandSupport1pure"
          >
            {t('showMore')}
          </Link>
        </div>
      </div>
      {quickOrderPopupShow && (
        <QuickOrderPopup open={quickOrderPopupShow} onClose={() => setQuickOrderPopupShow(false)} />
      )}

      <ProductDetailedView
        open={productdetailedpopup}
        onClose={() => setProductdetailedpopup(false)}
      />
    </div>
  );
}
