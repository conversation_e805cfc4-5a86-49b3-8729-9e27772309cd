'use client';
import React, { useEffect, useState } from 'react';
import { Roboto } from 'next/font/google';
import MajorVendors from '../components/major-vendors';
import Announcements from '../components/announcements';
import DashboardBanner from '../components/banner';
import OpenCarts from '../components/open-carts';
import RecentPurchase from '../components/recent-purchases';
import TopFiveProducts from '../components/topfiveproducts';
import ProductDiscountCoupon from '../components/products-discount-coupon';
import CreditManagement from '../components/credit-management';
import QuoteManagement from '../components/quote-management';
import InvoiceOpen from '../components/invoices-open';
import TopProducts from '../components/top-product';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function HomeTemplate() {
  const t = useTranslations();
  const [visible, setVisible] = useState<boolean>(false);

  const toggleVisibility = (): void => {
    if (window.scrollY > 0) {
      setVisible(true);
    } else {
      setVisible(false);
    }
  };

  const scrollToTop = (): void => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility);
    return () => {
      window.removeEventListener('scroll', toggleVisibility);
    };
  }, []);

  return (
    <>
      <div className={`${roboto.className}`}>
        <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] py-[8px] 3xl:py-[0.417vw]">
          {t('home')}
        </div>
        <div className="px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] pt-[17px] 3xl:pt-[0.885vw]">
          <h1 className="text-blackcolor text-[28px] md:text-[28px] lg:text-[28px] xl:text-[28px] 2xl:text-[28px] 3xl:text-[1.563vw] leading-[140%] font-[700] mb-[21px] 3xl:mb-[1.094vw]">
            {t('goodDay')} Jese Leos
          </h1>

          <DashboardBanner />
          <MajorVendors />
          <Announcements />
          <OpenCarts />

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px] 3xl:gap-[1.042vw] mb-[18px] 3xl:mb-[1.042vw]">
            <RecentPurchase />
            <TopFiveProducts />
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px] 3xl:gap-[1.042vw] mb-[18px] 3xl:mb-[1.042vw]">
            <ProductDiscountCoupon />
            <CreditManagement />
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px] 3xl:gap-[1.042vw] mb-[18px] 3xl:mb-[1.042vw]">
            <QuoteManagement />
            <InvoiceOpen />
          </div>

          <TopProducts />
        </div>
      </div>

      {visible && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 h-[40px] w-[40px] bg-[#768FB54D] hover:bg-[#768FB5] hover:text-[#3C4146] text-white shadow-lg transition"
          aria-label="Scroll to top"
        >
          <div className="rotate-90">
            <i className="cloud-arrowleft text-[14px] 3xl:text-[0.729vw]"></i>
          </div>
        </button>
      )}
    </>
  );
}
