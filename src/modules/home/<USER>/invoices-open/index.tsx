import React from 'react';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function InvoiceOpen() {
  const t = useTranslations();
  const InvoicesColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'invoice',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('invoice')} #
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <Link
          href={'/billing/invoices'}
          className="text-BrandSupport1pure cursor-pointer hover:underline"
        >
          {row.getValue('invoice')}
        </Link>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'invoicedate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('invoiceDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('invoicedate')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'currency',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('currency')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('currency')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'invoicevalue',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('invoiceValue')} (USD)
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="text-end">{row.getValue('invoicevalue')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'duedate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('dueDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('duedate')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between "
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      // cell: ({ row }) => <div className="">{row.getValue('status')}</div>,
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          'Not Due': {
            colorClass: 'bg-BrandPrimary100 text-BrandPrimary800',
          },
          Overdue: {
            colorClass: 'bg-BrandSupport2100 text-BrandSupport2800',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800',
        };

        return (
          <div className="cursor-pointer ">
            <div
              className={`inline-block py-[4px] xl:py-[4px] 3xl:py-[0.208vw] px-[10px] rounded-[2px] text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-semibold leading-[140%] ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 500,
    },
  ];

  type User = {
    invoice: string;
    invoicedate: string;
    currency: string;
    invoicevalue: string;
    duedate: string;
    status: string;
  };

  const Invoicesdata = [
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      currency: 'USD',
      invoicevalue: '1200.00',
      duedate: '10/10/2024',
      status: 'Not Due',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      currency: 'USD',
      invoicevalue: '1200.00',
      duedate: '10/10/2024',
      status: 'Not Due',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      currency: 'USD',
      invoicevalue: '1200.00',
      duedate: '10/10/2024',
      status: 'Not Due',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      currency: 'USD',
      invoicevalue: '1200.00',
      duedate: '10/10/2024',
      status: 'Overdue',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      currency: 'USD',
      invoicevalue: '1200.00',
      duedate: '10/10/2024',
      status: 'Overdue',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      currency: 'USD',
      invoicevalue: '1200.00',
      duedate: '10/10/2024',
      status: 'Overdue',
    },
    {
      invoice: '651535',
      invoicedate: '10/10/2024',
      currency: 'USD',
      invoicevalue: '1200.00',
      duedate: '10/10/2024',
      status: 'Not Due',
    },
  ];

  return (
    <div className="bg-background">
      <div className="flex justify-between gap-1 px-[16px] 3xl:px-[0.833vw] py-[12px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokedefault">
        <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] font-[700] leading-[140%]">
          {t('invoicesOpen')}
        </div>
        <div className="flex items-center gap-3">
          <i className="cloud-threedot text-BrandNeutralpure text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i>
        </div>
      </div>

      <div className="p-[16px] 3xl:p-[0.833vw]">
        <div className="overflow-x-auto">
          <DataTable data={Invoicesdata} columns={InvoicesColumns} withCheckbox={false} />
        </div>
        <div className="flex items-center justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] w-full border-b border-r border-l  border-t-none rounded-bl-[4px] rounded-br-[4px]">
          <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
            1-10{' '}
            <span className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
              Of{' '}
            </span>
            <span className=" font-[600] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
              100
            </span>
          </div>
          <Link
            href="/billing/invoices"
            className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-BrandSupport1pure"
          >
            {t('showMore')}
          </Link>
        </div>
      </div>
    </div>
  );
}
