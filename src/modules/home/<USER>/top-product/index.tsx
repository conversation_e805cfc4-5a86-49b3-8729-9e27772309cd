import React, { useState } from 'react';
import {
  Button,
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import ProductDetailedView from '@/modules/marketplace/components/product-details-view';
import { useTranslations } from 'next-intl';

export default function TopProducts() {
  const [productdetailsPopupOpen, setProductdetailsPopupOpen] = useState(false);
  const data = [
    {
      type: 'Productivity & Collaboration',
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: 'Productivity & Collaboration',
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: 'Productivity & Collaboration',
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: 'Productivity & Collaboration',
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: 'Productivity & Collaboration',
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
    {
      type: 'Productivity & Collaboration',
      title: 'Microsoft 365 Apps for Enterprise',
      segment: 'Commercial',
      SKUID: 'DZH318Z009PGJ:0007',
      options: 'Term (2)',
      billType: 'Monthly, Yearly',
      totalValue: 'USD 1,000.00',
    },
  ];
  const t = useTranslations();

  return (
    <div className="bg-background my-[18px] 3xl:my-[1.042vw] pt-[18px] 3xl:pt-[1.042vw] px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] pb-[16px] 2xl:pb-[18px] 3xl:pb-[0.938vw]">
      <div className="text-blackcolor text-[18px] md:text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[700]">
        {t('topProductsForUae')}
      </div>

      <div className="pt-[23px] 3xl:pt-[1.198vw]">
        <Carousel>
          <CarouselContent
            onClick={() => {
              setProductdetailsPopupOpen(true);
            }}
            className="flex items-center cursor-pointer"
          >
            {data.map((items, index) => {
              return (
                <CarouselItem
                  key={index}
                  className="basis-1/1 md:basis-1/2 xl:basis-1/3 2xl:basis-1/4"
                >
                  <div className="group border border-InterfaceStrokesoft bg-background hover:shadow-[0px_10px_15px_-3px_rgba(0,0,0,0.10),0px_4px_6px_0px_rgba(0,0,0,0.05)] mb-3">
                    <div className="py-[16px] 2xl:py-[18px] 3xl:py-[1.042vw] px-[20px] 2xl:px-[22px] 3xl:px-[1.25vw]">
                      <div className="flex justify-between gap-1 mb-[8px] 3xl:mb-[0.417vw]">
                        <Image
                          width={40}
                          height={40}
                          className="w-[40px] 3xl:w-[2.083vw] h-[40px] 3xl:h-[2.083vw]"
                          src="/images/svg/microsoft_icon.svg"
                          alt="microsoft logo"
                        />
                        <div className="flex items-center gap-[12px] 3xl:gap-[0.625vw]">
                          <i className="cloud-grid text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer"></i>
                          <i className="cloud-productview text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer"></i>
                          <i className="cloud-favriote text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.833vw] cursor-pointer"></i>
                        </div>
                      </div>

                      <div className="text-InterfaceTextsubtitle text-[13px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[6px] 3xl:mb-[0.417vw] uppercase">
                        {items.type}
                      </div>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="group-hover:text-BrandSupport17001 text-InterfaceTexttitle text-[20px] 3xl:text-[1.042vw] font-[700] leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
                              {items.title}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent className="min-w-[307px] 3xl:min-w-[15.99vw] bg-background">
                            <div className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%]">
                              {t('description')} :
                            </div>
                            <p className="text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                              {t(
                                'thePremiumOfficeSuiteForOrganizationsIncludingWordExcelPowerpointOutlookOnenoteAccessAndSkypeForBusiness'
                              )}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <div className="space-y-[4px] 3xl:space-y-[0.208vw] mb-[14px] 3xl:mb-[0.729vw]">
                        <div className="grid grid-cols-12 gap-1">
                          <div className="col-span-3 text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('segment')}{' '}
                          </div>
                          <div className="col-span-9 text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                            : {items.segment}
                          </div>
                        </div>
                        <div className="grid grid-cols-12 gap-1">
                          <div className="col-span-3 text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('skuIdLabel')}{' '}
                          </div>
                          <div className="col-span-9 text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                            : {items.SKUID}
                          </div>
                        </div>
                        <div className="grid grid-cols-12 gap-1">
                          <div className="col-span-3 text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('options')}
                          </div>
                          <div className="col-span-9 text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                            : {items.options}
                          </div>
                        </div>
                        <div className="grid grid-cols-12 gap-1">
                          <div className="col-span-3 text-InterfaceTextdefault text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                            {t('billType')}
                          </div>
                          <div className="col-span-9 text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                            : {items.billType}
                          </div>
                        </div>
                      </div>

                      <div className="text-InterfaceTexttitle text-[20px] 3xl:text-[1.042vw] font-[700] leading-[140%] mb-[14px] 3xl:mb-[0.729vw]">
                        {items.totalValue}
                      </div>
                      <div className="flex items-center gap-[16px] 3xl:gap-[0.833vw] mb-[14px] 3xl:mb-[0.729vw]">
                        <div className="flex items-center gap-2">
                          <i className="cloud-discount-fillshap text-BrandSupport2400 text-[12px] 3xl:text-[0.625vw]"></i>
                          <div className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%]">
                            {t('discountCoupon')}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <i className="cloud-promotion-tag text-BrandSupport1pure text-[12px] 3xl:text-[0.625vw]"></i>
                          <div className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[100%]">
                            {t('promotions')}
                          </div>
                        </div>
                      </div>

                      <div>
                        <Button className="flex justify-center bg-yellowbg px-[14px] xl:px-[14px] 2xl:px-[14px] 3xl:px-[0.729vw] py-[8px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] rounded-none font-[500] text-InterfaceTexttitle">
                          <i className="cloud-cart  text-[16px] 3xl:text-[0.833vw]"></i>
                          <span className="text text-[14px] xl:text-[16px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                            {t('addToCart')}
                          </span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              );
            })}
          </CarouselContent>

          <CarouselPrevious className="bg-background rounded-none border-none arrowShadow right-[40px] left-auto -top-[35px] prevIcon" />
          <CarouselNext className="bg-background rounded-none border-none arrowShadow right-0 -top-[35px] nextIcon" />
        </Carousel>
      </div>
      <ProductDetailedView
        open={productdetailsPopupOpen}
        onClose={() => setProductdetailsPopupOpen(false)}
      />
    </div>
  );
}
