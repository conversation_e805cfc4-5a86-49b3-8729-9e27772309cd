import React, { useState } from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import ProductDetailedView from '@/modules/marketplace/components/product-details-view';
import { useTranslations } from 'next-intl';

export default function TopFiveProducts() {
  const [tab, setTab] = useState(0);
  const [productdetailsPopupOpen, setProductdetailsPopupOpen] = useState(false);
  const data = [
    {
      title: 'Commercial Market Place',
      SKUID: 'DZH318Z09PGJ:0007',
      VendorSKUID: 'DZH318Z09PGJ:0007',
      units: '50',
      totalvalue: 'USD 1,000.00',
    },
    {
      title: 'Commercial Market Place',
      SKUID: 'DZH318Z09PGJ:0007',
      VendorSKUID: 'DZH318Z09PGJ:0007',
      units: '50',
      totalvalue: 'USD 1,000.00',
    },
    {
      title: 'Commercial Market Place',
      SKUID: 'DZH318Z09PGJ:0007',
      VendorSKUID: 'DZH318Z09PGJ:0007',
      units: '50',
      totalvalue: 'USD 1,000.00',
    },
    {
      title: 'Commercial Market Place',
      SKUID: 'DZH318Z09PGJ:0007',
      VendorSKUID: 'DZH318Z09PGJ:0007',
      units: '50',
      totalvalue: 'USD 1,000.00',
    },
    {
      title: 'Commercial Market Place',
      SKUID: 'DZH318Z09PGJ:0007',
      VendorSKUID: 'DZH318Z09PGJ:0007',
      units: '50',
      totalvalue: 'USD 1,000.00',
    },
    {
      title: 'Commercial Market Place',
      SKUID: 'DZH318Z09PGJ:0007',
      VendorSKUID: 'DZH318Z09PGJ:0007',
      units: '50',
      totalvalue: 'USD 1,000.00',
    },
  ];
  const t = useTranslations();

  return (
    <div className="bg-background">
      <div className="flex justify-between gap-1 px-[16px] 3xl:px-[0.833vw] py-[12px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokedefault">
        <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] font-[700] leading-[140%]">
          {t('myTop5Products')}
        </div>
        <div className="flex items-center gap-3">
          <i className="cloud-threedot text-BrandNeutralpure hover:bg-BrandNeutral100 rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[8px] xl:px-[8px] 2xl:px-[10px] 3xl:px-[0.6vw] text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i>
        </div>
      </div>
      <div className="flex items-center ml-[16px] 3xl:ml-[0.833vw]">
        <Button
          onClick={() => setTab(0)}
          className={`${tab === 0 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none rounded-bl-[6px] 3xl:rounded-bl-[0.333vw]`}
        >
          {t('allTime')}
        </Button>
        <Button
          onClick={() => setTab(1)}
          className={`${tab === 1 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none`}
        >
          {t('ytd')}
        </Button>
        <Button
          onClick={() => setTab(2)}
          className={`${tab === 2 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none`}
        >
          {t('currentMonth')}
        </Button>
        <Button
          onClick={() => setTab(3)}
          className={`${tab === 3 ? 'bg-BrandPrimary100 text-BrandPrimary700 font-[700]' : 'bg-InterfaceSurfacecomponentmuted text-InterfaceTextdefault font-[500]'} px-[12px] 3xl:px-[0.625vw] py-[8px] 3xl:py-[0.417vw] text-[12px] 3xl:text-[0.625vw] rounded-none rounded-br-[6px] 3xl:rounded-br-[0.333vw]`}
        >
          {t('currentWeek')}
        </Button>
      </div>
      <div className="p-[16px] 3xl:gap-[0.833vw]">
        <div className="h-[320px] 3xl:h-[16.667vw] overflow-auto">
          {data.map((items, index) => {
            return (
              <div
                key={index}
                className="grid grid-cols-12 gap-1 p-[12px] 3xl:p-[0.625vw] border-b border-InterfaceStrokesoft group hover:bg-InterfaceSurfacecomponentmuted"
              >
                <div className="col-span-8">
                  <div
                    onClick={() => {
                      setProductdetailsPopupOpen(true);
                    }}
                    className="text-InterfaceTexttitle cursor-pointer group-hover:underline group-hover:text-InterfaceTextprimary text-[14px] 3xl:text-[0.833vw] font-[500] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]"
                  >
                    {items.title}
                  </div>
                  <div className="flex flex-wrap items-center gap-[16px] 3xl:gap-[0.833vw]">
                    <div className="group-hover:bg-[#fff] bg-InterfaceSurfacecomponentmuted text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[4px] 3xl:px-[0.208vw] py-[2px] 3xl:py-[0.104vw]">
                      {t('skuId')} : {items.SKUID}
                    </div>
                    <div className="group-hover:bg-[#fff] bg-InterfaceSurfacecomponentmuted text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[4px] 3xl:px-[0.208vw] py-[2px] 3xl:py-[0.104vw]">
                      {t('vendorSkuId')} : {items.VendorSKUID}
                    </div>
                  </div>
                </div>
                <div className="col-span-4 grid grid-cols-12">
                  <div className="col-span-5">
                    <div className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                      {t('units')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                      {items.units}
                    </div>
                  </div>
                  <div className="col-span-7">
                    <div className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                      {t('totalValue')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                      {items.totalvalue}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <ProductDetailedView
        open={productdetailsPopupOpen}
        onClose={() => setProductdetailsPopupOpen(false)}
      />
    </div>
  );
}
