import React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

export default function MajorVendors() {
  const t = useTranslations();
  return (
    <div className="bg-background mt-[18px] 3xl:mt-[1.042vw] pt-[18px] 3xl:pt-[1.042vw] px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] pb-[28px] 2xl:pb-[30px] 3xl:pb-[1.667vw]">
      <div className="text-blackcolor text-[18px] md:text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[700]">
        {t('majorVendors')}
      </div>

      <div className="pt-[23px] 3xl:pt-[1.198vw]">
        <Carousel>
          <CarouselContent className="flex items-center gap-2">
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/microsoft1.svg'}
                width={100}
                height={37}
                alt="microsoft"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/google1.svg'}
                width={100}
                height={37}
                alt="google"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/aws.svg'}
                width={50}
                height={37}
                alt="aws"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/acronis.svg'}
                width={100}
                height={37}
                alt="acronis"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/ibm.svg'}
                width={70}
                height={37}
                alt="ibm"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/dropbox.svg'}
                width={100}
                height={37}
                alt="dropbox"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/adobe.svg'}
                width={100}
                height={37}
                alt="adobe"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/avepoint.svg'}
                width={200}
                height={37}
                alt="avepoint"
                className="object-contain mx-auto"
              />
            </CarouselItem>
            <CarouselItem className="basis-1/2 md:basis-1/6 xl:basis-1/7 2xl:basis-1/9">
              <Image
                src={'images/cisco.svg'}
                width={60}
                height={37}
                alt="cisco"
                className="object-contain mx-auto"
              />
            </CarouselItem>
          </CarouselContent>

          <CarouselPrevious className="bg-background rounded-none border-none arrowShadow right-[40px] left-auto -top-[35px] prevIcon" />
          <CarouselNext className="bg-background rounded-none border-none arrowShadow right-0 -top-[35px] nextIcon" />
        </Carousel>
      </div>
    </div>
  );
}
