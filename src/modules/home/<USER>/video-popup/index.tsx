import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function VideoPopup() {
  const t = useTranslations();
  return (
    <div>
      <Dialog>
        <DialogTrigger asChild>
          <Button
            variant="skybluegradient"
            className="bg-BrandNeutralpure border border-BrandNeutral700 text-InterfaceTextwhite hover:bg-[#a6bad9] hover:border-[#98acca] text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] 2xl:py-[13px] 3xl:py-[0.681vw]"
          >
            <i className="cloud-videosquare"></i> {t('watchVideo')}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[800px] p-0 rounded-none shadow-none border-0 bg-transparent">
          <video width="320" height="240" autoPlay controls className="w-full videoBg">
            <source src="/images/watch_video.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </DialogContent>
      </Dialog>
    </div>
  );
}
