import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import { useTranslations } from 'next-intl';

export default function CreditManagement() {
  const t = useTranslations();
  return (
    <div className="bg-background">
      <div className="flex justify-between gap-1 px-[16px] 3xl:px-[0.833vw] py-[12px] 3xl:py-[0.625vw] border-b border-b-InterfaceStrokedefault">
        <div className="text-InterfaceTexttitle text-[18px] 3xl:text-[0.938vw] font-[700] leading-[140%]">
          {t('creditManagementUsd')}
        </div>
        <div className="flex items-center gap-3">
          <i className="cloud-threedot text-BrandNeutralpure hover:bg-BrandNeutral100 rounded-[3px] py-[4px] xl:py-[4px] 2xl:py-[4px] 3xl:py-[0.3vw] px-[8px] xl:px-[8px] 2xl:px-[10px] 3xl:px-[0.6vw] text-[14px] 3xl:text-[0.729vw] cursor-pointer"></i>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-[10px] 3xl:gap-[0.512vw] p-[14px] 3xl:p-[0.729vw]">
        <div className="bg-InterfaceSurfacecomponentmuted p-[12px] 3xl:p-[0.625vw]">
          <div className="text-InterfaceTexttitle text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
            {t('assigned')}
          </div>
          <div className="h-[270px] 3xl:h-[15.271vw]">
            <Piechart
              tooltip={{
                trigger: 'item',
                position: 'top',
                confine: true,
                formatter: () => {
                  return `<div style="padding-top:0px;">Total No Of<br/>Item - 600</div>`;
                },
              }}
              title={{
                text: t('total'),
                subtext: 'USD 500,000.00',
                right: 'center',
                top: '30%',
                bottom: 'center',
                textStyle: {
                  color: '#7F8488', // title text color
                  fontSize: 12,
                  fontWeight: 'normal',
                },
                subtextStyle: {
                  color: '#212325', // subtitle text color
                  fontSize: 12,
                  fontWeight: 'bold',
                },
              }}
              legends={{
                orient: 'vertical', // vertical layout
                bottom: 0,
                itemHeight: 10,
                itemWidth: 10,
                icon: 'circle',
                data: [t('available'), t('consumed')],
                formatter: (name) => {
                  if (name === t('available'))
                    return name + ': {styleA|USD} {styleGreen|250,000.00}';
                  if (name === t('consumed')) return name + ': {styleB|USD} {styleRed|250,000.00}';
                  return '{styleC|' + name + '}';
                },
                textStyle: {
                  color: '#7F8488',
                  rich: {
                    styleA: {
                      color: '#212325',
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                    styleB: {
                      color: '#212325',
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                    styleC: {
                      color: '#7F8488',
                      fontWeight: 'lighter',
                      fontSize: 14,
                    },
                    styleGreen: {
                      color: '#00953A', // Green color
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                    styleRed: {
                      color: '#D42600', // Red color
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                  },
                },
              }}
              name={'Assigned'}
              radius={[50, 90]}
              center={['50%', '42%']}
              rosetype={'area'}
              itemstyle={{
                borderRadius: 5,
                borderColor: '#F6F7F8',
                borderWidth: 3,
              }}
              labelline={{
                length: 1,
                length2: 1,
              }}
              label={{
                show: true,
                position: 'inside',
                formatter: '{c}',
                fontSize: 10,
                fontWeight: 'normal',
                color: '#000',
                backgroundColor: '#FFFFFF',
                borderColor: '#E5E7EB',
                borderWidth: 1,
                borderRadius: 0,
                padding: [2, 4],
              }}
              data={[
                { value: 300001, name: t('available'), itemStyle: { color: '#5D9D4A' } },
                { value: 250000, name: t('consumed'), itemStyle: { color: '#AFFFCD' } },
              ]}
            />
          </div>
        </div>
        <div className="bg-InterfaceSurfacecomponentmuted p-[12px] 3xl:p-[0.625vw]">
          <div className="text-InterfaceTexttitle text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
            {t('consumed')}
          </div>
          <div className="h-[270px] 3xl:h-[15.271vw]">
            <Piechart
              tooltip={{
                trigger: 'item',
                position: 'top',
                confine: true,
                formatter: () => {
                  return `<div style="padding-top:0px;">Total No Of<br/>Item - 600</div>`;
                },
              }}
              title={{
                text: t('total'),
                subtext: 'USD 250,000.00',
                right: 'center',
                top: '30%',
                bottom: 'center',
                textStyle: {
                  color: '#7F8488', // title text color
                  fontSize: 12,
                  fontWeight: 'normal',
                },
                subtextStyle: {
                  color: '#212325', // subtitle text color
                  fontSize: 12,
                  fontWeight: 'bold',
                },
              }}
              legends={{
                orient: 'vertical', // vertical layout
                bottom: 0,
                itemHeight: 10,
                itemWidth: 10,
                icon: 'circle',
                data: [t('billed'), t('unbilled')],
                formatter: (name) => {
                  if (name === t('billed')) return name + ': {styleA| USD 100,000.00' + '}';
                  if (name === t('unbilled')) return name + ': {styleB| USD 100,000.00' + '}';
                  return '{styleC|' + name + '}';
                },
                textStyle: {
                  color: '#7F8488',
                  rich: {
                    styleA: {
                      color: '#212325',
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                    styleB: {
                      color: '#212325',
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                    styleC: {
                      color: '#7F8488',
                      fontWeight: 'lighter',
                      fontSize: 14,
                    },
                  },
                },
              }}
              data={[
                { value: 100000, name: t('billed'), itemStyle: { color: '#73609B' } },
                { value: 150000, name: t('unbilled'), itemStyle: { color: '#4D9E99' } },
              ]}
              name={'Consumed'}
              radius={[50, 90]}
              center={['50%', '42%']}
              rosetype={'area'}
              itemstyle={{
                borderRadius: 5,
                borderColor: '#F6F7F8',
                borderWidth: 3,
              }}
              labelline={{
                length: 1,
                length2: 1,
              }}
              label={{
                show: true,
                position: 'inside',
                formatter: '{c}',
                fontSize: 10,
                fontWeight: 'normal',
                color: '#000',
                backgroundColor: '#FFFFFF',
                borderColor: '#E5E7EB',
                borderWidth: 1,
                borderRadius: 0,
                padding: [2, 4],
              }}
            />
          </div>
        </div>
        <div className="bg-InterfaceSurfacecomponentmuted p-[12px] 3xl:p-[0.625vw]">
          <div className="text-InterfaceTexttitle text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
            {t('billed')}
          </div>
          <div className="h-[270px] 3xl:h-[15.271vw]">
            <Piechart
              tooltip={{
                trigger: 'item',
                position: 'top',
                confine: true,
                formatter: () => {
                  return `<div style="padding-top:0px;">Total No Of<br/>Item - 600</div>`;
                },
              }}
              title={{
                text: t('total'),
                subtext: 'USD 100,000.00',
                right: 'center',
                top: '30%',
                bottom: 'center',
                textStyle: {
                  color: '#7F8488',
                  fontSize: 12,
                  fontWeight: 'normal',
                },
                subtextStyle: {
                  color: '#212325', // subtitle text color
                  fontSize: 12,
                  fontWeight: 'bold',
                },
              }}
              legends={{
                orient: 'vertical', // vertical layout
                bottom: 0,
                itemHeight: 10,
                itemWidth: 10,
                icon: 'circle',
                data: [t('notDue'), t('outstanding')],
                formatter: (name) => {
                  if (name === t('notDue')) return name + ': {styleA| USD 50,000.00' + '}';
                  if (name === t('outstanding')) return name + ': {styleB| USD 50,000.00' + '}';
                  return '{styleC|' + name + '}';
                },
                textStyle: {
                  color: '#7F8488',
                  rich: {
                    styleA: {
                      color: '#212325',
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                    styleB: {
                      color: '#212325',
                      fontWeight: 'bold',
                      fontSize: 13,
                    },
                    styleC: {
                      color: '#7F8488',
                      fontWeight: 'lighter',
                      fontSize: 14,
                    },
                  },
                },
              }}
              data={[
                { value: 50000, name: t('notDue'), itemStyle: { color: '#EB8A44' } },
                { value: 56000, name: t('outstanding'), itemStyle: { color: '#F8CA16' } },
              ]}
              name={'Billed'}
              radius={[50, 90]}
              center={['50%', '42%']}
              rosetype={'area'}
              itemstyle={{
                borderRadius: 5,
                borderColor: '#F6F7F8',
                borderWidth: 3,
              }}
              labelline={{
                length: 1,
                length2: 1,
              }}
              label={{
                show: true,
                position: 'inside',
                formatter: '{c}',
                fontSize: 10,
                fontWeight: 'normal',
                color: '#000',
                backgroundColor: '#FFFFFF',
                borderColor: '#E5E7EB',
                borderWidth: 1,
                borderRadius: 0,
                padding: [2, 4],
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
