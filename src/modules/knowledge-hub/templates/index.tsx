'use client';
import * as React from 'react';
import { Input, Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { Search } from 'lucide-react';
import Link from 'next/link';
import RowView from '../components/row-view';
import TableView from '../components/table-view';
import GridView from '../components/grid-view';
import AdvanceSearch from '../components/advance-search';
import { useTranslations } from 'next-intl';

export default function KnowledgeHubTemplate() {
  const t = useTranslations();
  const [activeIndexList, setActiveIndexList] = React.useState(0);

  return (
    <div className="w-full p-[20px] pr-[20px] xl:pr-[30px] 2xl:pr-[32px] 3xl:pr-[1.823vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] pl-[38px] xl:pl-[30px] 2xl:pl-[32px] 3xl:pl-[1.979vw]">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle mt-[16px] xl:mt-[18px] 3xl:mt-[1.042vw]">
          {t('knowledgeHub')}
        </div>
      </div>
      <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="flex items-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] gap-0.5">
          <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
            <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('download')}
          </Button>
          <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
            <i className="cloud-save font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('bookmark')}
          </Button>
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-10">
          <AdvanceSearch />
          <div className="col-span-8 pl-[16px] xl:pl-[18px] 3xl:pl-[1.042vw] space-y-[8px]">
            <div className="">
              <div className="flex justify-between items-center py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
                <div className="flex items-center gap-[10px] 3xl:gap-[0.625vw]">
                  <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 3xl:text-[0.938vw] font-semibold">
                    {t('allFiles')}
                  </div>
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12spx] 3xl:text-[0.729vw]">
                    {t('showingRecords')}
                  </div>
                </div>
                <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
                  <div className="relative w-[250px] md:w-[250px] lg:w-[300px] xl:w-[320px] 2xl:w-[380px] 3xl:w-[21.771vw] bg-white border border-InterfaceStrokedefault">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4 " />
                    <Input
                      type="text"
                      placeholder={t('search')}
                      className="pl-[35px] xl:pl-[35px] 2xl:pl-[35px] 3xl:pl-[1.823vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[14px] xl:text-[16px] 3xl:text-[0.833vw] h-[35px] xl:h-[39px] 2xl:h-[39px] 3xl:h-[1.983vw]"
                    />
                  </div>
                  <Link
                    href={''}
                    onClick={() => setActiveIndexList(0)}
                    className={`${
                      activeIndexList === 0
                        ? 'bg-BrandSupport1pure border border-BrandSupport1pure row-ic-active'
                        : 'bg-white border border-InterfaceStrokedefault row-ic'
                    } block  p-[16px] xl:p-[18px] 3xl:p-[0.938vw]  knowlegde-icons`}
                  ></Link>
                  <Link
                    href={''}
                    onClick={() => setActiveIndexList(1)}
                    className={`${
                      activeIndexList === 1
                        ? 'bg-BrandSupport1pure border border-BrandSupport1pure grid-ic-active'
                        : 'bg-white border border-InterfaceStrokedefault grid-ic'
                    } block p-[16px] xl:p-[18px] 3xl:p-[0.938vw] knowlegde-icons `}
                  ></Link>
                  <Link
                    href={''}
                    onClick={() => setActiveIndexList(2)}
                    className={`${
                      activeIndexList === 2
                        ? 'bg-BrandSupport1pure border border-BrandSupport1pure table-ic-active'
                        : 'bg-white border border-InterfaceStrokedefault table-ic'
                    } block  p-[16px] xl:p-[18px] 3xl:p-[0.938vw] knowlegde-icons `}
                  ></Link>
                </div>
              </div>
              <div>
                {activeIndexList === 0 ? (
                  <>
                    <RowView />
                  </>
                ) : null}
              </div>
              <div>
                {activeIndexList === 1 ? (
                  <>
                    <GridView />
                  </>
                ) : null}
              </div>
              <div>
                {activeIndexList === 2 ? (
                  <>
                    <TableView />
                  </>
                ) : null}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
