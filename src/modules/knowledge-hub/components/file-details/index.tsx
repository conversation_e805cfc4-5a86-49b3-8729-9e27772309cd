'use client';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types';
import Image from 'next/image';

export default function FileDetailsPopup({}: TermsSheetProps) {
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className="  hover:text-BrandSupport1pure  w-full px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center hover:bg-BrandNeutral100">
          <i className="cloud-readeye1 text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.729vw]"></i>
          View
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              File Details
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[480px] xl:h-[85vh] 2xl:h-[90vh] 3xl:h-[88vh]">
          <div className="space-y-[14px] xl:space-y-[16px] 3xl:space-y-[0.833vw]">
            <div className="space-y-[10px] xl:space-y-[12px] 3xl:space-y-[0.625vw]">
              <div className="flex justify-between border-b border-InterfaceStrokesoft pb-[10px] xl:pb-[12px] 3xl:pb-[0.625vw]">
                <div className="text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw]">
                  File Name
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Test file name 001
                </div>
              </div>
              <div className="flex justify-between border-b border-InterfaceStrokesoft pb-[10px] xl:pb-[12px] 3xl:pb-[0.625vw]">
                <div className="text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw]">
                  File Category
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Product Information
                </div>
              </div>
              <div className="flex justify-between border-b border-InterfaceStrokesoft pb-[10px] xl:pb-[12px] 3xl:pb-[0.625vw]">
                <div className="text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw]">
                  File Type
                </div>
                <div className="text-InterfaceTexttitle text-[14px] xl:text-[16px] 3xl:text-[0.833vw] font-semibold">
                  Document
                </div>
              </div>
            </div>
            <div className="mt-[5px] xl:mt-[5px] 3xl:mt-[0.26vw]">
              <div className="text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw] pb-[4px] xl:pb-[4px] ">
                File Description
              </div>
              <div className="text-InterfaceTexttitle text-[14px] xl:text-[16px] 3xl:text-[0.833vw] leading-[20px] xl:leading-[22px] 3xl:leading-[1.146vw]">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
                deserunt mollit anim id est laborum.
              </div>
            </div>
            <Button className=" bg-ButtonBaseDefault border border-InterfaceStrokesoft gap-2 items-center flex w-fit rounded-none font-[500] hover:bg-BrandNeutral100 text-InterfaceTextdefault">
              <i className="cloud-save font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-InterfaceTextdefault"></i>{' '}
              Bookmark
            </Button>
            <div className="grid grid-cols-12 gap-0  items-center">
              <div className="col-span-1 text-left">
                <div className="text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                  Tags :
                </div>
              </div>
              <div className="col-span-11 gap-1 flex">
                <div className="bg-[#E4F4E6] text-[#2A5130] py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                  Anti-Racist Practices
                </div>
                <div className="bg-[#E4F4E6] text-[#2A5130]  py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                  Tag 123
                </div>
                <div className="bg-[#E4F4E6] text-[#2A5130]  py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                  Tag 236
                </div>
              </div>
            </div>
          </div>
          <div className="mt-[28px] xl:mt-[32px] 3xl:mt-[1.667vw]">
            <div className="text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw]">
              Attachments:
            </div>
            <div className="border border-InterfaceStrokedefault bg-buttonbasedefaulthover2 mt-[8px] xl:mt-[8px] 3xl:mt-[0.417vw]">
              <div className="bg-InterfaceSurfacecomponentmuted py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] flex justify-between items-center">
                <div className="flex justify-between items-center gap-2 text-InterfaceTextdefault">
                  <i className="cloud-folder"></i>
                  <div>Test file name 001.pdf</div>
                </div>
                <div className="hover:bg-[#E5E7EB] border border-InterfaceStrokesoft hover:border-[#dfdfe2] flex items-center gap-2 text-InterfaceTextdefault p-[8px] xl:p-[8px] 3xl:p-[0.417vw]">
                  <i className="cloud-document-download1"></i>
                  <div className="cursor-pointer text-[12px] xl:text-[14px] 3xl:text-[0.729vw] ">
                    Download
                  </div>
                </div>
              </div>
              <Image src="/images/attatchment.png" alt="attatchment" width={555} height={610} />
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
