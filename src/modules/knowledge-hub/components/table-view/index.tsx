'use client';
import * as React from 'react';
import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import { ColumnDef } from '@tanstack/react-table';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import FileDetailsPopup from '../file-details';

export default function TableView() {
  const documenttypeIcons: Record<string, React.JSX.Element> = {
    Audio: <i className="cloud-audio-square text-[18px] xl:text-[20px] 3xl:text-[1.042vw]" />,
    Document: <i className="cloud-folder text-[18px] xl:text-[20px] 3xl:text-[1.042vw]" />,
    Website: <i className="cloud-world text-[18px] xl:text-[20px] 3xl:text-[1.042vw]" />,
    Video: <i className="cloud-video-play text-[18px] xl:text-[20px] 3xl:text-[1.042vw]" />,
    Image: <i className="cloud-gallery text-[18px] xl:text-[20px] 3xl:text-[1.042vw]" />,
  };
  const documenttypeStyles: Record<string, string> = {
    Audio: 'text-[14px] xl:text-[14px] 3xl:text-[0.729vw]',
    DOCX: 'text-[14px] xl:text-[14px] 3xl:text-[0.729vw]',
    TEXT: 'text-[14px] xl:text-[14px] 3xl:text-[0.729vw]',
  };

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'documenttype',
      enableSorting: true,
      size: 400,
      minSize: 400,
      maxSize: 400,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw]  font-medium justify-between"
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Document Type
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        const documenttype = row.getValue('documenttype') as string;
        const style = documenttypeStyles[documenttype] ?? '';
        const icon = documenttypeIcons[documenttype] ?? 'audio-ic';
        return (
          <div
            className={`relative flex items-center gap-2 text-InterfaceTextdefault rounded-none text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium ${style}`}
          >
            <span className="text-[18px] xl:text-[20px] 3xl:text-[1.042vw]">{icon}</span>
            <span className="ml-1 text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
              {documenttype}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'title',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw]  font-medium justify-between]"
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Title
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="text-BrandSupport1pure cursor-pointer">{row.getValue('title')}</div>
      ),
    },
    {
      accessorKey: 'datecreated',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Date Created
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastupdated',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-left  font-medium justify-between]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Last Updated
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'category',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col ">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-left  font-medium justify-between]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Category
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'tag',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-left  font-medium justify-between]"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Tag
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="bg-BrandHighlight100 text-BrandHighlight800 w-fit text-[10px] xl:text-[12px] 3xl:text-[0.625vw] py-1 px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium">
            {row.getValue('tag')}
          </div>
        );
      },
    },
    {
      accessorKey: 'action',
      size: 100,
      minSize: 100,
      maxSize: 100,
      header: () => (
        <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-medium text-center w-full">
          Action
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className="p-0 mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-[150px] xl:w-[150px] 3xl:w-[7.813vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <div className="">
                  <div className="hover:text-BrandSupport1pure hover:bg-BrandNeutral100">
                    <FileDetailsPopup open={false} onClose={() => {}} />
                  </div>
                  <div className=" hover:text-BrandSupport1pure px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                    <i className="cloud-document-download1 text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
                    Download
                  </div>
                  <div className=" hover:text-BrandSupport1pure px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                    <i className="cloud-save text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
                    Bookmark
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
    },
  ];

  const data = [
    {
      documenttype: 'Audio',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Document',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Website',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Video',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Image',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Audio',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Document',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Website',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Video',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Image',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Audio',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Document',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Website',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Video',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Image',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Audio',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Document',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Website',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Video',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Image',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'Microsoft',
    },
    {
      documenttype: 'Audio',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
    {
      documenttype: 'Document',
      title: 'Test File 001',
      datecreated: '05/05/2024',
      lastupdated: '03/03/2025',
      category: 'Product Info',
      tag: 'AWS',
    },
  ];

  type User = {
    documenttype: string;
    title: string;
    datecreated: string;
    lastupdated: string;
    category: string;
    tag: string;
  };

  return (
    <div className="bg-interfacesurfacecomponent tbl-shadow">
      <div className="">
        <DataTable data={data} columns={notificationColumns} withCheckbox={true} />
      </div>
      <TablePagination />
    </div>
  );
}
