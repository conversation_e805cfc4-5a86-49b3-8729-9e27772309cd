'use client';
import * as React from 'react';
// import { TablePagination } from '@/components/common/ui/pagination';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import FileDetailsPopup from '../file-details';

export default function RowView() {
  const t = useTranslations();
  const documents = [
    {
      id: 1,
      title: 'Support Hub Document and How Tos (1)',
      icons: ['cloud-folder'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      category: 'Category 01',
      tags: ['Anti-Racist Practices', 'Tag 123', 'Tag 236'],
    },
    {
      id: 1,
      title: 'Support Hub Video and How Tos (1)',
      icons: ['cloud-video-play'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      category: 'Category 01',
      tags: ['Anti-Racist Practices', 'Tag 123', 'Tag 236'],
    },
    {
      id: 1,
      title: 'Test Audio File 0069',
      icons: ['cloud-audio-square'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      category: 'Category 01',
      tags: ['Anti-Racist Practices', 'Tag 123', 'Tag 236'],
    },
    {
      id: 1,
      title: 'Folder one (4)',
      icons: ['cloud-foldericon'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      category: 'Category 01',
      tags: ['Anti-Racist Practices', 'Tag 123', 'Tag 236'],
    },
    {
      id: 1,
      title: 'Test Audio File 3 (25-01-2025) (1)',
      icons: ['cloud-audio-square'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      category: 'Category 01',
      tags: ['Anti-Racist Practices', 'Tag 123', 'Tag 236'],
    },
    {
      id: 1,
      title: 'Folder one (4)',
      icons: ['cloud-foldericon'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      category: 'Category 01',
      tags: ['Anti-Racist Practices', 'Tag 123', 'Tag 236'],
    },
    {
      id: 1,
      title: 'Support Hub Video and How Tos (1)',
      icons: ['cloud-video-play'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      category: 'Category 01',
      tags: ['Anti-Racist Practices', 'Tag 123', 'Tag 236'],
    },
    // Add more objects here as needed
  ];

  return (
    <div className="">
      <div className="h-[500px] xl:h-[652px] 3xl:h-[33.958vw] overflow-auto">
        <div className="grid grid-cols-1 gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw] ">
          {documents.map((doc) => (
            <div
              key={doc.id}
              className="col-span-1 bg-interfacesurfacecomponent border hover:border-BrandSupport17001 hover:bg-BrandNeutral50 p-[14px] xl:p-[16px] 3xl:p-[0.833vw] cardShadow"
            >
              <div className="grid grid-cols-2 gap-2 ">
                <div className="col-span-1 flex flex-col justify-between">
                  <div className="flex justify-start items-center gap-2">
                    {doc.icons?.map((icon, index) => (
                      <i key={index} className={`${icon} text-BrandSupport1pure`} />
                    ))}
                    <div className="text-BrandSupport1pure text-[16px] lg:text-[14px] 3xl:text-[0.833vw] font-medium leading-tight">
                      {doc.title}
                    </div>
                  </div>
                  <div className="text-[14px] lg:text-[10px] 3xl:text-[0.729vw] flex items-center gap-3">
                    <div className="text-InterfaceTextsubtitle">
                      {t('dateCreated')}:{' '}
                      <span className="text-InterfaceTextdefault">{doc.createdDate}</span>
                    </div>
                    <div className="text-InterfaceTextsubtitle">
                      {t('lastUpdatedDate')}:{' '}
                      <span className="text-InterfaceTextdefault">{doc.updatedDate}</span>
                    </div>
                  </div>
                </div>

                <div className="col-span-1 flex items-start justify-between ">
                  <div className="space-y-[12px] xl:space-y-[14px] 3xl:space-y-[0.729vw]">
                    <div className="grid grid-cols-12 gap-1  items-center">
                      <div className="col-span-3 lg:col-span-2 text-right">
                        <div className="text-InterfaceTextsubtitle text-[10px] lg:text-[8px] 3xl:text-[0.625vw]">
                          {t('category')} :
                        </div>
                      </div>
                      <div className="col-span-9 lg:col-span-10 ">
                        <div className="text-InterfaceTextdefault w-fit bg-BrandNeutral100 text-[10px] xl:text-[10px] 3xl:text-[0.625vw]  py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw]">
                          {doc.category}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-12 gap-1  items-center">
                      <div className="col-span-3 lg:col-span-2 text-right">
                        <div className="text-InterfaceTextsubtitle text-[10px] lg:text-[8px] 3xl:text-[0.625vw]">
                          {t('tags')} :
                        </div>
                      </div>
                      <div className="col-span-9 lg:col-span-10 gap-1 flex flex-wrap lg:flex-nowrap">
                        {doc.tags.map((tag, idx) => (
                          <div
                            key={idx}
                            className="bg-[#E4F4E6] text-[#2A5130] py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[10px] xl:text-[10px] 3xl:text-[0.625vw] flex items-center"
                          >
                            {tag}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Image src="/images/more.svg" height={18} width={18} alt="more options" />
                      </PopoverTrigger>
                      <PopoverContent className="p-0 mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-[150px] xl:w-[150px] 3xl:w-[7.813vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                        <div className="hover:text-BrandSupport1pure">
                          <div className="hover:text-BrandSupport1pure  hover:bg-BrandNeutral100">
                            <FileDetailsPopup open={false} onClose={() => {}} />
                          </div>
                          <div className=" hover:text-BrandSupport1pure px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] text-InterfaceTextdefault flex gap-2 items-center border-t border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                            <i className="cloud-document-download1 text-InterfaceTextdefault text-[17px] xl:text-[17px] 3xl:text-[0.829vw]" />
                            {t('download')}
                          </div>
                          <div className="hover:text-BrandSupport1pure px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] text-InterfaceTextdefault flex gap-2 items-center border-t border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                            <i className="cloud-save text-InterfaceTextdefault text-[17px] xl:text-[17px] 3xl:text-[0.829vw]" />
                            {t('bookmark')}
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="bg-interfacesurfacecomponent tbl-shadow">
        {/* <TablePagination /> */}
        <div className="flex items-center justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 3xl:py-[0.333vw] w-full border-b border-y-[2px] border-t-none rounded-bl-1 rounded-br-1 shadow-sm shadow-InterfaceStrokesoft">
          <div className="flex items-center justify-start gap-2">
            <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex items-center">
              <span className="font-normal text-InterfaceTextdefault">1-7 of</span>
              <span className=" font-medium text-InterfaceTexttitle pl-1"> 1000</span>
            </div>
            <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex items-center">
              <span className=" font-normal text-InterfaceTextdefault">Showing</span>
              <span className=" font-medium text-InterfaceTexttitle pl-1"> 7 /</span>
              <Select>
                <SelectTrigger className="h-7 w-18 py-1 px-[5px] xl:px-[5px] 2xl:px-[5px] 3xl:px-[0.26vw] rounded-[2px] border-none placeholder:text-InterfaceTextdefault">
                  <SelectValue
                    placeholder="page "
                    className=" placeholder:text-InterfaceTextdefault"
                  />
                </SelectTrigger>
                <SelectContent className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault bg-white z-50 cursor-pointer rounded-[2px] ">
                  <SelectItem value="page" className="  ">
                    page
                  </SelectItem>
                  <SelectItem value="20" className="">
                    20
                  </SelectItem>
                  <SelectItem value="50" className="">
                    50
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* <div className="flex items-center gap-2">
          <span className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex-nowrap text-InterfaceTextdefault">
            {t('itemPerPage')}:
          </span>
          <Select>
            <SelectTrigger className="h-8 w-16 px-2 py-1 rounded-[2px] border border-InterfaceStrokesoft placeholder:text-InterfaceTextdefault">
              <SelectValue placeholder="10 " className=" placeholder:text-InterfaceTextdefault" />
            </SelectTrigger>
            <SelectContent className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault bg-white z-50 cursor-pointer rounded-[2px] ">
              <SelectItem value="10" className="  ">
                10
              </SelectItem>
              <SelectItem value="20" className="">
                20
              </SelectItem>
              <SelectItem value="50" className="">
                50
              </SelectItem>
              <SelectItem value="100" className="">
                100
              </SelectItem>
            </SelectContent>
          </Select>
        </div> */}

            <div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <div>
                      <i className="cloud-previous text-[12px] xl:text-[12px] 3xl:text-[0.629vw] text-BrandSupport1pure"></i>
                    </div>
                  </PaginationItem>

                  <PaginationItem className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm">
                      1
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm" className="text-BrandSupport1pure">
                      2
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm">
                      3
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationEllipsis />
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm">
                      99
                    </PaginationLink>
                  </PaginationItem>
                  <div>
                    <i className="cloud-next  text-[12px] xl:text-[12px] 3xl:text-[0.629vw] text-BrandSupport1pure"></i>
                  </div>
                  <PaginationItem></PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
