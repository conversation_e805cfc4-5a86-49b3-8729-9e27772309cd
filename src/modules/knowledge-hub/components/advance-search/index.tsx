'use client';
import * as React from 'react';
import {
  Input,
  Label,
  // Select,
  // SelectContent,
  // SelectGroup,
  // SelectItem,
  // SelectTrigger,
  // SelectValue,
  Switch,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Search } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';
import { Select2 } from '@/components/common/ui/combo-box';
import { useTranslations } from 'next-intl';

export default function AdvanceSearch() {
  const t = useTranslations();
  return (
    <div className="col-span-2 border bg-white border-InterfaceStrokesoft p-[16px] xl:p-[18px] 3xl:p-[1.042vw]">
      <div className="mb-[22px] xl:mb-[24px] 3xl:mb-[1.458vw]">
        <div className="text-text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
          {t('advancedSearch')}
        </div>
      </div>
      <div className="space-y-[22px] xl:space-y-[24px] 3xl:space-y-[1.25vw]">
        <div className="space-y-[14px] xl:space-y-[16px] 3xl:space-y-[0.833vw]">
          <div className="relative w-full bg-white border border-InterfaceStrokedefault">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4 " />
            <Input
              type="text"
              placeholder={t('search')}
              className="pl-[35px] xl:pl-[35px] 2xl:pl-[35px] 3xl:pl-[1.823vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[14px] xl:text-[16px] 3xl:text-[0.833vw]"
            />
          </div>
          <div className="flex flex-wrap gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <div className="bg-BrandHighlight100 text-BrandHighlight800 border border-BrandPrimary300new py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[12px] xl:text-[10px] 3xl:text-[0.625vw] cursor-pointer flex items-center">
              {t('all')}
            </div>
            <div className="bg-BrandHighlight100 text-BrandHighlight800 border border-BrandPrimary300new py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[12px] xl:text-[10px] 3xl:text-[0.625vw] cursor-pointer flex items-center">
              {t('audio')}
            </div>
            <div className="bg-BrandHighlight100 text-BrandHighlight800 border border-BrandPrimary300new py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[12px] xl:text-[10px] 3xl:text-[0.625vw] cursor-pointer flex items-center">
              {t('document')}
            </div>
            <div className="bg-BrandHighlight100 text-BrandHighlight800 border border-BrandPrimary300new py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[12px] xl:text-[10px] 3xl:text-[0.625vw] cursor-pointer flex items-center">
              {t('image')}
            </div>
            <div className="bg-BrandHighlight100 text-BrandHighlight800 border border-BrandPrimary300new py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[12px] xl:text-[10px] 3xl:text-[0.625vw] cursor-pointer flex items-center">
              {t('video')}
            </div>
            <div className="bg-BrandHighlight100 text-BrandHighlight800 border border-BrandPrimary300new py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[12px] xl:text-[10px] 3xl:text-[0.625vw] cursor-pointer flex items-center">
              {t('website')}
            </div>
          </div>
        </div>
        <div className="relative ">
          <label
            htmlFor="required-email"
            className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
          >
            {t('category')}
          </label>
          <div className="mt-2">
            <Controller
              name="endCustomer"
              control={useForm().control}
              render={({ field }) => (
                <Select2
                  options={[
                    { label: 'All', value: 'All' },
                    { label: 'India', value: 'India' },
                    { label: 'UAE', value: 'UAE' },
                  ]}
                  placeholder="--Select--"
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              )}
            />
          </div>
        </div>
        <div className="relative ">
          <label
            htmlFor="required-email"
            className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
          >
            {t('format')}
          </label>
          <div className="mt-2">
            <Controller
              name="endCustomer"
              control={useForm().control} // Ensure control is passed here
              render={({ field }) => (
                <Select2
                  options={[
                    { label: 'All', value: 'All' },
                    { label: 'India', value: 'India' },
                    { label: 'UAE', value: 'UAE' },
                  ]}
                  placeholder="--Select--"
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              )}
            />
          </div>
        </div>
        <div className="relative ">
          <label
            htmlFor="required-email"
            className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
          >
            {t('tags')}
          </label>
          <div className="mt-2">
            <Controller
              name="endCustomer"
              control={useForm().control} // Ensure control is passed here
              render={({ field }) => (
                <Select2
                  options={[
                    { label: 'All', value: 'All' },
                    { label: 'India', value: 'India' },
                    { label: 'UAE', value: 'UAE' },
                  ]}
                  placeholder="--Select--"
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              )}
            />
          </div>
        </div>
        <div className="cust-switch space-y-[10px] xl:space-y-[10px] 3xl:space-y-[0.525vw]">
          <label
            htmlFor="required-email"
            className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
          >
            {t('showBookmarkedFile')}
          </label>
          <div className="flex items-center space-x-2">
            <Switch
              id="Bookmark"
              defaultChecked
              className="data-[state=unchecked]:bg-[#d1d1d1] data-[state=checked]:bg-InterfaceTextprimary  peer inline-flex shrink-0 cursor-pointer items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none"
            />
            <Label htmlFor="Bookmark" className="text-InterfaceTextsubtitle">
              {t('yes')}
            </Label>
          </div>
        </div>
      </div>
    </div>
  );
}
