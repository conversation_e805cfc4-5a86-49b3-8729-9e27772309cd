'use client';
import * as React from 'react';
// import { TablePagination } from '@/components/common/ui/pagination';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import FileDetailsPopup from '../file-details';

export default function GridView() {
  const documents = [
    {
      category: 'Category 06',
      title: 'Support Hub Document and How Tos (1)',
      icons: ['cloud-folder'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Support Hub Video and How Tos (1)',
      icons: ['cloud-video-play'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Test Audio File 0069',
      icons: ['cloud-audio-square'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Folder one (4)',
      icons: ['cloud-foldericon'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Test Audio File 3 (25-01-2025) (1)',
      icons: ['cloud-folder'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Folder one (4)',
      icons: ['cloud-foldericon'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Support Hub Video and How Tos (1)',
      icons: ['cloud-video-play'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Test Audio File 0069',
      icons: ['cloud-audio-square'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Support Hub Document and How Tos (1)',
      icons: ['cloud-folder'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Test Audio File 0069',
      icons: ['cloud-audio-square'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Support Hub Document and How Tos (1)',
      icons: ['cloud-folder'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    {
      category: 'Category 06',
      title: 'Support Hub Video and How Tos (1)',
      icons: ['cloud-video-play'],
      createdDate: '10/05/2024',
      updatedDate: '28/05/2024',
      tags: ['Product', 'AWS', 'Platform'],
    },
    // You can add more items here...
  ];

  return (
    <div className="">
      <div className="h-[500px] xl:h-[652px] 3xl:h-[33.958vw] overflow-auto">
        <div className="grid grid-cols-2 xl:grid-cols-3 gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw]">
          {documents.map((doc, index) => (
            <div
              key={index}
              className="col-span-1 bg-interfacesurfacecomponent border hover:border-BrandSupport17001 hover:bg-BrandNeutral50 p-[16px] xl:p-[14px] 3xl:p-[0.833vw] cardShadow"
            >
              <div className="flex justify-between items-center">
                <div className="text-InterfaceTextdefault bg-BrandNeutral100 text-[12px] xl:text-[10px] 3xl:text-[0.625vw] py-1 px-2">
                  {doc.category}
                </div>
                <div className="flex justify-between">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Image src="/images/more.svg" height={18} width={18} alt="more options" />
                    </PopoverTrigger>
                    <PopoverContent className="p-0 mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-[150px] xl:w-[150px] 3xl:w-[7.813vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                      <div>
                        <div className="hover:text-BrandSupport1pure  hover:bg-BrandNeutral100">
                          <FileDetailsPopup open={false} onClose={() => { }} />
                        </div>
                        <div className="hover:text-BrandSupport1pure px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                          <i className="cloud-document-download1 text-InterfaceTextdefault text-[17px] xl:text-[17px] 3xl:text-[0.829vw]"></i>
                          Download
                        </div>
                        <div className="hover:text-BrandSupport1pure px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                          <i className="cloud-save text-InterfaceTextdefault text-[17px] xl:text-[17px] 3xl:text-[0.829vw]"></i>
                          Bookmark
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="flex justify-start items-center gap-2 mt-[10px] xl:mt-[12px] 3xl:mt-[0.625vw] mb-[8px] xl:mb-[8px] 3xl:mb-[0.417vw] min-h-[3.62vw] xl:min-h-[3vw] 2xl:xl:min-h-[2vw]">
                {doc.icons?.map((icon, index) => (
                  <i key={index} className={`${icon} text-BrandSupport1pure`} />
                ))}
                <div className="text-BrandSupport1pure text-[16px] xl:text-[12px] 3xl:text-[0.833vw] font-medium leading-tight">
                  {doc.title}
                </div>
              </div>

              <div className="text-[14px] xl:text-[10px] 3xl:text-[0.729vw] space-y-0.5">
                <div className="text-InterfaceTextsubtitle">
                  Date Created :{' '}
                  <span className="text-InterfaceTextdefault">{doc.createdDate}</span>
                </div>
                <div className="text-InterfaceTextsubtitle">
                  Last Updated Date :{' '}
                  <span className="text-InterfaceTextdefault">{doc.updatedDate}</span>
                </div>
              </div>

              <div className="flex items-center justify-start gap-1 mt-[12px] xl:mt-[14px] 3xl:mt-[0.729vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[8px] 3xl:text-[0.625vw]">
                  Tags :
                </div>
                <div className="flex wrap gap-1">
                  {doc.tags.map((tag, tagIndex) => (
                    <div
                      key={tagIndex}
                      className="bg-BrandHighlight100 text-BrandHighlight800 py-[2px] px-[8px] xl:px-[8px] 3xl:px-[0.417vw] font-medium text-[12px] xl:text-[10px] 3xl:text-[0.625vw]"
                    >
                      {tag}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="bg-interfacesurfacecomponent tbl-shadow">
        {/* <TablePagination /> */}
        <div className="flex items-center justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 3xl:py-[0.333vw] w-full border-b border-y-[2px] border-t-none rounded-bl-1 rounded-br-1 shadow-sm shadow-InterfaceStrokesoft">
          <div className="flex items-center justify-start gap-2">
            <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex items-center">
              <span className="font-normal text-InterfaceTextdefault">1-7 of</span>
              <span className=" font-medium text-InterfaceTexttitle pl-1"> 1000</span>
            </div>
            <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex items-center">
              <span className=" font-normal text-InterfaceTextdefault">Showing</span>
              <span className=" font-medium text-InterfaceTexttitle pl-1"> 7 /</span>
              <Select>
                <SelectTrigger className="h-7 w-18 py-1 px-[5px] xl:px-[5px] 2xl:px-[5px] 3xl:px-[0.26vw] rounded-[2px] border-none placeholder:text-InterfaceTextdefault">
                  <SelectValue
                    placeholder="page "
                    className=" placeholder:text-InterfaceTextdefault"
                  />
                </SelectTrigger>
                <SelectContent className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault bg-white z-50 cursor-pointer rounded-[2px] ">
                  <SelectItem value="page" className="  ">
                    page
                  </SelectItem>
                  <SelectItem value="20" className="">
                    20
                  </SelectItem>
                  <SelectItem value="50" className="">
                    50
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* <div className="flex items-center gap-2">
                  <span className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex-nowrap text-InterfaceTextdefault">
                    {t('itemPerPage')}:
                  </span>
                  <Select>
                    <SelectTrigger className="h-8 w-16 px-2 py-1 rounded-[2px] border border-InterfaceStrokesoft placeholder:text-InterfaceTextdefault">
                      <SelectValue placeholder="10 " className=" placeholder:text-InterfaceTextdefault" />
                    </SelectTrigger>
                    <SelectContent className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault bg-white z-50 cursor-pointer rounded-[2px] ">
                      <SelectItem value="10" className="  ">
                        10
                      </SelectItem>
                      <SelectItem value="20" className="">
                        20
                      </SelectItem>
                      <SelectItem value="50" className="">
                        50
                      </SelectItem>
                      <SelectItem value="100" className="">
                        100
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}

            <div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <div>
                      <i className="cloud-previous text-[12px] xl:text-[12px] 3xl:text-[0.629vw] text-BrandSupport1pure"></i>
                    </div>
                  </PaginationItem>

                  <PaginationItem className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm">
                      1
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm" className="text-BrandSupport1pure">
                      2
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm">
                      3
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationEllipsis />
                  </PaginationItem>
                  <PaginationItem className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault">
                    <PaginationLink href="#" size="sm">
                      99
                    </PaginationLink>
                  </PaginationItem>
                  <div>
                    <i className="cloud-next  text-[12px] xl:text-[12px] 3xl:text-[0.629vw] text-BrandSupport1pure"></i>
                  </div>
                  <PaginationItem></PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
