import { z } from 'zod';

// Common regex patterns
const nameRegex = /^[A-Za-z0-9-' ]+$/;
const companyNameRegex = /^[A-Za-z0-9-& ]+$/;
// const redingtonReferenceRegex = /^[A-Za-z0-9- ]+$/;
const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
const pincodeRegex = /^[0-9]{6}$/;
// const phoneRegex = /^[0-9]{8,15}$/;

const LegalStatusEnum = z.enum([
  'Proprietorship',
  'Partnership',
  'Private Limited Company',
  'Public Limited Company',
  'Limited Liability Partnership',
  'Hindu Undivided Family',
  'Society/ Club/ Trust/ AOP',
  'Statutory Body',
  'Public Sector Undertaking',
  'Local Authority',
  'One Person Company',
  'Foreign Company',
  'Company Limited by Guarantee',
  'OPC PRIVATE LIMITED COMPANY',
  'OPC Private Limited',
  'RETAIL SHOP',
  '',
]);

type LegalStatusEnum = z.infer<typeof LegalStatusEnum>;

export const companyTypesRequiringCIN: LegalStatusEnum[] = [
  'Private Limited Company',
  'Public Limited Company',
  'Limited Liability Partnership',
  'One Person Company',
  'Foreign Company',
  'Company Limited by Guarantee',
  'OPC PRIVATE LIMITED COMPANY',
  'OPC Private Limited',
];

// Company Information Schema
export const companyInformationSchema = z
  .object({
    gstNumber: z.string().length(15, { message: 'GST Number must be 15 characters long' }),

    panNumber: z
      .string()
      .min(1, { message: 'PAN Number is required' })
      .regex(panRegex, { message: 'Invalid PAN Number format' }),

    isSezCompany: z.boolean(),

    companyName: z
      .string()
      .min(1, { message: 'Company Name is required' })
      .max(100, { message: 'Company Name must be at most 100 characters' })
      .regex(companyNameRegex, {
        message: 'Company Name can only contain letters, numbers, spaces, hyphens, and ampersands',
      }),

    cinNumber: z.string().optional(),

    website: z
      .string()
      .optional()
      .refine((val) => !val || val === '' || z.string().url().safeParse(val).success, {
        message: 'Invalid website URL',
      }),

    // incorporationDate: z.string().min(1, { message: 'Incorporation Date is required' }),

    address: z.object({
      street1: z
        .string()
        .min(1, { message: 'Address Line 1 is required' })
        .max(200, { message: 'Address Line 1 must be at most 200 characters' }),

      street2: z
        .string()
        .max(200, { message: 'Address Line 2 must be at most 200 characters' })
        .optional(),

      countryBusiness: z.string().min(1, { message: 'Country is required' }),

      city: z
        .string()
        .min(1, { message: 'City is required' })
        .max(50, { message: 'City must be at most 50 characters' }),

      state: z
        .string()
        .min(1, { message: 'State is required' })
        .max(50, { message: 'State must be at most 50 characters' }),

      postalCode: z
        .string()
        .min(1, { message: 'Pincode is required' })
        .regex(pincodeRegex, { message: 'Invalid pincode format' }),

      legalStatus: LegalStatusEnum,
    }),

    // communicationAddress: z.object({
    //   addressLine1: z
    //     .string()
    //     .min(1, { message: 'Address Line 1 is required' })
    //     .max(200, { message: 'Address Line 1 must be at most 200 characters' }),

    //   addressLine2: z
    //     .string()
    //     .max(200, { message: 'Address Line 2 must be at most 200 characters' })
    //     .optional(),

    //   city: z
    //     .string()
    //     .min(1, { message: 'City is required' })
    //     .max(50, { message: 'City must be at most 50 characters' }),

    //   state: z
    //     .string()
    //     .min(1, { message: 'State is required' })
    //     .max(50, { message: 'State must be at most 50 characters' }),

    //   pincode: z
    //     .string()
    //     .min(1, { message: 'Pincode is required' })
    //     .regex(pincodeRegex, { message: 'Invalid pincode format' }),

    //   country: z.string().min(1, { message: 'Country is required' }),

    //   sameAsRegistered: z.boolean(),
    // }),
    additionalInfo: z.object({
      numberOfOfficesInRegion: z
        .union([z.string().regex(/^\d+$/, { message: 'Must be numeric value' }), z.literal('')])
        .optional(),

      otherCountriesWithOffices: z.array(z.string()).optional(),

      numberOfWareHousesInRegion: z
        .union([z.string().regex(/^\d+$/, { message: 'Must be numeric value' }), z.literal('')])
        .optional(),

      numberOfEmployees: z
        .union([z.string().regex(/^\d+$/, { message: 'Must be numeric value' }), z.literal('')])
        .optional(),

      numberOfSalesStaff: z
        .union([z.string().regex(/^\d+$/, { message: 'Must be numeric value' }), z.literal('')])
        .optional(),

      numberOfTechnicalStaff: z
        .union([z.string().regex(/^\d+$/, { message: 'Must be numeric value' }), z.literal('')])
        .optional(),

      twitterAccount: z
        .string()
        .optional()
        .refine((val) => !val || val === '' || z.string().url().safeParse(val).success, {
          message: 'Invalid Twitter URL',
        }),

      facebookAccount: z
        .string()
        .optional()
        .refine((val) => !val || val === '' || z.string().url().safeParse(val).success, {
          message: 'Invalid Facebook URL',
        }),

      linkedinAccount: z
        .string()
        .optional()
        .refine((val) => !val || val === '' || z.string().url().safeParse(val).success, {
          message: 'Invalid LinkedIn URL',
        }),

      instagramAccount: z
        .string()
        .optional()
        .refine((val) => !val || val === '' || z.string().url().safeParse(val).success, {
          message: 'Invalid Instagram URL',
        }),
    }),
    contactInfo: z.object({
      countryCode: z.string(),

      mobileNumber: z
        .string()
        .min(1, { message: 'Mobile number is required' })
        .max(15, { message: 'Mobile number must be at most 15 digits' }),
    }),

    // hasSalesReference: z.boolean(),

    // redingtonReference: z
    //   .string()
    //   .max(100, { message: 'Reference must be at most 100 characters' })
    //   .regex(redingtonReferenceRegex, {
    //     message: 'Reference can only contain letters, numbers, spaces, and hyphens',
    //   })
    //   .optional(),

    // acceptTermsCondition: z
    //   .boolean()
    //   .refine((val) => val, { message: 'You must accept the terms and conditions' }),

    // recaptcha: z.boolean().refine((val) => val, { message: "Please verify you're not a robot" }),

    // isEmailVerified: z.boolean(),
    // isMobileVerified: z.boolean(),
  })
  .superRefine((data, ctx) => {
    const requiresCIN = companyTypesRequiringCIN.includes(data.address.legalStatus);

    if (requiresCIN && (!data.cinNumber || data.cinNumber.trim() === '')) {
      ctx.addIssue({
        path: ['cinNumber'],
        message: 'CIN Number is required for this legal status.',
        code: z.ZodIssueCode.custom,
      });
    }
  });

// Business Information Schema
export const keyContactSchema = z.object({
  firstName: z
    .string()
    .min(1, { message: 'First name is required' })
    .regex(nameRegex, { message: 'Invalid first name format' }),
  lastName: z
    .string()
    .min(1, { message: 'Last name is required' })
    .regex(nameRegex, { message: 'Invalid last name format' }),
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Invalid email address' }),
  mobileNumber: z
    .string()
    .min(1, { message: 'Mobile number is required' })
    .refine((val) => val.length >= 8 && val.length <= 15, {
      message: 'Mobile number must be between 8 and 15 digits',
    }),
});

export const optionalKeyContactSchema = z
  .object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    email: z.string().email({ message: 'Invalid email address' }).optional().or(z.literal('')),
    mobileNumber: z.string().optional(),
  })
  .optional();

export const businessInformationSchema = z.object({
  directorDetails: keyContactSchema,
  salesDetails: optionalKeyContactSchema,
  accountsDetails: optionalKeyContactSchema,
  authorizedSignatory: z
    .array(keyContactSchema)
    .min(1, { message: 'At least one authorized signatory is required' })
    .max(3, { message: 'Maximum 3 authorized signatories are allowed' }),
});

// Document Information Schema
export const documentFileSchema = z.object({
  id: z.string(),
  name: z.string().min(1, { message: 'Document name is required' }),
  file: z.any().nullable(),
  uploadedAt: z.string().nullable(),
  status: z.enum(['pending', 'uploaded', 'verified', 'rejected']),
});

export const documentSectionSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  isRequired: z.boolean(),
  documents: z.array(documentFileSchema),
  maxFileSize: z.number(),
  allowedFormats: z.array(z.string()),
});

export const documentInformationSchema = z
  .object({
    sections: z.array(documentSectionSchema),
  })
  .superRefine((data, ctx) => {
    // Check if all required sections have at least one document
    data.sections.forEach((section, index) => {
      if (section.isRequired && section.documents.length === 0) {
        ctx.addIssue({
          path: ['sections', index, 'documents'],
          message: `${section.title} is required`,
          code: z.ZodIssueCode.custom,
        });
      }
    });
  });

// Combined schema for the entire form
export const partnerRegistrationSchema = z.object({
  companyInformation: companyInformationSchema,
  businessInformation: businessInformationSchema,
  documentInformation: documentInformationSchema,
});

// brand preferences
export const BrandFormSchema = z.object({
  vendorId: z.string().optional(),
  brandId: z.array(z.string()).nonempty({ message: 'Please select at least one brand' }).optional(),
  brandCategoryId: z
    .array(z.string())
    .nonempty({ message: 'Please select at least one brand category' })
    .optional(),
});

// Type exports
export type CompanyInformationFormData = z.infer<typeof companyInformationSchema>;
export type BusinessInformationFormData = z.infer<typeof businessInformationSchema>;
export type DocumentInformationFormData = z.infer<typeof documentInformationSchema>;
export type PartnerRegistrationFormData = z.infer<typeof partnerRegistrationSchema>;
export type KeyContactFormData = z.infer<typeof keyContactSchema>;
export type BrandFormData = z.infer<typeof BrandFormSchema>;
