'use client';
import * as React from 'react';
import { useEffect } from 'react';
import {
  Input,
  Label,
  Button,
  ScrollArea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useDocumentInformation } from '../../hooks/usePartnerRegistration';
import { DocumentFile } from '@/store/slices/partner-registration';
import toast from 'react-hot-toast';
import { Upload, FileText, X } from 'lucide-react';
import { usePresignedUpload } from 'next-s3-upload';
import { logger } from '@/lib/utils/logger';
import { useValidation } from '@/lib/hooks/step-validation';

export default function DocumentInformation() {
  const {
    documentInfo,
    companyInfo,
    addPassportDoc,
    addAuditDoc,
    addTaxExemptionDoc,
    removePassportDoc,
    removeAuditDoc,
    removeTaxExemptionDoc,
    updateTaxExpiryDate,
  } = useDocumentInformation();

  const { setStepValid, validationTriggers } = useValidation();
  const { uploadToS3 } = usePresignedUpload();

  // Track validation trigger changes
  const prevValidationTriggerRef = React.useRef(validationTriggers[2] || 0);

  // Determine if form is valid based on document requirements
  const isFormValid = React.useMemo(() => {
    // For SEZ companies, tax exemption document and expiry date are required
    if (companyInfo.isSezCompany) {
      return (
        documentInfo.taxExemptionDocument.length > 0 && Boolean(documentInfo.taxExemptionExpiryDate)
      );
    }
    // For non-SEZ companies, no documents are strictly required
    return true;
  }, [
    companyInfo.isSezCompany,
    documentInfo.taxExemptionDocument.length,
    documentInfo.taxExemptionExpiryDate,
  ]);

  // Update parent validation state whenever form validity changes
  useEffect(() => {
    setStepValid(2, isFormValid); // Step 2 is Document Information
  }, [isFormValid, setStepValid]);

  // Handle validation triggers from parent - only when trigger count actually changes
  useEffect(() => {
    const currentTrigger = validationTriggers[2] || 0;
    if (currentTrigger > prevValidationTriggerRef.current) {
      prevValidationTriggerRef.current = currentTrigger;
      // Show a single consolidated error message
      if (!isFormValid && companyInfo.isSezCompany) {
        const missingItems = [];
        if (documentInfo.taxExemptionDocument.length === 0) {
          missingItems.push('Tax Exemption Supporting Document');
        }
        if (!documentInfo.taxExemptionExpiryDate) {
          missingItems.push('Tax Exemption Expiry Date');
        }
        if (missingItems.length > 0) {
        }
      }
    }
  }, [
    validationTriggers,
    isFormValid,
    companyInfo.isSezCompany,
    documentInfo.taxExemptionDocument.length,
    documentInfo.taxExemptionExpiryDate,
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // No need for additional validation here as it's handled by the validation context
    toast.success('Document information saved successfully!');
  };

  const validateFile = (file: File): boolean => {
    // Check file type
    if (file.type !== 'application/pdf') {
      toast.error('Please upload a valid PDF (max 30MB).');
      return false;
    }

    // Check file size (30MB limit)
    const maxSize = 30 * 1024 * 1024; // 30MB in bytes
    if (file.size > maxSize) {
      toast.error('Please upload a valid PDF (max 30MB).');
      return false;
    }

    return true;
  };

  const handleFileUpload = async (file: File, section: 'passport' | 'audit' | 'tax') => {
    if (!validateFile(file)) return;

    try {
      logger.log('Starting file upload:', {
        fileName: file.name,
        size: file.size,
        type: file.type,
      });

      const { url } = await uploadToS3(file);

      logger.log('Upload successful, received URL:', url);

      const newDocument: Omit<DocumentFile, 'id'> = {
        name: file.name,
        url,
        uploadedAt: new Date().toISOString(),
        status: 'uploaded',
        size: file.size,
      };

      switch (section) {
        case 'passport':
          addPassportDoc(newDocument);
          break;
        case 'audit':
          addAuditDoc(newDocument);
          break;
        case 'tax':
          addTaxExemptionDoc(newDocument);
          break;
      }

      toast.success('Document uploaded successfully.');
    } catch (error) {
      logger.log('Error uploading document', error);
      logger.log('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });
      toast.error('Failed to upload document. Please try again.');
    }
  };

  const FileUploadSection = ({
    title,
    description,
    documents,
    onUpload,
    onRemove,
    maxFiles,
    isRequired = false,
  }: {
    title: string;
    description: string;
    documents: DocumentFile[];
    onUpload: (file: File) => Promise<void>;
    onRemove: (docId: string) => void;
    maxFiles: number;
    isRequired?: boolean;
  }) => (
    <div className="border border-InterfaceStrokesoft p-[16px] xl:p-[18px] 2xl:p-[20px] 3xl:p-[1.042vw] space-y-4">
      <div>
        <h3 className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600]">
          {title} {isRequired && '*'}
        </h3>
        <p className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] mt-1">
          {description}
        </p>
        <p className="text-InterfaceTextsubtitle text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] mt-1">
          Max file size: 30MB | Allowed format: PDF {maxFiles > 1 && `| Max ${maxFiles} files`}
        </p>
      </div>

      <div className="space-y-4">
        {/* Upload Area */}
        {documents.length < maxFiles && (
          <div className="border-2 border-dashed border-InterfaceStrokesoft rounded-lg p-6 text-center">
            <input
              type="file"
              id={`upload-${title.replace(/\s+/g, '-').toLowerCase()}`}
              accept=".pdf"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  onUpload(file);
                  e.target.value = ''; // Reset input
                }
              }}
              className="hidden"
            />
            <label
              htmlFor={`upload-${title.replace(/\s+/g, '-').toLowerCase()}`}
              className="cursor-pointer"
            >
              <Upload className="mx-auto h-8 w-8 text-InterfaceTextsubtitle mb-2" />
              <p className="text-InterfaceTextdefault text-sm">Click to upload or drag and drop</p>
              <p className="text-InterfaceTextsubtitle text-xs mt-1">PDF files up to 30MB</p>
            </label>
          </div>
        )}

        {/* Display uploaded files */}
        {documents.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-InterfaceTexttitle text-[14px] font-[500]">Uploaded Files:</h4>
            <div className="space-y-2">
              {documents.map((document: DocumentFile) => (
                <div
                  key={document.id}
                  className="flex items-center justify-between p-3 bg-InterfaceSurfacecomponentmuted rounded border"
                >
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-red-600" />
                    <div className="flex flex-col">
                      <span className="text-sm text-InterfaceTextdefault font-medium">
                        {document.name}
                      </span>
                      <div className="flex items-center gap-4 text-xs text-InterfaceTextsubtitle">
                        <span>Size: {(document.size / (1024 * 1024)).toFixed(2)} MB</span>
                        <span>
                          Uploaded:{' '}
                          {document.uploadedAt
                            ? new Date(document.uploadedAt).toLocaleDateString()
                            : 'Unknown'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={`text-xs px-2 py-1 rounded font-medium ${
                        document.status === 'uploaded'
                          ? 'bg-green-100 text-green-800'
                          : document.status === 'verified'
                            ? 'bg-blue-100 text-blue-800'
                            : document.status === 'rejected'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                    </span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => onRemove(document.id)}
                      className="text-red-600 border-red-300 hover:bg-red-50 h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Show validation message for required sections */}
        {isRequired && documents.length === 0 && (
          <div className="text-red-500 text-sm">
            This section is required. Please upload at least one document.
          </div>
        )}
      </div>
    </div>
  );

  return (
    <>
      <form onSubmit={handleSubmit}>
        <div className="relative bg-interfacesurfacecomponent">
          <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
            <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
              Document Information
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
              * Fields are Mandatory
            </div>
          </div>
          <ScrollArea className="h-[560px] 3xl:h-[25.083vw]">
            <div className="py-[32px] space-y-[24px] xl:space-y-[28px] 2xl:space-y-[32px] 3xl:space-y-[1.667vw]">
              {/* Section 1: Copy of Passport (First & Last Page)/Aadhar Card(Front & Back) */}
              <FileUploadSection
                title="Copy of Passport (First & Last Page)/Aadhar Card(Front & Back)"
                description="Upload identity proof documents (passport pages or Aadhar card front and back)"
                documents={documentInfo.passportDocuments}
                onUpload={(file) => handleFileUpload(file, 'passport')}
                onRemove={removePassportDoc}
                maxFiles={4}
                isRequired={false}
              />

              {/* Section 2: Latest Audited Financials with Income Tax Return Acknowledgement Copy */}
              <FileUploadSection
                title="Latest Audited Financials with Income Tax Return Acknowledgement Copy"
                description="Upload your latest audited financial statements along with IT return acknowledgement"
                documents={documentInfo.auditFinancials}
                onUpload={(file) => handleFileUpload(file, 'audit')}
                onRemove={removeAuditDoc}
                maxFiles={1}
                isRequired={false}
              />

              {/* Section 3: Tax Exemption Supporting Document */}
              <div className="border border-InterfaceStrokesoft p-[16px] xl:p-[18px] 2xl:p-[20px] 3xl:p-[1.042vw] space-y-4">
                <div>
                  <h3 className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600]">
                    Tax Exemption Supporting Document {companyInfo.isSezCompany && '*'}
                  </h3>
                  <p className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] mt-1">
                    Upload tax exemption supporting documents{' '}
                    {companyInfo.isSezCompany ? '(Required for SEZ companies)' : '(Optional)'}
                  </p>
                  <p className="text-InterfaceTextsubtitle text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] mt-1">
                    Max file size: 30MB | Allowed format: PDF
                  </p>
                </div>

                {/* Expiry Date Field */}
                {companyInfo.isSezCompany && (
                  <div className="flex flex-col gap-1.5">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      Expiry Date *
                    </Label>
                    <Input
                      type="date"
                      required={companyInfo.isSezCompany}
                      value={documentInfo.taxExemptionExpiryDate}
                      onChange={(e) => updateTaxExpiryDate(e.target.value)}
                      className="w-full max-w-xs placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                    />
                  </div>
                )}

                <div className="space-y-4">
                  {/* Upload Area */}
                  {documentInfo.taxExemptionDocument.length === 0 && (
                    <div className="border-2 border-dashed border-InterfaceStrokesoft rounded-lg p-6 text-center">
                      <input
                        type="file"
                        id="upload-tax-exemption"
                        accept=".pdf"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleFileUpload(file, 'tax');
                            e.target.value = ''; // Reset input
                          }
                        }}
                        className="hidden"
                      />
                      <label htmlFor="upload-tax-exemption" className="cursor-pointer">
                        <Upload className="mx-auto h-8 w-8 text-InterfaceTextsubtitle mb-2" />
                        <p className="text-InterfaceTextdefault text-sm">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-InterfaceTextsubtitle text-xs mt-1">
                          PDF files up to 30MB
                        </p>
                      </label>
                    </div>
                  )}

                  {/* Display uploaded file */}
                  {documentInfo.taxExemptionDocument.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-InterfaceTexttitle text-[14px] font-[500]">
                        Uploaded File:
                      </h4>
                      <div className="space-y-2">
                        {documentInfo.taxExemptionDocument.map((document: DocumentFile) => (
                          <div
                            key={document.id}
                            className="flex items-center justify-between p-3 bg-InterfaceSurfacecomponentmuted rounded border"
                          >
                            <div className="flex items-center gap-3">
                              <FileText className="h-5 w-5 text-red-600" />
                              <div className="flex flex-col">
                                <span className="text-sm text-InterfaceTextdefault font-medium">
                                  {document.name}
                                </span>
                                <div className="flex items-center gap-4 text-xs text-InterfaceTextsubtitle">
                                  <span>Size: {(document.size / (1024 * 1024)).toFixed(2)} MB</span>
                                  <span>
                                    Uploaded:{' '}
                                    {document.uploadedAt
                                      ? new Date(document.uploadedAt).toLocaleDateString()
                                      : 'Unknown'}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span
                                className={`text-xs px-2 py-1 rounded font-medium ${
                                  document.status === 'uploaded'
                                    ? 'bg-green-100 text-green-800'
                                    : document.status === 'verified'
                                      ? 'bg-blue-100 text-blue-800'
                                      : document.status === 'rejected'
                                        ? 'bg-red-100 text-red-800'
                                        : 'bg-gray-100 text-gray-800'
                                }`}
                              >
                                {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                              </span>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={removeTaxExemptionDoc}
                                className="text-red-600 border-red-300 hover:bg-red-50 h-8 w-8 p-0"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Show validation message for SEZ companies */}
                  {companyInfo.isSezCompany && documentInfo.taxExemptionDocument.length === 0 && (
                    <div className="text-red-500 text-sm">
                      This document is required for SEZ companies. Please upload the tax exemption
                      supporting document.
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Upload Guidelines */}
            <div className="border-t border-InterfaceStrokesoft pt-[24px] xl:pt-[28px] 2xl:pt-[32px] 3xl:pt-[1.667vw]">
              <h3 className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] mb-4">
                Upload Guidelines
              </h3>
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                <div>
                  <h4 className="font-[500] text-InterfaceTexttitle mb-2">Accepted File Format:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>PDF documents (.pdf)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-[500] text-InterfaceTexttitle mb-2">File Requirements:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Maximum file size: 30MB per file</li>
                    <li>Files should be clear and readable</li>
                    <li>All documents should be valid and current</li>
                    <li>Ensure documents are properly scanned</li>
                  </ul>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
      </form>
    </>
  );
}
