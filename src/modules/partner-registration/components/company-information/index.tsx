'use client';
import * as React from 'react';
import { useEffect } from 'react';
import { Input, Label, Button, Checkbox } from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { PhoneInput } from '@/components/common/phone-input';
import { ScrollArea } from '@redington-gulf-fze/cloudquarks-component-library';
import { useCompanyInformation } from '../../hooks/usePartnerRegistration';
import {
  companyInformationSchema,
  companyTypesRequiringCIN,
  type CompanyInformationFormData,
} from '../../validations';
import toast from 'react-hot-toast';
import { countryService, partnerRegistrationService } from '@/lib/services/api';
import { logger } from '@/lib/utils/logger';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Select2 } from '@/components/common/ui/combo-box';
import { useValidation } from '@/lib/hooks/step-validation';
import { useTranslations } from 'next-intl';

export default function CompanyInformation() {
  const { companyInfo, updateCompanyInfo, updateRegisteredAddr, updateContact } =
    useCompanyInformation();

  const { setStepValid, validationTriggers } = useValidation();
  const t = useTranslations();

  const { data: countries } = useQuery({
    queryKey: ['countries'],
    queryFn: () => countryService.getCountries(),
  });

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    trigger,
    formState: { errors, isValid },
  } = useForm<CompanyInformationFormData>({
    resolver: zodResolver(companyInformationSchema),
    mode: 'onTouched',
    defaultValues: companyInfo as CompanyInformationFormData,
  });

  // Track validation trigger changes
  const prevValidationTriggerRef = React.useRef(validationTriggers[0] || 0);

  // Update parent validation state whenever form validity changes
  useEffect(() => {
    setStepValid(0, isValid); // Step 0 is Company Information
  }, [isValid, setStepValid]);

  // Handle validation triggers from parent - only when trigger count actually changes
  useEffect(() => {
    const currentTrigger = validationTriggers[0] || 0;
    if (currentTrigger > prevValidationTriggerRef.current) {
      prevValidationTriggerRef.current = currentTrigger;
      // Force validation of all fields
      trigger();
    }
  }, [validationTriggers, trigger]);

  useEffect(() => {
    // Sync form data with Redux state when form changes
    const subscription = watch((value) => {
      if (value) {
        // Update company information
        updateCompanyInfo({
          gstNumber: value.gstNumber || '',
          panNumber: value.panNumber || '',
          cinNumber: value.cinNumber || '',
          isSezCompany: value.isSezCompany || false,
          companyName: value.companyName || '',
          website: value.website || '',
          address: {
            street1: value.address?.street1 || '',
            street2: value.address?.street2 || '',
            countryBusiness: value.address?.countryBusiness || '',
            city: value.address?.city || '',
            state: value.address?.state || '',
            postalCode: value.address?.postalCode || '',
            legalStatus: value.address?.legalStatus || '',
          },
          additionalInfo: {
            numberOfOfficesInRegion: value.additionalInfo?.numberOfOfficesInRegion || '',
            otherCountriesWithOffices: Array.isArray(
              value.additionalInfo?.otherCountriesWithOffices
            )
              ? value.additionalInfo.otherCountriesWithOffices.filter(
                  (item): item is string => typeof item === 'string' && item.trim() !== ''
                )
              : [],
            numberOfWareHousesInRegion:
              value.additionalInfo?.numberOfWareHousesInRegion?.toString() || '',
            numberOfEmployees: value.additionalInfo?.numberOfEmployees?.toString() || '',
            numberOfTechnicalStaff: value.additionalInfo?.numberOfTechnicalStaff?.toString() || '',
            numberOfSalesStaff: value.additionalInfo?.numberOfSalesStaff?.toString() || '',
            twitterAccount: value.additionalInfo?.twitterAccount || '',
            facebookAccount: value.additionalInfo?.facebookAccount || '',
            linkedinAccount: value.additionalInfo?.linkedinAccount || '',
            instagramAccount: value.additionalInfo?.instagramAccount || '',
          },
          contactInfo: {
            mobileNumber: value.contactInfo?.mobileNumber || '',
            countryCode: value.contactInfo?.countryCode || '',
          },
        });

        // Update addresses
        if (value.address) {
          updateRegisteredAddr(value.address);
        }

        // Update contact details
        if (value.contactInfo) {
          updateContact(value.contactInfo);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    if (countries?.data) {
      setValue('address.countryBusiness', 'in');
      // Also set default country code if not already set
      if (!watch('contactInfo.countryCode')) {
        setValue('contactInfo.countryCode', 'in');
      }
    }
  }, [countries, setValue, watch]);

  // Reset form when Redux state changes (e.g., when loading saved data)
  useEffect(() => {
    // Only reset if there's actually meaningful data in Redux state
    if (companyInfo.gstNumber || companyInfo.panNumber || companyInfo.companyName) {
      const currentFormValues = watch();
      const hasChanges = JSON.stringify(currentFormValues) !== JSON.stringify(companyInfo);

      if (hasChanges) {
        reset(companyInfo as CompanyInformationFormData);
      }
    }
  }, [companyInfo, reset]); // Added reset back to dependencies

  const onSubmit = () => {
    if (isValid) {
      toast.success('Company information saved successfully!');
    } else {
      toast.error('Please fix the form errors before proceeding.');
    }
  };

  const { mutate: verifyGstNumber, isPending: verifyGstNumberLoading } = useMutation({
    mutationFn: (gstNo: string) => partnerRegistrationService.verifyGstNumber(gstNo),
  });

  const handleVerifyGstNumber = async () => {
    //validate gst number
    if (!watch('gstNumber')) {
      toast.error('GST number is required');
      return;
    }
    if (watch('gstNumber').length !== 15) {
      toast.error('GST number must be 15 characters long');
      return;
    }
    verifyGstNumber(watch('gstNumber'), {
      onSuccess: (data) => {
        logger.log('data', data.data?.taxpayerInfo.panNo);
        updateCompanyInfo({
          panNumber: data.data?.taxpayerInfo.panNo || '',
          companyName: data.data?.taxpayerInfo.tradeNam,
          address: {
            countryBusiness: 'in',
            state: data.data?.taxpayerInfo.pradr.addr.stcd || '',
            city:
              data.data?.taxpayerInfo.pradr.addr.city ||
              data.data?.taxpayerInfo.pradr.addr.dst ||
              '',
            street1:
              `${data.data?.taxpayerInfo.pradr.addr.bno}, ${data.data?.taxpayerInfo.pradr.addr.st}` ||
              '',
            street2: data.data?.taxpayerInfo.pradr.addr.loc || '',
            postalCode: data.data?.taxpayerInfo.pradr.addr.pncd || '',
            legalStatus: data.data?.taxpayerInfo.ctb || '',
          },
        });
        toast.success('GST number verified successfully!');
      },
      onError: (error) => {
        logger.log('error', error);
        toast.error(error.message);
      },
    });
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="relative bg-interfacesurfacecomponent">
          {/* Company Information */}
          <div className="relative">
            <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                {t('companyInformation')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
                * {t('fieldsAreMandatory')}
              </div>
            </div>
            <ScrollArea className="h-[560px] 3xl:h-[25.083vw]">
              <div className="pt-[16px] 3xl:pt-[0.833vw] grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                {/* GST Registration Number */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('gstRegistrationNumber')} *
                  </Label>
                  <div className="flex w-full gap-5">
                    <Controller
                      name="gstNumber"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          required
                          type="text"
                          placeholder={t('enterGSTNumber')}
                          className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                            errors.gstNumber ? 'border-red-500' : ''
                          }`}
                        />
                      )}
                    />
                    <Button
                      type="button"
                      variant="verifybtn"
                      size="sm"
                      disabled={verifyGstNumberLoading || !watch('gstNumber')}
                      className="px-[14px] disabled:cursor-not-allowed xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[4px] xl:py-[4px] 2xl:py-[6px] 3xl:py-[0.36vw] flex items-center justify-center hover:bg-BrandNeutralpure hover:text-InterfaceTextwhite gap-2 cursor-pointer bg-BrandNeutralpure text-InterfaceTextwhite font-medium text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] rounded-none border border-BrandNeutral700"
                      onClick={handleVerifyGstNumber}
                    >
                      <i className="cloud-send text-[5px] xl:text-[5px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                      {t('submit')}
                    </Button>
                  </div>
                  {errors.gstNumber && (
                    <span className="text-red-500 text-sm">{errors.gstNumber.message}</span>
                  )}
                </div>

                {/* PAN Number */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('panNumber')} *
                  </Label>
                  <div className="flex items-center gap-4">
                    <Controller
                      name="panNumber"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          disabled={true}
                          type="text"
                          placeholder={t('panNumber')}
                          className={`flex-1 placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                            errors.panNumber ? 'border-red-500' : ''
                          }`}
                        />
                      )}
                    />
                    <div className="flex items-center gap-1">
                      <Controller
                        name="isSezCompany"
                        control={control}
                        render={({ field }) => (
                          <Checkbox
                            id="sez"
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        )}
                      />
                      <label
                        htmlFor="sez"
                        className="text-InterfaceTextdefault cursor-pointer text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                      >
                        {t('isCompanyPartOfSEZ')}
                      </label>
                    </div>
                  </div>
                  {errors.panNumber && (
                    <span className="text-red-500 text-sm">{errors.panNumber.message}</span>
                  )}
                </div>

                {/* CIN Number */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('cinNumber')}
                    {watch('address.legalStatus') &&
                      companyTypesRequiringCIN.includes(watch('address.legalStatus')) &&
                      '*'}
                  </Label>
                  <Controller
                    name="cinNumber"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        placeholder={t('cinNumber')}
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                          errors.cinNumber ? 'border-red-500' : ''
                        }`}
                      />
                    )}
                  />
                  {errors.cinNumber && (
                    <span className="text-red-500 text-sm">{errors.cinNumber.message}</span>
                  )}
                </div>

                {/* Company Name */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('companyName')} *
                  </Label>
                  <Controller
                    name="companyName"
                    disabled={true}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        placeholder={t('companyName')}
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                          errors.companyName ? 'border-red-500' : ''
                        }`}
                      />
                    )}
                  />
                  {errors.companyName && (
                    <span className="text-red-500 text-sm">{errors.companyName.message}</span>
                  )}
                </div>

                {/* Address Section */}
                <div className="col-span-full">
                  <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px]">
                    {/* Address Line 1 */}
                    <div className="flex flex-col gap-1.5">
                      <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                        {t('streetAddress1')} *
                      </Label>
                      <Controller
                        name="address.street1"
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            disabled={true}
                            type="text"
                            placeholder={t('address')}
                            className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                              errors.address?.street1 ? 'border-red-500' : ''
                            }`}
                          />
                        )}
                      />
                      {errors.address?.street1 && (
                        <span className="text-red-500 text-sm">
                          {errors.address?.street1.message}
                        </span>
                      )}
                    </div>

                    {/* Address Line 2 */}
                    <div className="flex flex-col gap-1.5">
                      <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                        {t('streetAddress2')}
                      </Label>
                      <Controller
                        name="address.street2"
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            disabled={true}
                            type="text"
                            placeholder={t('address')}
                            className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                          />
                        )}
                      />
                    </div>

                    {/* Country */}
                    <div className="flex flex-col gap-1.5">
                      <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                        {t('countryOfBusiness')} *
                      </Label>
                      <Controller
                        name="address.countryBusiness"
                        control={control}
                        disabled={true}
                        render={({ field }) => (
                          <Select2
                            options={
                              countries?.data?.map((country) => ({
                                label: country.name,
                                value: country.iso_2,
                              })) ?? []
                            }
                            value={field.value}
                            onChange={field.onChange}
                            disabled={true}
                          />
                        )}
                      />
                      {errors.address?.countryBusiness && (
                        <span className="text-red-500 text-sm">
                          {errors.address.countryBusiness.message}
                        </span>
                      )}
                    </div>

                    {/* State */}
                    <div className="flex flex-col gap-1.5">
                      <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                        {t('state')} *
                      </Label>
                      <Controller
                        name="address.state"
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            type="text"
                            disabled={true}
                            placeholder={t('state')}
                            className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                              errors.address?.state ? 'border-red-500' : ''
                            }`}
                          />
                        )}
                      />
                      {errors.address?.state && (
                        <span className="text-red-500 text-sm">{errors.address.state.message}</span>
                      )}
                    </div>

                    {/* City */}
                    <div className="flex flex-col gap-1.5">
                      <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                        {t('city')} *
                      </Label>
                      <Controller
                        name="address.city"
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            type="text"
                            disabled={true}
                            placeholder={t('city')}
                            className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                              errors.address?.city ? 'border-red-500' : ''
                            }`}
                          />
                        )}
                      />
                      {errors.address?.city && (
                        <span className="text-red-500 text-sm">{errors.address.city.message}</span>
                      )}
                    </div>

                    {/* Postal Code */}
                    <div className="flex flex-col gap-1.5">
                      <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                        {t('postalCode')} *
                      </Label>
                      <Controller
                        name="address.postalCode"
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            disabled={true}
                            type="text"
                            placeholder={t('postalCode')}
                            className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                          />
                        )}
                      />
                      {errors.address?.postalCode && (
                        <span className="text-red-500 text-sm">
                          {errors.address.postalCode.message}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Legal Status */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('legalStatus')} *
                  </Label>
                  <Controller
                    name="address.legalStatus"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        disabled={true}
                        placeholder={t('legalStatus')}
                        className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none ${
                          errors.address?.legalStatus ? 'border-red-500' : ''
                        }`}
                      />
                    )}
                  />
                  {errors.address?.legalStatus && (
                    <span className="text-red-500 text-sm">
                      {errors.address.legalStatus.message}
                    </span>
                  )}
                </div>

                {/* Contact Details */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('contactNumber')} *
                  </Label>
                  <div className="flex items-start">
                    <Controller
                      name="contactInfo.mobileNumber"
                      control={control}
                      render={({ field }) => (
                        <PhoneInput
                          value={field.value}
                          onChange={(value) => field.onChange(value || '')}
                          onCountryChange={(countryData) => {
                            setValue('contactInfo.countryCode', countryData || 'in');
                          }}
                          defaultCountry="IN"
                          className="w-full"
                        />
                      )}
                    />
                  </div>
                  {errors.contactInfo?.mobileNumber && (
                    <span className="text-red-500 text-sm">
                      {errors.contactInfo.mobileNumber.message}
                    </span>
                  )}
                </div>

                {/* Website */}
                <div className="flex flex-col gap-1.5">
                  <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('companyWebsite')}
                  </Label>
                  <Controller
                    name="website"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="text"
                        placeholder={t('enterWebsiteUrl')}
                        className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                      />
                    )}
                  />
                  {errors?.website && (
                    <span className="text-red-500 text-sm">{errors.website.message}</span>
                  )}
                </div>
              </div>

              {/* Addition Information */}
              <div className="relative py-4 my-2">
                <div className="flex justify-between items-center flex-wrap 3xl:pb-[0.417vw] border-b border-t py-4 border-InterfaceStrokesoft">
                  <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                    {t('additionalInformation')}
                  </div>
                </div>
                <div className="pt-[16px] 3xl:pt-[0.833vw] grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                  {/* Number of Offices in Region */}
                  <div className="flex flex-col gap-1.5">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('numberOfOfficesInRegion')}
                    </Label>
                    <div className="flex w-full gap-5">
                      <Controller
                        name="additionalInfo.numberOfOfficesInRegion"
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            required
                            type="text"
                            placeholder={t('entervalue')}
                            className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
                          />
                        )}
                      />
                    </div>
                  </div>

                  {/* Other Countries with Offices */}
                  <div className="flex flex-col gap-1.5">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('otherCountriesWithOffices')}
                    </Label>
                    <div className="flex items-center gap-4">
                      <Controller
                        name="additionalInfo.otherCountriesWithOffices"
                        control={control}
                        render={({ field }) => (
                          <Select2
                            options={
                              countries?.data?.map((country) => ({
                                label: country.name,
                                value: country.iso_2,
                              })) ?? []
                            }
                            placeholder={t('multiSelectDropdownForSelectingCountry')}
                            value={field.value as string | string[]}
                            onChange={field.onChange}
                            multiSelect={true}
                          />
                        )}
                      />
                    </div>
                  </div>

                  {/* Number of Warehouses in Region */}
                  <div className="flex flex-col gap-1.5">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('numberOfWarehousesInRegion')}
                    </Label>
                    <Controller
                      name="additionalInfo.numberOfWareHousesInRegion"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="text"
                          placeholder={t('entervalue')}
                          className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
                        />
                      )}
                    />
                  </div>

                  {/* Number of Employees */}
                  <div className="flex flex-col gap-1.5">
                    <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                      {t('numberOfEmployees')}
                    </Label>
                    <Controller
                      name="additionalInfo.numberOfEmployees"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="text"
                          placeholder={t('entervalue')}
                          className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
                        />
                      )}
                    />
                  </div>

                  {/* Address Section */}
                  <div className="col-span-full">
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px]">
                      {/* Number of Sales Staff */}
                      <div className="flex flex-col gap-1.5">
                        <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                          {t('numberOfSalesStaff')}
                        </Label>
                        <Controller
                          name="additionalInfo.numberOfSalesStaff"
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              type="text"
                              placeholder={t('entervalue')}
                              className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none`}
                            />
                          )}
                        />
                      </div>

                      {/* Number of Technical Staff*/}
                      <div className="flex flex-col gap-1.5">
                        <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                          {t('numberOfTechnicalStaff')}
                        </Label>
                        <Controller
                          name="additionalInfo.numberOfTechnicalStaff"
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              type="text"
                              placeholder={t('entervalue')}
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                            />
                          )}
                        />
                      </div>

                      {/* Twitter Account */}
                      <div className="flex flex-col gap-1.5">
                        <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                          {t('twitterAccount')}
                        </Label>
                        <Controller
                          name="additionalInfo.twitterAccount"
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              type="text"
                              placeholder={t('enterAccountURL')}
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                            />
                          )}
                        />
                      </div>

                      {/* Facebook Account */}
                      <div className="flex flex-col gap-1.5">
                        <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                          {t('facebookAccount')}
                        </Label>
                        <Controller
                          name="additionalInfo.facebookAccount"
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              type="text"
                              placeholder={t('enterAccountURL')}
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                            />
                          )}
                        />
                      </div>

                      {/* LinkedIn Account */}
                      <div className="flex flex-col gap-1.5">
                        <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                          {t('linkedInAccount')}
                        </Label>
                        <Controller
                          name="additionalInfo.linkedinAccount"
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              type="text"
                              placeholder={t('enterAccountURL')}
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                            />
                          )}
                        />
                      </div>

                      {/* Instagram Account */}
                      <div className="flex flex-col gap-1.5">
                        <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                          {t('instagramAccount')}
                        </Label>
                        <Controller
                          name="additionalInfo.instagramAccount"
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              type="text"
                              placeholder={t('enterAccountURL')}
                              className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                            />
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>
      </form>
    </>
  );
}
