'use client';
import * as React from 'react';
import { Label, Button } from '@redington-gulf-fze/cloudquarks-component-library';

import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';

import { ColumnDef } from '@tanstack/react-table';
import { ScrollArea } from '@redington-gulf-fze/cloudquarks-component-library';
import { DataTable } from '@/components/common/ui/data-table';
import BrandAddPopup from '../success-popup';
import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import { Controller, useForm } from 'react-hook-form';
import { BrandFormData, BrandFormSchema } from '../../validations';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import { brandService } from '@/lib/services/api';
import { Brand, BrandCategoryGroup, Vendor } from '@/types/brand';
import { useAppDispatch, useAppSelector } from '@/store';
import { addBrandPreference, removeBrandPreference } from '@/store/slices/partner-registration';
import { useValidation } from '@/lib/hooks/step-validation';
import { useTranslations } from 'next-intl';

type BrandData = {
  vendor: { label: string; value: string };
  brand: { label: string; value: string };
  brandCategory: { label: string; value: string };
};

export default function BrandInformation() {
  const t = useTranslations();

  const [addPopupOpen, setAddPopupOpen] = React.useState<boolean>(false);
  const brandPreferences = useAppSelector(
    (state) => state.partnerRegistration.userBrandPreferences
  );

  const { setStepValid, validationTriggers } = useValidation();

  const dispatch = useAppDispatch();
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<BrandFormData>({
    resolver: zodResolver(BrandFormSchema),
    defaultValues: {
      vendorId: '',
      brandId: [],
      brandCategoryId: [],
    },
  });

  const brandColumns: ColumnDef<BrandData>[] = [
    {
      accessorKey: 'vendor',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      cell: ({ row }) => row.original.vendor.label,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();

        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between "
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('vendor')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <Input
              placeholder="Vendor"
              className="my-2 w-full h-9 rounded-[2px] border border-InterfaceStrokedefault   text-[14px] xl:text-[14px] 3xl:text-[0.729vw] placeholder:font-normal"
              onChange={(e) => column.setFilterValue(e.target.value)}
            /> */}
          </div>
        );
      },
    },
    {
      accessorKey: 'brand',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      cell: ({ row }) => row.original.brand.label,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();

        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brands')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <Input
              placeholder="Vendor"
              className="my-2 w-full h-9 rounded-[2px] border border-InterfaceStrokedefault   text-[14px] xl:text-[14px] 3xl:text-[0.729vw] placeholder:font-normal"
              onChange={(e) => column.setFilterValue(e.target.value)}
            /> */}
          </div>
        );
      },
    },

    {
      accessorKey: 'brandCategory',
      enableSorting: true,
      size: 500,
      minSize: 500,
      maxSize: 500,
      cell: ({ row }) => row.original.brandCategory.label,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();

        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between"
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brandCategory')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <Input
              placeholder="  Brand Category"
              className="my-2 w-full h-9 rounded-[2px] border border-InterfaceStrokedefault  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]  placeholder:font-normal"
              onChange={(e) => column.setFilterValue(e.target.value)}
            /> */}
          </div>
        );
      },
    },

    {
      accessorKey: 'action',
      header: () => (
        <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({ row }) => {
        const rowData = row.original;
        const handleDelete = () => {
          dispatch(
            removeBrandPreference({
              vendor: rowData.vendor,
              brand: rowData.brand,
              brandCategory: rowData.brandCategory,
            })
          );
        };
        return (
          <div
            onClick={handleDelete}
            className="flex cursor-pointer items-center gap-2 justify-center"
          >
            &nbsp;
            <i className="cloud-trash text-InterfaceTextsubtitle font-[600]"></i>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
  ];

  // Fetching dropdown data conditionally
  const { data: vendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async (): Promise<Vendor[]> => {
      const res = await brandService.getVendors();
      return res?.data ?? [];
    },
    select: (data: Vendor[]) =>
      data.map((vendor) => ({
        label: vendor.vendor_name,
        value: vendor.id,
      })),
  });

  const { data: brands } = useQuery({
    queryKey: ['brands', watch('vendorId')],
    queryFn: async (): Promise<Brand[]> => {
      const vendorId = watch('vendorId');
      let res: Brand[] = [];
      if (vendorId) {
        res = (await brandService.getBrands(vendorId))?.data as Brand[];
      }
      return res;
    },
    select: (data: Brand[]) =>
      data.map((brand) => ({
        label: brand.brand_name,
        value: brand.id,
      })),
  });

  const { data: brandCategories } = useQuery({
    queryKey: ['brand-categories', watch('brandId'), watch('vendorId')],
    queryFn: async () => {
      const vendorId = watch('vendorId');
      const brandIds = watch('brandId');

      if (!vendorId || !brandIds || !Array.isArray(brandIds)) return [];

      const res = await brandService.getBrandCategories(brandIds);
      const responseData: BrandCategoryGroup[] = res?.data ?? [];

      return responseData.map((brandItem) => ({
        label: brandItem.brand.name,
        options: brandItem.brand_category.map((cat) => ({
          label: cat.name,
          value: cat.id,
        })),
      }));
    },
  });

  // On adding new item to table => when user clicks add button
  const onSubmit = (data: BrandFormData) => {
    const vendorOption = vendors?.find((v) => v.value === data.vendorId);

    const selectedBrandIds = Array.isArray(data.brandId) ? data.brandId : [];
    const selectedCategoryIds = Array.isArray(data.brandCategoryId) ? data.brandCategoryId : [];

    selectedBrandIds.forEach((brandId) => {
      const brandOption = brands?.find((b) => b.value === brandId);

      // Match brand group by brand name (label)
      const categoryGroup = brandCategories?.find((group) => group.label === brandOption?.label);

      if (!vendorOption || !brandOption || !categoryGroup) return;

      const validCategories = categoryGroup.options.filter((cat: Select2Option) =>
        (selectedCategoryIds as string[]).includes(cat.value)
      );

      validCategories.forEach((brandCategoryOption: Select2Option) => {
        dispatch(
          addBrandPreference({
            vendor: vendorOption,
            brand: brandOption,
            brandCategory: brandCategoryOption,
          })
        );
      });
    });
  };
  // table data
  const displayTableData = React.useMemo(() => {
    return brandPreferences;
  }, [brandPreferences]);

  React.useEffect(() => {
    const isValid = brandPreferences.length > 0;
    setStepValid(4, isValid);
  }, [brandPreferences, validationTriggers[4]]);

  return (
    <>
      {/* <div className="px-[105px] xl:px-[260px] lg:px-[260px] 2xl:px-[280px] 3xl:px-[18.75vw] bg-InterfaceSurfacecomponentmuted"> */}
      <div className="relative bg-interfacesurfacecomponent   ">
        <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceSurfacecomponentmuted">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
            {t('brand')}/{t('brandCategory')} Preferences
          </div>
          <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
            * {t('fieldsAreMandatory')}
          </div>
        </div>
        <ScrollArea className="h-[560px] 3xl:h-[25.083vw]">
          <div className="flex text-InterfaceTextsubtitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] mt-2">
            Please select the Brands and Brand Categories
          </div>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="py-[16px] 3xl:pt-[0.833vw] items-end grid grid-cols-7 3xl:grid-cols-10 gap-[20px] xl:gap-[20px] 2xl:gap-[20px] 3xl:gap-[1.25vw]"
          >
            <div className="col-span-1 md:col-span-2 3xl:col-span-3 flex flex-col gap-1.5">
              <Label
                htmlFor="country_id"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('vendor')} *
              </Label>

              <Controller
                name="vendorId"
                control={control}
                render={({ field }) => (
                  <Select2
                    options={vendors ?? []}
                    value={field.value || ''}
                    onChange={field.onChange}
                    error={errors.vendorId?.message}
                    placeholder={t('select2')}
                  />
                )}
              />

              {/* <Select2 
                  {...register('vendor')} 
                  name="vendor"
                  label="--Select--"
                  options={}
                  value={getValues('country_id')} // Watch the designation value
                  onChange={(value: string) =>
                      // setValue('country_id', value, { shouldValidate: true })
                  }
                /> */}
            </div>
            <div className="col-span-1 md:col-span-2 3xl:col-span-3 flex flex-col gap-1.5">
              <Label
                htmlFor="country_id"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('brand')} *
              </Label>
              <Controller
                name="brandId"
                control={control}
                render={({ field }) => (
                  <Select2
                    options={brands ?? []}
                    value={field.value || ''}
                    onChange={field.onChange}
                    error={errors.brandId?.message}
                    placeholder={t('select2')}
                    multiSelect={true}
                  />
                )}
              />
            </div>
            <div className="col-span-1 md:col-span-2 3xl:col-span-3 flex flex-col gap-1.5 ">
              <Label
                htmlFor="country_id"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('brandCategory')} *
              </Label>

              <div className="flex w-full gap-1">
                <Controller
                  name="brandCategoryId"
                  control={control}
                  render={({ field }) => (
                    <Select2
                      options={brandCategories ?? []}
                      value={field.value || ''}
                      onChange={field.onChange}
                      error={errors.brandCategoryId?.message}
                      placeholder={t('select2')}
                      multiSelect={true}
                    />
                  )}
                />
              </div>
            </div>
            <div className="col-span-1 ">
              <Button
                type="submit"
                // onClick={(e) => {
                //   e.stopPropagation();
                //   setAddPopupOpen(true)
                // }}
                className="flex justify-center blueBtn px-[14px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] rounded-none font-[500]"
              >
                <i className="cloud-Add text-[10px]"></i>
                <span className="text-[14px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {t('add')}
                </span>
              </Button>
            </div>
          </form>

          <div className=" py-[16px] 3xl:pt-[0.833vw] ">
            <div className="font-[400] text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw]">
              {t('details')}
            </div>
          </div>

          <DataTable data={displayTableData} columns={brandColumns} />
        </ScrollArea>
      </div>

      {/* </div> */}
      <BrandAddPopup open={addPopupOpen} onClose={() => setAddPopupOpen(false)} />
    </>
  );
}
