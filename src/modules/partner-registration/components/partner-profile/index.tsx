'use client';
import * as React from 'react';
import {
  Input,
  Label,
  Checkbox,
  ScrollArea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { RadioGroup, RadioGroupItem } from '@redington-gulf-fze/cloudquarks-component-library';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';

import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import TermsConditions from '@/modules/partner-registration/components/terms-conditions';
import { partnerRegistrationService } from '@/lib/services/api/partner-registration';
import {
  Question,
  QuestionAnswerValue,
  ProfileAnswerData,
} from '@/types/partner-registration/questionnaire';
import { useProfileInformation, useFormPersistence } from '../../hooks/usePartnerRegistration';
import { useValidation } from '@/lib/hooks/step-validation';
import { useTranslations } from 'next-intl';

// Dynamic form schema that will be built based on questions
const createFormSchema = (questions: Question[]) => {
  const schemaObject: Record<string, z.ZodTypeAny> = {};

  questions.forEach((question) => {
    const fieldName = `question_${question.id}`;

    // Make all questions optional
    if (question.answer_type.type === 'input') {
      schemaObject[fieldName] = z.string().optional();
    } else if (
      question.answer_type.type === 'multiple choice' ||
      question.answer_type.type === 'multiple choice dropdown'
    ) {
      schemaObject[fieldName] = z.array(z.string()).optional();
    } else {
      schemaObject[fieldName] = z.string().optional();
    }
  });

  // Add terms and conditions - this is the only required field
  schemaObject.accept_terms_condition = z.boolean().refine((val) => val, {
    message: 'You must accept the terms and conditions',
  });

  return z.object(schemaObject);
};

type DynamicFormData = Record<string, QuestionAnswerValue | boolean>;

const QuestionRenderer: React.FC<{
  question: Question;
  index: number;
  value: QuestionAnswerValue;
  onChange: (value: QuestionAnswerValue) => void;
  error?: string;
}> = ({ question, index, value, onChange, error }) => {
  const options: Select2Option[] = question.answers.map((answer) => ({
    value: answer.id,
    label: answer.answer,
  }));

  const renderInput = () => {
    switch (question.answer_type.type) {
      case 'input':
        return (
          <Input
            type="text"
            placeholder={`Enter ${question.question}`}
            value={(value as string) || ''}
            onChange={(e) => onChange(e.target.value)}
            className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border ${
              error ? 'border-red-500' : 'border-InterfaceStrokehard'
            } rounded-none`}
          />
        );

      case 'single choice drop down':
        return (
          <Select2
            options={options}
            value={(value as string) || ''}
            onChange={(val) => onChange(val as string)}
            error={error}
            placeholder={`Select ${question.question}`}
          />
        );

      case 'single choice':
        return (
          <RadioGroup
            value={(value as string) || ''}
            className="flex gap-[16px] 3xl:gap-[0.833vw]"
            onValueChange={(val) => onChange(val)}
          >
            {question.answers.map((answer) => (
              <div key={answer.id} className="flex items-center gap-[6px]">
                <RadioGroupItem value={answer.id} id={`${question.id}_${answer.id}`} />
                <Label htmlFor={`${question.id}_${answer.id}`}>{answer.answer}</Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'multiple choice':
      case 'multiple choice dropdown':
        return (
          <Select2
            options={options}
            value={(value as string[]) || []}
            onChange={(val) => onChange(val as string[])}
            error={error}
            placeholder={`Select ${question.question}`}
            multiSelect={true}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={`flex gap-1.5 items-baseline space-x-1 ${error ? 'p-3 bg-red-50 border border-red-200 rounded-lg' : ''}`}
    >
      <span className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[500]">
        {index + 1}.
      </span>
      <div className="w-full">
        <Label
          htmlFor={`question_${question.id}`}
          className={`text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[500] ${error ? 'text-red-600' : ''}`}
        >
          {question.question}
        </Label>
        {renderInput()}
        {error && (
          <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded">
            <p className="text-red-600 text-sm font-medium flex items-center gap-2">
              <span className="text-red-500">⚠️</span>
              {error}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default function PartnerProfile() {
  const t = useTranslations();
  const { updateProfileInfo, profileInfo } = useProfileInformation();
  const { saveToSessionStorage } = useFormPersistence();
  const { setStepValid, validationTriggers } = useValidation();

  // Fetch questions using React Query
  const {
    data: questionnaireData,
    isLoading,
    error: fetchError,
  } = useQuery({
    queryKey: ['partner-profile-questionnaire'],
    queryFn: () => partnerRegistrationService.getProfileQuestionnaire(),
  });

  const questions = questionnaireData?.data?.questions || [];

  // Create dynamic form schema based on questions
  const formSchema = React.useMemo(() => {
    if (questions.length === 0) return z.object({});
    return createFormSchema(questions);
  }, [questions]);

  const {
    watch,
    setValue,
    trigger,
    formState: { errors, isValid },
    reset,
  } = useForm<DynamicFormData>({
    mode: 'onTouched',
    resolver: zodResolver(formSchema),
    defaultValues: {
      accept_terms_condition: profileInfo.acceptTermsCondition || false,
    },
  });

  // Use ref to prevent unnecessary re-subscriptions
  const questionsRef = React.useRef(questions);
  questionsRef.current = questions;

  // Track validation trigger changes
  const prevValidationTriggerRef = React.useRef(validationTriggers[3] || 0);

  // Set default values when questions are loaded
  React.useEffect(() => {
    if (questions.length > 0) {
      const defaultValues: DynamicFormData = {
        accept_terms_condition: profileInfo.acceptTermsCondition || false,
      };

      questions.forEach((question) => {
        const fieldName = `question_${question.id}`;
        if (
          question.answer_type.type === 'multiple choice' ||
          question.answer_type.type === 'multiple choice dropdown'
        ) {
          defaultValues[fieldName] = [];
        } else {
          defaultValues[fieldName] = '';
        }
      });

      reset(defaultValues);
    }
  }, [questions, reset, profileInfo.acceptTermsCondition]);

  // Reset form when profileInfo changes (when navigating between sections)
  React.useEffect(() => {
    if (questions.length > 0) {
      const savedDefaultValues: DynamicFormData = {
        accept_terms_condition: profileInfo.acceptTermsCondition || false,
      };

      questions.forEach((question) => {
        const fieldName = `question_${question.id}`;
        if (
          question.answer_type.type === 'multiple choice' ||
          question.answer_type.type === 'multiple choice dropdown'
        ) {
          savedDefaultValues[fieldName] = [];
        } else {
          savedDefaultValues[fieldName] = '';
        }
      });

      // Only reset if the current form value differs from saved state
      const currentTermsValue = watch('accept_terms_condition');
      if (currentTermsValue !== profileInfo.acceptTermsCondition) {
        setValue('accept_terms_condition', profileInfo.acceptTermsCondition || false, {
          shouldValidate: true,
        });
      }
    }
  }, [profileInfo, setValue, watch, questions]);

  // Update parent validation state whenever form validity changes
  React.useEffect(() => {
    setStepValid(3, isValid); // Step 3 is Partner Profile
  }, [isValid, setStepValid]);

  // Handle validation triggers from parent - only when trigger count actually changes
  React.useEffect(() => {
    const currentTrigger = validationTriggers[3] || 0;
    if (currentTrigger > prevValidationTriggerRef.current) {
      prevValidationTriggerRef.current = currentTrigger;
      trigger();
    }
  }, [validationTriggers, trigger]);

  // Watch form changes and update Redux state with debouncing
  React.useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const subscription = watch((value) => {
      if (value && questionsRef.current.length > 0) {
        // Clear previous timeout
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        // Debounce the update
        timeoutId = setTimeout(() => {
          // Transform form data to API format
          const profileAnswers: ProfileAnswerData[] = [];

          questionsRef.current.forEach((question) => {
            const fieldName = `question_${question.id}`;
            const formValue = value[fieldName];

            if (question.answer_type.type === 'input') {
              // For input fields, store the actual text value as answer_id
              const inputValue = formValue as string;
              if (inputValue) {
                profileAnswers.push({
                  question_id: question.id,
                  answer_id: inputValue,
                });
              }
            } else if (Array.isArray(formValue)) {
              // Multiple choice questions
              (formValue as string[]).forEach((answerId) => {
                profileAnswers.push({
                  question_id: question.id,
                  answer_id: answerId,
                });
              });
            } else if (formValue) {
              // Single choice questions
              profileAnswers.push({
                question_id: question.id,
                answer_id: formValue as string,
              });
            }
          });

          // Update Redux state
          updateProfileInfo({
            profile_info: profileAnswers,
            acceptTermsCondition: (value.accept_terms_condition as boolean) || false,
          });

          // Auto-save to session storage
          saveToSessionStorage();
        }, 500); // Reduced debounce time
      }
    });

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      subscription.unsubscribe();
    };
  }, [watch, updateProfileInfo, saveToSessionStorage]);

  // Memoize the onChange handler to prevent unnecessary re-renders
  const handleQuestionChange = React.useCallback(
    (questionId: string, value: QuestionAnswerValue) => {
      setValue(`question_${questionId}`, value as DynamicFormData[string], {
        shouldValidate: true,
      });
    },
    [setValue]
  );

  // Memoize the current form values
  const formValues = watch();

  if (isLoading) {
    return (
      <div className="relative bg-interfacesurfacecomponent">
        <div className="flex justify-center items-center h-[520px]">
          <div className="text-InterfaceTexttitle">{t('loadingQuestionnaire')}...</div>
        </div>
      </div>
    );
  }

  if (fetchError || !questionnaireData?.data) {
    return (
      <div className="relative bg-interfacesurfacecomponent">
        <div className="flex justify-center items-center h-[520px]">
          <div className="text-red-500">{t('failedToFetchQuestionnaire')}</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <form>
        <div className="relative bg-interfacesurfacecomponent">
          <div className="flex justify-between items-center flex-wrap pb-[8px] 3xl:pb-[0.417vw] border-b border-InterfaceSurfacecomponentmuted">
            <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
              {t('partnerProfile')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[300]">
              * {t('fieldsAreMandatory')}
            </div>
          </div>
          <ScrollArea className="h-[520px]">
            <div className="pt-[16px] 3xl:pt-[0.833vw] grid grid-cols-1 xl:grid-cols-1 md:grid-cols-1 gap-[20px]">
              {questions.map((question, index) => (
                <QuestionRenderer
                  key={question.id}
                  question={question}
                  index={index}
                  value={
                    (formValues[`question_${question.id}`] as QuestionAnswerValue) ||
                    (question.answer_type.type === 'multiple choice' ||
                    question.answer_type.type === 'multiple choice dropdown'
                      ? []
                      : '')
                  }
                  onChange={(value) => handleQuestionChange(question.id, value)}
                  error={errors[`question_${question.id}`]?.message as string}
                />
              ))}

              {/* Terms and Conditions */}
              <div className="flex gap-1.5 items-baseline space-x-1">
                <span className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[500]">
                  {questions.length + 1}.
                </span>
                <div className="w-full">
                  <Label
                    htmlFor="terms"
                    className={`text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[500] ${errors.accept_terms_condition ? 'text-red-600' : ''}`}
                  >
                    {t('redingtonTermsConditions')}*
                  </Label>
                  <div
                    className={`mt-2 p-3 rounded-lg border ${errors.accept_terms_condition ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-gray-50'}`}
                  >
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        required
                        id="terms"
                        checked={formValues.accept_terms_condition as boolean}
                        onCheckedChange={(checked: boolean) =>
                          setValue('accept_terms_condition', checked === true, {
                            shouldValidate: true,
                          })
                        }
                        className={errors.accept_terms_condition ? 'border-red-500' : ''}
                      />
                      <div className="text-InterfaceTextdefault cursor-pointer text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                        <label htmlFor="terms">
                          {t('iAcceptRedingtonTermsandConditions')} &nbsp;&nbsp;
                        </label>
                        <TermsConditions />
                      </div>
                    </div>
                    {errors.accept_terms_condition && (
                      <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded">
                        <p className="text-red-600 text-sm font-medium flex items-center gap-2">
                          <span className="text-red-500">⚠️</span>
                          {errors.accept_terms_condition.message}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
      </form>
    </>
  );
}
