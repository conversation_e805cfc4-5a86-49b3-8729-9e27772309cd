import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import { <PERSON>o } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export default function TermsConditions() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTrigger asChild>
        <span className="cursor-pointer text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[500]">
          <i className="cloud-folder  text-[5px] xl:text-[5px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>{' '}
          {t('termsAndConditions')}
        </span>
      </SheetTrigger>
      <SheetContent
        //   className="flex flex-col h-full w-[400px]"
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              {t('termsAndConditions')}
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        {/* Scrollable Content */}
        <div className="flex-grow overflow-y-auto p-[30px]  space-y-3">
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px]">
            {t('redingtonCloudquarksPlatformUserAccountCreationTCs')}
          </h1>
          <div className="space-y-[20px] 3xl:space-y-[1.042vw] my-[20px] 3xl:my-[1.042vw]">
            <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px]">
              {t('fraudulentOfferAlert')}
            </p>
            <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px]">
              {t('redingtonDisclaimer1')}
            </p>
          </div>
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px] mb-[8px]">
            {t('redingtonDoesNot')}:
          </h1>
          <div>
            <ul className="list-disc ">
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t(
                  'solicitAnyInvestmentInSchemesFromFreeEmailServicesLikeGmailRediffMailYahooMailEtc'
                )}
                ;
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t('requestPaymentOfAnyKindFromProspectiveInvestor')};
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t('unauthorizedMonetaryDeal')};
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t('redingtonOfferClarification')};
              </li>
            </ul>
          </div>
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px] mb-[8px] mt-[20px]">
            {t('pleaseNote')}:
          </h1>
          <div>
            <ul className="list-disc ">
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t('fakeSolicitationWarning')};
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t('nonAffiliatedOfferNotice')};
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t(
                  'anyoneMakingAnyInvestmentBusinessAssociationOfferInReturnForMoneyOrWhoIsNotAuthorizedByRedington'
                )}
              </li>
              <li className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px] ml-[20px]">
                {t('legalActionWarning')}
              </li>
            </ul>
          </div>
          <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[140%] mt-[20px] 3xl:mt-[1.042vw] mb-[30px] 3xl:mb-[1.563vw]">
            {t('weRequestvisitwebsite')}{' '}
            <Link href={'#'} className="text-[#1570EF] underline ">
              <span>https://redingtongroup.com/</span>
            </Link>{' '}
            {t('authenticInformation')}
          </p>
          <h1 className="text-[#212325] text-[18px] 3xl:text-[0.938vw] font-semibold leading-[25.2px] mb-[20px] 3xl:mb-[1.042vw]">
            {t('acceptanceOfTerms')}
          </h1>
          <p className="text-[#3C4146] text-[16px] 3xl:text-[0.833vw] font-normal leading-[22.4px]">
            {t('theServicesThatRedingtonProvides')}
          </p>
        </div>

        {/* Footer */}
        <SheetFooter className="mr-3 mb-2">
          <SheetClose>
            <div
              // type="button"
              // onClick={onClose}
              // variant="skybluegradient"
              className="blueBtn lg:px-[13px] cursor-pointer flex justify-center items-center gap-2 xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              {' '}
              <i className="cloud-closecircle"></i>
              <span>{t('close')}</span>
            </div>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
