import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { <PERSON>o } from 'next/font/google';
import { Input, Label, Button } from '@redington-gulf-fze/cloudquarks-component-library';
import * as React from 'react';
import {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';

import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

const schema = z.object({
  accountId: z
    .string()
    .min(10, 'Account ID must be 10 digits')
    .max(10, 'Account ID must be 10 digits')
    .regex(/^\d+$/, 'Account ID must be numeric'),
});

type FormData = z.infer<typeof schema>;

export default function OnBoardingPopup() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      accountId: '',
    },
  });
  const t = useTranslations();
  const onSubmit = () => {};

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          id="Onboarding"
          className="px-[10px] xl:px-[10px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] hover:bg-[#e6e8e9] hover:text-[#1570EF] hover:font-[500] text-[#3C4146] bg-transparent rounded-none text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
        >
          <i className="cloud-hexauser text-center text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"></i>
          {t('getOnboarded')}
        </Button>
      </SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        //   className="flex flex-col h-full w-[400px]"
        className={`${roboto.className} flex flex-col h-full sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokedefault p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[24px] 2xl:text-[24px] 3xl:text-[1.25vw] font-semibold leading-[140%]">
              {t('cloudquarksOnboarding')}
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col flex-grow overflow-y-auto">
          <div className="flex-grow overflow-y-auto space-y-3 p-[24px] xl:p-[24px] 2xl:p-[34px] 3xl:p-[1.25vwvw] font-[400]">
            <div className="text-InterfaceTextsubtitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
              {t(
                'ifYouAreRegisteredPartnerWithRedingtonPleaseEnterYourRedingtonAccountNumberAndSelectTheCountry'
              )}
            </div>
            <div className="grid grid-cols-1 xl:grid-cols-1 md:grid-cols-1 gap-[24px]">
              <div className="flex flex-col gap-1.5">
                <Label
                  htmlFor="accountId"
                  className="text-InterfaceTexttitle text-[14x] xl:text-[14x] 2xl:text-[16x] 3xl:text-[0.729vw]-[500]"
                >
                  {t('redingtonAccountId')} *
                </Label>
                <Input
                  type="text"
                  id="accountId"
                  {...register('accountId')}
                  className={`placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard`}
                  placeholder={t('enterAccountId')}
                />
                {errors.accountId && (
                  <span className="text-red-500 text-xs">{errors.accountId.message}</span>
                )}

                <div>
                  <HoverCard>
                    <HoverCardTrigger
                      asChild
                      className="text-BrandSupport1pure text-[14x] xl:text-[14x] 2xl:text-[16x] 3xl:text-[0.729vw]-[500] cursor-pointer"
                    >
                      <div>{t('moreInfo')}</div>
                    </HoverCardTrigger>
                    <HoverCardContent className="w-[552px] bg-BrandNeutral100">
                      <div className="flex flex-col items-start gap-4 p-4">
                        <div className="flex items-center gap-4 w-full">
                          <ul className="list-disc text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.729vw] font-[400] text-InterfaceTextdefault ml-5 space-y-1">
                            <li>{t('accountNumberDefinition')}</li>
                            <li>
                              {t(
                                'redingtonAccountNumberIsADigitNumericValueExampleWhichIsTaggedWithSegmentCloudServices'
                              )}
                            </li>
                            <li>{t('contactRedingtonForAccount')}</li>
                          </ul>
                        </div>
                      </div>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </div>
              {/* <div className="flex flex-col gap-1.5">
              <Label
                htmlFor="country_id"
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Country *
              </Label>
              <Select2
                placeholder='Select Country'
                options={["UAE", "India", "Turkey"]}
              />

            </div> */}
            </div>
          </div>

          {/* Non Registered Partners Section */}
          <div className="bg-InterfaceSurfacecomponentmuted border border-InterfaceStrokesoft  p-4 mx-6 mt-6 mb-4">
            <div className="text-InterfaceTexttitle text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[0.938vw] font-[600] mb-2">
              {t('nonRegisteredPartners')}
            </div>
            <div className="text-InterfaceTextdefault text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[400] mb-4">
              {t('registeredPartnerDescription')}
            </div>

            <Link href="/account/partner-registration">
              <Button
                type="button"
                className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-BrandNeutral700 bg-BrandNeutralpure text-background text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
                variant="skybluegradient"
                size="sm"
              >
                <i className="cursor-pointer cloud-card text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
                {t('proceedRegistrationButton')}
              </Button>
            </Link>
          </div>

          <SheetFooter className="mr-3 mb-2 m-6">
            <Button
              type="button"
              className="py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none border border-[#E5E7EB] bg-[#f5f9fc] text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
              variant="outline"
              size="sm"
            >
              <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              variant="skybluegradient"
              className="blueBtn lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
            >
              {' '}
              <i className="cloud-profile"></i>
              {t('submit')}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
