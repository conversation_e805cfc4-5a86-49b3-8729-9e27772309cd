'use client';
import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
} from '@redington-gulf-fze/cloudquarks-component-library';
// import Link from 'next/link';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types/components';
import { useForm, Controller } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import {
  setFilters,
  // clearFilters,
} from '@/store/slices/filters/filter-slice';
import { RootState } from '@/store';
import { useTranslations } from 'next-intl';

interface Props extends TermsSheetProps {
  moduleKey: string;
}
interface FormValues {
  createdOnFrom?: Date;
  createdOnTo?: Date;
  validTillFrom?: Date;
  validTillTo?: Date;
  status?: string;
  createdBy?: string;
  initiatedBy?: string;
}

export default function ApplyFilterPopup({ moduleKey }: Props) {
  const t = useTranslations();
  const closeRef = React.useRef<HTMLButtonElement>(null);
  const dispatch = useDispatch();
  const filterState = useSelector((state: RootState) => state.filters[moduleKey]);
  const filters = filterState?.filters || {};

  const statusOptions = [
    { label: 'All', value: 'All' },
    { label: 'UAE', value: 'mea' },
    { label: 'Turky', value: 'tukry' },
  ];

  const toDate = (value: string | Date | null | undefined): Date | undefined => {
    if (!value) return undefined;
    const date = typeof value === 'string' ? new Date(value) : value;
    return date instanceof Date && !isNaN(date.getTime()) ? date : undefined;
  };

  const defaultValues: FormValues = {
    createdOnFrom: toDate(filters.createdOnFrom as string | Date | null | undefined),
    createdOnTo: toDate(filters.createdOnTo as string | Date | null | undefined),
    validTillFrom: toDate(filters.validTillFrom as string | Date | null | undefined),
    validTillTo: toDate(filters.validTillTo as string | Date | null | undefined),
    status:
      typeof filters.status === 'object' && filters.status !== null && 'value' in filters.status
        ? filters.status.value
        : '',
    createdBy:
      typeof filters.createdBy === 'object' &&
      filters.createdBy !== null &&
      'value' in filters.createdBy
        ? filters.createdBy.value
        : '',
    initiatedBy:
      typeof filters.initiatedBy === 'object' &&
      filters.initiatedBy !== null &&
      'value' in filters.initiatedBy
        ? filters.initiatedBy.value
        : '',
  };

  const { control, handleSubmit, reset } = useForm<FormValues>({ defaultValues });
  const prevFiltersRef = React.useRef(filters);

  React.useEffect(() => {
    if (JSON.stringify(prevFiltersRef.current) !== JSON.stringify(filters)) {
      reset({
        createdOnFrom: toDate(filters.createdOnFrom as string | Date | null | undefined),
        createdOnTo: toDate(filters.createdOnTo as string | Date | null | undefined),
        validTillFrom: toDate(filters.validTillFrom as string | Date | null | undefined),
        validTillTo: toDate(filters.validTillTo as string | Date | null | undefined),
        status:
          typeof filters.status === 'object' && filters.status !== null && 'value' in filters.status
            ? (filters.status as { value: string }).value
            : '',
        createdBy:
          typeof filters.createdBy === 'object' &&
          filters.createdBy !== null &&
          'value' in filters.createdBy
            ? filters.createdBy.value
            : '',
        initiatedBy:
          typeof filters.initiatedBy === 'object' &&
          filters.initiatedBy !== null &&
          'value' in filters.initiatedBy
            ? filters.initiatedBy.value
            : '',
      });
      prevFiltersRef.current = filters;
    }
  }, [filters, reset]);
  const onSubmit = (data: FormValues) => {
    const statusLabel = statusOptions.find((opt) => opt.value === data.status)?.label || '';

    const payload = {
      createdOnFrom: data.createdOnFrom ?? null,
      createdOnTo: data.createdOnTo ?? null,
      validTillFrom: data.validTillFrom ?? null,
      validTillTo: data.validTillTo ?? null,
      status: data.status ? { value: data.status, label: statusLabel } : undefined,
      initiatedBy: data.initiatedBy ? { value: data.initiatedBy, label: statusLabel } : undefined,
      createdBy: data.createdBy ? { value: data.createdBy, label: statusLabel } : undefined,
    };
    dispatch(setFilters({ moduleKey: moduleKey, filters: payload }));
    closeRef.current?.click();
  };
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-2  h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="relative flex items-center">
            <i className="cloud-filtericon text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto h-[400px] xl:h-[500px] 3xl:h-[32.25vw]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('dateCreated')}
                </Label>
                <div className="grid grid-cols-2 gap-[10px] xl:gap-[10px] 3xl:gap-[0.521vw]">
                  <div className="col-span-1">
                    <Controller
                      control={control}
                      name="createdOnFrom"
                      render={({ field }) => (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span className="text-InterfaceTextsubtitle text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                  {t('selectFromDate')}
                                </span>
                              )}
                              <i className="cloud-canlendar"></i>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 bg-[#fff]" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                  </div>
                  <div className="col-span-1">
                    <Controller
                      control={control}
                      name="createdOnTo"
                      render={({ field }) => (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span className="text-InterfaceTextsubtitle text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                  {t('selectToDate')}
                                </span>
                              )}
                              <i className="cloud-canlendar"></i>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 bg-[#fff]" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                  </div>
                </div>
              </div>
              <div className="grid gap-1.5">
                <Label
                  htmlFor="firstname"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('validTillDate')}
                </Label>
                <div className="grid grid-cols-2 gap-[10px] xl:gap-[10px] 3xl:gap-[0.521vw]">
                  <div className="col-span-1">
                    <Controller
                      control={control}
                      name="validTillFrom"
                      render={({ field }) => (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span className="text-InterfaceTextsubtitle text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                  {t('selectFromDate')}
                                </span>
                              )}
                              <i className="cloud-canlendar"></i>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 bg-[#fff]" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                  </div>
                  <div className="col-span-1">
                    <Controller
                      control={control}
                      name="validTillTo"
                      render={({ field }) => (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span className="text-InterfaceTextsubtitle text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                                  {t('selectToDate')}
                                </span>
                              )}
                              <i className="cloud-canlendar"></i>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 bg-[#fff]" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                  </div>
                </div>
              </div>
              <div className="relative ">
                <label
                  htmlFor="required-email"
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  {t('initiatedBy')}
                </label>
                <div className="mt-2">
                  <Controller
                    control={control}
                    name="initiatedBy"
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectValue placeholder={t('selectInitiatedby')} className="" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#fff]">
                          <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                            {statusOptions.map((opt) => (
                              <SelectItem key={opt.value} value={opt.value}>
                                {opt.label}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>

              <div className="relative ">
                <label
                  htmlFor="required-email"
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  {t('createdBy')}
                </label>
                <div className="mt-2">
                  <Controller
                    control={control}
                    name="createdBy"
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectValue placeholder={t('selectCreatedBy')} className="" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#fff]">
                          <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                            {statusOptions.map((opt) => (
                              <SelectItem key={opt.value} value={opt.value}>
                                {opt.label}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>

              <div className="relative ">
                <label
                  htmlFor="required-email"
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  {t('status')}
                </label>
                <div className="mt-2">
                  <Controller
                    control={control}
                    name="status"
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectValue placeholder={t('selectStatus')} className="" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#fff]">
                          <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                            {statusOptions.map((opt) => (
                              <SelectItem key={opt.value} value={opt.value}>
                                {opt.label}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                <SheetClose asChild>
                  <Button
                    size="sm"
                    ref={closeRef}
                    // onClick={() => {
                    //   dispatch(clearFilters());
                    // }}
                    className="flex gap-[8px] 3xl:gap-[0.417vw] bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                      {t('cancel')}
                    </div>
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  size="sm"
                  className="flex items-center gap-[8px] 3xl:gap-[0.417vw] submittbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-filter text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    {t('applyFilter')}
                  </div>
                </Button>
              </div>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
