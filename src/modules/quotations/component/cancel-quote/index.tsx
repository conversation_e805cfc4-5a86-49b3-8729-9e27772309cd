'use client';

import { CancelOuoteSheetProps } from '@/types';
import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
  SheetClose,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
  SelectGroup,
  SelectItem,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import Link from 'next/link';
import { useState } from 'react';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function CancelQuote({ open, onClose }: CancelOuoteSheetProps) {
  const [confirmed, setConfirmed] = useState(false);
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTrigger>
        {/* <div className="absolute bottom-0 left-0 right-0 px-[18px] xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[16px] 3xl:py-[0.833vw] ">
          <div className="w-full bg-buttonbasedefaulthover2 text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokesoft py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
            Cancel
          </div>
        </div> */}
      </SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-[26px] xl:px-[26px] 3xl:px-[1.354vw] mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-danger text-closecolor text-[72px] font-normal"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] font-normal leading-[140%] text-center">
                Decline Quote
              </div>
            </div>
          </div>
          {!confirmed ? (
            <>
              <div className=" mx-[80px] lg:mx-[80px] xl:mx-[80px] 2xl:mx-[80px] 3xl:mx-[4.167vw] text-InterfaceTextsubtitle text-[20px] font-normal leading-[140%] text-center">
                Would you like to decline this quote? This action cannot be reversed, please confirm
                to proceed further.
              </div>
              <div className="flex justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
                <button
                  onClick={() => setConfirmed(true)}
                  className="text-[16px] text-background bg-closecolor hover:bg-[#067532] font-medium px-[16px] py-[10px] flex justify-center items-center gap-2 w-[150px] 3xl:w-[7.813vw]"
                >
                  <i className="cloud-tickcircle text-[18px]"></i>
                  Yes, Confirm
                </button>
                <SheetClose asChild>
                  <button className="text-[16px] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium px-[16px] py-[10px] flex justify-center items-center gap-2 w-[150px] 3xl:w-[7.813vw]">
                    <i className="cloud-closecircle text-[18px]"></i>
                    No, Later
                  </button>
                </SheetClose>
              </div>
            </>
          ) : (
            <>
              <div className="space-y-[30px]">
                <div className="relative ">
                  <label
                    htmlFor="required-email"
                    className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                  >
                    Ticket Category
                  </label>
                  <div className="mt-2">
                    <Select defaultValue="All">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectValue placeholder="Select Country" className="" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                          <SelectItem value="All">All</SelectItem>
                          <SelectItem value="mea">UAE</SelectItem>
                          <SelectItem value="tukry">Turky</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Textarea
                    className="rounded-[2px] border border-InterfaceStrokehard"
                    placeholder="Type here....."
                  />
                  <div className=" text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceStrokehard pt-[12px] xl:pt-[12px] 3xl:pt-[0.625vw]">
                    Max 50 characters
                  </div>
                </div>
              </div>
              <div className="flex justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
                <Link href={''}>
                  <button
                    onClick={() => setConfirmed(true)}
                    className="text-[16px] text-background bg-closecolor hover:bg-[#067532] font-medium px-[16px] py-[10px] flex justify-center items-center gap-2 w-[150px] 3xl:w-[7.813vw]"
                  >
                    <i className="cloud-tickcircle text-[18px]"></i>
                    Submit
                  </button>
                </Link>

                <SheetClose asChild>
                  <button className="text-[16px] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium px-[16px] py-[10px] flex justify-center items-center gap-2 w-[150px] 3xl:w-[7.813vw]">
                    <i className="cloud-closecircle text-[18px]"></i>
                    No, Later
                  </button>
                </SheetClose>
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
