'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';
export default function CreateQuote() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
          <i className="cloud-Add text-Brand<PERSON>up<PERSON>1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
          {t('createNewQuote')}
        </Button>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[570px] xl:max-w-[570px] 3xl:max-w-[29.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('createNewQuote')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[500px] 2xl:h-[550px] 3xl:h-[36.25vw]">
          <div className="space-y-[10px] xl:space-y-[10px] 3xl:space-y-[0.521vw] text-center">
            <div className="bg-BrandHighlight50 p-[45px] xl:p-[45px] 3xl:p-[2.344vw] border border-BrandHighlight400 text-center space-y-[18px] xl:space-y-[18px] 3xl:space-y-[0.846vw] ">
              <div>
                <i className="cloud-savedoc text-BrandHighlight400 text-[15px] xl:text-[15px] 3xl:text-[20px]"></i>
                <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-medium text-InterfaceTextsubtitle pt-[4px]">
                  {t('STEPONE')}
                </div>
              </div>
              <div className="text-[15px] xl:text-[15px] 3xl:text-[0.833vw] font-normal text-InterfaceTextsubtitle px-[30px]">
                {t('quoteInstructions1')}
              </div>
            </div>
            <div>
              <i className="cloud-rightarrowfill text-[30px] xl:text-[32px] 2xl:text-[32px] 3xl:text-[1.802vw] text-BrandPrimary600 transform rotate-90 inline-block"></i>
            </div>
            <div className="bg-BrandHighlight50 p-[45px] xl:p-[45px] 3xl:p-[2.344vw] border border-BrandHighlight400 text-center space-y-[18px] xl:space-y-[18px] 3xl:space-y-[0.846vw] ">
              <div>
                <i className="cloud-task text-BrandHighlight400 text-[15px] xl:text-[15px] 3xl:text-[20px]"></i>
                <div className="text-[12px] xl:text-[12px] 3xl:text-[0.625vw] font-medium text-InterfaceTextsubtitle pt-[4px]">
                  {t('STEPTWO')}
                </div>
              </div>
              <div className="text-[15px] xl:text-[15px] 3xl:text-[0.833vw] font-normal text-InterfaceTextsubtitle px-[30px]">
                {t('quoteInstructions2')}
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
