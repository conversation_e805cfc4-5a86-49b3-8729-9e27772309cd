'use client';
import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Calendar,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
// import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import ApplyFilterPopup from '../quotation-filter/index';
import Link from 'next/link';
import { format } from 'date-fns';
import { CancelQuote } from '../cancel-quote';
import AppliedFilter from '@/components/common/filter-chip/index';
import { useTranslations } from 'next-intl';
export default function Quotation() {
  const t = useTranslations();
  const quotationFilterConfig = [
    { key: 'createdOnFrom', label: 'Created From', isDate: true },
    { key: 'createdOnTo', label: 'Created To', isDate: true },
    { key: 'validTillFrom', label: 'Valid Till From', isDate: true },
    { key: 'validTillTo', label: 'Valid Till To', isDate: true },
    { key: 'status', label: 'Status', isDate: false },
    { key: 'initiatedBy', label: 'Initiated By', isDate: false },
    { key: 'createdBy', label: 'Created By', isDate: false },
  ];
  const [fromDate, setFromDate] = React.useState<Date | undefined>(new Date('2025-01-01'));
  const [toDate, setToDate] = React.useState<Date | undefined>(new Date('2025-03-01'));
  const [cancelquote, setCancelquote] = React.useState<boolean>(false);

  const clearAll = () => {
    setFromDate(undefined);
    setToDate(undefined);
  };

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'requestid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('quoteID')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className=" h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Quote ID"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => (
        <Link
          href={`/sales/quotations/request-quotation?status=${row.getValue('status') as string}`}
        >
          <div className=" cursor-pointer ">{row.getValue('requestid')}</div>
        </Link>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'cdpid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('cartID')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Cart ID"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('cdpid')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'requestedby',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('dateCreated')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <ColumnHeaderFilter
              placeholder="Date Created"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('requestedby')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'requesteddate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('initiatedBy')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder=" Initiated By"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('requesteddate')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'requestreason',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('createdBy')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Created By"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('requestreason')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'requestcredit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('totalItems')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Total Items"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('requestcredit')}</div>,

      minSize: 400,
      maxSize: 400,
    },
    {
      accessorKey: 'requestcredit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('currency')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Currency"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('requestcredit')}</div>,

      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: 'requestcredit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('totalValue')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Total Value"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('requestcredit')}</div>,

      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: 'requestcredit',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('validTillDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Select Date"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('requestcredit')}</div>,

      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Select"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Approved: {
            colorClass: 'bg-[#EDFFF3] text-[#00953A] border-[#71FFA8]',
          },
          Rejected: {
            colorClass: 'bg-[#FDF2C8] text-[#903D10] border-[#FACE4F]',
          },
          Canceled: {
            colorClass: 'bg-[#FFF3F0] text-[#D42600] border-[#FFB5A5]',
          },
          InReview: {
            colorClass: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
          },
          Expired: {
            colorClass: 'bg-[#F5F6F8] text-[#91A5C3] border-[#DBE1EA]',
          },
          Declined: {
            colorClass: 'bg-[#E8F4E4] text-[#2A4423] border-[#E5E7EB]',
          },
          Accepted: {
            colorClass: 'bg-[#E8F4E4] text-[#72B35F] border-[#D2E8CA]',
          },
          New: {
            colorClass: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800 border-gray-300',
        };
        const handleClick = () => {
          if (status === 'Pending' || status === 'Failed') {
            window.location.href = `/sales/quotations/request-quotation?status=${status}`;
          }
        };

        return (
          <div className="cursor-pointer ">
            <div
              onClick={handleClick}
              className={`inline-flex py-[6px] xl:py-[6px] 3xl:py-[0.357vw] px-[10px] rounded-[2px] text-[15px] xl:text-[15px] 3xl:text-[0.781vw] font-semibold border items-center text-center justify-center ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 100,
      maxSize: 120,
    },

    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('actions')}
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[150px] xl:w-[150px] 3xl:w-[7.833vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <Link
                  href={`/sales/quotations/request-quotation?status=${row.getValue('status') as string}`}
                >
                  <div>
                    <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
                      <i className="cloud-edit text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
                      {t('edit')}
                    </div>
                  </div>
                </Link>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    requestid: string;
    cdpid: string;
    requestedby: string;
    requesteddate: string;
    requestreason: string;
    requestcredit: string;
    oldcredit: string;
    revisedcredit: string;
    status: string;
  };

  const registrationdata = [
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Expired',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Canceled',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Declined',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Accepted',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Rejected',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Declined',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Approved',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'New',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
    {
      requestid: 'QHSKW123',
      cdpid: 'CID-1001',
      requestedby: '01/03/2025',
      requesteddate: 'Alpha Data',
      requestreason: 'Will Jacks',
      requestcredit: '2',
      status: 'Declined',
      oldcredit: '50,000.00',
      revisedcredit: '24/04/2025',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">List of Quotes</div>
            <div className="text-InterfaceTextsubtitle font14">{t('showingRecords')}</div>
          </div>
          <div className="  flex gap-[24px] xl:gap-[24px] 3xl:gap-[1.25vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:font14 text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[10px] xl:top-[10px] 3xl:top-[10px] left-[12px] font16 "></i>
            </div>
            <div>
              <ApplyFilterPopup moduleKey="quotationFilter" open={false} onClose={() => {}} />
            </div>
          </div>
        </div>
        <AppliedFilter moduleKey="quotationFilter" filterConfig={quotationFilterConfig} />
        <div className="overflow-x-auto">
          <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] text-sm font-normal">
            <div className="flex items-center bg-[#F4F5F7] px-2 py-[6px] rounded-sm">
              <span className="text-[#6B7280]">{t('createdDate')} :</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {fromDate ? format(fromDate, 'dd-MM-yyyy') : 'Select Date'}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={fromDate} onSelect={setFromDate} />
                </PopoverContent>
              </Popover>
              {fromDate && (
                <button onClick={() => setFromDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              )}
            </div>

            <div className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              <span className="text-InterfaceTextsubtitle">{t('status')} :</span> All
              <i className="cloud-closecircle text-ClearAll"></i>
            </div>
            {(fromDate || toDate) && (
              <button
                onClick={clearAll}
                className="flex items-center text-[#8B8B8B] font-[400] hover:text-black"
              >
                <i className="cloud-closecircle mr-[4px]"></i>
                {t('clearAll')}
              </button>
            )}
          </div>
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={true} />
        </div>
        <TablePagination />
      </div>

      {/* <DeclineQuote
      open={declinequote}
      onClose={()=>setDeclinequote(false)}
      /> */}
      <CancelQuote open={cancelquote} onClose={() => setCancelquote(false)} />
    </>
  );
}
