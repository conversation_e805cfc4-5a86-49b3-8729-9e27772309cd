'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import QuotationBarchart from '@/components/common/charts/quotationbarchart';
import { useTranslations } from 'next-intl';
export default function QuotationCards() {
  const t = useTranslations();
  return (
    <div>
      <div className="grid grid-cols-3 gap-[16px] xl:gap-[10px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div>
          <div className="grid grid-cols-6  p-[20px] 2xl:p-[24px] 3xl:p-[1.25vw] rounded-[12px] 3xl:rounded-[0.625vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative h-[170px] 3xl:h-[10vw]">
            <div className="col-span-1 3xl:col-span-2">
              {/* <i className="cloud-folder text-[#7F8488] text-[29px]"></i> */}
            </div>

            <div className="col-span-5 3xl:col-span-4 w-full h-[110px] 3xl:h-[7vw]">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: 0,
                  top: 23,
                  itemGap: 15,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['partner', 'redington', 'vendor'].map((item) => t(item)),
                  textStyle: { fontSize: 10 },
                }}
                name={'Nightingale Chart'}
                radius={[30, 70]}
                center={['40%', '50%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 10,
                  length2: 10,
                  lineStyle: {
                    width: 1,
                    color: '#000', // make the arrow black
                  },
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 65, name: t('partner'), itemStyle: { color: '#73609B' } },
                  { value: 50, name: t('redington'), itemStyle: { color: '#4D9E99' } },
                  { value: 50, name: t('vendor'), itemStyle: { color: '#5BB559' } },
                ]}
              />
            </div>
            <div className="absolute left-[21px] bottom-[35px]">
              <div className="pb-4">
                <i className="cloud-folder text-[#7F8488] text-[29px]"></i>
              </div>
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[14px] font-semibold leading-[140%]">
                {t('totalQuotes')}*
              </div>
              <div className="text-[40px] 2xl:text-[40px] 3xl:text-[40px] font-semibold truncate text-InterfaceTextdefault">
                175
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-6  p-[20px] 2xl:p-[24px] 3xl:p-[1.25vw] rounded-[12px] 3xl:rounded-[0.625vw] h-[170px] 3xl:h-[10vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
            <div className="col-span-1 3xl:col-span-2">
              {/* <i className="cloud-note-remove text-[#7F8488] text-[29px]"></i> */}
            </div>

            <div className="col-span-5 3xl:col-span-4 w-full h-[110px] 3xl:h-[7vw]">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: 0,
                  top: 23,
                  itemGap: 15,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['valid', 'expired'].map((item) => t(item)),
                  textStyle: { fontSize: 10 },
                }}
                name={'Nightingale Chart'}
                radius={[30, 70]}
                center={['40%', '50%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 10,
                  length2: 10,
                  lineStyle: {
                    width: 1,
                    color: '#000', // make the arrow black
                  },
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 10, name: t('valid'), itemStyle: { color: '#1570EF' } },
                  { value: 8, name: t('expired'), itemStyle: { color: '#FACE4F' } },
                ]}
              />
              {/* <Piechart
                legends={{
                  orient: 'vertical',
                  right: -5,
                  top: 23,
                  itemGap: 20,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['Valid', 'Expired'],
                }}
                name={'Nightingale Chart'}
                radius={[30, 65]}
                center={['40%', '50%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 10,
                  length2: 10,
                  lineStyle: {
                    width: 1,
                    color: '#000', // make the arrow black
                  },
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 10, name: 'Valid', itemStyle: { color: '#1570EF' } },
                  { value: 8, name: 'Expired', itemStyle: { color: '#FACE4F' } },
                ]}
              /> */}
            </div>

            <div className="absolute left-[21px] bottom-[35px]">
              <div className="pb-4">
                <i className="cloud-note-remove text-[#7F8488] text-[29px]"></i>
              </div>
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[14px] font-semibold leading-[140%]">
                {t('awaitingAction')}*
              </div>
              <div className="text-[40px] 2xl:text-[40px] 3xl:text-[40px] font-semibold truncate text-InterfaceTextdefault">
                15
              </div>
            </div>
          </div>
        </div>

        <div className=" gap-[5px] 3xl:gap-[0.625vw] p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)]">
          <div className="w-full h-[150px]">
            <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold">
              {t('quotesByStatus')}*
            </div>
            <QuotationBarchart />
          </div>
        </div>
      </div>
    </div>
  );
}
