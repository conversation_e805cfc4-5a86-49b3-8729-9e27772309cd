'use client';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import ActivityLog from '@/modules/quotations/component/activity-log';
import PartnerRemarks from '@/modules/quotations/component/partner-remarks';
import {
  Button,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { DeclineQuote } from '../../decline-quote';
import ItemDetails from '../components/item-details';
import { useSearchParams } from 'next/navigation';
// import { CancelQuote } from '../../cancel-quote';
import { useTranslations } from 'next-intl';

export default function RequestQuotation() {
  const t = useTranslations();
  const BreadcrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('sales'),
      href: '/sales/quotations',
    },
    {
      label: t('quotations'),
      href: '/sales/quotations',
    },
  ];

  const searchParams = useSearchParams(); // App Router
  const [status, setStatus] = useState<
    'Approved' | 'Rejected' | 'Canceled' | 'InReview' | 'Expired' | 'Declined' | 'Accepted' | 'New'
  >('New');
  useEffect(() => {
    const s = searchParams.get('status');
    if (
      s === 'Approved' ||
      s === 'Rejected' ||
      s === 'Canceled' ||
      s === 'InReview' ||
      s === 'Expired' ||
      s === 'Declined' ||
      s === 'Accepted' ||
      s === 'New'
    ) {
      setStatus(s);
    }
  }, [searchParams]);
  const statusConfig: Record<typeof status, string> = {
    Approved: 'bg-[#EDFFF3] text-[#00953A] border-[#71FFA8]',
    Rejected: 'bg-[#FDF2C8] text-[#903D10] border-[#FACE4F]',
    Canceled: 'bg-[#FFF3F0] text-[#D42600] border-[#FFB5A5]',
    InReview: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
    Expired: 'bg-[#F5F6F8] text-[#91A5C3] border-[#DBE1EA]',
    Declined: 'bg-[#E8F4E4] text-[#2A4423] border-[#E5E7EB]',
    Accepted: 'bg-[#E8F4E4] text-[#72B35F] border-[#D2E8CA]',
    New: 'bg-[#DAEEFF] text-[#1B4EB2] border-[#90D1FF]',
  };

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="grid grid-cols-12 px-[28px] xl:px-[32px] 3xl:px-[1.667vw] py-[28px] xl:py-[28px] 3xl:py-[1.458vw] gap-[22px] xl:gap-[24px] 3xl:gap-[1.25vw]">
        <div className="col-span-9">
          <div className="flex items-center justify-between bg-interfacesurfacecomponent py-[14px] xl:py-[14px] 3xl:py-[0.833vw] px-[20px] xl:px-[20px] 3xl:px-[1.042vw] cardShadow">
            <div className="font20 text-InterfaceTexttitle font-semibold">
              {t('requestForQuotation')} - RFQ1001{' '}
              <span
                className={`mx-[12px] inline-flex py-[2px] xl:py-[2px] 3xl:py-[2px] px-[10px] rounded-[2px] text-[15px] xl:text-[15px] 3xl:text-[0.781vw] font-semibold border items-center text-center justify-center ${statusConfig[status]}`}
              >
                {t('status')}
              </span>
            </div>
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div>
                <Link href="/sales/quotations">
                  <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[linear-gradient(180deg,_#FEFEFE_0%,_#F6F8FA_100%)] border border-InterfaceStrokesoft flex gap-[8px] 3xl:gap-[0.417vw] rounded-none text-InterfaceTextdefault font-normal">
                    <i className="cloud-back text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                    {t('back')}
                  </Button>
                </Link>
              </div>
              <div>
                <PartnerRemarks />
              </div>
              <div>
                <ActivityLog />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-[22px] xl:gap-[24px] 3xl:gap-[1.25vw] ">
            <div className="cardShadow bg-interfacesurfacecomponent col-span-1 py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw] ">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
                {t('quoteInformation')}
              </div>
              <div className="">
                <div className="grid grid-cols-3 gap-[18px] xl:gap-[20px] 3xl:gap-[1.042vw] ">
                  <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('cartID')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      CT100001
                    </div>
                  </div>
                  <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('quoteID')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      RFQ1001
                    </div>
                  </div>
                  <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('quoteCreated')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      03/03/2025
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-[18px] xl:gap-[20px] 3xl:gap-[1.042vw] ">
                  <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('initiatedBy')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      Alpha Systems
                    </div>
                  </div>
                  <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('createdBy')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      Owen Smith
                    </div>
                  </div>
                  <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                      {t('referenceNo')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                      #00112233
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="cardShadow bg-interfacesurfacecomponent col-span-1 py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw] ">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-[600] mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
                {t('endCustomerInformation')}
              </div>
              <div className="mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw] space-y-[22px] xl:space-y-[24px] 3xl:spac-y-[1.25vw]">
                <div className="flex gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
                  <div className="relative w-full">
                    <Select defaultValue="Select End Customer">
                      <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm leading-6 border border-InterfaceStrokehard rounded-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                        <SelectValue placeholder="Customer Name if Selected" className="" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup className="bg-[#FFF] text-InterfaceTextsubtitle border-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] ">
                          <SelectItem value="Select End Customer">Select End Customer</SelectItem>
                          <SelectItem value="india">India</SelectItem>
                          <SelectItem value="mea">UAE</SelectItem>
                          <SelectItem value="tukry">Turky</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="cardShadow bg-interfacesurfacecomponent mt-[22px] xl:mt-[22px] 3xl:mt-[1.25vw]">
            <ItemDetails />
          </div>
          <div></div>
        </div>

        <div className=" col-span-3 relative bg-interfacesurfacecomponent cardShadow py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
          <div className="flex justify-between">
            <div className="text-InterfaceTexttitle font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw]">
              {t('quoteSummary')}
            </div>
            <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
              {t('USD')}
            </div>
          </div>

          <div className="mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw] bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[8px] 3xl:px-[0.417vw] py-2">
            <div className="flex justify-between items-center text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw] py-[10px] xl:py-[10px] 3xl:py-[0.625vw]">
              <div className="font-[400]"> {t('totalValue')}</div>
              <div className="font-[600]">8,000.00</div>
            </div>
          </div>
          <div className="absolute bottom-[52px] left-0 right-0 px-[18px] xl:px-[20px] 3xl:px-[1.042vw] py-[14px] xl:py-[16px] 3xl:py-[0.833vw] ">
            <Link href="/sales/quotations">
              <div className="space-y-2">
                <div className="w-full cursor-pointer bg-BrandPrimarypurenew text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokesoft py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] text-white">
                  {t('reSubmit')}
                </div>
              </div>
            </Link>
          </div>
          <DeclineQuote />
        </div>
      </div>
    </>
  );
}
