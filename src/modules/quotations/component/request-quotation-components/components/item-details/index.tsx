'use client';
import React, { useEffect, useState } from 'react';
import { Input } from '@redington-gulf-fze/cloudquarks-component-library';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

type User = {
  id: string;
  productname: string;
  currency: string;
  price: string;
  quantity: string;
  targetprice: string;
  approvedQuantity?: string;
  approvedPrice?: string;
  subtotal: string;
};

function ItemDetails() {
  const t = useTranslations();
  const searchParams = useSearchParams(); // App Router
  const [status, setStatus] = useState<
    'Approved' | 'Rejected' | 'Canceled' | 'InReview' | 'Expired' | 'Declined' | 'Accepted' | 'New'
  >('New');
  useEffect(() => {
    const s = searchParams.get('status');
    if (
      s === 'Approved' ||
      s === 'Rejected' ||
      s === 'Canceled' ||
      s === 'InReview' ||
      s === 'Expired' ||
      s === 'Declined' ||
      s === 'Accepted' ||
      s === 'New'
    ) {
      setStatus(s);
    }
  }, [searchParams]);
  const initialData: User[] = [
    {
      id: '1',
      productname: 'Advanced Communications',
      currency: 'USD',
      price: '500.00',
      quantity: '4',
      targetprice: '450.00',
      approvedQuantity: '2',
      approvedPrice: '3,500.00',
      subtotal: '2000.00',
    },
    {
      id: '2',
      productname: 'Cloud Storage',
      currency: 'USD',
      price: '100.00',
      quantity: '4',
      targetprice: '450.00',
      approvedQuantity: '2',
      approvedPrice: '3,500.00',
      subtotal: '200.00',
    },
    {
      id: '3',
      productname: 'Advanced Communications',
      currency: 'USD',
      price: '500.00',
      quantity: '4',
      targetprice: '450.00',
      approvedQuantity: '4',
      approvedPrice: '3,500.00',
      subtotal: '2000.00',
    },
    {
      id: '4',
      productname: 'Advanced Communications',
      currency: 'USD',
      price: '500.00',
      quantity: '4',
      targetprice: '450.00',
      approvedQuantity: '4',
      approvedPrice: '3,500.00',
      subtotal: '2000.00',
    },
  ];

  const [data, setData] = useState<User[]>(initialData);
  // const [showDetails, setShowDetails] = useState(false);
  const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>({});

  const updateQuantity = (id: string, delta: number) => {
    setData((prevData) =>
      prevData.map((item) => {
        if (item.id === id) {
          const currentQuantity = parseInt(item.quantity) || 0;
          const newQuantity = Math.max(currentQuantity + delta, 0);
          const pricePerUnit = parseFloat(item.price) || 0;
          const newSubtotal = (pricePerUnit * newQuantity).toFixed(2);
          return {
            ...item,
            quantity: newQuantity.toString(),
            subtotal: newSubtotal,
          };
        }
        return item;
      })
    );
  };

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          {t('productName')}
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        const isExpanded = expandedRows[item.id] || false;
        return (
          <div>
            <div className="text-[14px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
              {item.productname}
            </div>
            {isExpanded && (
              <div className="flex flex-col">
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Brand: <span className="text-InterfaceTextdefault font-normal">Microsoft</span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Segment: <span className="text-InterfaceTextdefault font-normal">Commercial</span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Term: <span className="text-InterfaceTextdefault font-normal">1 Year</span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Bill Type: <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                </div>
              </div>
            )}
            <div
              className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextprimary font-[400] cursor-pointer"
              onClick={() =>
                setExpandedRows((prev) => ({
                  ...prev,
                  [item.id]: !prev[item.id],
                }))
              }
            >
              {isExpanded ? 'Hide Details' : 'View Details'}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'currency',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('currency')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.currency}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] border border-InterfaceStrokehard"
          />
        </div>
      ),
    },
    {
      accessorKey: 'price',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('listPrice')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.price}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] border border-InterfaceStrokehard"
          />
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('quantity')}
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;

        if (status === 'Approved') {
          // Just render a readonly input like other fields
          return (
            <div className="flex justify-center items-center h-full">
              <Input
                value={item.quantity}
                readOnly
                className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] border border-InterfaceStrokehard"
              />
            </div>
          );
        }

        // Editable quantity with + / - buttons
        return (
          <div className="flex justify-center items-center h-full">
            <div className="flex items-center justify-center border border-[#FFD916] px-3 gap-3 bg-white">
              <div onClick={() => updateQuantity(item.id, -1)} className="p-0 m-0 cursor-pointer">
                <span className="text-[18px] text-InterfaceTexttitle font-[400]">-</span>
              </div>
              <Input
                value={item.quantity}
                readOnly
                className="w-[40px] h-[32px] text-center text-InterfaceTexttitle font-[500] border-none focus:ring-0 focus-visible:ring-0 shadow-none"
              />
              <div onClick={() => updateQuantity(item.id, 1)} className="p-0 m-0 cursor-pointer">
                <span className="text-[18px] text-InterfaceTexttitle font-[400]">+</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'targetprice',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          {t('targetPrice')}
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;

        const className =
          status === 'Approved'
            ? 'w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] border border-InterfaceStrokehard'
            : 'w-[100px] text-center text-InterfaceTextdefault font-[500] bg-[#FFF] border border-InterfaceStrokehard';

        return (
          <div className="flex justify-center items-center h-full">
            <Input value={item.targetprice} className={className} />
          </div>
        );
      },
    },
  ];

  // 👇 Inject conditional columns inline
  if (status === 'Approved') {
    notificationColumns.push(
      {
        accessorKey: 'approvedQuantity',
        header: () => (
          <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
            {t('approvedQuantity')}
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex justify-center items-center h-full">
            <Input
              value={row.original.approvedQuantity}
              className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] border border-InterfaceStrokehard"
            />
          </div>
        ),
      },
      {
        accessorKey: 'approvedPrice',
        header: () => (
          <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
            {t('approvedPrice')}
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex justify-center items-center h-full">
            <Input
              value={row.original.approvedPrice}
              className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] border border-InterfaceStrokehard"
            />
          </div>
        ),
      }
    );
  }

  // Always push subtotal at the end
  notificationColumns.push({
    accessorKey: 'subtotal',
    header: () => (
      <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
        {t('subTotal')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex justify-center items-center h-full">
        <Input
          value={row.original.subtotal}
          readOnly
          className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] border border-InterfaceStrokehard"
        />
      </div>
    ),
  });

  return (
    <div className="">
      <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw] border-b border-InterfaceStrokesoft">
        <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
          {t('itemDetails')}
        </div>
      </div>
      <div className="px-[20px] 3xl:px-[1.042vw] py-[16px] 3xl:py-[0.833vw]">
        <div className="product-table custtable custtable1">
          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
            <DataTable data={data} columns={notificationColumns} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ItemDetails;
