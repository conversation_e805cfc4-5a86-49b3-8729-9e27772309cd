'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
  SheetClose,
  Textarea,
  Button,
} from '@redington-gulf-fze/cloudquarks-component-library';
import {
  Sheet,
  SheetContent,
  SheetTitle,
  She<PERSON><PERSON>rigger,
  SelectGroup,
  Select<PERSON>tem,
  SheetFooter,
} from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';

export default function NewCartPopup() {
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-medium">
          <i className="cloud-reciept text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
          New Cart
        </Button>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              New Cart
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[500px] 2xl:h-[550px] 3xl:h-[36.25vw]">
          <div className="space-y-[10px] xl:space-y-[10px] 3xl:space-y-[0.521vw]">
            <div className="space-y-[18px]">
              <div className="relative ">
                <label
                  htmlFor="required-email"
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Cart Title
                </label>
                <div className="mt-2">
                  <Select defaultValue="All">
                    <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] ">
                      <SelectValue placeholder="Cart Title" className="" />
                    </SelectTrigger>
                    <SelectContent className="bg-interfacesurfacecomponent">
                      <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectItem value="All">Enter Cart Title</SelectItem>
                        <SelectItem value="mea">UAE</SelectItem>
                        <SelectItem value="tukry">Turky</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <label
                  htmlFor="required-email"
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  Description
                </label>
                <div>
                  <Textarea
                    className="rounded-[2px] border border-InterfaceStrokehard"
                    placeholder="Type here....."
                  />
                  <div className=" text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceStrokehard pt-[12px] xl:pt-[12px] 3xl:pt-[0.625vw]">
                    Character Limit: 50
                  </div>
                </div>
              </div>
              <div className="relative ">
                <label
                  htmlFor="required-email"
                  className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
                >
                  End Customer
                </label>
                <div className="mt-2">
                  <Select defaultValue="All">
                    <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm border border-InterfaceStrokehard rounded-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] ">
                      <SelectValue placeholder="Cart Title" className="" />
                    </SelectTrigger>
                    <SelectContent className="bg-interfacesurfacecomponent">
                      <SelectGroup className="text-InterfaceTextsubtitle border-none text-[12px] md:text-[14px] xl:text-[14px] 3xl:text-[0.729vw]">
                        <SelectItem value="All"> Select End Customer</SelectItem>
                        <SelectItem value="mea">UAE</SelectItem>
                        <SelectItem value="tukry">Turky</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                <Link href="/#">
                  <Button
                    size="sm"
                    className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                      Cancel
                    </div>
                  </Button>
                </Link>
                <Link href="/#">
                  <Button
                    size="sm"
                    className="flex items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-filledcart text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                      Add and Go To Cart
                    </div>
                  </Button>
                </Link>
              </div>
            </div>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}
