'use client';

import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function ConvertQuote() {
  return (
    <Sheet>
      <SheetTrigger>
        <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
          <i className="cloud-trash text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
          convert to Quote
        </div>
      </SheetTrigger>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full overflow-y-auto px-[26px] xl:px-[26px] 3xl:px-[1.354vw] mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-info text-BrandSupport1pure text-[72px] font-normal"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[33px] xl:text-[33px] 2xl:text-[33px] 3xl:text-[1.823vw] font-normal leading-[150%] text-center">
                Convert Quote to Cart
              </div>
            </div>
          </div>

          <div className=" mx-[60px] lg:mx-[60px] xl:mx-[60px] 2xl:mx-[60px] 3xl:mx-[3.125vw] text-InterfaceTextdefault text-[18px] xl:text-[18px] 3xl:text-[0.981vw] font-normal leading-[140%] text-center">
            Would you like to accept and convert this quote to cart?
          </div>
          <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
            <button className="text-[15px] xl:text-[15px] 3xl:text-[0.781vw] text-background bg-[#067532] font-normal py-[8px] xl:py-[8px] 3xl:py-[0.417vw] flex justify-center items-center gap-2 w-[200px] xl:w-[200px] 3xl:w-[10.417vw]">
              Accept & Convert to Cart
            </button>
            <SheetClose asChild>
              <button className="text-[15px] xl:text-[15px] 3xl:text-[0.781vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-normal w-[200px] xl:w-[200px] 3xl:w-[10.417vw] py-[8px] xl:py-[8px] 3xl:py-[0.417vw] flex justify-center items-center gap-2 ">
                Accept & Convert Later
              </button>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
