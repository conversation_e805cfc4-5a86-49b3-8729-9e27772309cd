'use client';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Link from 'next/link';
import React from 'react';
import Quotation from '../component/quotation-tabel';
import QuotationCards from '../component/quotation-cards';
import CreateQuote from '../component/create-new-quote';
import { useTranslations } from 'next-intl';
export default function QuotationTemplate() {
  const t = useTranslations();

  return (
    <div className="">
      <div className=" h-full">
        <div className="relative h-full px-[32px] py-[20px] 3xl:px-[1.667vw] 3xl:py-[1.042vw] bg-[#F6F7F8]">
          <div className="pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw]">
            <div className="flex flex-col pb-[10px] xl:pb-[10px] 3xl:pb-[0.525vw]">
              <div className="text-interfacetexttitle2 text-[20px] xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                {t('quotations')}
              </div>
              <div className="italic text-InterfaceTextsubtitle text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw]">
                *{t('asOnDate')}
              </div>
            </div>
            <div>
              <QuotationCards />
            </div>
            <div className="flex gap-[2px] my-[20px] xl:my-[20px] 3xl:my-[1.042vw]">
              <Link href="">
                <Button className="text-[#A9B9D0] py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none font-normal">
                  <i className="cloud-cart text-[#A9B9D0] text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  {t('addToCart')}
                </Button>
              </Link>
              <Link href="">
                <Button className="text-[#A9B9D0] py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none  font-normal">
                  <i className="cloud-closecircle text-[#A9B9D0] text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  {t('cancel')}
                </Button>
              </Link>

              <CreateQuote />

              <Link href="">
                <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
                  <i className="cloud-download text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
                  {t('download')}
                </Button>
              </Link>
            </div>

            <div className="bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
              <div>
                <Quotation />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
