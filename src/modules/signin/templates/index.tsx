'use client';
import React from 'react';
import SignInForm from '../components/forms';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { RegionSelector } from '../../../components/common/region-selection';
import { <PERSON>o } from 'next/font/google';
import { useTranslations } from 'next-intl';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
export default function LoginTemplate() {
  const t = useTranslations();
  const tf = useTranslations();

  return (
    <>
      <div className="relative bannervideo h-screen">
        <video autoPlay loop muted className="w-full h-full">
          <source src="/images/video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div className="absolute top-[10px] xl:top-[10px] 2xl:top-[10px] 3xl:top-[0.833vw] z-50 left-[0] right-[0]">
          <div className=" mx-[40px] xl:mx-[48px] 3xl:mx-[2.5vw]">
            <div className="flex justify-between  px-[8px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.933vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] items-center">
              <div>
                <Image
                  src={'/images/cloudquarks_logo.svg'}
                  width={220}
                  height={55}
                  alt="Logo"
                  className="w-[200px] h-[50px] xl:w-[230px] xl:h-[55px]  3xl:w-[11.979vw] 3xl:h-[2.865vw] "
                />
              </div>

              <div className="w-[100px] xl:w-[120px] 3xl:w-[6.25vw]">
                <RegionSelector />
              </div>
            </div>
          </div>

          <div
            className="grid grid-cols-1 md:grid-cols-5  gap-2 mx-[120px] lg:mx-[100px ] xl:mx-[75px] 1xl:mx-[150px] 2xl:mx-[170px] 3xl:mx-[15.26vw]
           pt-[0px] xl:pt-[30px] 2xl:pt-[78px] 3xl:pt-[7.396vw] pb-[130px] xl:pb-[60px] 1xl:pb-[90px] 2xl:pb-[80px] 3xl:pb-[6.50vw]"
          >
            <div className="col-span-1 md:col-span-3 flex items-center">
              <div>
                <div className="flex mt-[30px] xl:mt-[35px] 3xl:mt-[1.823vw]">
                  <h1
                    className={`${roboto.className} text-[30px] md:text-[40px] xl:text-[40px] 2xl:text-[45px] 3xl:text-[2.34vw] leading-[130%] font-light text-textgreencolor`}
                  >
                    {t('headline')}
                  </h1>
                </div>
                <div className="flex mt-[10px] xl:mt-[18px] 3xl:mt-[0.938vw]">
                  <p
                    className={`${roboto.className} text-[18px] md:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] leading-[140%] font-light text-interfacetextinverse`}
                  >
                    {t('subheadline')}
                  </p>
                </div>
              </div>
            </div>

            <div className="col-span-1 md:col-span-2 flex flex-wrap flex-row justify-center auto-rows-max login contentbg">
              <div className="w-full flex flex-col gap-[20px] lg:gap-[20px] xl:gap-[16px] 2xl:gap-[35px] 3xl:gap-[2.08vw] px-[10px] lg:xl-[10px] xl:px-[20px] 2xl:px-[15px] 3xl:px-[1.183vw] py-[14px] lg:py-[10px] xl:py-[20px] 2xl:py-[30px] 3xl:py-[2.520vw]">
                <div>
                  <h2 className="text-[14px] lg:text-[14px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold text-interfacetextinverse ">
                    {t('signInTitle')}
                  </h2>
                </div>

                <div>
                  <SignInForm />

                  <div className="my-[12px] lg:my-[12px] xl:my-[12px] 2xl:my-[12px] 3xl:my-[0.625vw] flex items-center justify-center gap-1 text-[12px] lg:text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-normal text-center text-InterfaceTextsubtitle">
                    <div className="bg-InterfaceTextsubtitle h-[1px] w-[49px] xl:w-[2.552vw]"></div>
                    {t('orContinueWith')}
                    <div className="bg-InterfaceTextsubtitle h-[1px] w-[49px] xl:w-[2.552vw]"></div>
                  </div>
                  <div className="flex items-center flex-wrap gap-[10px] xl:gap-[10px] 3xl:gap-[0.525vw]">
                    <Button className="flex-1 border border-loginMicrosoftborder flex justify-center gap-[8px] xl:gap-[8px] 2xl:gap-[10px] 3xl:gap-[0.521vw] py-[10px] xl:py-[10px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] bg-transparent text-InterfaceTextlighter rounded-none">
                      <img
                        src="/images/microsoft-updated.svg"
                        alt="profile"
                        className="h-[16px] xl:h-[18px] 2xl:h-[20px] 3xl:h-[1.042vw] w-[16px] xl:w-[18px] 2xl:w-[18px] 3xl:w-[1.042vw]"
                      />
                      <div className="text-[8px] lg:text-[8px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextlighter">
                        Microsoft 365
                      </div>
                    </Button>
                    <Button className=" flex-1 border border-loginMicrosoftborder flex justify-center gap-[8px] lg:gap-[6px] xl:gap-[10px] 3xl:gap-[0.521vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[10px] xl:px-[16px] 3xl:px-[0.833vw] bg-transparent text-InterfaceTextlighter rounded-none">
                      <img
                        src="/images/google-updated.svg"
                        alt="profile"
                        className="h-[16px] xl:h-[18px] 2xl:h-[20px] 3xl:h-[1.042vw] w-[16px] xl:w-[18px] 2xl:w-[18px] 3xl:w-[1.042vw]"
                      />
                      <div className="text-[8px] lg:text-[8px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextlighter">
                        Google Workspace
                      </div>
                    </Button>
                    <Button className=" flex-1 border border-loginMicrosoftborder flex justify-center gap-[10px] xl:gap-[10px] 3xl:gap-[0.521vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] bg-transparent text-InterfaceTextlighter rounded-none">
                      <img
                        src="/images/linkedin.svg"
                        alt="profile"
                        className="h-[16px] xl:h-[18px] 2xl:h-[20px] 3xl:h-[1.042vw] w-[16px] xl:w-[18px] 2xl:w-[18px] 3xl:w-[1.042vw]"
                      />
                      <div className="text-[8px] lg:text-[8px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextlighter">
                        Linkedin
                      </div>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="absolute bottom-0 right-0 left-0 flex justify-center items-center gap-4 text-[#8B949C] text-[12px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[400]">
            <div>{tf('copyright')} </div> |<div> {tf('designedBy')} </div> |
            <div>{tf('allRightsReserved')} </div>
          </div>
        </div>
      </div>
    </>
  );
}
