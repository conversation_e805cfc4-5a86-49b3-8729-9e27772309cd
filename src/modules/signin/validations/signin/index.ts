import { z } from 'zod';

export const signInSchema = z.object({
  email: z
    .string()
    .email({ message: 'Please enter a valid email address.' })
    .min(1, { message: 'Email is required.' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters long.' })
    .max(20, { message: 'Password must not exceed 20 characters.' }),
  rememberMe: z.boolean(),
});

export type SignInFormData = z.infer<typeof signInSchema>;
