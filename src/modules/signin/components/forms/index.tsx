'use client';
import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Checkbox, Input, Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { useMutation } from '@tanstack/react-query';

import ForgotPassword from '../forgot-password';
import { logger } from '@/lib/utils/logger';
import { isApiResponseError } from '@/lib/utils/util';
import toast from 'react-hot-toast';
import { userService } from '@/lib/services/api';
import { COOKIES_KEYS } from '@/lib/enums';
import { clientCookiesService } from '@/lib/services/storage';
import { useRouter } from 'next/navigation';
import SetResetLink from '../set-reset-link';
import { signInSchema, SignInFormData } from '../../validations/signin';
import { useTranslations } from 'next-intl';

function SignInForm() {
  const t = useTranslations();
  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm<SignInFormData>({
    mode: 'all',
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });
  const router = useRouter();
  const [isForgotPassword, setIsForgotPassword] = useState<boolean>(false);
  const [forgotPasswordSuccess, setForgotPasswordSuccess] = useState<boolean>(false);

  const { mutate: signInUser, isPending: isSubmitting } = useMutation({
    mutationFn: (data: { email: string; password: string }) => userService.signInGuestUser(data),
    onSuccess: (response) => {
      clientCookiesService.set(COOKIES_KEYS.TOKEN, response?.data?.token);
      userService.createSession(response?.data?.token as string);
      router.push('/account');
    },
    onError: (error: unknown) => {
      if (isApiResponseError(error)) {
        logger.error('Error:', error?.data?.message);
        toast.error(error?.data?.message);
      }
    },
  });

  const onSubmit: SubmitHandler<SignInFormData> = async (data) => {
    const payload = { email: data?.email, password: data?.password };
    signInUser(payload);
  };

  const handleForgotPasswordChange = (state: boolean) => {
    if (state) {
      setForgotPasswordSuccess(true);
    }
    setIsForgotPassword(false);
  };

  const onError = (errors: unknown) => {
    logger.log('Form errors:', errors);
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit, onError)} autoComplete="off">
        {/* Email Field */}
        <div className="relative mb-[12px] lg:mb-[12px] xl:mb-[12px] 2xl:mb-[12px] 3xl:mb-[0.625vw]">
          <label
            htmlFor="required-email"
            className="text-interfacetextinverse text-[12px] lg:text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
          >
            {t('emailLabel')} <span className="text-red-500">*</span>
          </label>
          <Input
            type="email"
            id="required-email"
            {...register('email')}
            className="text-[12px] lg:text-[12px] xl:text-[14px] 3xl:text-[0.729vw] flex-1 loginborder w-full px-[10px] lg:px-[10px] xl:px-[14px] 3xl:px-[0.729vw] py-[0px] lg:py-[4px] xl:py-[14px] 2xl:py-[14px] 3xl:py-[0.729vw] bg-transparent focus:outline-none mt-[6px] 3xl:mt-[0.36vw] text-interfacesurfacecomponent placeholder:text-InterfaceTextsubtitle"
            placeholder={t('enterEmailPlaceHolder')}
          />
          {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>}
        </div>

        {/* Password Field */}
        <div className="relative mb-[12px] lg:mb-[12px] xl:mb-[12px] 2xl:mb-[12px] 3xl:mb-[0.625vw]">
          <label
            htmlFor="required-password"
            className="text-interfacetextinverse text-[12px] lg:text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
          >
            {t('password')} <span className="text-red-500">*</span>
          </label>
          <Input
            type="password"
            id="required-password"
            {...register('password')}
            className="text-[12px] lg:text-[12px] xl:text-[14px] 3xl:text-[0.729vw] flex-1 loginborder w-full  px-[10px] lg:px-[10px] xl:px-[14px] 3xl:px-[0.729vw] py-[0px] lg:py-[4px] xl:py-[14px] 2xl:py-[14px] 3xl:py-[0.729vw] bg-transparent focus:outline-none mt-[6px] 3xl:mt-[0.36vw] text-interfacesurfacecomponent placeholder:text-InterfaceTextsubtitle"
            placeholder="**************"
          />
          {errors.password && (
            <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>
          )}
        </div>

        {/* Remember Me Checkbox */}
        <div className="flex items-center justify-between align-center my-[12px] lg:my-[12px] xl:my-[12px] 2xl:my-[12px] 3xl:my-[0.625vw]">
          <div className="flex items-center space-x-2 custcheckbox">
            <Checkbox
              checked={getValues('rememberMe')}
              onCheckedChange={(value: boolean) =>
                setValue('rememberMe', value, { shouldValidate: true })
              }
              id="terms"
              {...register('rememberMe')}
            />
            <label
              htmlFor="terms"
              className="text-interfacetextinverse text-[12px] lg:text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium"
            >
              {t('rememberMe')}
            </label>
          </div>
          <div>
            <div
              onClick={() => setIsForgotPassword(true)}
              className="cursor-pointer text-[12px] lg:text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-semibold text-BrandPrimarypure"
            >
              {t('forgotPassword')}
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex w-full">
          <Button
            disabled={isSubmitting}
            type="submit"
            className="p-[10px] lg:p-[8px] xl:p-[10px] 3xl:p-[0.525vw] text-[12px] lg:text-[12px] xl:text-[16px] 3xl:text-[0.833vw] text-white w-full transition ease-in duration-200 text-center font-medium bg-BrandPrimarypure rounded-none"
          >
            {t('signInButton')}
          </Button>
        </div>
      </form>

      <div className="flex justify-center">
        <ForgotPassword open={isForgotPassword} onOpenChange={handleForgotPasswordChange} />
        <SetResetLink
          open={forgotPasswordSuccess}
          onOpenChange={() => setForgotPasswordSuccess(false)}
        />
      </div>
    </>
  );
}

export default SignInForm;
