'use client';
import * as React from 'react';
import {
  Button,
  Sheet,
  SheetContent,
  Input,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { isApiResponseError } from '@/lib/utils/util';
import toast from 'react-hot-toast';
import { userService } from '@/lib/services/api';
import { NETWORK_STATUS } from '@/lib/enums';
import { logger } from '@/lib/utils/logger';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, SubmitHandler } from 'react-hook-form';
import { ForgotPasswordData, forgotPasswordSchema } from '../../validations/forgot-password';

export default function ForgotPassword({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ForgotPasswordData>({
    mode: 'all',
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const [isSubmitting, setIsSubmitting] = React.useState<boolean>(false);

  const onSubmit: SubmitHandler<ForgotPasswordData> = async (payload) => {
    setIsSubmitting(true);
    try {
      const response = await userService?.forgotPassword(payload);
      if (response?.status == NETWORK_STATUS.SUCCESS) {
        reset();
        // toast.success(response?.message)
        onOpenChange(true);
      }
    } catch (error) {
      if (isApiResponseError(error)) {
        logger.error('Error:', error?.data?.message);
        toast.error(error?.data?.message);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const onError = (errors: unknown) => {
    logger.log('Form errors:', errors);
  };

  const handleOpenChange = () => {
    reset();
    onOpenChange(false);
  };

  return (
    <>
      <Sheet open={open} onOpenChange={handleOpenChange}>
        <SheetTitle></SheetTitle>
        <SheetContent className="sm:max-w-[500px] xl:max-w-[600px] 3xl:max-w-[31.25vw] flex items-center justify-center h-full overflow-auto">
          <div>
            <form onSubmit={handleSubmit(onSubmit, onError)}>
              <div className="flex justify-center">
                <i className="cloud-info2 text-[20px] lg:text-[30px] xl:text-[50px] 2xl:text-[50px] 3xl:text-[2.563vw] text-[#418E4C]"></i>
              </div>
              <div className="my-[30px] 3xl:my-[1.563vw]">
                <div className="text-InterfaceTexttitle text-[28px] xl:text-[30px] 3xl:text-[1.875vw] font-normal leading-[50.4px] text-center">
                  Forgot Password?
                </div>
                <div className="text-[#7F8488] text-[15px] xl:text-[17px] 3xl:text-[1.042vw] font-normal leading-[28px] text-center mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
                  {`Don’t`} worry, {`we’ll`} help you reset it. Enter your <br></br>email address
                  below.
                </div>
              </div>
              <div className="relative my-[30px] 3xl:my-[1.563vw]">
                <Input
                  {...register('email')}
                  type="email"
                  id="required-email"
                  placeholder="Enter your email"
                  className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokehard flex-1 w-full py-2 px-4 bg-transparent focus:outline-none mt-2 placeholder:text-InterfaceTextsubtitle"
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
                )}
              </div>
              <div className="flex justify-center">
                <Button
                  disabled={isSubmitting}
                  type="submit"
                  className="flex gap-[8px] 3xl:gap-[0.417vw] loginbtnnew text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] w-[210px] xl:w-[280px] 3xl:w-[14.583vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  size="md"
                >
                  <div>
                    <i className="cloud-circletick text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[0.833vw]">
                    Send Reset Link
                  </div>
                </Button>
              </div>
              <div className="my-[16px] 3xl:my-[0.833vw] flex justify-center items-center gap-[10px] 3xl:gap-[0.521vw]">
                <div className="bg-[#E5E7EB] w-[80px] 3xl:w-[4.167vw] h-[2px] 3xl:h-[0.104vw] flex items-center"></div>
                <div className="text-[#7F8488] text-[12px] 3xl:text-[0.833vw] font-normal leading-[12px]">
                  or
                </div>
                <div className="bg-[#E5E7EB] w-[80px] 3xl:w-[4.167vw] h-[2px] 3xl:h-[0.104vw] flex items-center"></div>
              </div>
            </form>
            <div className="flex justify-center">
              <Button
                onClick={() => onOpenChange(false)}
                type="button"
                className="flex gap-[8px] bg-trasparent w-[160px] xl:w-[180px] 3xl:w-[10.417vw] py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none h-fit border border-[#E5E7EB]"
              >
                <div>
                  <img
                    src="/images/lock.svg"
                    alt="profile"
                    className="h-[20px] xl:h-[20px] 3xl:h-[1.042vw] w-[20px] xl:w-[20px] 3xl:w-[1.042vw]"
                  />
                </div>
                <div className="text-[#3C4146]  text-[15px] xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[16px] flex items-center">
                  Back to Login
                </div>
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
