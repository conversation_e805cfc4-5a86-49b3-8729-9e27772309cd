'use client';
import * as React from 'react';
import {
  <PERSON>ton,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ontent,
  SheetTitle,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function SetResetLink({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTitle></SheetTitle>
      <SheetContent className="sm:max-w-[500px] xl:max-w-[600px] 3xl:max-w-[31.25vw] flex items-center justify-center h-full overflow-auto">
        <div>
          <div className="flex justify-center">
            <i className="cloud-circletick text-[20px] lg:text-[30px] xl:text-[50px] 2xl:text-[50px] 3xl:text-[2.563vw] text-[#418E4C]"></i>
          </div>
          <div className="my-[30px] 3xl:my-[1.563vw]">
            <div className="text-InterfaceTexttitle text-[28px] xl:text-[30px] 3xl:text-[1.875vw] font-normal leading-[50.4px] text-center">
              Check Your Mail
            </div>
            <div className="text-[#7F8488] text-[15px] xl:text-[17px] 3xl:text-[1.042vw] font-normal leading-[28px] text-center mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
              Link to reset your password has been emailed <br></br>to your registered email address
            </div>
          </div>

          <div className="flex justify-center">
            <SheetClose>
              <Button className="flex gap-[8px] bg-trasparent w-[160px] xl:w-[180px] 3xl:w-[10.417vw] py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none h-fit border border-[#E5E7EB]">
                <div>
                  <img
                    src="/images/lock.svg"
                    alt="profile"
                    className="h-[20px] xl:h-[20px] 3xl:h-[1.042vw] w-[20px] xl:w-[20px] 3xl:w-[1.042vw]"
                  />
                </div>
                <div className="text-[#3C4146]  text-[15px] xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-[16px] flex items-center">
                  Back to Login
                </div>
              </Button>
            </SheetClose>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
