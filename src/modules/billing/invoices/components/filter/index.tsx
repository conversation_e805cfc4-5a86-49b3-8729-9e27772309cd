'use client';
import * as React from 'react';
import {
  Select,
  SheetTitle,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Calendar,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { useTranslations } from 'next-intl';
const endCustomerOptions: Option[] = [
  { value: 'all', label: 'All' },
  { value: 'customer1', label: 'Customer 1' },
  { value: 'customer2', label: 'Customer 2' },
];

const brandOptions: Option[] = [
  { value: 'all', label: 'All' },
  { value: 'apple', label: 'Apple' },
  { value: 'samsung', label: 'Samsung' },
];

const brandCategoryOptions: Option[] = [
  { value: 'all', label: 'All' },
  { value: 'mobile', label: 'Mobile' },
  { value: 'laptop', label: 'Laptop' },
];

const billTypeOptions: Option[] = [
  { value: 'all', label: 'All' },
  { value: 'proforma', label: 'Proforma' },
  { value: 'tax', label: 'Tax' },
];

const statusOptions: Option[] = [
  { value: 'all', label: 'All' },
  { value: 'due', label: 'Due' },
  { value: 'overdue', label: 'Overdue' },
];

export default function Filter() {
  const t = useTranslations();
  const [billFromDate, setBillFromDate] = React.useState<Date | undefined>(undefined);
  const [billToDate, setBillToDate] = React.useState<Date | undefined>(undefined);

  const [dueFromDate, setDueFromDate] = React.useState<Date | undefined>(undefined);
  const [dueToDate, setDueToDate] = React.useState<Date | undefined>(undefined);

  const [endCustomer, setEndCustomer] = React.useState<string>('all');
  const [brand, setBrand] = React.useState<string>('all');
  const [brandCategory, setBrandCategory] = React.useState<string>('all');
  const [billType, setBillType] = React.useState<string>('all');
  const [status, setStatus] = React.useState<string>('all');

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className="  flex items-center gap-2 text-InterfaceTextdefault hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="">
            <i className="cloud-filtericon  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]  font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col  gap-0 sm:max-w-[400px] lg:max-w-[490px] xl:max-w-[540px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className=" h-[300px] md:h-[320px] lg:h-[360px] xl:h-[480px] 2xl:h-[650px] 3xl:h-[37.458vw] p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
          {/* Invoice Bill Date */}
          <div>
            <Label className="text-InterfaceTexttitle text-[12px] 2xl:text-[14px] font-[500] pb-[6px] block">
              {t('invoiceBillDate')}
            </Label>
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px]">
              <DatePopover
                date={billFromDate}
                setDate={setBillFromDate}
                placeholder={t('fromDate')}
              />
              <DatePopover date={billToDate} setDate={setBillToDate} placeholder={t('toDate')} />
            </div>
          </div>

          {/* Invoice Due Date */}
          <div>
            <Label className="text-InterfaceTexttitle text-[12px] 2xl:text-[14px] font-[500] pb-[6px] block">
              {t('invoiceDueDate')}
            </Label>
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-[20px]">
              <DatePopover
                date={dueFromDate}
                setDate={setDueFromDate}
                placeholder={t('fromDate')}
              />
              <DatePopover date={dueToDate} setDate={setDueToDate} placeholder={t('toDate')} />
            </div>
          </div>

          <DropdownField
            label={t('endCustomer')}
            value={endCustomer}
            onChange={setEndCustomer}
            placeholder="All"
            options={endCustomerOptions}
          />
          <DropdownField
            label={t('brand')}
            value={brand}
            onChange={setBrand}
            placeholder="All"
            options={brandOptions}
          />
          <DropdownField
            label={t('brandCategory')}
            value={brandCategory}
            onChange={setBrandCategory}
            placeholder="All"
            options={brandCategoryOptions}
          />
          <DropdownField
            label={t('billType')}
            value={billType}
            onChange={setBillType}
            placeholder="All"
            options={billTypeOptions}
          />
          <DropdownField
            label={t('status')}
            value={status}
            onChange={setStatus}
            placeholder="All"
            options={statusOptions}
          />
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose>
                <div
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[8px] 3xl:py-[0.521vw] px-[12px]  xl:px-[12px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[15px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[14px] xl:text-[15px] 3xl:text-[0.833vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </div>
              </SheetClose>
              <SheetClose>
                <Button className=" applybtn flex items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[8px] 3xl:py-[0.521vw] px-[12px] xl:px-[12px] 3xl:px-[0.833vw] rounded-none">
                  <div>
                    <i className="cloud-filter text-interfacetextinverse flex items-center text-[16px] xl:text-[16px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[14px] xl:text-[15px] 3xl:text-[0.833vw]">
                    {t('applyFilter')}
                  </div>
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}

interface DatePopoverProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  placeholder: string;
}

const DatePopover: React.FC<DatePopoverProps> = ({ date, setDate, placeholder }) => (
  <Popover>
    <PopoverTrigger asChild>
      <Button
        variant="outline"
        className={cn(
          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none',
          !date && 'text-muted-foreground'
        )}
      >
        {date ? format(date, 'PPP') : <span>{placeholder}</span>}
        <i className="cloud-canlendar text-InterfaceTextsubtitle" />
      </Button>
    </PopoverTrigger>
    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite " align="start">
      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
    </PopoverContent>
  </Popover>
);

interface Option {
  value: string;
  label: string;
}

interface DropdownFieldProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  options: Option[];
}

const DropdownField: React.FC<DropdownFieldProps> = ({
  label,
  value,
  onChange,
  placeholder,
  options,
}) => (
  <div className="flex flex-col gap-1.5">
    <Label className="text-InterfaceTexttitle text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
      {label}
    </Label>
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className="rounded-none bg-[#FFF] border-none">
        <SelectGroup className="text-[#212325] text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
          {options.map((opt) => (
            <SelectItem key={opt.value} value={opt.value}>
              {opt.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  </div>
);
