'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet,
  Sheet<PERSON>lose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import InsightsTable from '../table/insights-table';
import Piechart from '@/components/common/charts/piechart';
import { useTranslations } from 'next-intl';
const data = [
  { value: 180.22, name: 'Azure Site Recovery', itemStyle: { color: '#73609B' } },
  { value: 200.1, name: 'Backup', itemStyle: { color: '#4D9E99' } },
  { value: 350.75, name: 'Bandwidth', itemStyle: { color: '#F2994A' } },
  { value: 400, name: 'Storage', itemStyle: { color: '#2D9CDB' } },
  { value: 297.4, name: 'Virtual Machines', itemStyle: { color: '#27AE60' } },
  { value: 120.6, name: 'Virtual Machines Licenses', itemStyle: { color: '#EB5757' } },
  { value: 278.31, name: 'Virtual Network', itemStyle: { color: '#9B51E0' } },
  { value: 150.9, name: 'Others', itemStyle: { color: '#F2C94C' } },
];

const legendRich = {
  name: {
    width: 200, // increased width for names
    align: 'left' as const,
    fontSize: 14,
    padding: [0, 5, 0, 0], // right padding to add spacing
  },
  value: {
    width: 50, // increased width for values
    align: 'right' as const,
    fontSize: 14,
    color: '#666',
  },
};
export default function InsightsPopup({ selectedId }: { selectedId: string }) {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className="">
          <div className="py-[10px] text-sm 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-transparent bg-[linear-gradient(180deg,_#FEFEFE_0%,_#F6F8FA_100%)] border border-InterfaceStrokesoft flex gap-[8px] 3xl:gap-[0.417vw] rounded-none text-InterfaceTextdefault font-[500] items-center">
            <i className="cloud-lamp-on text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
            {t('insights')}
          </div>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[1080px] xl:max-w-[1080px] 3xl:max-w-[56.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="flex items-center gap-4">
              <SheetClose>
                {' '}
                <i className="cloud-back text-BrandSupport1pure text-[14px] xl:text-[20px] 2xl:text-[20px] 3xl:text-[1.042vw]"></i>
              </SheetClose>
              <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
                {t('invoice')} #{selectedId}
              </div>
            </div>

            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-full ">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="grid grid-cols-3 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                  <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      {t('endCustomerName')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                      Rwanda Customer
                    </div>
                  </div>
                  <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      {t('subscriptionId')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                      3f61e4c6-4de1-4d9b-80e0-bc91bbd98f9c
                    </div>
                  </div>
                  <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      {t('subscriptionName')}
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                      Azure Plan
                    </div>
                  </div>
                  <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Billing Period
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                      01-02-2024 - 04-02-2025
                    </div>
                  </div>
                  <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                    <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Invoice No.
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                      CR-1101
                    </div>
                  </div>
                </div>

                {/* Invoice section with charts */}
                <div className="grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-7 2xl:grid-cols-7 3xl:grid-cols-7 gap-[22px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                  <div className="col-span-1 lg:col-span-1 xl:col-span-4 2xl:col-span-4 border p-3 3xl:col-span-4  h-[250px] xl:h-[250px] 3xl:h-[12.500vw]">
                    <div>
                      <p>Total Invoices (USD)</p>
                    </div>
                    <Piechart
                      legends={{
                        orient: 'vertical',
                        left: 0,
                        top: 10,
                        itemGap: 8,
                        itemHeight: 8,
                        itemWidth: 8,
                        bottom: 0,
                        textStyle: {
                          fontSize: 14,
                          rich: legendRich,
                        },
                        formatter: (name: string) => {
                          const item = data.find((d) => d.name === name);
                          return `{name|${name}} :{value|${item ? item.value.toFixed(2) : ''}}`;
                        },
                        selectedMode: false,
                        data: [
                          'Azure Site Recovery',
                          'Backup',
                          'Bandwidth',
                          'Storage',
                          'Virtual Machines',
                          'Virtual Machines Licenses',
                          'Virtual Network',
                          'Others',
                        ],
                      }}
                      name={'Nightingale Chart'}
                      radius={[50, 90]}
                      center={['80%', '40%']}
                      //   rosetype={'radius'}
                      itemstyle={{
                        borderRadius: 4,
                        borderColor: '#fff',
                        borderWidth: 3,
                      }}
                      labelline={{
                        show: false,
                      }}
                      label={{
                        show: false,
                      }}
                      data={[
                        {
                          value: 11,
                          name: 'Azure Site Recovery',
                          itemStyle: { color: '#F2980E' },
                        }, // 8
                        { value: 35, name: 'Backup', itemStyle: { color: '#42536D' } }, // 1
                        { value: 45, name: 'Bandwidth', itemStyle: { color: '#4D9E99' } }, // 2
                        { value: 8, name: 'Storage', itemStyle: { color: '#3B662F' } }, // 3
                        { value: 8, name: 'Virtual Machines', itemStyle: { color: '#72B35F' } }, // 4
                        {
                          value: 8,
                          name: 'Virtual Machines Licenses',
                          itemStyle: { color: '#ACD69F' },
                        }, // 5
                        { value: 8, name: 'Virtual Network', itemStyle: { color: '#B24F0B' } }, // 6
                        { value: 8, name: 'Others', itemStyle: { color: '#D67309' } }, // 7
                      ]}
                      graphic={{
                        type: 'text',
                        left: '72.5%',
                        top: '35%',
                        style: {
                          rich: {
                            title: {
                              fontSize: 12,
                              fill: '#19212A',
                              fontWeight: 'bold',
                              align: 'center',
                            },
                            value: {
                              fontSize: 16,
                              fill: '#000000',
                              fontWeight: 'bold',
                              align: 'center',
                            },
                          },
                          text: '{title|Total (USD)}\n{value|$1,000,000}',
                          align: 'center',
                          verticalAlign: 'middle',
                        },
                      }}
                    />
                  </div>
                  <div className="col-span-1 lg:col-span-1 xl:col-span-3 2xl:col-span-3 border flex flex-col justify-between p-3">
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('invoiceValue')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        INR 556,283.65
                      </div>
                    </div>
                    <div className="flex flex-col gap-[4px] border-b border-b-InterfaceStrokesoft py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        Invoice Value before Taxes
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        INR 556,283.65
                      </div>
                    </div>
                    <div className="flex flex-col gap-[4px] py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
                      <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                        {t('vat')}
                      </div>
                      <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                        INR 0.00
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <InsightsTable />
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
