'use client';
import Piechart from '@/components/common/charts/piechart';
import React from 'react';
import CustomerBarChart from '../charts/customer';
import BillTypeBarChart from '../charts/bill-type';
import { Select2 } from '@/components/common/ui/combo-box';
import { logger } from '@/lib/utils/logger';
import { useTranslations } from 'next-intl';

export default function Info() {
  const handleCurrecySelect = (value: string) => {
    logger.log(value);
  };

  const t = useTranslations();

  return (
    <div>
      <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-semibold leading-[140%]">
        {t('invoices')}
      </div>
      <div className="text-[#7F8488] pb-3 text-[12px] 3xl:text-[0.625vw] leading-[140%] font-normal">
        <i>* Based on invoice date range selected</i>
      </div>

      <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-[16px] 2xl:gap-[10px] 3xl:gap-[0.833vw]">
        <div className=" p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)] relative">
          <div className="">
            <i className="cloud-folder text-[#7F8488] text-[26px]"></i>
          </div>
          <div className="absolute z-10 right-2 top-2 ">
            <Select2
              value={''}
              placeholder="Select Currency"
              onChange={(value) => handleCurrecySelect(value as string)}
              options={[
                { value: 'usd', label: 'USD' },
                { value: 'aed', label: 'AED' },
              ]}
              classNames={{
                trigger:
                  ' px-[6px] py-[4px] xl:py-[6px] text-xs border border-gray-300 justify-between',
                item: 'text-xs px-[6px] py-[4px] 2xl:py-[6px]',
                itemSelectedIcon: 'text-primary',
              }}
              styles={{
                trigger: {
                  backgroundColor: '#fff',
                  fontWeight: 400,
                },
              }}
            />
          </div>

          <div className="grid grid-cols-6">
            <div className="col-span-2 flex flex-col items-start justify-end">
              <div className="text-[#3C4146] text-[11px] 2xl:text-[13px] 3xl:text-[0.677vw] font-medium text-md leading-[140%]">
                {t('totalInvoiceValueUsd')}*
              </div>
              <div className="text-[25px] xl:text-[25px] 2xl:text-[30px] 3xl:text-[1.475vw] font-semibold truncate text-InterfaceTextdefault">
                100,000.00
              </div>
            </div>

            <div className=" w-full h-[140px] xl:h-[140px] 2xl:h-[140px] 3xl:h-[5.885vw] col-span-4">
              <Piechart
                legends={{
                  orient: 'vertical',
                  right: -5,
                  top: 30,
                  itemGap: 4,
                  itemHeight: 7,
                  itemWidth: 7,
                  bottom: 0,
                  textStyle: {
                    fontSize: 8,
                  },
                  data: ['Amazon', 'Microsoft CSP', 'Google Cloud'],
                  selectedMode: false,
                }}
                name={'Nightingale Chart'}
                radius={[16, 40]}
                center={['41%', '36%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 4,
                  length2: 2,
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 50, name: 'Amazon', itemStyle: { color: '#73609B' } },
                  { value: 50, name: 'Microsoft CSP', itemStyle: { color: '#4D9E99' } },
                  { value: 50, name: 'Google Cloud', itemStyle: { color: '#ccc91b' } },
                ]}
              />
            </div>
          </div>
        </div>

        <div className="gap-[5px] 3xl:gap-[0.625vw] p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)]">
          <div className="w-full ">
            <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold">
              Invoices by Top 5 Customers*
            </div>
            <div className=" w-full h-[140px] xl:h-[145px] 2xl:h-[145px] 3xl:h-[6.085vw] ">
              <CustomerBarChart />
            </div>
          </div>
        </div>

        <div className=" gap-[5px] 3xl:gap-[0.625vw] p-[12px] 2xl:3xl:p-[16px] 3xl:p-[0.833vw] rounded-[8px] 3xl:rounded-[0.417vw] bg-[#fff] shadow shadow-[0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10)]">
          <div className="w-full ">
            <div className="text-InterfaceTextdefault text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold">
              Invoice Value by Bill Type*
            </div>
            <div className=" w-full h-[140px] xl:h-[145px] 2xl:h-[145px] 3xl:h-[6.085vw] ">
              <BillTypeBarChart />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
