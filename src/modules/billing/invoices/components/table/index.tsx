import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React, { useState } from 'react';
import { Column, ColumnDef } from '@tanstack/react-table';
import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import CreditLimitFilter from '../filter';
import InvoiceDetailsPopup from '../details-popup';
import { useTranslations } from 'next-intl';

export default function Table() {
  const t = useTranslations();
  const [openView, setOpenView] = useState(false);
  const [selectedId, setSelectedId] = useState<string>('');

  const handleView = (id: string) => {
    setSelectedId(id);
    setOpenView(true);
  };

  type Invoice = {
    endCustomer: string;
    invoiceNumber: string;
    invoiceDate: string;
    brandCategory: string;
    description: string;
    currency: string;
    amount: string;
    dueDate: string;
    status: string;
  };

  const createSortableHeader = (
    label: string,
    column: Column<Invoice, unknown>,
    placeholder: string
  ) => {
    const isSorted = column.getIsSorted();
    return (
      <div className="flex flex-col">
        <button
          type="button"
          className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
            {label}
          </div>
          {isSorted === 'asc' ? (
            <ArrowUp className="h-[12px] w-[12px]" />
          ) : isSorted === 'desc' ? (
            <ArrowDown className="h-[12px] w-[12px]" />
          ) : (
            <ArrowUpDown className="h-[12px] w-[12px]" />
          )}
        </button>
        <ColumnHeaderFilter
          placeholder={placeholder}
          onChange={(val) => column.setFilterValue(val)}
        />
      </div>
    );
  };

  const invoiceColumns: ColumnDef<Invoice>[] = [
    {
      accessorKey: 'endCustomer',
      header: ({ column }) => createSortableHeader(t('endCustomer'), column, t('endCustomer')),
      cell: ({ row }) => <div>{row.getValue('endCustomer')}</div>,
    },
    {
      accessorKey: 'invoiceNumber',
      header: ({ column }) => createSortableHeader(t('invoiceNumber'), column, t('invoiceNumber')),
      cell: ({ row }) => (
        <a href="#" className="text-blue-600 hover:underline">
          {row.getValue('invoiceNumber')}
        </a>
      ),
    },
    {
      accessorKey: 'invoiceDate',
      header: ({ column }) => createSortableHeader(t('invoiceDate2'), column, t('invoiceDate2')),
      cell: ({ row }) => <div>{row.getValue('invoiceDate')}</div>,
    },
    {
      accessorKey: 'brandCategory',
      header: ({ column }) => createSortableHeader(t('brandCategory'), column, t('brandCategory')),
      cell: ({ row }) => <div>{row.getValue('brandCategory')}</div>,
    },
    {
      accessorKey: 'description',
      header: ({ column }) => createSortableHeader(t('description'), column, t('description')),
      cell: ({ row }) => <div>{row.getValue('description')}</div>,
    },
    {
      accessorKey: 'currency',
      header: ({ column }) => createSortableHeader(t('currency'), column, t('currency')),
      cell: ({ row }) => <div>{row.getValue('currency')}</div>,
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => createSortableHeader(t('amount'), column, t('amount')),
      cell: ({ row }) => <div>{row.getValue('amount')}</div>,
    },
    {
      accessorKey: 'dueDate',
      header: ({ column }) => createSortableHeader(t('dueDate'), column, t('dueDate')),
      cell: ({ row }) => <div>{row.getValue('dueDate')}</div>,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => createSortableHeader(t('status'), column, t('status')),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const statusMap: Record<string, string> = {
          Due: 'bg-yellow-100 text-yellow-800 border-yellow-400',
          Overdue: 'bg-red-100 text-red-800 border-red-400',
        };
        const classes = statusMap[status] || 'bg-gray-100 text-gray-800 border-gray-300';
        return (
          <span className={`px-2 py-1 border rounded text-xs font-medium ${classes}`}>
            {status}
          </span>
        );
      },
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2 justify-center">
          <Popover>
            <PopoverTrigger asChild>
              <i
                className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex"
                title="Details"
              ></i>
            </PopoverTrigger>
            <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[200px] z-20 bg-interfacesurfacecomponent cursor-pointer">
              <div onClick={() => handleView(row.getValue('invoiceNumber'))} className="">
                <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
                  <i className="cloud-folder text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw]"></i>
                  {t('invoiceDetails')}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      ),
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0 z-10',
        cellClassName: 'sticky right-0 z-10',
      },
    },
  ];

  const invoiceData = [
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000151845',
      invoiceDate: '2/2/2025',
      brandCategory: 'CSP',
      description: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '24,500.00',
      dueDate: '5/5/2025',
      status: 'Due',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158453',
      invoiceDate: '12/2/2025',
      brandCategory: 'CSP',
      description: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '-49,850.00',
      dueDate: '5/5/2025',
      status: 'Due',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158452',
      invoiceDate: '15/2/2025',
      brandCategory: 'Azure RI',
      description: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '5,000.00',
      dueDate: '5/5/2025',
      status: 'Due',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158412',
      invoiceDate: '16/2/2025',
      brandCategory: 'Azure RI',
      description: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '10,000.00',
      dueDate: '5/5/2025',
      status: 'Due',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158413',
      invoiceDate: '12/3/2025',
      brandCategory: 'Azure Plan',
      description: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '25,500.00',
      dueDate: '5/5/2025',
      status: 'Overdue',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158414',
      invoiceDate: '13/3/2025',
      brandCategory: 'AWS',
      description: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '26,500.00',
      dueDate: '5/5/2025',
      status: 'Overdue',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158415',
      invoiceDate: '14/3/2025',
      brandCategory: 'Software Per..',
      description: 'Google Workspace Commitment - Annual',
      currency: 'USD',
      amount: '32,500.00',
      dueDate: '5/5/2025',
      status: 'Due',
    },
    {
      endCustomer: 'Alpha Systems',
      invoiceNumber: '0000158416',
      invoiceDate: '15/3/2025',
      brandCategory: 'Google Cloud..',
      description: 'Google Workspace Commitment - Monthly',
      currency: 'USD',
      amount: '45,500.00',
      dueDate: '5/5/2025',
      status: 'Due',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-2 ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {t('invoiceDetails')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] leading-[140%] mt-[4px] 3xl:mt-[0.208vw]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
            {/* <div className="relative w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw]">
  <i className="cloud-search text-InterfaceTextsubtitle absolute inset-y-0 left-[12px] flex items-center text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>

  <Input
    type="text"
    placeholder="Search"
    className="w-full pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
  />
</div> */}
            <div className="relative w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw]">
              <div className="absolute inset-y-0 left-[12px] flex items-center pointer-events-none">
                <i className="cloud-search text-InterfaceTextsubtitle text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw]" />
              </div>

              <Input
                type="text"
                placeholder="Search"
                className="w-full h-[40px] xl:h-[40px] 2xl:h-[42px] 3xl:h-[2.2vw]
      pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw]
      pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw]
      text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]
      placeholder:text-InterfaceTextsubtitle
      placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw]
      text-blackcolor border border-InterfaceStrokedefault rounded-[2px]"
              />
            </div>

            {/* <div className="flex items-center gap-2 cursor-pointer">
              <i className="cloud-filtericon"></i>Filter
            </div> */}

            <CreditLimitFilter />
          </div>
        </div>
        <div className="flex items-center justify-start px-[14px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] gap-2 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <div className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <span className="text-InterfaceTextsubtitle">{t('startDate')} :</span> 01-01-2025
            <i className="cloud-closecircle text-ClearAll"></i>
          </div>
          <div className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <span className="text-InterfaceTextsubtitle">{t('endDate2')}:</span> 01-06-2025
            <i className="cloud-closecircle text-ClearAll"></i>
          </div>
          <div className="text-InterfaceTextdefault bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <span className="text-InterfaceTextsubtitle">{t('endCustomer')} :</span> All
            <i className="cloud-closecircle text-ClearAll"></i>
          </div>
          <div className="text-ClearAll  py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
            <i className="cloud-closecircle text-ClearAll"></i> Clear All
          </div>
        </div>
        <div className="overflow-x-auto">
          <DataTable data={invoiceData} columns={invoiceColumns} withCheckbox={false} />
        </div>
        <TablePagination />
      </div>
      <InvoiceDetailsPopup
        selectedId={selectedId}
        open={openView}
        onClose={() => setOpenView(false)}
      />
    </>
  );
}
