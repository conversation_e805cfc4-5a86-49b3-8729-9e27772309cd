'use client';
import React from 'react';
import Link from 'next/link';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Info from '../components/info';
import Table from '../components/table';

export default function InvoicesTemplate() {
  return (
    <>
      <div className="w-full px-6 pt-4">
        <div className="">
          <Info />
        </div>

        <div className="flex gap-[2px] my-[20px] xl:my-[20px] 3xl:my-[1.042vw]">
          <Link href="">
            <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
              <i className="cloud-download text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
              Download
            </Button>
          </Link>
        </div>

        <div>
          <Table />
        </div>
      </div>
    </>
  );
}
