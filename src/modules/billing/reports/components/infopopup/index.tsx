'use client';
import * as React from 'react';
import {
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON>lose,
  She<PERSON><PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';

export default function Infopopup() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="w-full">
        <div className="w-full px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-info text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
          {t('info')}
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('information')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] md:h-[550px] lg:h-[550px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]  space-y-3">
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] mt-[12px]">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('invoiceBillDate')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    01/01/2025 - 01/02/2025
                  </div>
                </div>
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('endCustomer')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Alpha Systems
                  </div>
                </div>
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('brandCategory')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Category 1
                  </div>
                </div>
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('reportType2')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Type 1
                  </div>
                </div>
                <div className=" space-y-2 border-b border-[#E5E7EB] pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    {t('reportTile')}
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Test Title
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
