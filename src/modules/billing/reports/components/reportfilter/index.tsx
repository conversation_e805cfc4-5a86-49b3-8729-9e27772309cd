'use client';
import * as React from 'react';
import {
  Select,
  Sheet<PERSON><PERSON><PERSON>,
  SelectContent,
  SelectGroup,
  SheetHeader,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
  SheetFooter,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Calendar,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { useTranslations } from 'next-intl';

export default function BillingFilter() {
  const t = useTranslations();
  const [date, setDate] = React.useState<Date>();
  const [brand, setBrand] = React.useState('');
  const [requestreason, setRequestreason] = React.useState('');
  const [status, setStatus] = React.useState('');

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <div className=" flex items-center gap-2 text-InterfaceTextdefault hover:bg-BrandNeutral100 hover:text-BrandSupport1pure h-full py-[8px] xl:py-[8px] 3xl:py-[0.417vw] px-[10px] xl:px-[12px] 3xl:px-[0.625vw]">
          <div className="">
            <i className="cloud-filtericon  text-[14px] xl:text-[14px] 3xl:text-[0.729vw]"></i>
          </div>
          <p className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw]  font-medium">
            {t('filter')}
          </p>
        </div>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('applyFilter')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="h-[400px] lg:h-[600px] xl:h-[75vh] 2xl:h-[85vh] 3xl:h-[85vh] p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto">
          <div className="space-y-[19px] xl:space-y-[18px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="pb-[6px]">
                <Label
                  htmlFor="LastUpdatedDate "
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('requestedDate')}
                </Label>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                <div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                          !date &&
                            'text-muted-foreground px-[12px] py-[8px] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle">{t('fromDate')}</span>
                        )}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none',
                          !date &&
                            'text-muted-foreground px-[12px] py-[8px] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle">{t('toDate')}</span>
                        )}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
            <div>
              <div className="pb-[6px]">
                <Label
                  htmlFor="LastUpdatedDate "
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  Invoice Due Date
                </Label>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                <div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                          !date &&
                            'text-muted-foreground px-[12px] py-[8px] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle">From Date</span>
                        )}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none',
                          !date &&
                            'text-muted-foreground px-[12px] py-[8px] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]'
                        )}
                      >
                        {date ? (
                          format(date, 'PPP')
                        ) : (
                          <span className="text-InterfaceTextsubtitle">To Date</span>
                        )}
                        <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('endCustomer')}
              </Label>
              <Select value={requestreason} onValueChange={setRequestreason}>
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="completed">-</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('brand')}
              </Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('brandCategory')}
              </Label>
              <Select value={brand} onValueChange={setBrand}>
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="completed">-</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                Bill Type
              </Label>
              <Select value={brand} onValueChange={setBrand}>
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="completed">-</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('status')}
              </Label>
              <Select value={brand} onValueChange={setBrand}>
                <SelectTrigger className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent className="rounded-none bg-[#FFF] border-none">
                  <SelectGroup className="text-[#212325]  text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400]">
                    <SelectItem value="completed">-</SelectItem>
                    <SelectItem value="pending">-</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 left-0 bg-[#FFF] border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose>
                <Button
                  href="#"
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px]
                                    3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </Button>
              </SheetClose>
              <SheetClose>
                <Button className="applybtn flex items-center gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none">
                  <div>
                    <i className="cloud-filter text-interfacetextinverse flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    Apply Filter
                  </div>
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
