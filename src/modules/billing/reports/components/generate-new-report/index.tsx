'use client';
import * as React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Sheet,
  <PERSON>etClose,
  <PERSON>etContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>et<PERSON>ooter,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Calendar,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { format } from 'date-fns';
import { cn } from '@/lib/utils/util';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Select2 } from '@/components/common/ui/combo-box';
import { useTranslations } from 'next-intl';

const generateReportSchema = z
  .object({
    fromDate: z.date({
      required_error: 'From date is required',
      invalid_type_error: 'Please select a valid from date',
    }),
    toDate: z.date({
      required_error: 'To date is required',
      invalid_type_error: 'Please select a valid to date',
    }),
    endCustomer: z.string().min(1, 'End customer is required'),
    brandCategory: z.string().min(1, 'Brand category is required'),
    reportType: z.string().min(1, 'Report type is required'),
    reportTitle: z.string().min(1, 'Report title is required'),
  })
  .refine((data) => data.toDate >= data.fromDate, {
    message: 'To date must be after or equal to from date',
    path: ['toDate'],
  });

export type GenerateReportFormData = z.infer<typeof generateReportSchema>;

export default function GenerateNewReport() {
  const t = useTranslations();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<GenerateReportFormData>({
    resolver: zodResolver(generateReportSchema),
    mode: 'onTouched',
    defaultValues: {
      endCustomer: '',
      brandCategory: '',
      reportType: '',
      reportTitle: '',
    },
  });

  const onSubmit = () => {};

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] bg-[#FFF] hover:bg-[#f6f7f8] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
          <i className="cloud-Add text-BrandSupport1pure text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
          {t('generateNewReport')}
        </Button>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px]">
        <SheetHeader className="hideclose border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('generateNewReport')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] lg:h-[500px] xl:h-[500px] 2xl:h-[550px] 3xl:h-[36.25vw]">
          <div className="space-y-[19px] xl:space-y-[17px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="pb-[6px]">
                <Label
                  htmlFor="LastUpdatedDate "
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('invoiceBillDate')} <span className="text-[red]">*</span>
                </Label>
              </div>
              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]">
                <div>
                  <Controller
                    name="fromDate"
                    control={control}
                    render={({ field }) => (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none ',
                              !field.value &&
                                'text-muted-foreground px-[12px] py-[8px] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]',
                              errors.fromDate && 'border-red-500'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span className="">{t('fromDate')}</span>
                            )}
                            <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                  {errors.fromDate && (
                    <p className="text-red-500 text-xs mt-1">{errors.fromDate.message}</p>
                  )}
                </div>
                <div className="">
                  <Controller
                    name="toDate"
                    control={control}
                    render={({ field }) => (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-full text-InterfaceTextsubtitle justify-between border border-InterfaceStrokehard text-left font-normal rounded-none',
                              !field.value &&
                                'text-muted-foreground px-[12px] py-[8px] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]',
                              errors.toDate && 'border-red-500'
                            )}
                          >
                            {field.value ? format(field.value, 'PPP') : <span>{t('toDate')}</span>}
                            <i className="cloud-canlendar text-InterfaceTextsubtitle"></i>
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 bg-InterfaceTextwhite" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                  {errors.toDate && (
                    <p className="text-red-500 text-xs mt-1">{errors.toDate.message}</p>
                  )}
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('endCustomer')} <span className="text-[red]">*</span>
              </Label>
              <Controller
                name="endCustomer"
                control={control}
                render={({ field }) => (
                  <Select2
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t('selectEndCustomer')}
                    options={[
                      { value: 'all', label: 'All (Multi Select)' },
                      { value: 'customer1', label: 'Customer 1' },
                      { value: 'customer2', label: 'Customer 2' },
                    ]}
                    classNames={{
                      trigger: cn(
                        'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[11px] xl:px-[11px] 2xl:px-[11px] 3xl:px-[0.573vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]',
                        errors.endCustomer && 'border-red-500'
                      ),
                      item: 'text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] px-3 py-2',
                    }}
                    styles={{
                      popoverContent: {
                        border: 'none',
                        backgroundColor: '#fff',
                      },
                    }}
                  />
                )}
              />
              {errors.endCustomer && (
                <p className="text-red-500 text-xs mt-1">{errors.endCustomer.message}</p>
              )}
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[13px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]  font-[500] "
              >
                {t('brandCategory')} <span className="text-[red]">*</span>
              </Label>
              <Controller
                name="brandCategory"
                control={control}
                render={({ field }) => (
                  <Select2
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t('selectBrandCategory')}
                    options={[
                      { value: 'all', label: 'All' },
                      { value: 'hardware', label: 'Hardware' },
                      { value: 'software', label: 'Software' },
                    ]}
                    classNames={{
                      trigger: cn(
                        'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[11px] xl:px-[11px] 2xl:px-[11px] 3xl:px-[0.573vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]',
                        errors.brandCategory && 'border-red-500'
                      ),
                      item: 'text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] px-3 py-2',
                    }}
                  />
                )}
              />
              {errors.brandCategory && (
                <p className="text-red-500 text-xs mt-1">{errors.brandCategory.message}</p>
              )}
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('reportType2')} <span className="text-[red]">*</span>
              </Label>
              <Controller
                name="reportType"
                control={control}
                render={({ field }) => (
                  <Select2
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select Report Type"
                    options={[
                      { value: 'summary', label: 'Summary Report' },
                      { value: 'detailed', label: 'Detailed Report' },
                      { value: 'analytics', label: 'Analytics Report' },
                    ]}
                    classNames={{
                      trigger: cn(
                        'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[11px] xl:px-[11px] 2xl:px-[11px] 3xl:px-[0.573vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]',
                        errors.reportType && 'border-red-500'
                      ),
                      item: 'text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] px-3 py-2',
                    }}
                  />
                )}
              />
              {errors.reportType && (
                <p className="text-red-500 text-xs mt-1">{errors.reportType.message}</p>
              )}
            </div>
            <div className="flex flex-col gap-1.5">
              <Label
                htmlFor=""
                className="text-InterfaceTexttitle text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
              >
                {t('reportTitle')} <span className="text-[red]">*</span>
              </Label>
              <Controller
                name="reportTitle"
                control={control}
                render={({ field }) => (
                  <Select2
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select Report Title"
                    options={[
                      { value: 'monthly', label: 'Monthly Sales Report' },
                      { value: 'quarterly', label: 'Quarterly Performance Report' },
                      { value: 'annual', label: 'Annual Revenue Report' },
                    ]}
                    classNames={{
                      trigger: cn(
                        'placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none px-[11px] xl:px-[11px] 2xl:px-[11px] 3xl:px-[0.573vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[13px] xl:text-[13px] 2xl:text-[14px] 3xl:text-[0.729vw]',
                        errors.reportTitle && 'border-red-500'
                      ),
                      item: 'text-[#212325] text-[12px] md:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] px-3 py-2',
                    }}
                  />
                )}
              />
              {errors.reportTitle && (
                <p className="text-red-500 text-xs mt-1">{errors.reportTitle.message}</p>
              )}
            </div>
          </div>
        </div>
        <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
          <div className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] w-full flex justify-end ">
            <div className="flex gap-[12px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <SheetClose asChild>
                <Button
                  variant="outline"
                  className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[12px] 3xl:px-[0.625vw] rounded-none"
                >
                  <div>
                    <i className="cloud-closecircle flex items-center text-[16px] xl:text-[15px] 3xl:text-[0.938vw]"></i>
                  </div>
                  <div className="text-[#3C4146] text-[15px] xl:text-[15px] 3xl:text-[0.938vw] font-medium leading-[16px] flex items-center">
                    {t('cancel')}
                  </div>
                </Button>
              </SheetClose>

              <Button
                onClick={handleSubmit(onSubmit)}
                className=" bg-[#00953A] border border-[#067532] flex items-center gap-[8px] 3xl:gap-[0.417vw]  text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[14px] 3xl:px-[0.729vw] rounded-none"
                size="md"
                type="button"
              >
                <div>
                  <i className="cloud-filter text-interfacetextinverse flex items-center text-[16px] xl:text-[15px] 3xl:text-[0.833vw]"></i>
                </div>
                <div className=" text-InterfaceTextwhite font-medium leading-[16px] flex items-center text-[15px] xl:text-[15px] 3xl:text-[0.833vw]">
                  Generate Report
                </div>
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
