'use client';
import React from 'react';
import GenerateNewReport from '../components/generate-new-report';
import BillingReportTable from '../components/report-table';
import { useTranslations } from 'next-intl';

export default function Reports() {
  const t = useTranslations();
  return (
    <>
      <div className=" h-full">
        <div className=" h-full px-6 py-4 bg-[#F6F7F8]">
          <div className="">
            <div className="flex flex-col ">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
                {t('reports')}
              </div>
            </div>
          </div>

          <div className="flex gap-[2px] my-[16px] xl:my-[16px] 3xl:my-[0.833vw]">
            <GenerateNewReport />
          </div>
          <div>
            <BillingReportTable />
          </div>
        </div>
      </div>
    </>
  );
}
