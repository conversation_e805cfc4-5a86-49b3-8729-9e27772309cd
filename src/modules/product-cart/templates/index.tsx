'use client';
import React from 'react';
import QuoteSummary from '../component/quote-summary';
import ItemDetails from '../component/item-details';
import RequestQuotation from '../component/request-quotation';
import ActivityLog from '../component/activity-log';

export default function ProductCartTemplate() {
  return (
    <>
      <div className="px-[28px] xl:px-[32px] 3xl:px-[1.667vw] pb-[30px] xl:pb-[40px] 3xl:pb-[2.292vw] pt-[22px] xl:pt-[24px] 3xl:pt-[1.25vw]">
        <div className="dark:bg-[#0F1013] grid grid-cols-12 gap-[22px] xl:gap-[24px] 3xl:gap-[1.25vw] h-screen">
          <div className="col-span-9 bg-interfacesurfacecomponent cardShadow pt-[14px] xl:pt-[16px] 3xl:pt-[0.833vw] pb-[18px] xl:pb-[20px] 3xl:pb-[1.042vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
            <div className="flex justify-between items-center mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
              <div className="text-InterfaceTexttitle font-semibold text-[18px] xl:text-[20px] 3xl:text-[1.042vw]">
                Request for Quotation
              </div>
              <ActivityLog />
            </div>
            <RequestQuotation />
            <ItemDetails />
          </div>
          <div className="col-span-3 relative bg-interfacesurfacecomponent cardShadow py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
            <QuoteSummary />
          </div>
        </div>
      </div>
    </>
  );
}
