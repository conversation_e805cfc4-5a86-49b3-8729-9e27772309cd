'use client';
import React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function RequestQuotation() {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-[22px] xl:gap-[24px] 3xl:gap-[1.25vw]">
        <div className="col-span-1 py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw] border border-InterfaceStrokedefault">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
            Cart Information
          </div>
          <div className="grid grid-cols-3 gap-[18px] xl:gap-[20px] 3xl:gap-[1.042vw] ">
            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Cart ID
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                CT100001
              </div>
            </div>
            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Quote ID
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                RFQ1001
              </div>
            </div>
            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Quote Created
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                03/03/2025
              </div>
            </div>

            <div className="py-[10px] xl:py-[12px] 3xl:py-[0.625vw] space-y-1 border-b border-InterfaceStrokesoft">
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                Created By
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                Owen Smith
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-1 py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw] border border-InterfaceStrokedefault">
          <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
            End Customer Information
          </div>
          <div className="mb-[14px] mb-[16px] 3xl:mb-[0.833vw] space-y-[22px] xl:space-y-[24px] 3xl:spac-y-[1.25vw]">
            <div className="flex gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div className="relative w-full">
                <Select defaultValue="Select End Customer">
                  <SelectTrigger className="text-InterfaceTextsubtitle placeholder-text-sm leading-6 border border-InterfaceStrokehard rounded-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
                    <SelectValue placeholder="Select End Customer" className="" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup className=" text-InterfaceTextsubtitle border-none text-[12px] 2xl:text-[14px] xl:text-[14px] 3xl:text-[0.729vw] ">
                      <SelectItem value="Select End Customer">Select End Customer</SelectItem>
                      <SelectItem value="india">India</SelectItem>
                      <SelectItem value="mea">UAE</SelectItem>
                      <SelectItem value="tukry">Turky</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
