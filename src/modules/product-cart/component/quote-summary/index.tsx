'use client';
import React from 'react';

export default function QuoteSummary() {
  return (
    <>
      <div className="flex justify-between items-center">
        <div className="text-InterfaceTexttitle font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw]">
          Quote Summary
        </div>
        <div className="text-InterfaceTexttitle font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw]">
          (USD)
        </div>
      </div>
      <div className="mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw] bg-InterfaceSurfacecomponentmuted px-[8px] xl:px-[8px] 3xl:px-[0.417vw] py-2">
        <div className="flex justify-between items-center text-InterfaceTextdefault text-[14px] xl:text-[16px] 3xl:text-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw]">
          <div>Total Quote Value</div>
          <div className="font-semibold">8,000.00 </div>
        </div>
      </div>
      <div className="absolute bottom-0 left-0 right-0 py-[14px] xl:py-[16px] 3xl:py-[0.833vw] px-[18px] xl:px-[20px] 3xl:px-[1.042vw]">
        <div className="space-y-2">
          <div className="w-full text-InterfaceTextwhite bg-BrandPrimarypurenew text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokesoft font-medium py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
            Submit
          </div>
          <div className="w-full text-InterfaceTextdefault bg-buttonbasedefaulthover2 text-center text-[12px] xl:text-[14px] 3xl:text-[0.729vw] border border-InterfaceStrokesoft font-medium py-[10px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw]">
            Cancel
          </div>
        </div>
      </div>
    </>
  );
}
