'use client';
import React, { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import { Input } from '@redington-gulf-fze/cloudquarks-component-library';

type User = {
  id: string;
  productname: string;
  currency: string;
  price: string;
  quantity: string;
  targetprice: string;
  subtotal: string;
};

export default function ItemDetails() {
  const initialData: User[] = [
    {
      id: '1',
      productname: 'Advanced Communications',
      currency: 'USD',
      price: '500.00',
      quantity: '4',
      targetprice: '450.00',
      subtotal: '2000.00',
    },
    {
      id: '2',
      productname: 'Cloud Storage',
      currency: 'USD',
      price: '100.00',
      quantity: '4',
      targetprice: '450.00',
      subtotal: '200.00',
    },
  ];

  const [data, setData] = useState<User[]>(initialData);
  const [showDetails, setShowDetails] = useState(false);

  const updateQuantity = (id: string, delta: number) => {
    setData((prevData) =>
      prevData.map((item) => {
        if (item.id === id) {
          const currentQuantity = parseInt(item.quantity) || 0;
          const newQuantity = Math.max(currentQuantity + delta, 0);
          const pricePerUnit = parseFloat(item.price) || 0;
          const newSubtotal = (pricePerUnit * newQuantity).toFixed(2);
          return {
            ...item,
            quantity: newQuantity.toString(),
            subtotal: newSubtotal,
          };
        }
        return item;
      })
    );
  };

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          Product Name
        </div>
      ),
      cell: () => {
        return (
          <div>
            <div className="text-[14px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
              Advanced Communications
            </div>

            {showDetails && (
              <div className="flex flex-col">
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Brand: <span className="text-InterfaceTextdefault font-normal">Microsoft</span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Segment: <span className="text-InterfaceTextdefault font-normal">Commercial</span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Term: <span className="text-InterfaceTextdefault font-normal">1 Year</span>
                </div>
                <div className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTexttitle font-medium">
                  Bill Type: <span className="text-InterfaceTextdefault font-normal">Monthly</span>
                </div>
              </div>
            )}

            {showDetails ? (
              <div
                className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextprimary font-[400] cursor-pointer"
                onClick={() => setShowDetails(false)}
              >
                Hide Details
              </div>
            ) : (
              <div
                className="text-[10px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextprimary font-[400] cursor-pointer"
                onClick={() => setShowDetails(true)}
              >
                View Details
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'currency',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Currency
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.currency}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
    {
      accessorKey: 'price',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Price
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.price}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Quantity
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex justify-center items-center h-full">
            <div className="flex items-center justify-center custborder rounded-none px-3 gap-3 bg-white">
              <div onClick={() => updateQuantity(item.id, -1)} className="p-0 m-0 cursor-pointer">
                <span className="text-[18px] text-InterfaceTexttitle font-[400]">-</span>
              </div>
              <Input
                value={item.quantity}
                readOnly
                className="w-[40px] h-[32px] text-center text-InterfaceTexttitle font-[500] border-none focus:ring-0 focus-visible:ring-0 shadow-none"
              />
              <div onClick={() => updateQuantity(item.id, 1)} className="p-0 m-0 cursor-pointer">
                <span className="text-[18px] text-InterfaceTexttitle font-[400]">+</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: ' targetprice',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Target Price
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.targetprice}
            // readOnly
            className="w-[100px] text-center text-InterfaceTextdefault font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
    {
      accessorKey: 'subtotal',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Sub Total
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center h-full">
          <Input
            value={row.original.subtotal}
            readOnly
            className="w-[100px] text-center text-BrandNeutral500 font-[500] bg-[#FFF] custinputborder"
          />
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="mt-[20px] xl:mt-[22px] 3xl:mt-[1.25vw]">
        <div className="text-InterfaceTexttitle text-[16px] xl:text-[18px] 3xl:text-[0.938vw] font-semibold">
          Item Details
        </div>
      </div>
      <div className="">
        <div className="product-table custtable custtable1">
          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw] bg-[#FFF]">
            <DataTable data={data} columns={notificationColumns} />
          </div>
        </div>
      </div>
    </>
  );
}
