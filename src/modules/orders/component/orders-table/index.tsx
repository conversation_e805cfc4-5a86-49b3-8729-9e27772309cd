'use client';
import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Calendar } from '@redington-gulf-fze/cloudquarks-component-library';
// import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import ApplyFilterPopup from '../apply-filter-popup';
import Link from 'next/link';
import { CloneOrderPopup } from './clonepopup';
import { useTranslations } from 'next-intl';
export default function Orderstable() {
  const t = useTranslations();
  const [fromDate, setFromDate] = React.useState<Date | undefined>(new Date('2025-01-01'));
  const [toDate, setToDate] = React.useState<Date | undefined>(new Date('2025-03-01'));

  const clearAll = () => {
    setFromDate(undefined);
    setToDate(undefined);
  };

  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'order',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('order')}#
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Order #"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: () => {
        return <div className="text-InterfaceTextprimary cursor-pointer">41400000428</div>;
      },

      minSize: 400,
    },
    {
      accessorKey: 'ordertype',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('orderType')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Order Type"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('ordertype')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'orderdate',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('orderDate')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <ColumnHeaderFilter
              placeholder="Order Date"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('orderdate')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endCustomer')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <ColumnHeaderFilter
              placeholder="End Customer"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('endcustomer')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'orderplacedby',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('orderPlacedBy2')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <ColumnHeaderFilter
              placeholder="Order Placed by"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('orderplacedby')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'brandcategory',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('brandCategory')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Brand Category"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('brandcategory')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'currency',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('currency')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Currency"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('currency')}</div>,

      minSize: 600,
    },
    {
      accessorKey: 'totalordervalue',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('totalOrderValue')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Total Order Value"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('totalordervalue')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-semibold justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('status')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Status"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        const statusConfig = {
          Completed: {
            colorClass: 'bg-BrandHighlight100 text-BrandHighlight800 border-BrandHighlight300',
          },
          Pending: {
            colorClass: 'bg-BrandSupport2100 text-BrandSupport2800 border-BrandSupport2300',
          },
          Failed: {
            colorClass: 'bg-[rgba(255,181,165,0.3)] text-[#D42600] border-[rgba(212,38,0,0.4)]',
          },
          PartiallyCompleted: {
            colorClass: 'bg-BrandPrimary100 text-BrandPrimary800 border-BrandPrimary300',
          },
        };

        const { colorClass } = statusConfig[status as keyof typeof statusConfig] || {
          colorClass: 'bg-gray-100 text-gray-800 border-gray-300',
        };
        const handleClick = () => {
          if (status === 'Pending' || status === 'Failed') {
            window.location.href = `/sales/orders/order-view?status=${status}`;
          }
        };

        return (
          <div className="cursor-pointer ">
            <div
              onClick={handleClick}
              className={`inline-block py-[4px] xl:py-[4px] 3xl:py-[0.357vw] px-[8px] rounded-[2px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] border  ${colorClass}`}
            >
              <div>{status}</div>
            </div>
          </div>
        );
      },

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle font-semibold text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[160px] xl:w-[160px] 2xl:w-[180px] 3xl:w-[9.833vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <div className="">
                  <Link
                    href={`/sales/orders/order-view?status=${row.getValue('status') as string}`}
                  >
                    <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
                      <i className="cloud-readeye1 text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
                      {t('view')}
                    </div>
                  </Link>

                  <CloneOrderPopup />
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    order: string;
    ordertype: string;
    orderdate: string;
    endcustomer: string;
    orderplacedby: string;
    brandcategory: string;
    currency: string;
    totalordervalue: string;
    status: string;
  };

  const registrationdata = [
    {
      order: '356000023412',
      ordertype: 'New Order',
      orderdate: '04/03/2025',
      endcustomer: 'Computer Systems',
      orderplacedby: 'William Berger',
      brandcategory: 'AWS',
      currency: 'USD',
      totalordervalue: '5,000.00',
      status: 'Completed',
    },
    {
      order: '356000023412',
      ordertype: 'New Order',
      orderdate: '04/03/2025',
      endcustomer: 'Computer Systems',
      orderplacedby: 'William Berger',
      brandcategory: 'AWS',
      currency: 'USD',
      totalordervalue: '5,000.00',
      status: 'Completed',
    },
    {
      order: '356000023412',
      ordertype: 'New Order',
      orderdate: '04/03/2025',
      endcustomer: 'Computer Systems',
      orderplacedby: 'William Berger',
      brandcategory: 'AWS',
      currency: 'USD',
      totalordervalue: '5,000.00',
      status: 'Completed',
    },
    {
      order: '356000023412',
      ordertype: 'New Order',
      orderdate: '04/03/2025',
      endcustomer: 'Computer Systems',
      orderplacedby: 'William Berger',
      brandcategory: 'AWS',
      currency: 'USD',
      totalordervalue: '5,000.00',
      status: 'Completed',
    },
    {
      order: '356000023412',
      ordertype: 'New Order',
      orderdate: '04/03/2025',
      endcustomer: 'Computer Systems',
      orderplacedby: 'William Berger',
      brandcategory: 'AWS',
      currency: 'USD',
      totalordervalue: '5,000.00',
      status: 'Pending',
    },
    {
      order: '356000023412',
      ordertype: 'New Order',
      orderdate: '04/03/2025',
      endcustomer: 'Computer Systems',
      orderplacedby: 'William Berger',
      brandcategory: 'AWS',
      currency: 'USD',
      totalordervalue: '5,000.00',
      status: 'Failed',
    },
    {
      order: '356000023412',
      ordertype: 'New Order',
      orderdate: '04/03/2025',
      endcustomer: 'Computer Systems',
      orderplacedby: 'William Berger',
      brandcategory: 'AWS',
      currency: 'USD',
      totalordervalue: '5,000.00',
      status: 'PartiallyCompleted',
    },
  ];
  return (
    <>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <div className="flex justify-between border-b border-InterfaceStrokesoft px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] ">
          <div className="flex items-center gap-[12px] ">
            <div className="text-InterfaceTexttitle text-[18px] font-semibold">
              {' '}
              {t('listOfOrders')}
            </div>
            <div className="text-InterfaceTextsubtitle text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
              {t('showingRecords')}
            </div>
          </div>
          <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('search')}
                className="w-[300px] xl:w-[300px] 2xl:w-[350px] 3xl:w-[20.833vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[12px] xl:top-[11px] 2xl:top-[12px] 3xl:top-[12px] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            <ApplyFilterPopup open={false} onClose={() => {}} />
          </div>
        </div>

        <div className="overflow-x-auto">
          <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw] px-[16px] xl:px-[16px] 3xl:px-[0.833vw]  py-[12px] xl:py-[12px] 3xl:py-[0.625vw] text-sm font-normal">
            {/* From Date */}
            <div className="flex items-center bg-[#F4F5F7] px-2 py-[6px] rounded-sm">
              <span className="text-[#6B7280]">{t('fromDate')}:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {fromDate ? format(fromDate, 'dd-MM-yyyy') : 'Select Date'}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={fromDate} onSelect={setFromDate} />
                </PopoverContent>
              </Popover>
              {fromDate && (
                <button onClick={() => setFromDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              )}
            </div>

            {/* To Date */}
            <div className="flex items-center bg-[#F4F5F7] px-[12px] 3xl:px-[0.625vw] py-[4px] 3xl:py-[0.366vw] rounded-sm">
              <span className="text-[#6B7280]"> {t('toDate')}:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="ml-1 font-semibold text-InterfaceTextdefault outline-none">
                    {toDate ? format(toDate, 'dd-MM-yyyy') : 'Select Date'}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#FFF]">
                  <Calendar mode="single" selected={toDate} onSelect={setToDate} />
                </PopoverContent>
              </Popover>
              {toDate && (
                <button onClick={() => setToDate(undefined)} className="ml-1">
                  <i className="cloud-closecircle ml-[4px] text-[#8B8B8B] font-[400] hover:text-black"></i>
                </button>
              )}
            </div>

            {/* Clear All */}
            {(fromDate || toDate) && (
              <button
                onClick={clearAll}
                className="flex items-center text-[#8B8B8B] font-[400] hover:text-black"
              >
                <i className="cloud-closecircle mr-[4px]"></i>
                {t('clearAll')}
              </button>
            )}
          </div>
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={true} />
        </div>
        <TablePagination />
      </div>
    </>
  );
}
