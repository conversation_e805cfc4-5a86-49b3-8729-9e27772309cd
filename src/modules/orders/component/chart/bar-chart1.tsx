'use client';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { useTranslations } from 'next-intl';
export default function Barchart1() {
  const t = useTranslations();
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      left: '20%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
      itemHeight: 10,
      itemWidth: 10,
      textStyle: {
        fontSize: 11,
      },
    },
    grid: {
      left: '22%',
      right: '0%',
      bottom: '20%',
      top: '8%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['aws', 'csp', 'googlePer'].map((item) => t(item)),
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
          interval: 0, // ensures all labels are shown
          rotate: 0, // try 15 or 30 if needed for longer labels
        },
      },
    ],
    yAxis: [
      {
        name: t('orderValue'),
        type: 'value',
        min: 0,
        max: 800,
        interval: 200,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#7F8488',
          fontSize: 10,
        },
      },
    ],
    series: [
      {
        name: 'Order Value',
        type: 'bar',
        barWidth: '70%',
        data: [450, 300, 520],
        label: {
          show: true,
          position: 'insideTop',
          fontSize: 10,
          color: '#FFF',
          formatter: '{c}',
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#F2980E' }, // Top color
            { offset: 1, color: '#F8B720' }, // Bottom color
          ]),
          borderRadius: [2, 2, 0, 0],
        },
      },
    ],
  };
  return (
    <ReactEcharts
      echarts={echarts}
      option={option}
      style={{ width: '100%', height: '100%' }}
      opts={{ renderer: 'svg' }}
    />
  );
}
