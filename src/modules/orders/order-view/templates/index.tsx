'use client';
import * as React from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import ViewTable from '../component/view-table';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
export default function OrdersViewTemplate() {
  const t = useTranslations();
  const searchParams = useSearchParams(); // App Router
  const [status, setStatus] = useState<'Pending' | 'Completed' | 'Failed' | undefined>(undefined);
  useEffect(() => {
    const s = searchParams.get('status');
    if (s === 'Pending' || s === 'Completed' || s === 'Failed') {
      setStatus(s);
    }
  }, [searchParams]);

  const statusStyleMap: Record<'Pending' | 'Completed' | 'Failed', string> = {
    Pending: 'bg-[#FFF9C4] text-[#8A6D00] border-[#FFF176]',
    Failed: 'bg-[#FFCDD2] text-[#B71C1C] border-[#EF9A9A]',
    Completed: 'bg-[#E8F5E9] text-[#2E7D32] border-[#A5D6A7]',
  };
  return (
    <div className="p-[20px] pl-[20px] xl:pl-[20px] 2xl:pl-[100px] 3xl:pl-[5.21vw] pr-[20px] xl:pr-[30px] 2xl:pr-[100px] 3xl:pr-[5.21vw] pb-[20px] xl:pb-[20px] 3xl:pb-[1.042vw] w-full">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle mt-[16px] xl:mt-[18px] 3xl:mt-[1.042vw]">
          {t('order')}41400000428
        </div>
      </div>
      <div className="space-y-[20px] space-y-[22px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <Button className="ticketcard text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
            <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('printOrder')}
          </Button>
          <Link href="/sales/orders">
            <Button className="ticketcard text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
              <i className="cloud-back font-medium text-[14px] xl:text-[15px] 3xl:text-[0.833vw] text-BrandSupport1pure"></i>{' '}
              {t('back')}
            </Button>
          </Link>
        </div>

        <div className="p-[30px] xl:p-[30px] 2xl:p-[32px] 3xl:p-[1.67vw] bg-[#FFF] flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
          <div className="flex justify-between items-center gap-[4px]">
            <div className="flex flex-col gap-[4px]">
              <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.833vw] font-[600] leading-[100%]">
                {t('created')}:
              </div>
              <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                April 1, 2025 (Innovate First Innovate Last)
              </div>
            </div>

            <div
              className={`inline-block py-[2px] xl:py-[2px] 3xl:py-[0.1vw] px-[8px] rounded-[2px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold border ${status ? statusStyleMap[status] : ''}`}
            >
              <div>{status}</div>
            </div>
          </div>

          <div className="p-[20px] xl:p-[20px] 3xl:p-[1.042vw] border border-InterfaceStrokesoft">
            <div className="grid grid-cols-4 gap-[32px] 3xl:gap-[1.67vw]">
              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('billFrom')}{' '}
                </div>
                <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[140%]">
                    Redington Gulf FZE
                  </div>
                  <div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Plot No. S 30902 South,
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Jabel Ali Free Zone Dubai,
                    </div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      United Arab Emirates
                    </div>
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('VATTRN')}: </span> ***************
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('billTo')}
                </div>
                <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                    Delphi Consulting LLC
                  </div>
                  <div>
                    <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      Suite 1407, Concord Tower, Al Safouh 2 Media city, Dubai. Dubai, Dubai <br />
                      United Arab Emirates
                    </div>
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('TEL')}</span> ***************
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('redingtonAccountNumber')}: </span> **********
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('serviceRecipient')}{' '}
                </div>
                <div className="flex flex-col ">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                    The First Group
                  </div>
                  <div>
                    <div className="mt-[8px] 3xl:mt-[0.417vw] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                      22nd Floor Tameem House Barsha Heights Dubai, Dubai, 24573 <br />
                      United Arab Emirates.
                    </div>
                  </div>
                  <div className="mt-[2px] text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[500]">{t('TEL')}: </span> 566869304
                  </div>
                </div>
                <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
                  <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
                    {t('organizationTenant')}
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('tenantID')}: </span>{' '}
                    f6e191f8-5aed-4670-86d4-616277ebe4c0
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('tenantName')}: </span>{' '}
                    thefirstgroup.onmicrosoft.com
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-[16px] 3xl:gap-[0.833vw]">
                <div className="text-InterfaceTexttitle text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] font-[600] leading-[140%]">
                  {t('orderDetails')}{' '}
                </div>
                <div className="flex flex-col gap-[2px]">
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('store')}: </span> BH-AR (New)
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('vendor')}: </span> Amazon
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('brand')}: </span> AWS
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('brandCategory')}: </span> AWS
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('paymentTerms')}: </span> R032-Advance Terms
                  </div>
                  <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                    {' '}
                    <span className="font-[600]">{t('reference')}: </span> LPO-543543...{' '}
                    <span className="font-[400] text-InterfaceTextprimary underline cursor-pointer">
                      {t('view')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <ViewTable />
            <div className="flex items-center justify-end mr-[100px]">
              <div className="flex flex-col gap-[6px] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.36vw]">
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('subtotal')}
                </div>
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('estimatedVAT')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
                  {t('grandTotal')}
                </div>
              </div>
              <div className="flex flex-col gap-[6px] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.36vw]">
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  USD 1.00{' '}
                </div>
                <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  USD 0.00{' '}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[600] leading-[140%]">
                  USD 1.00{' '}
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-[8px] 3xl:gap-[0.417vw]">
            <div className="text-InterfaceTexttitle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[600] leading-[140%]">
              {t('disclaimer')}:
            </div>
            <div className="text-InterfaceTextdefault text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
              Net Order Value quoted above is inclusive of Value Added Taxes (VAT) as currently
              applicable. Any additional cost implication arising from change in local taxes
              structure will lead to increase in prices to that effect at the time of billing and
              this will be borne by the partner and on partners account
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
