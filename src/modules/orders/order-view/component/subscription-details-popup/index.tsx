'use client';
import * as React from 'react';
import {
  SheetTitle,
  // Sheet<PERSON>eader,
  Sheet,
  // SheetClose,
  She<PERSON><PERSON>ontent,
  Sheet<PERSON>rigger,
  // But<PERSON>,
  SheetClose,
} from '@redington-gulf-fze/cloudquarks-component-library';

export default function SubscriptionDetailsPopup() {
  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger>
        <i className="cloud-doc text-InterfaceTextsubtitle cursor-pointer flex " title="Doc"></i>
      </SheetTrigger>
      <SheetContent className="hideclose flex flex-col h-full gap-0 sm:max-w-[550px] xl:max-w-[550px] 3xl:max-w-[28.65vw] p-[0px]">
        <div className="h-full overflow-y-auto px-[26px] xl:px-[26px] 3xl:px-[1.354vw] mt-[100px] lg:mt-[100px] xl:mt-[100px] 2xl:mt-[100px] 3xl:mt-[5.208vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-folder text-[#5094f3] text-[72px]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] font-normal leading-[140%] text-center">
                Subscription Details
              </div>
            </div>
          </div>
          <div className=" text-InterfaceTextsubtitle text-[20px] font-normal leading-[140%] text-center">
            Order is in progress. Awaiting response <br /> from Vendor
          </div>
          <div className="flex justify-center items-center gap-[16px] 3xl:gap-[0.833vw] mt-[40px] 3xl:mt-[2.083vw] text-center">
            <SheetClose asChild>
              <button className="bg-[#0B9444] text-[16px] text-white font-medium px-[16px] py-[10px] flex justify-center items-center gap-2 w-[150px] 3xl:w-[7.813vw]">
                <i className="cloud-closecircle text-[18px]"></i>
                close
              </button>
            </SheetClose>
          </div>
        </div>
        {/* <div className="relative p-[20px] xl:p-[22px] 3xl:p-[1.25vw] overflow-auto h-[420px] xl:h-[480px] 2xl:h-[560px] 3xl:h-[37.25vw]">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div>
              <div className="flex flex-col space-y-[24px]">
                <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    End Customer
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    innovate Itd
                  </div>
                </div>
                <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Product Name
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Amazon Web Services
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-1 gap-[20px]  space-y-3 mt-[20px]">
                <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] mt-[12px]">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Subscription ID
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    345678901256
                  </div>
                </div>
                <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Quantity
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    1
                  </div>
                </div>
                <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Starts On
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    01-03-2025
                  </div>
                </div>
                <div className=" space-y-2 border-b border-InterfaceStrokesoft pb-[12px] xl:pb-[12px] 3xl:pb-[0.625vw] ">
                  <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    Expires On
                  </div>
                  <div className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                    01-03-2026
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="absolute top-[16px] right-[20px]">
            <div className="cursor-pointer ">
              <div
                className={`inline-block py-[2px] xl:py-[2px] 3xl:py-[0.1vw] px-[10px] rounded-[2px] text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold border bg-[#E8F4E4] text-[#315229] border-[#ACD69F]`}
              >
                <div>Fulfilled</div>
              </div>
            </div>
          </div>
        </div> */}
      </SheetContent>
    </Sheet>
  );
}
