'use client';
import * as React from 'react';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import Orderstable from '../component/orders-table';
import OrdersCards from '../component/orders-cards';
import { useTranslations } from 'next-intl';
export default function OrdersTemplate() {
  const t = useTranslations();
  return (
    <div className="py-[20px] xl:py-[16px] 3xl:py-[1.042vw] px-[32px] xl:px-[24px] 3xl:px-[1.667vw] w-full">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle mt-[16px] xl:mt-[18px] 3xl:mt-[1.042vw]">
          {t('orders')}
        </div>
        <div className="italic text-[10px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[400] text-InterfaceTextsubtitle">
          *{t('basedOnTheSelectedDateRange')}
        </div>
      </div>
      <div className="space-y-[16px] xl:space-y-[16px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="">
          <div>
            <OrdersCards />
          </div>
        </div>
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure text-InterfaceTextdefault">
            <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('download')}
          </Button>
        </div>

        <div className="bg-interfacesurfacecomponent tbl-shadow">
          <Orderstable />
        </div>
      </div>
    </div>
  );
}
