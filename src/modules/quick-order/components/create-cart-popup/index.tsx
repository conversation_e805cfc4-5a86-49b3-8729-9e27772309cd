'use client';

import { ActivitylogSheetProps } from '@/types';
import {
  Button,
  Input,
  Label,
  SheetClose,
  SheetFooter,
  SheetHeader,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Sheet, SheetContent, SheetTitle } from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { Select2, Select2Option } from '@/components/common/ui/combo-box';
import { useState } from 'react';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function CreateCartPopup({ open, onClose }: ActivitylogSheetProps) {
  const [endcustomer, setEndcustomer] = useState('');
  const endCustomerOptions: Select2Option[] = [
    { label: 'Alpha Systems - Binghati Towers, 201, AI Mankhool, Dubai, UAE', value: 'Binghati' },
    { label: 'Alpha Systems - ABC Towers, 201, AI Mankhool, Dubai, UAE', value: 'ABC' },
    { label: 'Alpha Systems - 123 Block, 201, AI Mankhool, Dubai, UAE', value: '123' },
    { label: 'Alpha Systems - SkyHigh Towers, 201, AI Mankhool, Dubai, UAE', value: 'SkyHigh' },
  ];

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetTitle></SheetTitle>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] h-screen`}
        side={'right'}
      >
        <SheetHeader className=" border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              New Cart
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <div className="h-screen overflow-y-auto p-[20px] lg:p-[20px] xl:p-[24px] 2xl:p-[24px] 3xl:p-[1.25vw]">
          <div className="space-y-[24px] h-full">
            <div className="flex flex-col gap-1.5">
              <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                Cart Name <span className="text-red-600">*</span>
              </Label>
              <Input
                placeholder="Enter Cart Name"
                className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none focus:border-InterfaceStrokehard focus:ring-0 text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[8px] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
              />
            </div>
            <div className="flex flex-col gap-1.5">
              <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                Description <span className="text-red-600">*</span>
              </Label>
              <Textarea
                className="rounded-[2px] border border-InterfaceStrokehard my-[6px] xl:my-[6px] 2xl:my-[6px] 3xl:my-[0.313vw]"
                placeholder="Enter Remarks"
              />
              <p className="text-InterfaceTextsubtitle font12 font-[400] leading-[140%]">
                Character Limit: 50
              </p>
            </div>

            <div className="flex flex-col gap-1.5">
              <Label className="text-InterfaceTexttitle text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500] leading-[140%]">
                End Customer <span className="text-red-600">*</span>
              </Label>

              <Select2
                options={endCustomerOptions}
                value={endcustomer}
                placeholder="--Select End Customer--"
                onChange={(val) => setEndcustomer(val as string)}
              />
            </div>
          </div>
        </div>
        <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft bg-white absolute bottom-0 xl:px-[24px] 2xl:px-[24px] 3xl:px-[1.25vw] w-full">
          <SheetClose asChild>
            <Button
              type="button"
              className="graygradientbtn border border-InterfaceStrokesoft text-InterfaceTextdefault px-[16px] py-[8px] rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] flex items-center gap-2"
              variant="outline"
              size="sm"
              // onClick={handleClose}
            >
              <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              Close
            </Button>
          </SheetClose>
          <Button
            type="submit"
            className="bg-BrandPrimarypure text-white px-[16px] py-[8px] rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] flex items-center gap-2"
          >
            <i className="cloud-cart"></i>
            Create and Go To Cart
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
