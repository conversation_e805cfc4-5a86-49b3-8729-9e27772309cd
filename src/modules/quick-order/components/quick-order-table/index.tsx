'use client';
import React, { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import { Button, Input } from '@redington-gulf-fze/cloudquarks-component-library';
import Image from 'next/image';
import { QuickOrderPopup } from '../quick-order-popup';
import { CreateCartPopup } from '../create-cart-popup';

type User = {
  id: string;
  search?: string;
  image?: string;
  productname: string;
  term?: string;
  billtype?: string;
  price: string;
  quantity: string;
  subtotal: string;
};

export default function QuickOrderTable() {
  const [quickorderpopup, setQuickOrderpopup] = React.useState(false);
  const [createcartpopup, setCreateCart] = React.useState(false);

  const initialData: User[] = [
    {
      id: '1',
      search: '',
      image: '/images/svg/redington_logo.svg',
      productname: 'Reserved VM Instance, Cool_GRS_Data_Stored_10',
      term: '1 Year',
      billtype: 'Monthly',
      price: '500.00',
      quantity: '4',
      subtotal: '500.00',
    },
    {
      id: '2',
      image: '/images/svg/redington_logo.svg',
      productname: 'Microsoft 365 E5 Security',
      term: '2 Year',
      billtype: 'Monthly',
      price: '1,500.00',
      quantity: '1',
      subtotal: '1,500.00',
    },
  ];

  const [data, setData] = useState<User[]>(initialData);

  const updateQuantity = (id: string, delta: number) => {
    setData((prevData) =>
      prevData.map((item) => {
        if (item.id === id) {
          const currentQuantity = parseInt(item.quantity) || 0;
          const newQuantity = Math.max(currentQuantity + delta, 0);
          const pricePerUnit = parseFloat(item.price) || 0;
          const newSubtotal = (pricePerUnit * newQuantity).toFixed(2);
          return {
            ...item,
            quantity: newQuantity.toString(),
            subtotal: newSubtotal,
          };
        }
        return item;
      })
    );
  };

  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'search',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          Search
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="relative">
            <Input
              type="text"
              placeholder="Search by Product Name or SKU ID"
              className="w-[276px] xl:w-[276px] 2xl:w-[276px] 3xl:w-[14.375vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-InterfaceTextsubtitle placeholder:font-[300] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[14px] 3xl:placeholder:text-[0.729vw] text-blackcolor !border border-InterfaceStrokehard focus:outline-none focus:ring-0 focus:border-InterfaceStrokehard h-full"
            />
            <i className="cloud-search absolute text-[#828A91] top-[10px] xl:top-[10px] 3xl:top-[0.521vw] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw] "></i>
          </div>
        );
      },
    },
    {
      accessorKey: 'image',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          Photo
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="text-[14px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
            <Image
              src={item.image || '#'}
              alt={item.productname}
              width={50}
              height={50}
              className="w-[50px] h-[50px] object-cover rounded"
            />
          </div>
        );
      },
    },
    {
      accessorKey: 'productname',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
          Product
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div>
            <div className="text-[14px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTexttitle font-semibold">
              {item.productname}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
                <span className="text-InterfaceTexttitle font-medium">Term:</span> 1 Year
              </div>
              <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle">
                <span className="text-InterfaceTexttitle font-medium">Bill Type:</span> Monthly
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'price',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-end text-InterfaceTextsubtitle">
          Price
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-end items-center h-full">
          <div className="text-InterfaceTextdefault font-medium font16 text-end">
            {row.original.price}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-center text-InterfaceTextsubtitle">
          Quantity
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex justify-center items-center h-full">
            <div className="flex items-center justify-center custborder rounded-none px-3 gap-3 bg-white">
              <div onClick={() => updateQuantity(item.id, -1)} className="cursor-pointer">
                <span className="text-[18px] text-InterfaceTextdefault font-[400]">-</span>
              </div>
              <Input
                value={item.quantity}
                readOnly
                className="w-[40px] h-[32px] text-center text-InterfaceTextdefault font-[500] border-none focus:ring-0 shadow-none"
              />
              <div onClick={() => updateQuantity(item.id, 1)} className="cursor-pointer">
                <span className="text-[18px] text-InterfaceTextdefault font-[400]">+</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'subtotal',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-end text-InterfaceTextsubtitle">
          Sub Total
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-end items-center h-full">
          <div className="text-InterfaceTextdefault font-medium font16 text-end">
            {row.original.subtotal}
          </div>
        </div>
      ),
    },
    {
      id: 'actions',
      header: () => (
        <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextsubtitle text-center">
          Action
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex justify-center items-center gap-[14px] 3xl:gap-[0.729vw] h-full">
            <span className="cursor-pointer" onClick={() => setQuickOrderpopup(true)}>
              <i className="cloud-edit2 text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] text-center text-InterfaceTextprimary"></i>
            </span>
            <span className="cursor-pointer">
              <i className="cloud-refresh text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] text-center text-InterfaceTextprimary"></i>
            </span>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className="bg-[#FFF] rounded-[4px]">
        <div className="product-table custtableborder custtable custtable1 px-[16px] 3x:px-[0.833vw] pt-[4px] 3xl:pt-[0.21vw] pb-[16px] 3xl:pb-[0.833vw]">
          <DataTable data={data} columns={notificationColumns} />
        </div>

        <div className="flex justify-end gap-[16px] 3xl:gap-[0.833vw]  px-[16px] 3x:px-[0.833vw]">
          <Button className="graygradientbtn border border-InterfaceStrokesoft text-InterfaceTextdefault px-[16px] py-[8px] rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] flex items-center gap-2">
            <i className="cloud-Add font12"></i>
            Add Lines
          </Button>
          <Button
            onClick={() => setCreateCart(true)}
            className="bg-BrandPrimarypure text-white px-[16px] py-[8px] rounded-none text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] flex items-center gap-2"
          >
            <i className="cloud-cart font14"></i>
            Create Cart
          </Button>
        </div>
      </div>
      <QuickOrderPopup open={quickorderpopup} onClose={() => setQuickOrderpopup(false)} />
      <CreateCartPopup open={createcartpopup} onClose={() => setCreateCart(false)} />
    </>
  );
}
