'use client';

import { Button, SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetFooter,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import Image from 'next/image';
// import { useRouter } from 'next/navigation';
import { useState } from 'react';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

interface SheetProps {
  open: boolean;
  onClose: () => void;
}

export function QuickOrderPopup({ open, onClose }: SheetProps) {
  const [selected, setSelected] = useState('commercial');
  const [selectedterm, setSelectedTerm] = useState('1Month');
  const [selectedtype, setSelectedType] = useState('1Month');

  const optionssegment = [
    { key: 'commercial', label: 'Commercial' },
    { key: 'segment', label: 'Segment' },
  ];
  const optionsterm = [
    { key: '1month', label: '1 Month' },
    { key: '1year', label: '1 Year' },
  ];
  const optionstype = [
    { key: 'monthly', label: 'Monthly' },
    { key: 'annual', label: 'Annual' },
  ];

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[500px] 2xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose `}
        side={'right'}
      >
        <div className="overflow-auto h-[500px] xl:h-[580px] 2xl:h-[620px] 3xl:h-[32.292vw] ">
          <div className="px-4 mt-[30px] lg:mt-[40px] xl:mt-[60px] 2xl:mt-[60px] 3xl:mt-[3.646vw] mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
            <div className="flex flex-col justify-center items-center gap-[40px] xl:gap-[30px] 3xl:gap-[2.083vw]">
              <i className="cloud-info2 text-BrandSupport1pure text-[72px] xl:text-[42px] 2xl:text-[50px] 3xl:text-[2.804vw]"></i>
              <div className="flex flex-col justify-center items-center gap-[8px] xl:gap-[6px] 3xl:gap-[0.417vw]">
                <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[24px] 2xl:text-[30px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                  Quick Order
                </div>
              </div>
            </div>
            <div className="my-[30px] lg:my-[40px] xl:my-[40px] 2xl:my-[40px] 3xl:my-[2.083vw]">
              <div className="border border-InterfaceSurfacepagemuted p-[16px] xl:p-[16px] 3xl:p-[0.833vw]">
                <div className="flex items-center gap-[16px] xl:gap-[12px] 3xl:gap-[0.833vw] mb-[16px] xl:mb-[12px] 3xl:mb-[0.833vw]">
                  <Image
                    src="/images/product_img.png"
                    width={55}
                    height={55}
                    alt="logo"
                    className="w-[55px] h-[55px] xl:w-[50px] xl:h-[55px] 3xl:w-[2.865vw] 3xl:h-[2.865vw]"
                  />

                  <div>
                    <div className="text-InterfaceTexttitle font20 font-bold leading-[140%]">
                      Microsoft 365 Apps for <br /> Enterprise
                    </div>
                  </div>
                </div>

                <div className="mb-[16px] xl:mb-[12px] 3xl:mb-[0.833vw]">
                  <p className="text-InterfaceTextsubtitle font12 font-normal leading-[140%]">
                    Description
                  </p>
                  <div className="text-InterfaceTextdefault font14 font-[400] leading-[140%]">
                    Microsoft 365 Apps for Enterprise is a subscription-based productivity suite
                    designed for large organizations, offering the latest versions of Microsoft
                    Office applications, enhanced security, and cloud-based collaboration features.
                  </div>
                </div>

                <div className="pb-[16px] xl:pb-[12px] 3xl:pb-[0.833vw]  border-b border-InterfaceStrokesoft  mb-[16px] xl:mb-[20px] 3xl:mb-[1.042vw]">
                  <p className="text-InterfaceTextsubtitle font12 font-normal leading-[140%]">
                    Price
                  </p>
                  <div className="text-InterfaceTexttitle font20 font-bold leading-[140%]">
                    USD 5,000.00
                  </div>
                </div>

                <div>
                  <div className="py-[18px] xl:py-[20px] 3xl:py-[1.042vw]  border-y border-InterfaceStrokesoft  ">
                    <div className="text-InterfaceTexttitle font16 font-[500] leading-[140%] mb-[10px] 2xl:mb-[8px] xl:mb-[8px] 3xl:mb-[0.833vw] ">
                      Segment
                    </div>
                    <div className="flex overflow-hidden w-full gap-[10px] xl:gap-[12px] 3xl:gap-[0.833vw]">
                      {optionssegment.map((option, index) => (
                        <button
                          key={option.key}
                          type="button"
                          onClick={() => setSelected(option.key)}
                          className={` py-[6px] px-[8px] xl:py-[6px] xl:px-[8px] 2xl:py-[8px] 
                        2xl:px-[8px] 3xl:py-[0.417vw] 3xl:px-[0.417vw] flex items-center gap-2 font14  transition-all w-full leading-[140%] border border-InterfaceStrokeSoft ${
                          selected === option.key
                            ? 'bg-gray-900 text-white font-[500]'
                            : 'bg-white text-black font-[400]'
                        } ${index === 1 ? 'border-l border-gray-300' : ''}`}
                        >
                          {selected === option.key ? (
                            // <CheckCircle className="w-4 h-4" />
                            <i className="cloud-fillcircletick" />
                          ) : (
                            <i className="cloud-circletick" />
                          )}
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="py-[18px] xl:py-[20px] 3xl:py-[1.042vw]  border-y border-InterfaceStrokesoft  ">
                    <div className="text-InterfaceTexttitle font16 font-[500] leading-[140%] mb-[10px] 2xl:mb-[8px] xl:mb-[8px] 3xl:mb-[0.833vw] ">
                      Term
                    </div>
                    <div className="flex overflow-hidden w-full gap-[10px] xl:gap-[12px] 3xl:gap-[0.833vw]">
                      {optionsterm.map((option, index) => (
                        <button
                          key={option.key}
                          type="button"
                          onClick={() => setSelectedTerm(option.key)}
                          className={` py-[6px] px-[8px] xl:py-[6px] xl:px-[8px] 2xl:py-[8px] 
                        2xl:px-[8px] 3xl:py-[0.417vw] 3xl:px-[0.417vw] flex items-center gap-2 font14  transition-all w-full leading-[140%] border border-InterfaceStrokeSoft ${
                          selectedterm === option.key
                            ? 'bg-gray-900 text-white font-[500]'
                            : 'bg-white text-black font-[400]'
                        } ${index === 1 ? 'border-l border-gray-300' : ''}`}
                        >
                          {selectedterm === option.key ? (
                            // <CheckCircle className="w-4 h-4" />
                            <i className="cloud-fillcircletick" />
                          ) : (
                            <i className="cloud-circletick" />
                          )}
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="py-[18px] xl:py-[20px] 3xl:py-[1.042vw]  border-y border-InterfaceStrokesoft  ">
                    <div className="text-InterfaceTexttitle font16 font-[500] leading-[140%] mb-[10px] 2xl:mb-[8px] xl:mb-[8px] 3xl:mb-[0.833vw] ">
                      Bill Type
                    </div>
                    <div className="flex overflow-hidden w-full gap-[10px] xl:gap-[12px] 3xl:gap-[0.833vw]">
                      {optionstype.map((option, index) => (
                        <button
                          key={option.key}
                          type="button"
                          onClick={() => setSelectedType(option.key)}
                          className={` py-[6px] px-[8px] xl:py-[6px] xl:px-[8px] 2xl:py-[8px] 
                        2xl:px-[8px] 3xl:py-[0.417vw] 3xl:px-[0.417vw] flex items-center gap-2 font14  transition-all w-full leading-[140%] border border-InterfaceStrokeSoft ${
                          selectedtype === option.key
                            ? 'bg-gray-900 text-white font-[500]'
                            : 'bg-white text-black font-[400]'
                        } ${index === 1 ? 'border-l border-gray-300' : ''}`}
                        >
                          {selectedtype === option.key ? (
                            // <CheckCircle className="w-4 h-4" />
                            <i className="cloud-fillcircletick" />
                          ) : (
                            <i className="cloud-circletick" />
                          )}
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <SheetFooter className="pr-5 py-4 border-t border-InterfaceStrokesoft bg-white absolute bottom-0 w-full">
          <SheetClose asChild>
            <Button
              type="button"
              className="cancelbtn py-[8px] xl:py-[8px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[14px] xl:px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] flex items-center cursor-pointer rounded-none text-InterfaceTextdefault text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%]"
              variant="outline"
              size="sm"
              // onClick={handleClose}
            >
              <i className="cloud-closecircle text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]"></i>
              Cancel
            </Button>
          </SheetClose>
          <Button
            type="submit"
            className="newGreenBtn text-interfacesurfacecomponent rounded-none !border-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw]"
          >
            <i className="cloud-save-2"></i>
            Submit
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
