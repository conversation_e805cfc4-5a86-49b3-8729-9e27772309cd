'use client';
import React from 'react';
import { <PERSON><PERSON> } from 'next/font/google';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

function JoinUs() {
  return (
    <>
      <div className={`${roboto.className}`}>
        <div className="join-us-bg w-full py-[90px] xl:py-[120px]">
          <div className="text-center ">
            <div className=" text-[30px] font-semibold text-[#fff]">
              Are you ready to join us in the Cloud?
            </div>
            <div className="text-[#fff] text-[15px] 2xl:text-[16px] 3xl:text-[17px] font-normal my-[12px]">
              8,000+ global customers found a better way to work. Your turn.
            </div>
            <div className="flex justify-center mt-[37px] xl:mt-[41px] 3xl:mt-[2.135vw]">
              <Link
                href={`#`}
                className="border rounded-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] bg-BrandSupport1pure applybtn text-white"
              >
                Become a Partner
              </Link>
            </div>
          </div>
        </div>

        <div className="text-center pt-[80px] pb-[60px] px-[40px] md:px-[60px] lg:px-[90px] xl:px-[180px] 2xl:px-[260px] bg-[#F7F8F6]">
          <div className="grid grid-cols-3 gap-[20px] lg:gap-[30px] 2xl:gap-[35px] 3xl:gap-[40px]">
            <div className="py-[25px] 2xl:py-[30px] px-[20px] xl:px-[25px] 2xl:px-[30px] rounded-[3px] shadow shadow-[#d4d4d4] bg-[#fff]">
              <div>
                <i className=" cloud-location text-[38px] xl:text-[38px] text-[#4FB155] cursor-pointer"></i>
                <div className="text-[15px] 2xl:text-[16px] font-normal text-[#3C4146] pt-[13px]">
                  Redington Value, H Hotel, Business Tower - 1, Sheikh Zayed Road, Dubai, UAE
                </div>
              </div>
            </div>
            <div className="py-[25px] 2xl:py-[30px] px-[20px] xl:px-[25px] 2xl:px-[30px] rounded-[3px] shadow shadow-[#d4d4d4] bg-[#fff]">
              <div>
                <i className=" cloud-sms text-[38px] xl:text-[38px] text-[#4FB155] cursor-pointer"></i>
                <div className="text-[15px] 2xl:text-[16px] font-normal text-[#3C4146] pt-[13px]">
                  <EMAIL>
                </div>
              </div>
            </div>
            <div className="py-[25px] 2xl:py-[30px] px-[20px] xl:px-[25px] 2xl:px-[30px] rounded-[3px] shadow shadow-[#d4d4d4] bg-[#fff]">
              <div>
                <i className=" cloud-calling text-[38px] xl:text-[38px] text-[#4FB155] cursor-pointer"></i>
                <div className="text-[15px] 2xl:text-[16px] font-normal text-[#3C4146] pt-[13px]">
                  +971-45161500
                </div>
              </div>
            </div>
          </div>
          <div className="text-[14px] xl:text-[15px] 2xl:text-[15px] text-center text-[#3C4146] pt-[22px]">
            ©2025 Redington Group | Designed and Developed by Hexalytics | All Rights Reserved.
          </div>
        </div>
      </div>
    </>
  );
}

export default JoinUs;
