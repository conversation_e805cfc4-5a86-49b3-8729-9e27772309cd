'use client';
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { Roboto } from 'next/font/google';
import { SignUpPopup } from '@/modules/signin/components/signup-popup';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

function PortalBanner() {
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);

    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className={roboto.className}>
      <div className="relative bannervideo">
        <video autoPlay loop muted className="w-full h-auto">
          <source src="/images/video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div className="absolute top-[10px] xl:top-[10px] 2xl:top-[10px] 3xl:top-[0.833vw] z-50  left-[0] right-[0]">
          <div className=" custom_header  mx-[140px] lg:mx-[150px]  xl:mx-[200px] 2xl:mx-[280px]  3xl:mx-[16.417vw]">
            <div className="grid grid-cols-12 lg:grid-cols-12 xl:grid-cols-12 2xl:grid-cols-12 px-[8px] xl:px-[16px] 2xl:px-[16px] 3xl:px-[0.933vw] py-[5px] xl:py-[0px] 2xl:py-[8px] 3xl:py-[0.417vw] items-center">
              <div className="col-span-6 lg:col-span-7 xl:col-span-7 3xl:col-span-8">
                <div className="grid grid-cols-12 items-center xl:gap-[50px]">
                  <div className="col-span-4 lg:col-span-4 xl:col-span-4">
                    <div>
                      <Image
                        src={'/images/Cloudquarkslogo.svg'}
                        width={220}
                        height={55}
                        alt="Logo"
                        className="w-[90px] h-[25px] lg:w-[130px] lg:h-[40px] xl:w-[230px] xl:h-[55px]  3xl:w-[11.979vw] 3xl:h-[2.865vw] "
                      />
                    </div>
                  </div>
                  <div className="col-span-8 lg:col-span-8  xl:col-span-8 ">
                    <ul className="flex gap-[16px] xl:gap-[24px] 2xl:gap-[24px] 3xl:gap-[1.25vw] list-none text-InterfaceTextdefault text-[10px] lg:text-[15px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[1.042vw] font-[500]">
                      <li>
                        <Link href="">Home</Link>{' '}
                      </li>
                      <li>
                        <div
                          className="cursor-pointer"
                          onClick={() => scrollToSection('oursolutions')}
                        >
                          Our Solutions
                        </div>
                      </li>
                      <li>
                        <div
                          className="cursor-pointer"
                          onClick={() => scrollToSection('howitworks')}
                        >
                          How it Works
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-span-6 lg:col-span-5 xl:col-span-5 3xl:col-span-4">
                <div className="flex justify-end gap-[10px] xl:gap-[16px] items-center">
                  <div className="col-span-4">
                    <div>
                      <Link
                        href="/login"
                        className="signinbtn text-[16px] lg:text-[14px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[0.938vw]  text-InterfaceTextdefault  rounded-none w-full leading-[100%] border border-InterfaceStrokesoft px-[16px] md:px-[14px] xl:px-[16px] 3xl:px-[0.833vw] py-[7px] md:py-[7px] xl:py-[10px] 3xl:py-[0.625vw] flex items-center gap-2 cursor-pointer"
                      >
                        Sign In
                      </Link>
                    </div>
                  </div>
                  <div className="col-span-4">
                    <div>
                      <SignUpPopup />
                    </div>
                  </div>
                  <div className="col-span-4">
                    <Select defaultValue="india">
                      <SelectTrigger className="bg-transparent text-InterfaceTextdefault placeholder-text-sm  rounded-none border-BrandNeutral text-[16px] lg:text-[14px] xl:text-[16px] 2xl:text-[18px] 3xl:text-[1.042vw] [&>svg]:text-[#3C4146]">
                        <i className="cloud-world mr-[5px]"></i>
                        <SelectValue placeholder="Country" className="" />
                      </SelectTrigger>
                      <SelectContent className="rounded-none bg-overlaybg border-none">
                        <SelectGroup className="text-interfacetextinverse  text-[11px] xl:text-[14px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[400]">
                          <SelectItem value="india">India</SelectItem>
                          <SelectItem value="mea">MEA</SelectItem>
                          <SelectItem value="tukry">Turky</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-[90px] xl:mt-[160px] 3xl:mt-[8.854vw] flex justify-center relative items-center gap-[10px] lg:gap-[15px] xl:gap-[18px] 3xl:gap-[1.042vw]">
            <div>
              <Image src={'/images/tagline-left.png'} width={185} height={2} alt="Logo" />
            </div>
            <div
              className="border border-BrandSupport2300 text-BrandSupport2300 px-[18px] md:px-[18px] xl:px-[18px] 3xl:px-[0.938vw] py-[8px] md:py-[8px] xl:py-[8px]
          3xl:py-[0.417vw] flex justify-center gap-[18px] items-center"
            >
              <i className="cloud-handstars text-[22px]"></i>
              <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.938vw] italic leading-[100%]">
                Let’s grow together – become a partner
              </div>
            </div>
            <div>
              <Image src={'/images/tagline-right.png'} width={185} height={2} alt="Logo" />
            </div>
          </div>
          <div className="flex justify-center mt-[30px] xl:mt-[35px] 3xl:mt-[1.823vw]">
            <h1 className="text-center text-[30px] md:text-[55px] xl:text-[55px] 3xl:text-[3.542vw] leading-[120%] font-[700] text-InterfaceTextwhite">
              Accelerate your business <br />
              with <span className="textbg relative z-50 pl-[10px] pr-[20px]">CloudQuarks</span>
            </h1>
          </div>
          <div className="flex justify-center mt-[10px] xl:mt-[18px] 3xl:mt-[0.938vw]">
            <p className="text-center text-[18px] md:text-[18px] xl:text-[18px] 3xl:text-[0.938vw] leading-[140%] font-[300] text-interfacetextinverse">
              Join a community of more than 36,000+ end customers and tap into a <br />
              comprehensive selection of solutions, services and resources to transform <br />{' '}
              business growth today.
            </p>
          </div>
          <div className="flex justify-center mt-[10px] xl:mt-[18px] 3xl:mt-[0.938vw]">
            <Button
              variant="skybluegradient"
              className="border applybtn text-white 3xl:text-[1.042vw]"
            >
              Become a Partner
            </Button>
          </div>
          <div className="flex justify-center mt-[10px] xl:mt-[18px] 3xl:mt-[0.938vw]">
            <div className="flex -space-x-3">
              <Avatar>
                <AvatarImage src="/images/image1.png" alt="image1" />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
              <Avatar>
                <AvatarImage src="/images/image2.png" alt="image2" />
                <AvatarFallback>VC</AvatarFallback>
              </Avatar>
              <Avatar>
                <AvatarImage src="/images/image3.png" alt="image3" />
                <AvatarFallback>NJ</AvatarFallback>
              </Avatar>
              <Avatar>
                <AvatarImage src="/images/image4.png" alt="image4" />
                <AvatarFallback>NJ</AvatarFallback>
              </Avatar>
              <Avatar>
                <AvatarImage src="/images/image5.png" alt="image5" />
                <AvatarFallback>NJ</AvatarFallback>
              </Avatar>
            </div>
          </div>
          <div className="flex justify-center mt-[10px] xl:mt-[12px] 3xl:mt-[0.625vw]">
            <p className="text-InterfaceTextlighter text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.625vw] font-normal leading-[140%] text-center">
              5,000+ Partners already <br /> registered with us
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PortalBanner;
