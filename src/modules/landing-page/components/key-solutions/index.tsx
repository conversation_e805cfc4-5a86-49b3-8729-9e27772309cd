'use client';
import React from 'react';
import Image from 'next/image';
import { <PERSON><PERSON> } from 'next/font/google';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

function KeySolutions() {
  return (
    <>
      <section className={`${roboto.className}`}>
        <div className="bg-[#F7F8F6] px-[50px] lg:px-[100px] xl:px-[150px] 2xl:px-[190px] 3xl:px-[16.146vw] py-[80px] 3xl:py-[4.167vw]">
          <div className="text-center">
            <div className="inline-block border border-[#4FB155] text-[#4FB155] text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] lg:py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] mb-[16px] 3xl:mb-[0.833vw]">
              Our Key Solutions
            </div>
            <h1 className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.563vw] font-bold leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
              Less complexity. More cloud powered capability.
            </h1>
            <p className="text-InterfaceTextdefault text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] font-normal leading-[140%]">
              Take advantage of all that we offer at CloudQuarks. Partner with us and gain access to{' '}
              <br />
              the wealth of knowledge and expertise needed to advance your business.
            </p>
          </div>

          <div className="mt-[38px] lg:mt-[40px] xl:mt-[42px] 2xl:mt-[45px] 3xl:mt-[2.5vw]">
            <div className="grid grid-cols-12 gap-[38px] lg:gap-[40px] xl:gap-[42px] 2xl:gap-[45px] 3xl:gap-[2.5vw]">
              <div className="col-span-5 framShadow">
                <Image
                  src={'/images/complexity.png'}
                  className="object-left h-full pt-[32px] 2xl:pt-[32px] 3xl:pt-[1.823vw] pl-[38px] 2xl:pl-[38px] 3xl:pl-[2.135vw] w-full"
                  height={564}
                  width={438}
                  alt=""
                />
              </div>
              <div className="col-span-7">
                <div className="grid grid-cols-2 gap-4">
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Vendor Management
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Subscription Management
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Credit Management
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Invoice Management
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Customer Management
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Security and Compliance
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Interactive Catalog
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Partner Mini Shop
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Seamless Order Management
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                  <div className="py-[13px] 3xl:py-[0.833vw] px-[18px] 3xl:px-[1.042vw] bg-background cardShadow2">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%] pb-[10px] x:pb-[6px] 3xl:pb-[0.521vw]">
                      Reports & Decision Boards
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-center mt-[37px] xl:mt-[41px] 3xl:mt-[2.135vw]">
            <Link
              href={`#`}
              className="border rounded-none lg:px-[13px] xl:px-[13px] 2xl:px-[15px] 3xl:px-[0.781vw] lg:py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] bg-BrandSupport1pure applybtn text-white"
            >
              Become a Partner
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}

export default KeySolutions;
