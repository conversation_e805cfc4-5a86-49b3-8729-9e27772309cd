'use client';
import React from 'react';
import { Inter, <PERSON>o } from 'next/font/google';
import Image from 'next/image';
import Link from 'next/link';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

const inter = Inter({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

function Transformation() {
  return (
    <>
      <section className={`${roboto.className}`}>
        <div className="bg-[#F7F8F6] px-[50px] lg:px-[100px] xl:px-[150px] 2xl:px-[190px] 3xl:px-[16.146vw] py-[80px] 3xl:py-[4.167vw]">
          <div className="grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-12 1366xl:grid-cols-12 2xl:grid-cols-12 3xl:grid-cols-12 gap-[25px] lg:gap-[28px] xl:gap-[32px] 2xl:gap-[36px] 3xl:gap-[1.875vw]">
            <div className="col-span-4">
              <div className="space-y-[25px] lg:gap-[28px] xl:gap-[30px] 2xl:gap-[30px] 3xl:space-y-[1.667vw]">
                <h1 className="text-InterfaceTexttitle text-[25px] lg:text-[25px] xl:text-[28px] 2xl:text-[30px] 3xl:text-[1.563vw] font-bold leading-[140%]">
                  <p>Transformation.</p>
                  <p>{`It's`} not just a buzz word. </p>
                  <p>{`It's`} what we do.</p>
                </h1>
                <div className="space-y-[16px] xl:space-y-[0.833vw]">
                  <p className="text-InterfaceTextdefault text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-normal leading-[140%]">
                    In {`today's`} digital landscape, transformation means more than moving to the
                    {` cloud—it's`} about reimagining how businesses operate, scale, and innovate.
                  </p>
                  <p className="text-InterfaceTextdefault text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-normal leading-[140%]">
                    As your trusted cloud products partner, we {`don't `} just provide solutions—we
                    drive outcomes. From seamless migrations to optimized cloud infrastructure and
                    cutting-edge services, {`we're`} with you every step of the way.
                  </p>
                  <p className="text-InterfaceTextdefault text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] font-normal leading-[140%]">
                    Because real transformation {`isn't`} just about technology {`—it's`} about
                    unlocking your full potential.
                  </p>
                </div>
                <div className="">
                  <Link
                    href={`#`}
                    className="border rounded-none lg:px-[15px] xl:px-[15px] 2xl:px-[17px] 3xl:px-[0.791vw] lg:py-[10px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.425vw] bg-BrandSupport1pure applybtn text-white"
                  >
                    Become a Partner
                  </Link>
                </div>
              </div>
            </div>
            <div className="col-span-4">
              <div
                className={`${inter.className} h-[45%] xl:h-[43%] 1366xl:h-[42%] 2xl:h-[43%] 3xl:h-[43%] cardShadow p-[22px] lg:p-[22px] xl:p-[22px] 2xl:p-[24px] 3xl:p-[1.25vw] text-center space-y-[15px] 3xl:space-y-[0.781vw] mt-[70px] lg:mt-[75px] xl:mt-[80px] 2xl:mt-[85px] 3xl:mt-[4.427vw] bg-[#fff]`}
              >
                {/* <i className="cloud-handshake text-[#4FB155] text-[68px] lg:text-[70px] xl:text-[70px] 2xl:text-[72px] 3xl:text-[3.75vw]"></i> */}

                <Image
                  src={'/images/relationships_icon.svg'}
                  className=" mx-auto w-[16px] lg:w-[85px] xl:w-[85px] 2xl:w-[85px] 3xl:w-[4.427vw] h-[80px] lg:h-[80px] xl:h-[80px] 2xl:h-[80px] 3xl:h-[4.167vw]"
                  height={80}
                  width={85}
                  alt=""
                />
                <h3 className="text-InterfaceTexttitle text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-semibold leading-normal">
                  Transform your relationships
                </h3>
                <p
                  className={`${inter.className} text-[#3C4146] text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[300] leading-[125%]`}
                >
                  Catapult your business. Take advantage <br /> of the longstanding relationships{' '}
                  {`we've`}
                  <br />
                  developed across the channel. Make <br /> meaningful connections that are aligned{' '}
                  <br /> to your business strategy.
                </p>
              </div>

              <div
                className={`${inter.className} h-[45%] xl:h-[43%] 1366xl:h-[42%] 2xl:h-[43%] 3xl:h-[43%] cardShadow p-[22px] lg:p-[22px] xl:p-[22px] 2xl:p-[24px] 3xl:p-[1.25vw] text-center space-y-[15px] 3xl:space-y-[0.781vw] mt-[26px] lg:mt-[28px] xl:mt-[30px] 2xl:mt-[32px] 3xl:mt-[1.667vw] bg-[#fff]`}
              >
                {/* <i className="cloud-gear text-lightGreenColor text-[68px] lg:text-[70px] xl:text-[70px] 2xl:text-[72px] 3xl:text-[3.75vw]"></i> */}
                <Image
                  src={'/images/perfomace_icon.svg'}
                  className=" mx-auto w-[16px] lg:w-[85px] xl:w-[85px] 2xl:w-[85px] 3xl:w-[4.427vw] h-[80px] lg:h-[80px] xl:h-[80px] 2xl:h-[80px] 3xl:h-[4.167vw]"
                  height={80}
                  width={85}
                  alt=""
                />
                <h3 className="text-InterfaceTexttitle text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-semibold leading-normal">
                  Transform your performance
                </h3>
                <p
                  className={`${inter.className} text-[#3C4146] text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[300] leading-[125%]`}
                >
                  Drive optimization and efficiencies. Elevate your performance with digital
                  platforms, high-velocity teams, and a multitude of other capabilities.
                </p>
              </div>
            </div>
            <div className="col-span-4">
              <div
                className={`${inter.className} h-[45%] xl:h-[43%] 1366xl:h-[42%] 2xl:h-[43%] 3xl:h-[43%] cardShadow p-[22px] lg:p-[22px] xl:p-[22px] 2xl:p-[24px] 3xl:p-[1.25vw] text-center space-y-[15px] 3xl:space-y-[0.781vw] mt-[70px] lg:mt-[75px] xl:mt-[80px] 2xl:mt-[85px] 3xl:mt-[4.427vw] bg-[#fff]`}
              >
                {/* <i className="cloud-financialprofit text-lightGreenColor text-[68px] lg:text-[70px] xl:text-[70px] 2xl:text-[72px] 3xl:text-[3.75vw]"></i> */}
                <Image
                  src={'/images/business_icon.svg'}
                  className=" mx-auto w-[16px] lg:w-[85px] xl:w-[85px] 2xl:w-[85px] 3xl:w-[4.427vw] h-[80px] lg:h-[80px] xl:h-[80px] 2xl:h-[80px] 3xl:h-[4.167vw]"
                  height={80}
                  width={85}
                  alt=""
                />
                <h3 className="text-InterfaceTexttitle text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-semibold leading-normal">
                  Transform your business
                </h3>
                <p
                  className={`${inter.className} text-[#3C4146] text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[300] leading-[125%]`}
                >
                  Expand your horizons. Tap into new and adjacent markets. Reimagine what’s possible
                  with managed services, technical pre and post sales expertise, financing and more.
                </p>
              </div>

              <div
                className={`${inter.className} h-[45%] xl:h-[43%] 1366xl:h-[42%] 2xl:h-[43%] 3xl:h-[43%] cardShadow p-[22px] lg:p-[22px] xl:p-[22px] 2xl:p-[24px] 3xl:p-[1.25vw] text-center space-y-[15px] 3xl:space-y-[0.781vw] mt-[26px] lg:mt-[28px] xl:mt-[30px] 2xl:mt-[32px] 3xl:mt-[1.667vw] bg-[#fff]`}
              >
                {/* <i className="cloud-mind text-lightGreenColor text-[68px] lg:text-[70px] xl:text-[70px] 2xl:text-[72px] 3xl:text-[3.75vw]"></i> */}
                <Image
                  src={'/images/market_icon.svg'}
                  className=" mx-auto w-[16px] lg:w-[85px] xl:w-[85px] 2xl:w-[85px] 3xl:w-[4.427vw] h-[80px] lg:h-[80px] xl:h-[80px] 2xl:h-[80px] 3xl:h-[4.167vw]"
                  height={80}
                  width={85}
                  alt=""
                />
                <h3 className="text-InterfaceTexttitle text-[18px] lg:text-[18px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-semibold leading-normal">
                  Transform your Go-to-Market
                </h3>
                <p
                  className={`${inter.className} text-[#3C4146] text-[14px] lg:text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[300] leading-[125%]`}
                >
                  Open new doors. Capture new business. Connect the optimal multivendor solutions
                  for your customers and identify gaps where they need more or different solutions
                  and services.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

export default Transformation;
