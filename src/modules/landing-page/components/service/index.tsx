'use client';
import React from 'react';
import Image from 'next/image';
import { <PERSON>o } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

function Service() {
  return (
    <>
      <section className={`${roboto.className}`}>
        <div className="bg-interfacesurfacecomponent px-[50px] lg:px-[100px] xl:px-[150px] 2xl:px-[190px] 3xl:px-[16.146vw] py-[80px] 3xl:py-[4.167vw]">
          <div className="text-center">
            <div className="inline-block border border-[#4FB155] text-[#4FB155] text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] lg:py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] mb-[16px] 3xl:mb-[0.833vw]">
              See How CloudQuarks Works for you
            </div>
            <h1 className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.563vw] font-bold leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
              Efficient, scalable Cloud services and better, faster partner service
            </h1>
            <p className="text-InterfaceTextdefault text-[16px] lg:text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.938vw] font-normal leading-[140%]">
              At CloudQuarks, {`we're`} on the leading edge of {`today's`} innovations and enabling
              you to <br /> work faster, smarter and more collaboratively to solve customer
              challenges.
            </p>
          </div>

          <div className="mt-[38px] lg:mt-[40px] xl:mt-[42px] 2xl:mt-[45px] 3xl:mt-[2.5vw] relative">
            <div className="framShadow p-[20px] 3xl:p-[1.042vw] rounded-[16px] 3xl:rounded-[0.833vw]">
              <Image
                src={'/images/EndCustomers.png'}
                className="object-cover w-full h-[500px] "
                height={500}
                width={1030}
                alt=""
              />
            </div>
            <div className="absolute inset-0 flex items-center justify-center cursor-pointer">
              <Image
                src={'/images/play-button1.svg'}
                height={220}
                width={220}
                alt="Play Button"
                className=" transition-all duration-700 hover:w-[220px] hover:h-[210px] xl:hover:w-[210px] xl:hover:h-[230px] w-[180px] xl:w-[180px] h-[200px] xl:h-[220px]"
              />
            </div>
          </div>
          <div className="mt-[50px] xl:mt-[50px] 3xl:mt-[2.604vw] mb-[20px] xl:mb-[20px] 3xl:mb-[1.042vw]">
            <div className="text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[16px] text-[#000] text-center">
              Trusted by 100+ Vendors worldwide
            </div>
          </div>
          <div className="grid grid-cols-9   place-items-center">
            <div>
              <img alt="" className="" src="/images/microsoft.png" />
            </div>
            <div>
              <img alt="" className=" " src="/images/amazon2.png" />
            </div>
            <div>
              <img alt="" src="/images/Acepoint.png" />
            </div>
            <div>
              <img alt="" className="pl-[40px] " src="/images/acronis2.png" />
            </div>

            <div>
              <img alt="" className="pl-[30px]" src="/images/dropbox1.png" />
            </div>
            <div>
              <img alt="" className="pl-[30px]" src="/images/adobe.png" />
            </div>
            <div>
              <img alt="" src="/images/google.png" />
            </div>
            <div>
              <img alt="" src="/images/cisco.png" />
            </div>
            <div>
              <img alt="" className="h-[60px] 2xl:h-[70px]" src="/images/ibm.png" />
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

export default Service;
