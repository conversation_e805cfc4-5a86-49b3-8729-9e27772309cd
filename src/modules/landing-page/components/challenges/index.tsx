'use client';
import React from 'react';
import Image from 'next/image';
import { <PERSON>o } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
});

function Challenges() {
  return (
    <>
      <section className={`${roboto.className}`}>
        <div className=" bg-interfacesurfacecomponent px-[50px] lg:px-[100px] xl:px-[150px] 2xl:px-[190px] 3xl:px-[16.146vw] py-[80px] 3xl:py-[4.167vw]">
          <div className="text-center">
            <div className="inline-block border border-[#4FB155] text-[#4FB155] text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%] px-[12px] lg:px-[12px] xl:px-[12px] 2xl:px-[12px] 3xl:px-[0.625vw] py-[6px] lg:py-[6px] xl:py-[6px] 2xl:py-[6px] 3xl:py-[0.313vw] mb-[16px] 3xl:mb-[0.833vw]">
              Major Problems
            </div>
            <h1 className="text-InterfaceTexttitle text-[24px] lg:text-[24px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.563vw] font-[500] leading-[140%] mb-[8px] 3xl:mb-[0.417vw]">
              {' '}
              Key challenges faced by the partners{' '}
            </h1>
            <p className="text-InterfaceTextdefault text-[16px] lg:text-[16px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.930vw] font-normal leading-[140%]">
              The challenges faced by the B2B business owners are quite different <br /> from the
              ones faced by directly dealing with the customers.
            </p>
          </div>

          <div className="mt-[38px] lg:mt-[40px] xl:mt-[42px] 2xl:mt-[45px] 3xl:mt-[2.5vw]">
            <div className="grid grid-cols-2 gap-[38px] lg:gap-[40px] xl:gap-[42px] 2xl:gap-[45px] 3xl:gap-[2.5vw]">
              <div className="framShadow">
                <Image
                  src={'/images/stressed_businessman.png'}
                  className="object-cover w-full pt-[35px] 3xl:pt-[1.823vw] pl-[41px] 3xl:pl-[2.135vw]"
                  height={570}
                  width={80}
                  alt=""
                />
              </div>
              <div className="space-y-[18px] 2xl:space-y-[20px] 3xl:space-y-[1.042vw] flex flex-col items-center justify-center">
                <div className="grid grid-cols-7 gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="col-span-1 flex justify-center">
                    <div className="bg-[#00953A] h-[50px] lg:h-[50px] xl:h-[50px] 2xl:h-[54px] 3xl:h-[2.917vw] w-[50px] lg:w-[50px] xl:w-[50px] 2xl:w-[54px] 3xl:w-[2.917vw] rounded-full flex justify-center items-center">
                      <i className="cloud-puzzle  text-InterfaceTextwhite text-[32px] 3xl:text-[1.667vw]"></i>
                    </div>
                  </div>
                  <div className="col-span-6">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%]">
                      Complex Purchase Path
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Selling to a customer follows an easy path wherein a customer browses through
                      the products.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-7 gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="col-span-1 flex justify-center">
                    <div className="bg-[#00953A] h-[50px] lg:h-[50px] xl:h-[50px] 2xl:h-[54px] 3xl:h-[2.917vw] w-[50px] lg:w-[50px] xl:w-[50px] 2xl:w-[54px] 3xl:w-[2.917vw] rounded-full flex justify-center items-center">
                      <i className="cloud-brokenshield  text-InterfaceTextwhite text-[32px] 3xl:text-[1.667vw]"></i>
                    </div>
                  </div>
                  <div className="col-span-6">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%]">
                      Failed Managing Client Relationships
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Another significant problem faced by B2B platforms is to maintain and manage
                      client relationships.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-7 gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="col-span-1 flex justify-center">
                    <div className="bg-[#00953A] h-[50px] lg:h-[50px] xl:h-[50px] 2xl:h-[54px] 3xl:h-[2.917vw] w-[50px] lg:w-[50px] xl:w-[50px] 2xl:w-[54px] 3xl:w-[2.917vw] rounded-full flex justify-center items-center">
                      <i className="cloud-pricing  text-InterfaceTextwhite text-[32px] 3xl:text-[1.667vw]"></i>
                    </div>
                  </div>
                  <div className="col-span-6">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%]">
                      Higher Product Value & Varying Prices
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      The product value of B2B orders is usually significantly more than that of B2C
                      ecommerce platforms.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-7 gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="col-span-1 flex justify-center">
                    <div className="bg-[#00953A] h-[50px] lg:h-[50px] xl:h-[50px] 2xl:h-[54px] 3xl:h-[2.917vw] w-[50px] lg:w-[50px] xl:w-[50px] 2xl:w-[54px] 3xl:w-[2.917vw] rounded-full flex justify-center items-center">
                      <i className="cloud-technology  text-InterfaceTextwhite text-[32px] 3xl:text-[1.667vw]"></i>
                    </div>
                  </div>
                  <div className="col-span-6">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%]">
                      Technological Development
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      The ecommerce marketplaces are not equally integrated for the buyers and
                      suppliers, the user experience is much low as compared to B2C businesses
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-7 gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="col-span-1 flex justify-center">
                    <div className="bg-[#00953A] h-[50px] lg:h-[50px] xl:h-[50px] 2xl:h-[54px] 3xl:h-[2.917vw] w-[50px] lg:w-[50px] xl:w-[50px] 2xl:w-[54px] 3xl:w-[2.917vw] rounded-full flex justify-center items-center">
                      <i className="cloud-searchbar  text-InterfaceTextwhite text-[32px] 3xl:text-[1.667vw]"></i>
                    </div>
                  </div>
                  <div className="col-span-6">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%]">
                      Delivery of large-scale products
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      Unlike B2C ecommerce companies, the B2B platforms have to deal with the
                      challenge of handling and delivering large-scale products quickly and
                      efficiently.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-7 gap-[16px] 3xl:gap-[0.833vw]">
                  <div className="col-span-1 flex justify-center">
                    <div className="bg-[#00953A] h-[50px] lg:h-[50px] xl:h-[50px] 2xl:h-[54px] 3xl:h-[2.917vw] w-[50px] lg:w-[50px] xl:w-[50px] 2xl:w-[54px] 3xl:w-[2.917vw] rounded-full flex justify-center items-center">
                      <i className="cloud-portfolio  text-InterfaceTextwhite text-[32px] 3xl:text-[1.667vw]"></i>
                    </div>
                  </div>
                  <div className="col-span-6">
                    <h3 className="text-InterfaceTexttitle text-[17px] lg:text-[17px] xl:text-[17px] 2xl:text-[17px] 3xl:text-[0.938vw] font-bold leading-[140%]">
                      Providing a User-Friendly Experience
                    </h3>
                    <p className="text-InterfaceTextdefault text-[13px] lg:text-[13px] xl:text-[13px] 2xl:text-[13px] 3xl:text-[0.729vw] font-normal leading-[140%]">
                      A B2B ecommerce platform should be providing a self-service option as well as
                      a self-service system for its merchants.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

export default Challenges;
