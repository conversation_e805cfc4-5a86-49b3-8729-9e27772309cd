'use client';
import * as React from 'react';
import { Search } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/datatable';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Input,
  Badge,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import ApplyFilterPopup from '../component/apply-filter-popup';
import CouponViewPopup from '../component/coupon-view-popup';
import AddNewCartPopup from '../component/add-new-cart-popup';
import { Button } from '@redington-gulf-fze/cloudquarks-component-library';
import { TablePagination } from '@/components/common/pagination';
import Piechart from '@/components/common/charts/piechart';
import AppliedFilter from '@/components/common/filter-chip/index';
import { useTranslations } from 'next-intl';

export default function DiscountCouponsTemplate() {
  const t = useTranslations();

  const statusStyles: Record<string, string> = {
    Active: 'text-BrandPrimarypurenew border border-BrandPrimary300new bg-BrandPrimary50new ',
    Expired: 'text-BrandPrimarypurenew border border-BrandPrimary300new bg-BrandPrimary50new ',
    Redeemed: 'text-BrandSupport1pure border border-BrandSupport1300 bg-BrandSupport150 ',
  };
  const notificationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'couponid',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('couponId')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'couponcode',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-end font-medium justify-between"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('couponCode')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'datecreated',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('dateCreated')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'validtill',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('validTill')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'products',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('noOfProducts')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'usagetype',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('usageType')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'redeemedcount',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('redeemedCount')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-1 text-left  font-medium justify-between "
              onClick={() => column.toggleSorting(isSorted === 'asc')}
            >
              {t('status')}

              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px] text-[#605f5f]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px] text-[#605f5f]" />
              )}
            </button>
          </div>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const style = statusStyles[status] || 'bg-muted text-muted-foreground';
        return (
          <Badge
            className={`rounded-none py-1 px-2 text-[10px] xl:text-[12px] 3xl:text-[0.625vw] font-medium ${style}`}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <Popover>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className="p-0 mr-[35px] xl:mr-[35px] 3xl:mr-[1.863vw] rounded-none w-[160px] xl:w-[160px] 3xl:w-[8.213vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                <div className="">
                  <CouponViewPopup open={false} onClose={() => {}} />
                  <AddNewCartPopup open={false} onClose={() => {}} />
                  {/* <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center border-t w-full border-InterfaceStrokesoft hover:bg-BrandNeutral100">
                    <i className="cloud-closecircle text-InterfaceTextdefault text-[17px]  xl:text-[17px] 3xl:text-[0.829vw]"></i>
                    Cancel
                  </div> */}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 100,
      minSize: 100,
      maxSize: 100,
    },
  ];

  const data = [
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Active',
    },
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Expired',
    },
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Redeemed',
    },
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Active',
    },
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Expired',
    },
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Active',
    },
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Active',
    },
    {
      couponid: 'CID-1001',
      couponcode: 'QHSKW123',
      datecreated: '03/03/2025',
      validtill: '30/03/2025',
      products: '5',
      usagetype: 'Multiple',
      redeemedcount: '2',
      status: 'Active',
    },
  ];

  type User = {
    couponid: string;
    couponcode: string;
    datecreated: string;
    validtill: string;
    products: string;
    usagetype: string;
    redeemedcount: string;
  };
  const discountCouponFilterConfig = [
    { key: 'createdOnFrom', label: 'Created From', isDate: true },
    { key: 'createdOnTo', label: 'Created To', isDate: true },
    { key: 'validTillFrom', label: 'Valid Till From', isDate: true },
    { key: 'validTillTo', label: 'Valid Till To', isDate: true },
    { key: 'status', label: 'Status', isDate: false },
  ];

  return (
    <div className="px-[30px] xl:px-[30px] 2xl:px-[32px] py-[20px] xl:py-[20px] 3xl:py-[1.042vw] w-full">
      <div className="">
        <div className="text-[16px] xl:text-[18px] 3xl:text-[1.042vw] font-semibold text-InterfaceTexttitle">
          {t('discountCoupons')}
        </div>
        <p className="italic text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          *{t('asOnDate')}
        </p>
      </div>
      <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw] mt-[14px] xl:mt-[16px] 3xl:mt-[0.833vw]">
        <div className="grid grid-cols-12 md:grid-cols-12 lg:grid-cols-12 gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw]">
          <div className="col-span-12 md:col-span-6 lg:col-span-4 border bg-white border-InterfaceStrokesoft p-[20px] xl:p-[24px] 3xl:p-[1.25vw] rounded-[10px] xl:rounded-[12px] 3xl:rounded-[0.625vw] grid grid-cols-8  min-h-[140px] xl:min-h-[160px] 3xl:min-h-[10vw] ">
            <div className="col-span-3">
              <div className="mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
                <i className="cloud-discount-shap text-[26px] xl:text-[28px] 3xl:text-[1.667vw] text-[#7F8488] cloud-cards"></i>
              </div>
              <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium text-InterfaceTextdefault">
                {t('totalCoupons')}*
              </div>
              <div className="text-[25px] xl:text-[30px] 3xl:text-[1.875vw] font-semibold text-InterfaceTextdefault leading-[40px] xl:leading-[50px] 3xl:leading-[2.604vw] mt-1">
                175
              </div>
            </div>
            <div className="col-span-5">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 0, // distance from the right edge
                  top: '30%', // vertically centered
                  itemGap: 15,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['active', 'expired'].map((item) => t(item)),
                  textStyle: { fontSize: 10 },
                }}
                name={'Redeemed Coupons'}
                radius={['40%', '85%']}
                center={['40%', '60%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 10,
                  length2: 10,
                  lineStyle: {
                    width: 1,
                    color: '#000', // make the arrow black
                  },
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 90, name: t('active'), itemStyle: { color: '#EB8A44' } },
                  { value: 85, name: t('expired'), itemStyle: { color: '#F8CA16' } },
                ]}
              />
            </div>
          </div>
          <div className="col-span-12 md:col-span-6 lg:col-span-4 border bg-white border-InterfaceStrokesoft p-[14px] xl:p-[16px] 3xl:p-[0.833vw] rounded-[10px] xl:rounded-[12px] 3xl:rounded-[0.625vw] grid grid-cols-8 min-h-[140px] xl:min-h-[160px] 3xl:min-h-[10vw]">
            <div className="col-span-3">
              <div className="mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
                <i className="cloud-verify text-[26px] xl:text-[28px] 3xl:text-[1.667vw] text-[#7F8488] cloud-card1"></i>
              </div>
              <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium text-InterfaceTextdefault">
                {t('redeemedCoupons')}*
              </div>
              <div className="text-[25px] xl:text-[30px] 3xl:text-[1.875vw] font-semibold text-InterfaceTextdefault leading-[40px] xl:leading-[50px] 3xl:leading-[2.604vw] mt-1">
                75
              </div>
            </div>
            <div className="col-span-5">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 0,
                  top: '30%', // vertically centered
                  itemGap: 15,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['amazon', 'googleCloud', 'microsoftCSP'].map((item) => t(item)),
                  textStyle: { fontSize: 10 },
                }}
                name={'Redeemed Coupons'}
                radius={['40%', '85%']}
                center={['40%', '60%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 10,
                  length2: 10,
                  lineStyle: {
                    width: 1,
                    color: '#000', // make the arrow black
                  },
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 40, name: t('amazon'), itemStyle: { color: '#73609B' } },
                  { value: 35, name: t('googleCloud'), itemStyle: { color: '#4D9E99' } },
                  { value: 35, name: t('microsoftCSP'), itemStyle: { color: '#5BB559' } },
                ]}
              />
            </div>
          </div>
          <div className="col-span-12 md:col-span-6 lg:col-span-4 border bg-white border-InterfaceStrokesoft p-[14px] xl:p-[16px] 3xl:p-[0.833vw] rounded-[10px] xl:rounded-[12px] 3xl:rounded-[0.625vw] grid grid-cols-8 min-h-[140px] xl:min-h-[160px] 3xl:min-h-[10vw]">
            <div className="col-span-3">
              <div className="mb-[14px] xl:mb-[16px] 3xl:mb-[0.833vw]">
                <i className="cloud-ticketDiscount text-[26px] xl:text-[28px] 3xl:text-[1.667vw] text-[#7F8488] cloud-card-edit"></i>
              </div>
              <div className="text-[12px] xl:text-[14px] 3xl:text-[0.729vw] font-medium text-InterfaceTextdefault leading-tight">
                {t('unRedeemedCoupons')}*
              </div>
              <div className="text-[25px] xl:text-[30px] 3xl:text-[1.875vw] font-semibold text-InterfaceTextdefault leading-[40px] xl:leading-[50px] 3xl:leading-[2.604vw] mt-1">
                60
              </div>
            </div>
            <div className="col-span-5">
              <Piechart
                legends={{
                  orient: 'vertical', // vertical layout
                  right: 0, // distance from the right edge
                  top: '30%', // vertically centered
                  itemGap: 15,
                  itemHeight: 10,
                  itemWidth: 10,
                  data: ['amazon', 'googleCloud', 'microsoftCSP'].map((item) => t(item)),
                  textStyle: { fontSize: 10 },
                }}
                name={'Redeemed Coupons'}
                radius={['40%', '85%']}
                center={['40%', '60%']}
                rosetype={'area'}
                itemstyle={{
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 3,
                }}
                labelline={{
                  length: 10,
                  length2: 10,
                  lineStyle: {
                    width: 1,
                    color: '#000', // make the arrow black
                  },
                }}
                label={{
                  formatter: '{c}',
                }}
                data={[
                  { value: 35, name: t('amazon'), itemStyle: { color: '#2A4423' } },
                  { value: 30, name: t('googleCloud'), itemStyle: { color: '#5D9D4A' } },
                  { value: 30, name: t('microsoftCSP'), itemStyle: { color: '#ACD69F' } },
                ]}
              />
            </div>
          </div>
        </div>
        <div className="flex items-center gap-0.5 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
          <Button className="ticketcard gap-2 items-center flex w-fit rounded-none font-normal hover:bg-BrandNeutral100 hover:text-BrandSupport1pure">
            <i className="cloud-download font-medium text-[16px] xl:text-[18px] 3xl:text-[1.042vw] text-BrandSupport1pure"></i>{' '}
            {t('download')}
          </Button>
        </div>

        <div className="bg-interfacesurfacecomponent tbl-shadow">
          <div className="flex justify-between px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[12px] xl:py-[12px] 3xl:py-[0.625vw] border-b border-InterfaceStrokesoft">
            <div className="flex items-center gap-[10px] xl:gap-[12px] 3xl:gap-[0.625vw]">
              <div className="text-InterfaceTexttitle text-[18px] font-semibold">
                {t('listOfDiscountCoupons')}
              </div>
              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
                {t('showingRecords')}
              </div>
            </div>
            <div className="  flex gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw] items-center">
              <div className="relative w-[300px] xl:w-[418px] 3xl:w-[21.771vw] bg-white border border-InterfaceStrokedefault">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4 " />
                <Input
                  type="text"
                  placeholder="Search"
                  className="pl-[35px] xl:pl-[35px] 2xl:pl-[35px] 3xl:pl-[1.5vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] text-[14px] xl:text-[16px] 3xl:text-[0.833vw]"
                />
              </div>
              <ApplyFilterPopup moduleKey="discountCoupon" open={false} onClose={() => {}} />
            </div>
          </div>
          <div className="flex items-center justify-start px-[14px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] xl:py-[12px] 3xl:py-[0.625vw] gap-2 text-[12px] xl:text-[14px] 3xl:text-[0.729vw]">
            <div className="group text-InterfaceTextdefault hover:text-InterfaceTexttitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              <span className="text-InterfaceTextsubtitle">{t('fromDate')} :</span> 01-01-2025
              <i className="cloud-closecircle text-ClearAll group-hover:text-InterfaceTexttitle"></i>
            </div>
            <div className="group text-InterfaceTextdefault hover:text-InterfaceTexttitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              <span className="text-InterfaceTextsubtitle">{t('toDate')} :</span> 01-03-2025
              <i className="cloud-closecircle text-ClearAll group-hover:text-InterfaceTexttitle"></i>
            </div>
            <div className="group text-InterfaceTextdefault hover:text-InterfaceTexttitle bg-BrandNeutral100 py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              <span className="text-InterfaceTextsubtitle">{t('status')}:</span> Active
              <i className="cloud-closecircle text-ClearAll group-hover:text-InterfaceTexttitle"></i>
            </div>
            <div className="text-ClearAll py-1 px-[10px] xl:px-[12px] 3xl:px-[0.625vw] flex items-center gap-[8px] xl:gap-[8px] 3xl:gap-[0.417vw]">
              <i className="cloud-closecircle text-ClearAll"></i>
              {t('clearAll')}
            </div>
          </div>
          {/* <DataTable data={data} columns={notificationColumns} withCheckbox={false} />
          </div> */}
          <AppliedFilter moduleKey="discountCoupon" filterConfig={discountCouponFilterConfig} />
          <DataTable data={data} columns={notificationColumns} withCheckbox={false} />
          <TablePagination />
        </div>
      </div>
    </div>
  );
}
