'use client';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  She<PERSON>,
  SheetClose,
  <PERSON>etContent,
  <PERSON>etTrigger,
  SheetFooter,
  Input,
  Label,
  Textarea,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { TermsSheetProps } from '@/types';
import { Select2 } from '@/components/common/ui/combo-box';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
const cartSchema = z.object({
  cartTitle: z.string().nonempty('Cart Title is required'),
  endCustomer: z.string().nonempty('End Customer is required'),
  description: z
    .string()
    .optional()
    .refine((val) => !val || val.trim().split(/\s+/).length <= 50, {
      message: 'Description must not exceed 50 words',
    }),
});
export default function AddNewCartPopup({}: TermsSheetProps) {
  const t = useTranslations();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(cartSchema),
    defaultValues: {
      cartTitle: '',
      description: '',
      endCustomer: '',
    },
  });

  const onSubmit = () => {
    //console.log('Form data:', data);
    reset();
  };

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="hover:bg-BrandNeutral100 w-full">
        <div className=" gap-2 3xl:gap-[0.417vw] items-center flex rounded-none  px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextdefault hover:bg-BrandNeutral100 ">
          <i className="cloud-Add font-normal text-[20px] xl:text-[12px] 3xl:text-[0.625vw] text-InterfaceTextdefault"></i>{' '}
          {t('Create New Cart')}
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft p-[20px] lg-[20px] xl:p-[22px] 3xl:p-[1.25vw]">
          <SheetTitle className="flex justify-between">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[20px] xl:text-[22px] 2xl:text-[24px] 3xl:text-[1.25vw] text-[#19212A] font-[600] leading-[140%]">
              {t('createANewCartFromCoupon')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw]  overflow-auto h-[400px] xl:h-[450px] 2xl:h-[600px] 3xl:h-[32.25vw]">
            <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
              <div className="grid gap-1.5">
                <Label
                  htmlFor="cartTitle"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('cartTitle')} <span className="text-[#f12e2e]"> *</span>
                </Label>
                <Input
                  {...register('cartTitle')}
                  type="text"
                  id="companyname"
                  placeholder={t('enterCartTitle')}
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard"
                />
                {errors.cartTitle && (
                  <p className="text-[#f12e2e] text-[12px]">{errors.cartTitle.message}</p>
                )}
              </div>

              <div className="grid gap-1.5">
                <Label
                  htmlFor="description"
                  className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]"
                >
                  {t('description')}
                </Label>
                <Textarea
                  {...register('description')}
                  id="description"
                  placeholder=""
                  className="placeholder:text-InterfaceTextsubtitle text-blackcolor border border-InterfaceStrokehard rounded-none"
                />
                <div className="-mb-2 text-InterfaceTextsubtitle text-[10px] xl:text-[12px] 3xl:text-[0.625vw]">
                  {' '}
                  Max characters : 50 Words
                </div>
                {errors.description && (
                  <p className="text-[#f12e2e] text-[12px]">{errors.description.message}</p>
                )}
              </div>
              <div className="relative grid gap-1 ">
                <Label className="text-InterfaceTexttitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[500]">
                  {t('endCustomer')}
                </Label>
                <div className="">
                  <Controller
                    name="endCustomer"
                    control={control}
                    render={({ field }) => (
                      <Select2
                        options={[
                          { label: 'All', value: 'All' },
                          { label: 'India', value: 'India' },
                          { label: 'UAE', value: 'UAE' },
                        ]}
                        value={field.value ?? ''}
                        onChange={field.onChange}
                        placeholder="Select End Customer"
                      />
                    )}
                  />
                </div>
                {errors.endCustomer && (
                  <p className="text-[#f12e2e] text-[12px]">{errors.endCustomer.message}</p>
                )}
              </div>
            </div>
          </div>
          <SheetFooter className="absolute bottom-0 w-full right-0 border-t border-InterfaceStrokedefault">
            <div className="bg-interfacesurfacecomponent p-[20px] xl:p-[22px] 3xl:p-[1.25vw] w-full flex justify-end ">
              <div className="flex gap-[20px] xl:gap-[22px] 3xl:gap-[1.25vw]">
                <SheetClose asChild>
                  <Button
                    size="sm"
                    onClick={() => {
                      reset();
                    }}
                    className="flex gap-[8px] 3xl:gap-[0.417vw] cancelbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                  >
                    <div>
                      <i className="cloud-closecircle flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                    </div>
                    <div className="text-[#3C4146] text-[15px] xl:text-[16px] 3xl:text-[1.042vw] font-medium leading-[16px] flex items-center">
                      {t('cancel')}
                    </div>
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  size="sm"
                  className="applybtn flex gap-[8px] 3xl:gap-[0.417vw] loginbtn text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw] font-[500] leading-[100%] py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[16px] 3xl:px-[0.833vw] rounded-none"
                >
                  <div>
                    <i className="cloud-fillcircletick text-[#EEEEF0] flex items-center text-[16px] xl:text-[18px] 3xl:text-[1.042vw]"></i>
                  </div>
                  <div className=" text-white font-medium leading-[16px] flex items-center text-[15px] xl:text-[16px] 3xl:text-[1.042vw]">
                    {t('submit')}
                  </div>
                </Button>
              </div>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
