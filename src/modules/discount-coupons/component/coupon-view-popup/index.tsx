import React from 'react';
import { TermsSheetProps } from '@/types';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON>lose,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/common/ui/data-table';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { useTranslations } from 'next-intl';
export default function CouponViewPopup({}: TermsSheetProps) {
  const t = useTranslations();
  const discountColumns: ColumnDef<DiscountUser>[] = [
    {
      accessorKey: 'productname',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('productName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'sku',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('SKU')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'segment',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('segment')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'term',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('term')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'billtype',
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('billType')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'discount',
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('discount')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'minqty',
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('minQty')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
  ];
  const redemptionColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'productname',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('productName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'sku',
      enableSorting: true,
      size: 200,
      minSize: 200,
      maxSize: 200,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('SKU')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'dateredeemed',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('dateRedeemed')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endCustomer')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: 'order',
      size: 300,
      minSize: 300,
      maxSize: 300,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              onClick={() => column.toggleSorting(isSorted === 'asc')}
              className="flex items-center gap-1  font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
            >
              <div className=" text-[12px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('orderId')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
          </div>
        );
      },
      cell: () => {
        return (
          <div className="text-InterfaceTextprimary cursor-pointer">
            <span className="text-InterfaceTextprimary border-b border-InterfaceTextprimary ">
              23235313
            </span>
          </div>
        );
      },
    },
  ];

  const discountdata = [
    {
      productname: 'Microsoft Office 365',
      sku: 'JKIW12HKSD78J',
      segment: 'Segment 1',
      term: 'Team 1',
      billtype: 'Type 1',
      discount: '10',
      minqty: '10',
    },
    {
      productname: 'Microsoft Office 365',
      sku: 'JKIW12HKSD78J',
      segment: 'Segment 1',
      term: 'Team 1',
      billtype: 'Type 1',
      discount: '10',
      minqty: '10',
    },
    {
      productname: 'Microsoft Office 365',
      sku: 'JKIW12HKSD78J',
      segment: 'Segment 1',
      term: 'Team 1',
      billtype: 'Type 1',
      discount: '10',
      minqty: '10',
    },
    {
      productname: 'Microsoft Office 365',
      sku: 'JKIW12HKSD78J',
      segment: 'Segment 1',
      term: 'Team 1',
      billtype: 'Type 1',
      discount: '10',
      minqty: '10',
    },
  ];
  const redemptiondata = [
    {
      productname: 'Microsoft Office 365',
      sku: 'JKIW12HKSD78J',
      dateredeemed: '13/03/2025',
      endcustomer: 'Alex Hales',
    },
    {
      productname: 'Microsoft Office 365',
      sku: 'JKIW12HKSD78J',
      dateredeemed: '13/03/2025',
      endcustomer: 'Alex Hales',
    },
  ];

  type DiscountUser = {
    productname: string;
    sku: string;
    segment: string;
    term: string;
    billtype: string;
    discount: string;
    minqty: string;
  };
  type User = {
    productname: string;
    sku: string;
    dateredeemed: string;
    endcustomer: string;
  };

  return (
    <Sheet>
      <SheetTitle></SheetTitle>
      <SheetTrigger className="hover:bg-BrandNeutral100 w-full">
        <div className="px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px] xl:text-[12px] 3xl:text-[0.729vw] text-InterfaceTextdefault flex gap-2 items-center hover:bg-BrandNeutral100">
          <i className="cloud-readeye1 text-InterfaceTextdefault text-[20px] xl:text-[12px] 3xl:text-[0.829vw]"></i>
          {t('view')}
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col h-full gap-0 sm:max-w-[800px] xl:max-w-[900px] 3xl:max-w-[46.875vw] p-[0px] hideclose ">
        {/* Header */}
        <SheetHeader className="border-b border-b-InterfaceStrokesoft px-[20px] lg:px-[20px] xl:px-[22px] 3xl:px-[1.25vw] py-[14px] lg:py-[14px] xl:py-[16px] 3xl:py-[0.833vw]">
          <SheetTitle className="flex justify-between items-center">
            <div className="text-InterfaceTexttitle text-[20px] lg:text-[22px] xl:text-[22px] 2xl:text-[22px] 3xl:text-[0.998vw] font-semibold leading-[140%] flex gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw] items-center">
              {t('couponDetails')}
            </div>
            <SheetClose>
              <i className="cloud-closecircle text-closecolor text-[26px] lg:text-[26px] xl:text-[26px] 2xl:text-[26px] 3xl:text-[1.458vw] cursor-pointer"></i>
            </SheetClose>
          </SheetTitle>
        </SheetHeader>

        <div className="p-[20px] xl:p-[22px] 3xl:p-[1.25vw] h-full overflow-auto bg-InterfaceSurfacecomponentmuted">
          <div className="space-y-[20px] xl:space-y-[22px] 3xl:space-y-[1.25vw]">
            <div className="bg-BrandHighlight100 flex items-center justify-between cardShadow p-[14px] xl:p-[16px] 3xl:p-[0.833vw]">
              <div className="text-BrandHighlight600 font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw] flex items-center gap-2">
                <i className="cloud-discount-shap text-[22px] xl:text-[22px] 3xl:text-[1.146vw] text-BrandHighlightpure"></i>
                {t('couponId')}:1101
              </div>
              <div className="py-[8px] xl:py-[10px] 3xl:py-[0.521vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] border border-BrandHighlightpure bg-BrandPrimarypurenew text-InterfaceTextwhite text-[14px] xl:text-[16px] 3xl:text-[0.833vw] font-medium leading-tight">
                {t('active')}
              </div>
            </div>
            <div className="grid grid-cols-3 gap-[14px] xl:gap-[16px] 3xl:gap-[0.833vw] p-[14px] xl:p-[16px] 3xl:p-[0.833vw] bg-interfacesurfacecomponent">
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  {t('dateCreated')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-semibold">
                  03/03/2025
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  {t('validTill')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                  03/05/2025
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  {t('couponCode')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                  RE1GAV-5ZB4
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  {t('usageType')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                  Multiple
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  {t('permittedCount')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                  4
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  {t('redeemedCount')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                  2
                </div>
              </div>
              <div className="col-span-1 space-y-1 border-b border-InterfaceStrokesoft py-[10px] xl:py-[12px] 3xl:py-[0.625vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                  {t('completeMatch')}
                </div>
                <div className="text-InterfaceTexttitle text-[12px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw] font-medium">
                  No
                </div>
              </div>
            </div>
            <div className="bg-interfacesurfacecomponent">
              <div className=" border border-InterfaceStrokesoft py-[10px] xl:py-[16px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw]">
                {t('discountDetails')}
              </div>
              <DataTable data={discountdata} columns={discountColumns} withCheckbox={false} />
            </div>
            <div className="bg-interfacesurfacecomponent ">
              <div className="border border-InterfaceStrokesoft py-[10px] xl:py-[16px] 3xl:py-[0.625vw] px-[14px] xl:px-[16px] 3xl:px-[0.833vw] font-semibold text-[16px] xl:text-[18px] 3xl:text-[0.938vw]">
                {t('redemptionDetails')}
              </div>
              <DataTable data={redemptiondata} columns={redemptionColumns} withCheckbox={false} />
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
