'use client';
import { Input } from '@redington-gulf-fze/cloudquarks-component-library';
import React, { useState } from 'react';
import GridView from '../components/grid-view';
import ListView from '../components/list-view';
import { useTranslations } from 'next-intl';

export default function OpenCarts() {
  const [view, setView] = useState('list');
  const t = useTranslations();

  return (
    <div className="">
      <div className="col-span-9 px-[28px] 2xl:px-[30px] 3xl:px-[1.667vw] py-[18px] 2xl:py-[18px] 3xl:py-[1.042vw]">
        <div className="flex items-center justify-between gap-2 pb-[18px] 3xl:pb-[1.042vw]">
          <div className="text-InterfaceTextdefault text-[18px] 3xl:text-[1.042vw] font-[600] leading-[140%]">
            {t('openCarts')} (10)
          </div>
          <div className="flex items-center gap-[22px] 3xl:gap-[1.25vw]">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search by Cart Name, Cart ID, Created by, End Customer"
                className="w-[400px] xl:w-[400px] 2xl:w-[609px] 3xl:w-[31.719vw] pl-[40px] xl:pl-[40px] 2xl:pl-[42px] 3xl:pl-[2.34vw] pr-[14px] xl:pr-[14px] 2xl:pr-[16px] 3xl:pr-[0.833vw] py-[8px] xl:py-[8px] 2xl:py-[8px] 3xl:py-[0.417vw] placeholder:text-[#828A91] placeholder:text-[14px] xl:placeholder:text-[14px] 2xl:placeholder:text-[16px] 3xl:placeholder:text-[0.833vw] text-blackcolor border border-InterfaceStrokedefault"
              />
              <i className="cloud-search text-[#777F86] absolute top-[10px] xl:top-[10px] 3xl:top-[0.521vw] left-[12px] text-[16px] xl:text-[16px] 2xl:text-[16px] 3xl:text-[0.833vw] "></i>
            </div>
            <div className="flex">
              <div
                onClick={() => setView('list')}
                className={`${view === 'list' ? 'text-interfacesurfacecomponent bg-InterfaceSurfacehcinverse' : 'text-InterfaceTexttitle bg-background cardShadow'} w-[40px] h-[40px] 3xl:w-[2.083vw] 3xl:h-[2.083vw]  flex items-center justify-center cursor-pointer`}
              >
                <i className="cloud-textalign"></i>
              </div>
              <div
                onClick={() => setView('grid')}
                className={`${view === 'grid' ? 'text-interfacesurfacecomponent bg-InterfaceSurfacehcinverse' : 'text-InterfaceTexttitle bg-background cardShadow'} w-[40px] h-[40px] 3xl:w-[2.083vw] 3xl:h-[2.083vw]  flex items-center justify-center cursor-pointer`}
              >
                <i className="cloud-grid"></i>
              </div>
            </div>
          </div>
        </div>

        <div className="">
          {view === 'grid' && (
            <div className="">
              <GridView />
            </div>
          )}
          {view === 'list' && (
            <div className="space-y-[16px] 3xl:space-y-[0.833vw]">
              <ListView />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
