import { DataTable } from '@/components/common/ui/data-table';
import { TablePagination } from '@/components/common/ui/pagination';
import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { ArrowDown, ArrowUp, ArrowUpDown } from 'lucide-react';
import React, { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
// import { ColumnHeaderFilter } from '@/components/common/columnfilter';
import { CartDeletePopup } from '../cart_delete_popup';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function ListView() {
  const [openKey, setOpenKey] = useState(0);
  const t = useTranslations();

  const handleClose = () => {
    // Change key to force Popover to remount = closes the popover
    setOpenKey((prev) => prev + 1);
  };
  const registrationColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'cartid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('cartID')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Cart ID"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: () => (
        <div className="text-InterfaceTextprimary text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] cursor-pointer">
          CT1009302
        </div>
      ),

      minSize: 400,
    },
    {
      accessorKey: 'quoteid',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('quoteID')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Quote ID"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('quoteid')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'cartname',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('cartName')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>

            {/* <ColumnHeaderFilter
              placeholder="Cart Name"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('cartname')}</div>,

      minSize: 800,
    },
    {
      accessorKey: 'createdby',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('createdBy')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Created by"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('createdby')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'endcustomer',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('endCustomer')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="End Customer"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('endcustomer')}</div>,

      minSize: 500,
    },
    {
      accessorKey: 'totalitems',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('totalItems')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Total Items"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="text-center">{row.getValue('totalitems')}</div>,

      minSize: 600,
    },
    {
      accessorKey: 'currency',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('currency')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Currency"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('currency')}</div>,

      minSize: 600,
    },
    {
      accessorKey: 'totalvalue',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('totalValue')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Total Value"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="text-center">{row.getValue('totalvalue')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'createdon',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                {t('createdOn')}
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Created On"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('createdon')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'expiringon',
      enableSorting: true,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <div className="flex flex-col">
            <button
              type="button"
              className="flex items-center gap-[40px] xl:gap-[40px] 3xl:gap-[2.083vw] text-end font-medium justify-between pt-[8px] xl:pt-[8px] 3xl:pt-[0.417vw]"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              <div className=" text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle">
                Expiring On
              </div>
              {isSorted === 'asc' ? (
                <ArrowUp className="h-[12px] w-[12px]" />
              ) : isSorted === 'desc' ? (
                <ArrowDown className="h-[12px] w-[12px]" />
              ) : (
                <ArrowUpDown className="h-[12px] w-[12px]" />
              )}
            </button>
            {/* <ColumnHeaderFilter
              placeholder="Expiring On"
              onChange={(val) => column.setFilterValue(val)}
            /> */}
          </div>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('expiringon')}</div>,

      minSize: 400,
    },
    {
      accessorKey: 'action',
      header: () => (
        <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] text-InterfaceTexttitle text-center w-full">
          {t('action')}
        </div>
      ),
      cell: ({}) => {
        return (
          <div className="flex items-center gap-2 justify-center ">
            <Popover key={openKey}>
              <PopoverTrigger asChild>
                <i
                  className="cloud-threedot text-InterfaceTextsubtitle cursor-pointer flex "
                  title="Info"
                ></i>
              </PopoverTrigger>
              <PopoverContent className=" p-0 mr-[60px] xl:mr-[60px] 3xl:mr-[3.125vw] rounded-none w-[100px] xl:w-[110px] 2xl:w-[120px] 3xl:w-[5.833vw] z-20 bg-interfacesurfacecomponent cursor-pointer">
                {/* <div className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
                                        <i className="cloud-trash text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
                                        Delete
                                    </div> */}
                <CartDeletePopup />
                <div
                  onClick={handleClose}
                  className=" px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft"
                >
                  <i className="cloud-closecircle text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
                  {t('close')}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      },
      size: 80,
      minSize: 80,
      maxSize: 80,
      meta: {
        className: 'sticky right-0  z-10',
        cellClassName: 'sticky right-0  z-10',
      },
    },
  ];

  type User = {
    cartid: string;
    quoteid: string;
    cartname: string;
    createdby: string;
    endcustomer: string;
    totalitems: string;
    currency: string;
    totalvalue: string;
    createdon: string;
    expiringon: string;
  };

  const registrationdata = [
    {
      cartid: 'CT1009302',
      quoteid: 'RFQ 1001',
      cartname: 'Cart Name 1',
      createdby: 'Greame Smith',
      endcustomer: 'GenX Systems',
      totalitems: '5',
      currency: 'AED',
      totalvalue: '50,000',
      createdon: '01/03/2025',
      expiringon: '03/03/2025, 14:30:24',
    },
    {
      cartid: 'CT1009302',
      quoteid: 'RFQ 1001',
      cartname: 'Cart Name 2',
      createdby: 'Kristin Watson',
      endcustomer: 'Alpha Systems',
      totalitems: '5',
      currency: 'AED',
      totalvalue: '55,000',
      createdon: '01/03/2025',
      expiringon: '03/03/2025, 14:30:24',
    },
    {
      cartid: 'CT1009302',
      quoteid: 'RFQ 1001',
      cartname: 'Cart Name 1',
      createdby: 'Greame Smith',
      endcustomer: 'GenX Systems',
      totalitems: '5',
      currency: 'AED',
      totalvalue: '50,000',
      createdon: '01/03/2025',
      expiringon: '03/03/2025, 14:30:24',
    },
    {
      cartid: 'CT1009302',
      quoteid: 'RFQ 1001',
      cartname: 'Cart Name 2',
      createdby: 'Kristin Watson',
      endcustomer: 'Alpha Systems',
      totalitems: '5',
      currency: 'AED',
      totalvalue: '55,000',
      createdon: '01/03/2025',
      expiringon: '03/03/2025, 14:30:24',
    },
    {
      cartid: 'CT1009302',
      quoteid: 'RFQ 1001',
      cartname: 'Cart Name 1',
      createdby: 'Greame Smith',
      endcustomer: 'GenX Systems',
      totalitems: '5',
      currency: 'AED',
      totalvalue: '50,000',
      createdon: '01/03/2025',
      expiringon: '03/03/2025, 14:30:24',
    },
    {
      cartid: 'CT1009302',
      quoteid: 'RFQ 1001',
      cartname: 'Cart Name 2',
      createdby: 'Kristin Watson',
      endcustomer: 'Alpha Systems',
      totalitems: '5',
      currency: 'AED',
      totalvalue: '55,000',
      createdon: '01/03/2025',
      expiringon: '03/03/2025, 14:30:24',
    },
  ];
  return (
    <>
      <div className="flex gap-[2px] mb-[20px] xl:mb-[20px] 3xl:mb-[1.042vw]">
        <Link href="">
          <Button className="py-[10px] 3xl:py-[0.521vw] px-[20px] 3xl:px-[1.042vw] hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft bg-[#FFF] flex gap-[8px] 3xl:gap-[0.417vw] shadow-[0px_1px_3px_0px_rgba(0,0,0,0.10),0px_1px_2px_-1px_rgba(0,0,0,0.10)] rounded-none text-InterfaceTextdefault font-normal">
            <i className="cloud-trash text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.833vw]"></i>
            Delete
          </Button>
        </Link>
      </div>
      <div className="grid grid-cols-1 bg-interfacesurfacecomponent border border-BrandNeutral100 rounded-1 shadow-md shadow-BrandNeutral100 ">
        <TablePagination />
        <div className="overflow-x-auto">
          <DataTable data={registrationdata} columns={registrationColumns} withCheckbox={true} />
        </div>
        <TablePagination />
      </div>
    </>
  );
}
