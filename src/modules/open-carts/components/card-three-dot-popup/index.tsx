'use client';
import { DeletePopup } from '@/modules/home/<USER>/open-carts/deletpopup';
import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useState } from 'react';
import { ClonePopup } from '@/modules/home/<USER>/open-carts/clonepopup';

export function CardThreeDotPopup() {
  const [popoverOpen, setPopoverOpen] = useState(false);

  // const handleCancel = () => {
  //   setPopoverOpen(false);
  // };

  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger asChild className="">
        <Button
          className={`text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] 
                      px-[12px] lg:px-[11px] xl:px-[12px] 2xl:px-[14px] 3xl:px-[0.729vw] 
                      py-[10px] lg:py-[8px] xl:py-[10px] 2xl:py-[10px] 3xl:py-[0.525vw] 
                      font-[500] rounded-none bg-transparent ${popoverOpen ? 'bg-BrandNeutral100' : ''}`}
        >
          <i
            className="cloud-threedot text-InterfaceTextsubtitle 
              text-[12px] lg:text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="absolute top-0 right-[-11px] xl:right-[-11px] 2xl:right-[-12px] 3xl:right-[-15px] p-0  w-[145px] xl:w-[145px] 2xl:w-[170px] 3xl:w-[8.91vw] rounded-none bg-[#FFF]">
        <div>
          <DeletePopup />
          <ClonePopup />
          {/* <div
            onClick={handleCancel}
            className="cursor-pointer hover:bg-BrandNeutral100 px-[14px] 2xl:px-[16px] 3xl:px-[0.833vw] py-[8px] 2xl:py-[10px] 3xl:py-[0.525vw] border-b border-InterfaceStrokesoft text-InterfaceTextdefault text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw]"
          >
            <i className="cloud-files mr-[8px] 3xl:mr-[0.417vw] text-InterfaceTextsubtitle" />
            Clone
          </div> */}
        </div>
      </PopoverContent>
    </Popover>
  );
}
