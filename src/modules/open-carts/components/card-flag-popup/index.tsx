'use client';
import {
  Too<PERSON><PERSON>,
  Too<PERSON><PERSON>Content,
  Toolt<PERSON>Trigger,
  TooltipProvider,
} from '@redington-gulf-fze/cloudquarks-component-library';

export function CardFlagPopup() {
  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <i className="cloud-flag text-InterfaceTextsubtitle text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]" />
          </TooltipTrigger>
          <TooltipContent className="tooltip-shadow rounded-[8px] bg-white px-[10px] xl:px-[10px] 2xl:px-[10px] 3xl:px-[0.521vw]">
            <p className="text-InterfaceTextdefault text-center text-[12px] lg:text-[14px] xl:text-[14px] 2xl:text-[14px] 3xl:text-[0.729vw]">
              {' '}
              Duplicate <br />
              Cart ID: CT10001
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
}
