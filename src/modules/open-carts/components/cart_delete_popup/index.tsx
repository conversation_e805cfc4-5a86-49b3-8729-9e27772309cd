'use client';

import { SheetClose } from '@redington-gulf-fze/cloudquarks-component-library';

import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { useTranslations } from 'next-intl';
import { <PERSON>o } from 'next/font/google';

const roboto = Roboto({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});

export function CartDeletePopup() {
  const t = useTranslations();
  return (
    <Sheet>
      <SheetTrigger>
        <div className="w-[100px] xl:w-[110px] 2xl:w-[120px] 3xl:w-[5.833vw] px-[14px] xl:px-[14px] 3xl:px-[0.729vw] py-[10px] xl:py-[10px] 3xl:py-[0.529vw] text-[14px]  xl:text-[14px] 3xl:text-[0.729vw]  hover:text-BrandSupport1pure hover:bg-InterfaceStrokesoft text-InterfaceTextdefault flex gap-2 items-center border-b border-InterfaceStrokesoft">
          <i className="cloud-trash text-InterfaceTextdefault text-[16px]  xl:text-[16px] 3xl:text-[0.779vw"></i>
          {t('delete')}
        </div>
      </SheetTrigger>
      <SheetContent
        className={`${roboto.className} sm:max-w-[600px] xl:max-w-[600px] 3xl:max-w-[31.25vw] p-[0px] hideclose`}
        side={'right'}
      >
        <div className="h-full flex flex-col justify-center items-center mx-[69px] lg:mx-[69px] xl:mx-[69px] 2xl:mx-[69px] 3xl:mx-[3.594vw]">
          <div className="flex flex-col justify-center items-center gap-[40px] 3xl:gap-[2.083vw]">
            <i className="cloud-dengerlight text-closecolor text-[72px]"></i>
            <div className="flex flex-col justify-center items-center gap-[8px] 3xl:gap-[0.417vw]">
              <div className="text-InterfaceTexttitle text-[36px] lg:text-[36px] xl:text-[36px] 2xl:text-[36px] 3xl:text-[1.875vw] font-normal leading-[140%] text-center">
                {t('deleteCart')}?
              </div>
              <div className="text-InterfaceTextsubtitle text-[20px] lg:text-[20px] xl:text-[20px] 2xl:text-[20px] 3xl:text-[1.042vw] font-normal leading-[140%] text-center">
                {t('wouldYouLikeToDeleteTheelectedCartYourActionCannotBeReversed')}
              </div>
            </div>
            <div className="flex flex-col justify-center items-center gap-[16px] 3xl:gap-[0.833vw] text-center">
              <SheetClose>
                <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-background bg-closecolor  hover:bg-[#00953A] font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                  <i className="cloud-save-2 text-[18px]"></i>
                  {t('yesDelete')}
                </div>
              </SheetClose>
              <div className="flex items-center gap-[8px] 3xl:gap-[0.417vw]">
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
                <p className="text-InterfaceTextsubtitle text-[12px] 3xl:text-[0.625vw]">
                  {t('or')}
                </p>
                <div className="bg-InterfaceStrokesoft w-[44px] 3xl:w-[2.292vw] h-[1px] 3xl:h-[0.052vw]"></div>
              </div>
              <SheetClose>
                <div className="text-[16px] md:text-[16px] xl:text-[16px] 3xl:text-[0.833vw] text-InterfaceTextdefault bg-ButtonBaseDefault hover:bg-buttonbasedefaulthover2 border border-InterfaceStrokesoft font-medium rounded-none leading-[100%] px-[16px] md:px-[16px] xl:px-[16px] 3xl:px-[0.833vw] py-[10px] md:py-[10px] xl:py-[10px] 3xl:py-[0.521vw] flex justify-center items-center gap-2 w-[200px] 3xl:w-[10.417vw]">
                  <i className="cloud-closecircle text-[18px]"></i>
                  {t('noLater')}
                </div>
              </SheetClose>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
