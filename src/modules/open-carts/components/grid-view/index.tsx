import React from 'react';
import CartPage from '../cart-card';
import { ProductListingTopPagination } from '../product-listing-pagination';
import { ProductListingBottomPagination } from '../product-listing-pagination/bottom-pagination';
import { ScrollArea } from '@redington-gulf-fze/cloudquarks-component-library';

export default function GridView() {
  return (
    <>
      <div className="">
        <div className="flex flex-wrap items-center justify-between bg-InterfaceSurfacecomponentmuted border-b border-InterfaceStrokesoft">
          <div className="flex items-center py-[14px] 3xl:py-[0.729vw] gap-[8px] 3xl:gap-[0.417vw]">
            <div className="text-[14px] xl:text-[14px] 3xl:text-[0.729vw] flex-nowrap text-InterfaceTextdefault">
              Page 1-10{' '}
            </div>
          </div>
          <ProductListingTopPagination />
        </div>
        <ScrollArea className="my-[28px] lg:my-[30px] xl:my-[30px] 2xl:my-[32px] 3xl:my-[1.667vw] h-[700px] 3xl:h-[36.458vw] ">
          <CartPage />
        </ScrollArea>
        <ProductListingBottomPagination />
      </div>
    </>
  );
}
