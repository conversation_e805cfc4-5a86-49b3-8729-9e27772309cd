import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Trigger,
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@redington-gulf-fze/cloudquarks-component-library';
import { DeletePopup } from '@/modules/customer-management/customers/components/delete-popup';
import { ClonePopup } from '@/modules/home/<USER>/open-carts/clonepopup';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const CartPage: React.FC = () => {
  const t = useTranslations();
  const data = [
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
      duplicate: true,
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
      duplicate: false,
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
      duplicate: false,
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
    {
      simmonsOn: '11/03/2025',
      systems: 'Alpha Systems',
      title: 'Cart Name One',
      description:
        'A brief description about the products selected by the customer and details of the products',
      cartId: 'CT100001',
      quoteId: 'RFQ1001',
      totalItems: '5',
      totalValue: 'USD 50,000.00',
      expiresAt: '10:30 PM, March 22, 2025',
    },
  ];

  return (
    <Link href="/order">
      <div className="pt-[18px] xl:pt-[18px] 2xl:pt-[20px] 3xl:pt-[1.042vw] grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 3xl:grid-cols-5 gap-[20px] 3xl:gap-[1.042vw]">
        {data.map((items, index) => (
          <div
            key={index}
            className="border border-InterfaceStrokesoft bg-background openCardShadow"
          >
            <div className="py-[18px] 2xl:py-[20px] 3xl:py-[1.042vw] px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
              <div className="flex justify-between gap-1 mb-[8px] 3xl:mb-[0.417vw]">
                <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('simmonsOn')} {items.simmonsOn}
                </div>
                <div className="flex items-center gap-3">
                  {items.duplicate === true ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <i className="cloud-danger text-closecolor text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i>
                        </TooltipTrigger>
                        <TooltipContent
                          side="bottom"
                          align="center"
                          className="min-w-[100px] 3xl:min-w-[5.208vw] bg-background"
                        >
                          <p className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] text-center">
                            {t('duplicate')} <br />
                            {t('cartID')}: {items.cartId}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : items.duplicate === false ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <i className="cloud-flag text-InterfaceTextsubtitle text-[16px] 3xl:text-[0.833vw] cursor-pointer"></i>
                        </TooltipTrigger>
                        <TooltipContent
                          side="bottom"
                          align="center"
                          className="min-w-[100px] 3xl:min-w-[5.208vw] bg-background"
                        >
                          <p className="text-InterfaceTextdefault text-[12px] 3xl:text-[0.625vw] font-[400] leading-[140%] text-center">
                            {t('duplicate')} <br />
                            {t('cartID')}: {items.cartId}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : null}

                  <Popover>
                    <PopoverTrigger asChild>
                      <i className="cloud-threedot text-BrandNeutralpure text-[16px] 3xl:text-[0.833vw] cursor-pointer">
                        {' '}
                      </i>
                    </PopoverTrigger>
                    <PopoverContent className="mr-[30px] xl:mr-[30px] 3xl:mr-[1.563vw] rounded-none w-fit z-20 bg-interfacesurfacecomponent cursor-pointer p-0">
                      <div>
                        <DeletePopup />
                        <ClonePopup />
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="text-InterfaceTextprimary text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                {items.systems}
              </div>
              <div className="text-InterfaceTexttitle text-[20px] 3xl:text-[1.042vw] font-[700] leading-[140%] mb-[4px] 3xl:mb-[0.208vw]">
                {items.title}
              </div>

              <div className="text-InterfaceTextsubtitle text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                {items.description}
              </div>

              <div className="flex items-center gap-[12px] 3xl:gap-[0.625vw] mt-[16px] 3xl:mt-[0.833vw]">
                <div className="border border-BrandSupport1300 bg-BrandSupport150 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.313vw]">
                  <div className="text-InterfaceTextprimary text-[12px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[100%]">
                    {t('cartID')}: {items.cartId}
                  </div>
                </div>
                <div className="border border-BrandSupport1300 bg-BrandSupport150 px-[8px] 3xl:px-[0.417vw] py-[6px] 3xl:py-[0.313vw]">
                  <div className="text-InterfaceTextprimary text-[12px] xl:text-[10px] 2xl:text-[12px] 3xl:text-[0.625vw] font-[500] leading-[100%]">
                    {t('quoteID')}: {items.quoteId}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-interfacesurfacecomponent border-t flex justify-between py-[10px] lg:py-[10px] xl:py-[10px] 2xl:py-[12px] 3xl:py-[0.625vw] px-[20px] lg:px-[22px] xl:px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
              <div className="text-blackcolor text-[11px] lg:text-[11px] xl:text-[11px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                {t('totalItems')}:{' '}
                <span className="font-semibold text-[11px] lg:text-[11px] xl:text-[11px] 2xl:text-[16px] 3xl:text-[0.729vw]">
                  5
                </span>
              </div>
              <div className="text-blackcolor text-[11px] lg:text-[11px] xl:text-[11px] 2xl:text-[14px] 3xl:text-[0.729vw]">
                {t('totalValue')}:{' '}
                <span className="font-semibold text-[11px] lg:text-[11px] xl:text-[11px] 2xl:text-[16px] 3xl:text-[0.729vw]">
                  USD 50,000.00
                </span>
              </div>
            </div>

            <div className="py-[10px] xl:py-[12px] border-t 2xl:py-[12px] 3xl:py-[0.625vw] px-[22px] 2xl:px-[24px] 3xl:px-[1.25vw]">
              <div className="flex items-center gap-1">
                <i className="cloud-clock11 text-Interfacefeedbackneutral700 text-[14px] xl:text-[14px] 2xl:text-[16px] 3xl:text-[0.833vw]">
                  {' '}
                </i>
                <div className="text-Interfacefeedbackneutral700 text-[12px] xl:text-[12px] 2xl:text-[14px] 3xl:text-[0.729vw] font-[400] leading-[140%]">
                  {t('expiresAt')} {items.expiresAt}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Link>
  );
};

export default CartPage;
