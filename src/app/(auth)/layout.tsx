import type { Metada<PERSON> } from 'next';
import { redirect } from 'next/navigation';
import { COOKIES_KEYS } from '@/lib/enums';
import { cookies } from 'next/headers';

export const metadata: Metadata = {
  title: 'Login',
};

export default async function AuthLayout({ children }: { children: React.ReactNode }) {
  const cookieStore = await cookies();
  const token = cookieStore.get(COOKIES_KEYS.TOKEN)?.value;

  if (token) {
    redirect('/account');
  }

  return <>{children}</>;
}
