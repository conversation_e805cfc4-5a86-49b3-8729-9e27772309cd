# App Directory

This directory contains all the Next.js pages and layouts using the App Router pattern.

## Purpose

- Root layouts and templates
- Page components
- Route groups
- Loading and error states
- API routes

## Structure

```
app/
├── (auth)/              # Auth route group
│   ├── login/          # Login page
│   └── register/       # Register page
├── api/                # API routes
├── layout.tsx          # Root layout
├── page.tsx           # Home page
└── providers.tsx      # App providers
```

## Best Practices

1. Use route groups (folders in parentheses) to organize related routes
2. Keep pages simple, move complex logic to components or hooks
3. Use loading.tsx and error.tsx for better UX
4. Implement layouts for shared UI between routes
