import { Providers } from './providers';
import '../styles/globals.css';
import { validateEnv } from '@/lib/config/env';
import { getCDNStylesheets } from '@/lib/config/cdn';
import { Inter } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import { Metadata } from 'next';
import { getLocale } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { getDirection } from '@/lib/utils/util';

const inter = Inter({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
});
// Validate environment variables
validateEnv();

export const metadata: Metadata = {
  title: {
    default: 'CloudQuarks',
    template: '%s | CloudQuarks',
  },
  description: 'CloudQuarks',
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const stylesheets = await getCDNStylesheets(['styles.css', 'globals/theme.css']);
  const locale = await getLocale();
  return (
    <html lang={locale} dir={getDirection(locale)}>
      <head>
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon.svg" />
        {stylesheets.map((stylesheet, index) => (
          <link key={index} rel="stylesheet" href={stylesheet} precedence="default" />
        ))}
      </head>
      <body className={inter.className}>
        <NextIntlClientProvider>
          <Providers>
            <Toaster position="top-right" toastOptions={{ duration: 5000 }} />
            {children}
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
