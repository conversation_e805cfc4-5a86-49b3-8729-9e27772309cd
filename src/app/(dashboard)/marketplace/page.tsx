import Marketplace from '@/modules/marketplace/template';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('marketplace'),
      href: '/marketplace',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>
      <div className="w-full">
        <Marketplace />
      </div>
    </>
  );
}

export default Page;
