import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import FaqTemplate from '@/modules/faq/templates';
import React from 'react';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();
  const SidebarNavItems = [
    {
      label: t('knowledgeHub'),
      href: '/help/knowledge-hub',
    },
    {
      label: t('serviceDesk'),
      href: '/help/service-desk',
    },
    {
      label: t('faqs'),
      href: '/help/faq',
    },
  ];

  const BreadcrumbNavItems = [
    {
      label: t('help'),
      href: '/help/service-desk',
    },
    {
      label: t('helpSupport'),
      href: '/help/service-desk',
    },
    {
      label: t('faqs'),
      href: '',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013] flex">
        <SubSidebar title={t('helpSupport')} navItems={SidebarNavItems} />
        <FaqTemplate />
      </div>
    </>
  );
}

export default Page;
