import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import LanguageCurrencyTemplate from '@/modules/settings/language-currency/templates';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();
  const SideBarNavItems = [
    {
      label: t('Currency & Language'),
      href: '/settings/language-currency',
    },
    {
      label: t('alerts'),
      href: '/settings/alerts/notifications',
    },
    {
      label: t('activityLog'),
      href: '/settings/activity-log',
    },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('settings'),
      href: '',
    },
    {
      label: t('languageCurrency'),
      href: '/settings/language-currency',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('settings')} navItems={SideBarNavItems} />
        <div className="w-full">
          <LanguageCurrencyTemplate />
        </div>
      </div>
    </>
  );
}

export default Page;
