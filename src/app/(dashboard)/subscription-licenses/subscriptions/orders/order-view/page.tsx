import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import React from 'react';
import OrdersViewTemplate from '@/modules/subscription-licenses/subscriptions/orders/order-view/templates';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();

  const SideBarNavItems = [
    {
      label: t('details'),
      href: '/subscription-licenses/subscriptions/details',
    },
    {
      label: t('orders'),
      href: '/subscription-licenses/subscriptions/orders',
    },
    {
      label: t('contracts'),
      href: '/subscription-licenses/subscriptions/contracts',
    },
    {
      label: t('usageCost'),
      href: '/subscription-licenses/subscriptions/usage-cost',
    },
    {
      label: t('activityLog'),
      href: '/subscription-licenses/subscriptions/activity-log',
    },
  ];

  const BreadcrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('subscriptionLicenses'),
      href: '/subscription-licenses',
    },

    {
      label: t('subscriptions'),
      href: '/subscriptions',
    },
    {
      label: t('orders'),
      href: '/subscription-licenses/subscriptions/orders',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013] flex">
        <SubSidebar title={t('subscriptions')} navItems={SideBarNavItems} />
        <OrdersViewTemplate />
      </div>
    </>
  );
}

export default Page;
