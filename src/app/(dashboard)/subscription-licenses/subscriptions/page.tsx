import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import SubscriptionsTemplate from '@/modules/subscription-licenses/subscriptions/template';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();
  const SideBarNavItems = [
    {
      label: t('subscriptions'),
      href: '/subscription-licenses/subscriptions',
    },
    {
      label: t('contracts'),
      href: '/subscription-licenses/contracts',
    },
    {
      label: t('renewalManagement'),
      href: '/subscription-licenses/renewal-management/expiring',
    },
    {
      label: t('insights'),
      href: '',
    },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('subscriptionLicenses'),
      href: '/subscription-licenses',
    },
    {
      label: t('subscriptions'),
      href: '',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('subscriptionLicenses')} navItems={SideBarNavItems} />
        <SubscriptionsTemplate />
      </div>
    </>
  );
}

export default Page;
