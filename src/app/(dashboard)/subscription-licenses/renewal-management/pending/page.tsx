import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import PendingTemplate from '@/modules/subscription-licenses/renewal-management/pending/templates';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();

  const SideBarNavItems = [
    {
      label: t('expiring'),
      href: '/subscription-licenses/renewal-management/expiring',
    },
    {
      label: t('pending'),
      href: '/subscription-licenses/renewal-management/pending',
    },
    {
      label: t('approved'),
      href: '/subscription-licenses/renewal-management/approved',
    },
    {
      label: t('renewing'),
      href: '/subscription-licenses/renewal-management/renewing',
    },
    {
      label: t('expired'),
      href: '/subscription-licenses/renewal-management/expired',
    },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('subscriptionLicenses'),
      href: '/subscription-licenses',
    },
    {
      label: t('renewalManagement'),
      href: '/subscription-licenses',
    },
    {
      label: t('pending'),
      href: '',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('renewalManagement')} navItems={SideBarNavItems} />
        <PendingTemplate />
      </div>
    </>
  );
}

export default Page;
