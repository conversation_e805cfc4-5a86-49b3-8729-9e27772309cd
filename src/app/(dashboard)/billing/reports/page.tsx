import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import Reports from '@/modules/billing/reports/templates';
import { getTranslations } from 'next-intl/server';
// import InvoiceDetailsPopup from '@/modules/billing/components/invoice-details-popup';

async function Page() {
  const t = await getTranslations();
  const SideBarNavItems = [
    {
      label: t('invoices'),
      href: '/billing/invoices',
    },
    {
      label: t('reports'),
      href: '/billing/reports',
    },
    // {
    //   label: 'Insights',
    //   href: '/billing/insights',
    // },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('reports'),
      href: '/billing/reports',
    },
    {
      label: t('reports'),
      href: '',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex">
        <SubSidebar title={t('billing')} navItems={SideBarNavItems} />
        <Reports />
      </div>
      {/* <InvoiceDetailsPopup/> */}
    </>
  );
}

export default Page;
