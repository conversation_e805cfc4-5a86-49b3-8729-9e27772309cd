import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';

const BreadCrumbNavItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Billing',
    href: '/account',
  },

  {
    label: 'Invoices',
    href: '/',
  },
];
const SideBarNavItems = [
  {
    label: 'Invoices',
    href: '',
  },
  {
    label: 'Reports',
    href: 'billing/reports',
  },
  {
    label: 'Insight',
    href: '',
  },
];

function Page() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex">
        <SubSidebar title="Billing" navItems={SideBarNavItems} />
        Billing
      </div>
    </>
  );
}

export default Page;
