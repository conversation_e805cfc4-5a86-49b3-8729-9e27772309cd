import React from 'react';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import InvoicesTemplate from '@/modules/billing/invoices/templates';
import { getTranslations } from 'next-intl/server';

export default async function Page() {
  const t = await getTranslations();
  const SideBarNavItems = [
    {
      label: t('invoices'),
      href: '/billing/invoices',
    },
    {
      label: t('reports'),
      href: '/billing/reports',
    },
    // {
    //   label: 'Insights',
    //   href: '/billing/insights',
    // },
  ];
  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('billing'),
      href: '/billing/invoices',
    },
    {
      label: t('invoices'),
      href: '',
    },
  ];
  return (
    <div>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('billing')} navItems={SideBarNavItems} />
        <InvoicesTemplate />
      </div>
    </div>
  );
}
