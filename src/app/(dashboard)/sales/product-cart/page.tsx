import React from 'react';
import ProductCartTemplate from '@/modules/product-cart/templates';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';

const BreadcrumbNavItems = [
  {
    label: 'Home',
    href: '/#',
  },
  {
    label: 'Request for Quotation',
    href: '/sales/product-cart',
  },
];

function Page() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013]">
        <ProductCartTemplate />
      </div>
    </>
  );
}

export default Page;
