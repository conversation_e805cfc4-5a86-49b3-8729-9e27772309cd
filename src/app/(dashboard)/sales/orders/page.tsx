import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import React from 'react';
import OrdersTemplate from '@/modules/orders/templates';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();
  const SidebarNavItems = [
    {
      label: t('discountCoupons'),
      href: '/sales/discount-coupons',
    },
    {
      label: t('quotations'),
      href: '/sales/quotations',
    },
    {
      label: t('orders'),
      href: '/sales/orders',
    },
    {
      label: 'Deals',
      href: '/sales/deals',
    },
  ];

  const BreadcrumbNavItems = [
    {
      label: t('discountCoupons'),
      href: '/sales/discount-coupons',
    },
    {
      label: t('quotations'),
      href: '/sales/quotations',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013] flex">
        <SubSidebar title={t('sales')} navItems={SidebarNavItems} />
        <OrdersTemplate />
      </div>
    </>
  );
}

export default Page;
