import React from 'react';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import DealDetailsTemplate from '@/modules/deals/components/dela-details-view/templates';

const BreadcrumbNavItems = [
  {
    label: 'Home',
    href: '/#',
  },
  {
    label: 'Sales',
    href: '/sales/discount-coupons',
  },
  {
    label: 'Deals',
    href: '/sales/deals',
  },
  {
    label: 'Deal Details',
    href: '/sales/deal-details',
  },
];

function Page() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013]">
        <DealDetailsTemplate />
      </div>
    </>
  );
}

export default Page;
