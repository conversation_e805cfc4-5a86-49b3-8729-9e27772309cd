import React from 'react';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import CustomersOrdersTemplate from '@/modules/customer-management/customers/orders/templates';
import { getTranslations } from 'next-intl/server';

export default async function Page() {
  const t = await getTranslations();

  const SideBarNavItems = [
    {
      label: t('details'),
      href: '/customer-management/customers/details',
    },
    {
      label: t('organizationAndTenants'),
      href: '/customer-management/customers/organization-tenants',
    },
    {
      label: t('orders'),
      href: '/customer-management/customers/orders',
    },
    {
      label: t('subscriptionLicenses'),
      href: '/customer-management/customers/subscription-licenses',
    },
    {
      label: t('contracts'),
      href: '/customer-management/customers/contracts',
    },
    {
      label: t('invoice'),
      href: '/customer-management/customers/invoice',
    },
    {
      label: t('activityLog'),
      href: '/customer-management/customers/activity-log',
    },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('customerManagement'),
      href: '/customer-management/customers/customer-management',
    },
    {
      label: t('customers'),
      href: '/customer-management/customers/customer-management',
    },
    {
      label: t('orders'),
      href: '/customer-management/customers/orders',
    },
  ];
  return (
    <div>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('customers')} navItems={SideBarNavItems} />
        <CustomersOrdersTemplate />
      </div>
    </div>
  );
}
