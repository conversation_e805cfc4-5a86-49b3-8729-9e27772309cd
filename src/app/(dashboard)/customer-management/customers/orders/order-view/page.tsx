import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import React from 'react';
import OrdersViewTemplate from '@/modules/customer-management/customers/orders/order-view/templates';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();

  const SidebarNavItems = [
    {
      label: t('details'),
      href: '/customer-management/customers/details',
    },
    {
      label: t('organizationAndTenants'),
      href: '/customer-management/customers/organization-tenants',
    },
    {
      label: t('orders'),
      href: '/customer-management/customers/orders/order-view',
    },
    {
      label: t('subscriptionLicenses'),
      href: '/customer-management/customers/subscription-licenses',
    },
    {
      label: t('contracts'),
      href: '/customer-management/customers/contracts',
    },
    {
      label: t('invoice'),
      href: '/customer-management/customers/invoice',
    },
    {
      label: t('activityLog'),
      href: '/customer-management/customers/activity-log',
    },
  ];

  const BreadcrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('customerManagement'),
      href: '/customer-management/customers/customer-management',
    },
    {
      label: t('customers'),
      href: '/customer-management/customers/Customers',
    },
    {
      label: t('orders'),
      href: '/customer-management/customers/orders',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013] flex">
        <SubSidebar title={t('customers')} navItems={SidebarNavItems} />
        <OrdersViewTemplate />
      </div>
    </>
  );
}

export default Page;
