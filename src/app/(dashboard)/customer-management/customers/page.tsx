import React from 'react';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import CustomersTemplate from '@/modules/customer-management/customers/templates';
import { getTranslations } from 'next-intl/server';

export default async function Page() {
  const t = await getTranslations();

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('customerManagement'),
      href: '/customer-management/customers',
    },

    {
      label: t('customers'),
      href: '/',
    },
  ];
  const SideBarNavItems = [
    {
      label: t('customers'),
      href: '/customer-management/customers',
    },
    {
      label: t('insights'),
      href: '/customer-management/insights',
    },
  ];
  return (
    <div>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('customerManagement')} navItems={SideBarNavItems} />
        <CustomersTemplate />
      </div>
    </div>
  );
}
