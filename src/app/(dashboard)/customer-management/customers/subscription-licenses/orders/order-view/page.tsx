import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import React from 'react';
import OrdersViewTemplate from '@/modules/subscription-licenses/subscriptions/orders/order-view/templates';

const SidebarNavItems = [
  {
    label: 'Details',
    href: '/subscription-licenses/subscriptions/details',
  },
  {
    label: 'Orders',
    href: '/subscription-licenses/subscriptions/orders',
  },
  {
    label: 'Contracts',
    href: '/subscription-licenses/subscriptions/contracts',
  },
  {
    label: 'Usage & Cost',
    href: '/subscription-licenses/subscriptions/usage-cost',
  },
  {
    label: 'Activity Log',
    href: '/subscription-licenses/subscriptions/activity-log',
  },
];

const BreadcrumbNavItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Subscription & Licenses',
    href: '/subscription-licenses/details',
  },
  {
    label: 'Subscriptions',
    href: '/subscription-licenses/subscriptions/details',
  },
  {
    label: 'Orders',
    href: '/subscription-licenses/subscriptions/orders',
  },
];

function Page() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013] flex">
        <SubSidebar title="Subscriptions" navItems={SidebarNavItems} />
        <OrdersViewTemplate />
      </div>
    </>
  );
}

export default Page;
