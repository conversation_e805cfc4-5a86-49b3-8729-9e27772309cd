import React from 'react';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import SubscriptionsOrdersTemplate from '@/modules/subscription-licenses/subscriptions/orders/templates';

const BreadCrumbNavItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Subscription & Licenses',
    href: '/subscription-licenses/subscriptions/details',
  },
  {
    label: 'Subscriptions',
    href: '/subscription-licenses/subscriptions/details',
  },
  {
    label: 'Orders',
    href: '/subscription-licenses/subscriptions/orders',
  },
];
const SideBarNavItems = [
  {
    label: 'Details',
    href: '/subscription-licenses/subscriptions/details',
  },
  {
    label: 'Orders',
    href: '/subscription-licenses/subscriptions/orders',
  },
  {
    label: 'Contracts',
    href: '/subscription-licenses/subscriptions/contracts',
  },
  {
    label: 'Usage & Cost',
    href: '/subscription-licenses/subscriptions/usage-cost',
  },
  {
    label: 'Activity Log',
    href: '/subscription-licenses/subscriptions/activity-log',
  },
];
export default function Page() {
  return (
    <div>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title="Subscriptions" navItems={SideBarNavItems} />
        <SubscriptionsOrdersTemplate />
      </div>
    </div>
  );
}
