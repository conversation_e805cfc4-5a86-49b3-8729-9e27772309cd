'use client';
import React from 'react';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';

import UsageCostTable from '@/modules/subscription-licenses/subscriptions/usage-cost/templates';

const BreadCrumbNavItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Subscriptions & Licenses',
    href: '/subscription-licenses',
  },

  {
    label: 'Subscriptions',
    href: '/subscriptions',
  },
  {
    label: 'Usage & Cost',
    href: '/',
  },
];
const SideBarNavItems = [
  {
    label: 'Usage & Cost',
    href: '/subscription-licenses/subscriptions/usage-cost',
  },
  {
    label: ' Activity Log',
    href: '/subscription-licenses/subscriptions/activity-log',
  },
];
export default function Page() {
  return (
    <div>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title="Finance" navItems={SideBarNavItems} />
        <UsageCostTable />
      </div>
    </div>
  );
}
