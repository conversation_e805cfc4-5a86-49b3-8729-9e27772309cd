import React from 'react';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import ActivitylogTemplate from '@/modules/customer-management/customers/activity-log/templates';

const BreadCrumbNavItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Customer Management',
    href: '/customer-management/customers/customer-management',
  },
  {
    label: 'Customers',
    href: '/customer-management/customers/customer-management',
  },
  {
    label: 'Activity Log',
    href: '/customer-management/customers/activity-log',
  },
];
const SideBarNavItems = [
  {
    label: 'Details',
    href: '/customer-management/customers/details',
  },
  {
    label: 'Organization & Tenants',
    href: '/customer-management/customers/organization-tenants',
  },
  {
    label: 'Orders',
    href: '/customer-management/customers/orders',
  },
  {
    label: 'Subscription & Licenses',
    href: '/customer-management/customers/subscription-licenses',
  },
  {
    label: 'Contracts',
    href: '/customer-management/customers/contracts',
  },
  {
    label: 'Invoice',
    href: '/customer-management/customers/invoice',
  },
  {
    label: 'Activity Log',
    href: '/customer-management/customers/activity-log',
  },
];
export default function Page() {
  return (
    <div>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title="Customers" navItems={SideBarNavItems} />
        <ActivitylogTemplate />
      </div>
    </div>
  );
}
