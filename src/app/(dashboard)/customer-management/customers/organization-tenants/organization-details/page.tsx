'use client';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import OrgDetailsTemplate from '@/modules/customer-management/customers/organization-details/template';

const SideBarNavItems = [
  {
    label: 'Details',
    href: '/customer-management/organization-tenants/organization-details',
  },
  {
    label: 'GDAP',
    href: '',
  },
];

const BreadCrumbNavItems = [
  {
    label: 'Home',
    href: '/home',
  },
  {
    label: 'Customer Management',
    href: '/customer-management/customers',
  },
  {
    label: 'Customers',
    href: '/customer-management/customers',
  },
  {
    label: 'Organization & Tenants',
    href: '/customer-management/customers/organization-details',
  },
  {
    label: 'Details',
    href: '/customer-management/customers/organization-details',
  },
];

function Page() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title="Customers" navItems={SideBarNavItems} />
        <OrgDetailsTemplate />
      </div>
    </>
  );
}

export default Page;
