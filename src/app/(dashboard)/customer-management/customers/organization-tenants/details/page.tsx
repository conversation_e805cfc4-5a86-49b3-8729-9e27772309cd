'use client';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import OrgDetailsTemplate from '@/modules/customer-management/customers/organization-details/template';
import { useTranslations } from 'next-intl';

function Page() {
  const t = useTranslations();
  const SideBarNavItems = [
    {
      label: t('details'),
      href: '/customer-management/customers/organization-tenants/details',
    },
    {
      label: 'GDAP',
      href: '/customer-management/customers/organization-tenants/gdap',
    },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('customerManagement'),
      href: '/customer-management/customers/customer-management',
    },
    {
      label: t('customers'),
      href: '/customer-management/customers/customer-management',
    },
    {
      label: t('organizationTenant'),
      href: '/customer-management/customers/organization-tenants',
    },
    {
      label: t('details'),
      href: '/customer-management/customers/organization-tenants/details',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('organizationTenant')} navItems={SideBarNavItems} />
        <OrgDetailsTemplate />
      </div>
    </>
  );
}

export default Page;
