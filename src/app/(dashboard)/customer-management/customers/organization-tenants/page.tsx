import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import OrganizationTenantsTemplate from '@/modules/customer-management/customers/organization-tenants/templates';
import React from 'react';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();
  const SideBarNavItems = [
    {
      label: t('details'),
      href: '/customer-management/customers/details',
    },
    {
      label: t('organizationAndTenants'),
      href: '/customer-management/customers/organization-tenants',
    },
    {
      label: t('orders'),
      href: '/customer-management/customers/orders',
    },
    {
      label: t('subscriptionLicenses'),
      href: '/customer-management/customers/subscription-licenses',
    },
    {
      label: t('contracts'),
      href: '/customer-management/customers/contracts',
    },
    {
      label: t('invoice'),
      href: '/customer-management/customers/invoice',
    },
    {
      label: t('activityLog'),
      href: '/customer-management/customers/activity-log',
    },
  ];

  const BreadcrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('customerManagement'),
      href: '/customer-management/customers',
    },

    {
      label: t('customers'),
      href: '/',
    },
    {
      label: t('organizationAndTenants'),
      href: '/customer-management/customers/organization-tenants',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadcrumbNavItems} />
      </div>
      <div className="dark:bg-[#0F1013] flex">
        <SubSidebar title={t('customers')} navItems={SideBarNavItems} />
        <div className="m-3 w-full">
          <OrganizationTenantsTemplate />
        </div>
      </div>
    </>
  );
}

export default Page;
