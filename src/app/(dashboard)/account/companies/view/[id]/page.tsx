import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import CompanyDetials from '@/modules/account/companies/view/templates';

const BreadCrumbNavItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Account Management',
    href: '/account',
  },

  {
    label: "My Company's",
    href: '/account/companies',
  },
];

function Page() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex">
        <CompanyDetials />
      </div>
    </>
  );
}

export default Page;
