import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import AccountInformation from '@/modules/account/companies/account-information/templates';

const BreadCrumbNavItems = [
  {
    label: 'Home',
    href: '/home',
  },
  {
    label: 'Account',
    href: '/account',
  },
  {
    label: 'My Account',
    href: '/account',
  },
  {
    label: 'Information',
    href: '',
  },
];

function Page() {
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="w-full min-h-screen">
        <AccountInformation />
      </div>
    </>
  );
}

export default Page;
