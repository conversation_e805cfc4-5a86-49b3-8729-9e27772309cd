import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import AllCompanyInformation from '@/modules/account/companies/templates';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();
  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: 'Account Management',
      href: '/account',
    },

    {
      label: t('myCompany'),
      href: '/account/companies',
    },
  ];
  const SideBarNavItems = [
    {
      label: t('myProfile'),
      href: '/account',
    },
    {
      label: t('myCompany'),
      href: '/account/companies',
    },
    {
      label: t('finance'),
      href: '/account/finance/credit-limit',
    },
    {
      label: t('agreements'),
      href: '/account/agreements',
    },
    {
      label: t('brandSetup'),
      href: '/account/brand-setup',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex">
        <SubSidebar title="Account Management" navItems={SideBarNavItems} />
        <div className="relative pt-[10px] lg:pt-[16px] xl:pt-[18px] 2xl:pt-[18px] 3xl:pt-[0.938vw] px-[14px] lg:px-[26px] xl:px-[30px] 2xl:px-[30px] 3xl:px-[1.667vw] pl-[36px]">
          <div className="pb-[14px] xl:pb-[14px] 2xl:pb-[14px] 3xl:pb-[0.729vw]">
            <div className="text-InterfaceTexttitle text-[16px] lg:text-[16px] xl:text-[18px] 2xl:text-[20px] 3xl:text-[1.042vw] font-[600]">
              {t('myCompanyS')}
            </div>
          </div>
          <AllCompanyInformation />
        </div>
      </div>
    </>
  );
}

export default Page;
