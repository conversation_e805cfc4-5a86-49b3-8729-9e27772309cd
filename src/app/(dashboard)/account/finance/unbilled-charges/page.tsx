import React from 'react';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import UnbilledChargesTemplate from '@/modules/account/finance/unbilled-charges/templates';
import { getTranslations } from 'next-intl/server';

export default async function Page() {
  const t = await getTranslations();
  const SideBarNavItems = [
    {
      label: t('creditLimitUtilization'),
      href: '/account/finance/credit-limit',
    },
    {
      label: t('statementOfAccounts'),
      href: '/account/finance/statement-accounts',
    },
    {
      label: t('outstanding'),
      href: '/account/finance/outstanding',
    },
    {
      label: t('unbilledCharges'),
      href: '/account/finance/unbilled-charges',
    },
    {
      label: t('paymentTerms'),
      href: '/account/finance/payment-terms',
    },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('accountManagement'),
      href: '/account',
    },
    {
      label: t('finance'),
      href: '',
    },
    {
      label: t('unbilledCharges'),
      href: '',
    },
  ];
  return (
    <div>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title={t('finance')} navItems={SideBarNavItems} />
        <UnbilledChargesTemplate />
      </div>
    </div>
  );
}
