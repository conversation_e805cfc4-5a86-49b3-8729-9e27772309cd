import Agreements from '@/modules/agreements/templates';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('accountManagement'),
      href: '/account',
    },

    {
      label: t('agreements'),
      href: '/account/agreements',
    },
  ];
  const SideBarNavItems = [
    {
      label: t('myAccount'),
      href: '/account',
    },
    {
      label: t('finance'),
      href: '/finance',
    },
    {
      label: t('agreements'),
      href: '/account/agreements',
    },
    {
      label: t('brandSetup'),
      href: '/account/brand-setup',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex">
        <SubSidebar title={t('accountManagement')} navItems={SideBarNavItems} />
        <Agreements />
      </div>
    </>
  );
}

export default Page;
