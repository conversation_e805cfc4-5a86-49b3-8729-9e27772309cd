import AccountTemplate from '@/modules/account/templates';
import { SubSidebar } from '@/components/layout/sub-sidebar';
import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import { getTranslations } from 'next-intl/server';

async function Page() {
  const t = await getTranslations();
  const SideBarNavItems = [
    {
      label: t('myProfile'),
      href: '/account',
    },
    {
      label: t('myCompany'),
      href: '/account/companies',
    },
    {
      label: t('finance'),
      href: 'account/finance/credit-limit',
    },
    {
      label: t('agreements'),
      href: '/account/agreements',
    },
    {
      label: t('brandSetup'),
      href: '/account/brand-setup',
    },
  ];

  const BreadCrumbNavItems = [
    {
      label: t('home'),
      href: '/home',
    },
    {
      label: t('accountManagement'),
      href: '/account',
    },
    {
      label: t('myProfile'),
      href: '',
    },
  ];
  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>

      <div className="flex w-full">
        <SubSidebar title="Account Mangement" navItems={SideBarNavItems} />
        <AccountTemplate />
      </div>
    </>
  );
}

export default Page;
