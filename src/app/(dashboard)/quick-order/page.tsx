import BreadcrumbComponent from '@/components/common/ui/bread-crumb';
import QuickOrder from '@/modules/quick-order/template/indext';
import React from 'react';

export default function page() {
  const BreadCrumbNavItems = [
    {
      label: 'Home',
      href: '/home',
    },
    {
      label: 'Quick Order',
      href: '/quick-order',
    },
  ];

  return (
    <>
      <div className="bg-BrandNeutral100 text-InterfaceTextsubtitle text-[12px] pl-[28px] 2xl:pl-[30px] 3xl:pl-[1.667vw] py-[8px] 3xl:py-[0.417vw] custom_shadow">
        <BreadcrumbComponent navItems={BreadCrumbNavItems} />
      </div>
      <div className="w-full">
        <QuickOrder />
      </div>
    </>
  );
}
