import type { Metadata } from 'next';
import SideBar from '@/components/layout/sidebar';
import Header from '@/components/layout/header';
import Footer from '@/components/layout/footer';
import { getDirection } from '@/lib/utils/util';
import { useLocale } from 'next-intl';
import LeftPading from '@/components/layout/leftpadding';

export const metadata: Metadata = {
  title: 'Account',
};

export default function DasboardLayout({ children }: { children: React.ReactNode }) {
  const locale = useLocale();
  const direction = getDirection(locale);

  return (
    <div dir={direction}>
      <Header />
      <SideBar />
      <LeftPading>
        <main>{children}</main>
        <Footer />
      </LeftPading>
    </div>
  );
}
