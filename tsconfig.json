{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@redington-gulf-fze/cloudquarks-component-library": ["node_modules/@redington-gulf-fze/cloudquarks-component-library/dist"], "simplebar-react": ["node_modules/simplebar-react/dist"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}