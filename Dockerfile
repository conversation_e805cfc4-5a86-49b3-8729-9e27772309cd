# syntax=docker/dockerfile:1.3

###############################
# Builder Stage
###############################
FROM node:alpine AS builder
 
WORKDIR /app
ENV NEXT_TELEMETRY_DISABLED=1

# Declare build-time variable
ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
 
# Install git + openssh so npm can pull private repos
RUN apk add --no-cache git openssh

COPY package*.json ./

# Install all dependencies (including optional)
RUN npm install --legacy-peer-deps

COPY . .
RUN npm run build

###############################
# Production Stage
###############################
FROM node:alpine AS runner
 
WORKDIR /app
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public
COPY --from=builder /app/locales ./locales

EXPOSE 3000
CMD ["node", "server.js"]
