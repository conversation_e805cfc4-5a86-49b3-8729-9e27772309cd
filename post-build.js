const fs = require('fs');
const path = require('path');

const copyDir = (src, dest) => {
  if (!fs.existsSync(src)) {
    console.warn(`Warning: Source folder "${src}" does not exist. Skipping.`);
    return;
  }

  fs.mkdirSync(dest, { recursive: true });
  fs.cpSync(src, dest, { recursive: true });
  console.log(`Copied ${src} → ${dest}`);
};

const rootDir = __dirname;
const standaloneDir = path.join(rootDir, '.next', 'standalone');

// Copy locales
copyDir(path.join(rootDir, 'locales'), path.join(standaloneDir, 'locales'));

// Copy public
copyDir(path.join(rootDir, 'public'), path.join(standaloneDir, 'public'));

// Copy static assets
copyDir(path.join(rootDir, '.next', 'static'), path.join(standaloneDir, '.next', 'static'));
