// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;
const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';

// Disable Sentry in development mode
if (IS_DEVELOPMENT) {
  // eslint-disable-next-line no-console
  console.info('Sentry is disabled in development mode.');
} else if (!SENTRY_DSN) {
  // eslint-disable-next-line no-console
  console.warn('NEXT_PUBLIC_SENTRY_DSN is not set. Error tracking will be disabled.');
}

// Only initialize Sentry in production
if (!IS_DEVELOPMENT && SENTRY_DSN && SENTRY_DSN?.length > 0) {
  Sentry.init({
    dsn: SENTRY_DSN,

    // Performance monitoring
    tracesSampleRate: 0.1,

    // Configure which requests to trace
    tracePropagationTargets: ['localhost', /^https:\/\/yourapi\.com\/api/],

    // Capture transactions for all network requests
    integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],

    // Replay settings
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,

    // Set environment
    environment: process.env.NODE_ENV,
  });
}
