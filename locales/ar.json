{"endSubscriptionNotice": "إنهاء الاشتراك نهائيا. يتم إبطال الوصول، وقد يتم حذف البيانات بعد فترة احتفاظ.", "requestedBy": "طل<PERSON> من قبل", "quickOrder": "طلب سريع", "lastUpdatedDate28052024": "تاريخ آخر تحديث: 28/05/2024", "users": "المستعملون", "bookmark": "الاشاره المرجعيه", "quoteInformation": "معلومات الاقتباس", "contractValue": "قيمة العقد", "transactionCurrencycurrencies": "عملة / عملات المعاملة *", "smith": "سميث", "total": "مجموع", "selectRegionStateOrProvince": "ح<PERSON><PERSON> المنطقة أو الولاية أو المقاطعة", "allAnnouncements": "جميع الإعلانات", "companyRegVatTaxId": "سجل الشركة / ضريبة القيمة المضافة / المعرف الضريبي", "enterZippostalCode": "أدخل الرمز البريدي / الرمز البريدي", "markAsUnread": "وضع علامة على أنه غير مقروء", "totalOrder": "إجمالي الطلب", "selectOrderPlacedBy": "حد<PERSON> الطلب الذي تم تقديمه بواسطة", "clone": "استنساخ", "outstandingInformation": "معلومات معلقة", "totalOutstanding": "إجمالي المعلقة", "purchaseInformation": "معلومات الشراء", "informationWidget": "أداة المعلومات", "contractInfo": "معلومات العقد", "renewal": "تجديد", "availableCreditLimit": "الحد الائتماني المتاح", "companyTaxId": "المعرف الضريبي للشركة", "streetAddress": "عنوان الشارع", "redingtonDisclaimer1": "علاوة على ذلك ، لا تطلب Redington أو تطلب وتقبل أي أموال مقابل أي مخططات في أي\n من المستثمرين وشركاء الأعمال ، سواء عبر الإنترنت أو غير ذلك. ريدنجتون\n لا يتحمل أي مسؤولية عن المبالغ التي يتم إيداعها / سحبها منها ردا على\n مثل هذه العروض المزيفة.", "notificationDate": "تاريخ الإخطار", "invoice2": "فاتورة #", "lastDays2": "آخر 15 يوما", "brandSetup": "إعداد العلامة التجارية", "downloadCsv": "تنزيل CSV", "supportedPlatformsHyperscalers": "المنصات المدعومة / Hyperscalers", "vendorSkuId2": "معرف SKU للمورد", "networkingConnectivity": "الشبكات / الاتصال", "category1": "الفئة 1", "open": "مفتوح", "pointOfContact": "نقطة الاتصال", "languageCurrencySettings": "إعدادات اللغة والعملة", "advancedSearch": "البحث المتقدم", "enterYearFound": "أدخل سنة العثور عليها", "usd20000000": "2,00,000.00 دولار أمريكي", "selectExistingSubscription": "ح<PERSON><PERSON> الاشتراك الحالي", "cartName": "اسم السلة", "toDate": "<PERSON><PERSON><PERSON> الآن", "lastName2": "اسم العائلة *", "upgradePlanNote": "انتقل إلى خطة/مستوى أعلى (على سبيل المثال، من Basic إلى Pro) مع المزيد من الميزات أو الدعم أو السعة", "subscriptionStartDate": "تاريخ بدء الاشتراك", "listOfUsers": "قائمة المستخدمين", "productivityCollaboration": "الإنتاجية والتعاون", "totalItems": "إجمالي العناصر", "info": "معلومات", "subscriptionLicenses": "الاشتراكات والتراخيص", "notDueUsd": "غير مستحق: USD 50,000.00", "addNewUser": "إضافة مستخدم جديد", "yesDelete": "نعم، حذف", "subscriptionDetail": "تفاصيل الاشتراك", "yearFounded": "سنة التأسيس", "contactRedingtonForAccount": "يرجى التواصل مع مدير حساب <PERSON>ington الخاص بك\n من سيساعدك في رقم حساب Redington الخاص بك", "or": "أو", "dateRedeemedTi": "تاريخ استرداد TI", "allMultiSelect": "الكل (تحديد متعدد)", "descriptionOfIssueRequest": "وصف القضية / الطلب", "invoice": "فاتورة", "quotations": "عروض الاسعار", "invoiceNumber": "رقم الفاتورة", "submitRequest": "تقديم طلب", "myTopCustomer": "أفضل 5 عملاء", "azurePlan": "خطة Azure", "validTill": "يسري العرض حتى", "invoiceBillDate": "تاريخ فاتورة الفاتورة", "fileDetails": "تفاصيل الملف", "fileName": "اسم الملف", "sales": "مبيعات", "accept": "تقبل", "termsOfSale": "شروط البيع", "redingtonCloudquarksPlatformUserAccountCreationTCs": "Redington CloudQuarks - شروط وأحكام إنشاء حساب مستخدم النظام الأساسي", "password": "شعار", "invoiceDetails": "تفاصيل الفاتورة", "accepted": "قبلت", "lastDays": "آخر 7 أيام", "billType": "نوع الفاتورة", "orderPlacement": "وضع الطلب", "agreements": "الاتفاقات", "languageSettings": "إعدادات اللغة", "brandName": "اسم العلامة التجارية", "cartInformation": "معلومات سلة التسوق", "checkEligibleBaseOffer": "تحقق من العرض الأساسي المؤهل", "endCustomerT1": "العميل النهائي T1", "tag236": "علامة 236", "netValue": "صافي القيمة", "verified": "التحقق!", "nextInvoiceDate": "تاريخ الفاتورة التالي", "companyProfiles": "ملفات الشركة", "notifications": "الإشعارات", "totalBilled": "إجمالي الفواتير", "primaryContact": "جهة الاتصال الأساسية", "preTaxTotal": "إجمالي ما قبل الضرائب", "cartTitle": "عنوان السلة", "allRecords": "جميع السجلات", "quantity7": "كم:", "doMoreWithMicrosoftPromo": "أنجز المزيد باستخدام Microsoft 365 E3 Promo", "order": "ترتيب#", "yes": "نعم", "azurePlanUsageAe3": "استخدام خطة Azure AE3", "profile": "ملف تعريف", "storageBackupDr": "التخزين / النسخ الاحتياطي / DR", "totalQuotes": "إجمالي الاقتباسات", "existing": "مو<PERSON><PERSON><PERSON>", "billedUsd": "الفاتورة: USD 100,000.00", "mergeWithExistingSubscription": "الدمج مع الاشتراك الحالي", "miniShop": "ميني شوب", "billingAddress": "عنوان الفواتير (1/2)", "lastUpdated": "آخر تحديث", "orderPrerequisiteNotice": "قبل تقديم طلب أو مراجعة طلب، يجب على شركتك الحصول على التزام شراء للعميل وتقديمه إلى Redington بناء على طلبها.", "selectTheCurrencyYouWantToPurchaseWith": "حد<PERSON> العملة التي تريد الشراء بها", "testReason": "سبب الاختبار ...", "redeemed": "افت<PERSON>ي", "addContinuePurchase": "إضافة ومتابعة الشراء", "teamReactive": "تفاعل الفريق", "homePurchaseCart": "عربة شراء > المنزل", "fraudulentOfferAlert": "هذا لإخطار عامة الناس بأن بعض الأشخاص عديمي الضمير غير مصرح لهم\n باستخدام اسم Redington Limited ؛ مجموعة Redington / الشركات التابعة (\"Redington\") والوقوف\n أنفسهم كموظفين وممثلين ووكلاء في Redington والشركات المرتبطة بها / المجموعة\n الشركات ، بدافع خفي لكسب مكاسب غير مشروعة و / أو خداع المحتملين\n المستثمرون وشركاء الأعمال والذين يقدمون فرصة استثمارية عن طريق الاحتيال\n عبر الإنترنت من خلال مواقع معينة أو من خلال المكالمات الهاتفية أو عن طريق إصدار خطابات عرض مزيفة\n وكذلك التماس منهم إيداع بعض المبلغ في حسابات مصرفية معينة. هؤلاء الناس\n كما تستخدم اسم Redington وعلامتها التجارية واسم المجال وشعارها بشكل غير مصرح به\n بهدف تشويه صورة وسمعة ريدنجتون.", "companyHexalytics": "شركة: Hexalytics", "innovateLimitedDetails": "ابتكار محدود / التفاصيل", "salesContactName": "اسم جهة اتصال المبيعات", "download": "تحميل", "type1": "النوع 1", "india": "الهند", "azure": "أسمانجوني", "dateOfPurchaseTu": "تاريخ الشراء TU", "reEnterPassword": "<PERSON><PERSON><PERSON> إ<PERSON><PERSON>ا<PERSON> كلمة المرور", "duplicate": "مكرره", "directPhone": "ها<PERSON><PERSON> م<PERSON>ر", "dynamicsCustomerServiceEnterpriseNonProfitPricing": "Dynamics 365 Customer Service Enterprise (التسعير غير الربحي)", "cloudLaasPaas": "سحابة لاس / PaaS", "expired": "انتهت", "knowledgeBase": "معارف", "knowledgeHub": "مركز المعرفة", "invoiceManagement": "إدارة الفواتير", "newRequest": "<PERSON><PERSON><PERSON> جديد", "commercialMarketplaceTest": "اختبار السوق التجاري", "basedOnTheSelectedDateRange": "استنادا إلى النطاق الزمني المحدد", "transactionAmount": "مب<PERSON>غ المعاملة", "legalActionWarning": "تحتفظ ريدنجتون بالحق في اتخاذ إجراءات قانونية، بما في ذلك الإجراءات الجنائية، ضد هؤلاء\n الأفراد / الكيانات", "innovateLimitedOrders": "ابت<PERSON>ار محدود", "totalInvoiceValueUsd": "إجمالي قيمة الفاتورة (بالدولار الأمريكي)", "overallStatus": "الوضع العام", "requestReason": "س<PERSON><PERSON> الطلب", "nickName2": "الاسم المستعار*", "cancel": "إلغاء الأمر", "creditLimitUtilization": "الحد الائتماني والاستخدام", "redeemedCoupons": "كوبونات مستردة", "consumed2": "استهلاك:", "dateCreated10052024": "تاريخ الإنشاء: 10/05/2024", "consumedUsd": "المستهلكة : USD 250,000.00", "cancelled": "الغاء", "addNewTicket": "إضافة تذكرة جديدة", "overdue": "المتاخره", "whatIsNewWithRedingtonCloudQuarks20": "ما الجديد في Redington Cloud Quarks\n 2.0?", "availableCredit": "الر<PERSON>ي<PERSON> المتاح", "createdOn": "تم الإنشاء في:", "approvedOn": "معت<PERSON><PERSON> على:", "available": "متوفر", "financePaymentTerms": "شروط التمويل / الدفع", "listOfDiscountCoupons": "قائمة كوبونات الخصم", "frequentlyAskedQuestionsFaqs": "الأسئلة المتكررة (FAQs)", "customerName": "اسم العميل", "pleaseSelectTheDropDownToUpgrade": "الرجاء تحديد القائمة المنسدلة إلى الترقية", "uae": "الامارات", "chargeStartDate": "تاريخ بدء الخصم", "partner": "شريك", "currencyFu": "العملة فو", "enterLegalEntityName": "أد<PERSON>ل اسم الكيان القانوني", "taxIdentificationNumber": "رقم التعريف الضريبي", "marketplace": "السوق", "reportTitle": "عنوان التقرير", "contactNumber": "رقم الاتصال", "backToHome": "العودة إلى المنزل", "googleWorkspaceBusinessPlus": "Google Workspace Business Plus", "assignedCreditLimitExisting": "الحد الائتماني المخصص (موجود)", "insights": "البصائر", "select2": "اخ<PERSON><PERSON>ر", "select3": "<PERSON><PERSON><PERSON><PERSON>", "pass": "د<PERSON><PERSON>", "quantity": "كم", "partnerRemarks": "ملاحظات الشريك", "timeStamp": "الطابع الزمني", "addGoToCart": "إضافة والانتقال إلى عربة التسوق", "unbilledCharges": "الرسوم غير المفوترة", "image": "صورة", "vendorSkuId": "معرف المورد التعريفي : DZH318Z09PGJ:0007", "resellerId": "معرف الموزع", "guestAccount": "حساب الضيف", "guyHawkinsDeliveredTheOrder": "قام جاي هوكينز بتسليم الطلب", "cloudquarksOnboarding": "CloudQuarks - الإعداد", "currencyLanguage": "العملة واللغة", "changeProductOrTermLengthOrBillFrequency": "تغيير مدة المنتج أو مدة الفصل الدراسي أو تكرار الفاتورة.", "accountManagementMyProfile": "إدارة الحساب > ملفي الشخصي", "redingtonGulfFze": "ريدنجتون الخليج م.م.ح", "nonAffiliatedOfferNotice": "لن تكون ريدنجتون مسؤولة أمام أي شخص يتصرف بناء على عرض لم يتم تقديمه بشكل مباشر من قبل\n ريدنجتون", "country": "بلد", "enterEmail": "أد<PERSON><PERSON> البريد الإلكتروني", "termsOfUse": "شروط الاستخدام", "subscriptionCount": "عد<PERSON> الاشتراكات*", "billingSummary": "ملخص الفواتير", "preliminaryValidations": "عمليات التحقق الأولية", "********": "31/12/2025", "dueDays": "أيام الاستحقاق", "cdpId": "معرف CDP", "website": "الموقع الإلكتروني", "totalOrderValue": "إجمالي قيمة الطلب", "quoteCreated": "تم إنشاء الاقتباس", "supportHubVideoAndHowTos1": "فيديو مركز الدعم وكيفية التحويل (1)", "openCart": "عربة مفتوحة", "alertsAnnouncements": "التنبيهات / الإعلانات", "yesReject": "نعم، رفض", "descriptionDetails": "الوصف/ التفاصيل", "orderId": "معر<PERSON> الطلب", "store": "دكان:", "netPrice": "السعر الصافي", "renewalManagementRenewing": "إدارة التجديد / التجديد", "lifeCycleManagement": "إدارة دورة الحياة", "validation": "التحقق", "userAccessAndPermission": "وصول المستخدم وإذناته", "nickName": "الاسم المستعار", "category04": "الفئة 04", "performedByUserSystemApi": "من قبل (المستخدم ، النظام ، واجهة برمجة التطبيقات)", "hexalytics": "سداسي", "selectBrandCategory": "حدد فئة العلامة التجارية", "listPrice": "قائمة الأسعار", "theFirstGroup": "المجموعة الأولى", "homePage": "صفحة رئيسية", "contractSchedule": "جدول العقد", "revisedAssignedCreditLimit": "المعدل - الح<PERSON> الائتماني المخصص", "createNewSubscription": "إنشاء اشتراك جديد", "email2": "الب<PERSON>يد الإلكتروني", "characterLimit50": "عد<PERSON> الأحر<PERSON> المسموح به: 50", "redingtonGroupAllRightsReserved": "مجموعة ريدنجتون | كل الحقوق محفوظة.", "1Year3": " 1 سنة", "confirmToProceed": "تأكيد للمتابعة", "role": "دور", "chargeStart": "بد<PERSON> الشحن", "reservedVmInstance": "مثيل VM المحجوز ، Cool_GRS_Data_Stored_10", "invoiceDescription": "وصف الفاتورة", "newSubscriptionQtyWillBe6": "الاكتتا<PERSON> الجديد الكمية ستكون ", "totalQuoteValue": "إجمالي قيمة التسعير", "purchaseCommitmentObligations": "التزامات التزام الشراء", "usd": "دولار", "invoicesOpen": "الفواتير - فتح", "addToNewCart": "أ<PERSON><PERSON> <PERSON><PERSON> السلة الجديدة", "endCustomerPurchaseCommitmentConfirmation": "تأكيد التزام الشراء للعميل النهائي", "wouldYouLikeToSubmitYourCreditLimit": "هل ترغب في تقديم الحد الائتماني الخاص بك", "ksa": "المملكة العربية السعودية", "order41400000429": "الطلب # 41400000429", "alex": "اليكس", "save": "<PERSON>ن<PERSON>ذ", "hp": "إتش بي", "discountValue": "قيمة الخصم", "orderValueByTop3BrandCategoryUsd": "قيمة الطلب حسب أفضل 3 فئات للعلامة التجارية (بالدولار الأمريكي)", "brandCategor": "تصنيف العلامة التجارية", "testTile": "اختب<PERSON>ر البلاط", "assignedCredit": "الرصيد المخصص", "totalOverdueByBrandUsd": "إجمالي التأخير حسب العلامة التجارية (دولار أمريكي)", "tax2": "ضريبة", "percentDiscount": "النسبة المئوية للخصم", "maxCharacters50Words": "الح<PERSON> الأقصى للأحرف: 50 كلمة", "subStatus": "الحالة الفرعية", "hello": "مرحبا!", "selectOrderType": "حدد نوع الطلب", "msAppsForEnterpriseDesc": "Microsoft 365 Apps for Enterprise عبارة عن مجموعة إنتاجية قائمة على الاشتراك مصممة للمؤسسات الكبيرة، وتقدم أحدث إصدارات تطبيقات Microsoft Office والأمان المحسن وميزات التعاون المستندة إلى السحابة.", "startDatel": "ابد<PERSON> datel", "markAsUnread2": "وضع علامة غير مقروءة", "chargeEnd": "نهاية الشحن", "salesOrders": "المبيعات > الطلبات", "initiate": "بادر", "medium": "متوسط", "accountNumberDefinition": "رقم حسا<PERSON> هو معرف فريد\n مقدمة من Redington لشركتك", "seeAllNotifications": "الاطلاع على جميع الإشعارات", "activity": "نشاط", "softwaredigitalProducts": "البرمجيات / المنتجات الرقمية", "listOfRecords": "قائمة السجلات", "discountDetails": "تفاصيل الخصم", "analytics": "تحليلات", "1Year": "1 سنة", "topProductsForUae": "أفضل المنتجات في الإمارات العربية المتحدة", "unbilledUsageValue": "قيمة الاستخدام غير المفوترة", "multiple": "ضعف", "enterEmailAddress": "أدخل عنوان البريد الإلكتروني-", "alertsNotifications": "التنبيهات / الإشعارات", "selectRequestedBy": "<PERSON><PERSON><PERSON> طلب من قبل", "minQty": "الح<PERSON> الأدنى للكمية", "approverRemarks": "ملاحظات الموافق", "announcementType": "نوع الإعلان", "totalConsumed": "إجمالي المستهلك", "aboutThisItem": "حول هذا البند", "homeSalesDiscountCoupons": "الصفحة الرئيسية > كوبونات خصم > المبيعات", "createdDate": "تاريخ الإنشاء", "expiryDate": "تاريخ انتهاء الصلاحية", "dateAccepted": "تاريخ القبول", "openTickets": "فتح التذاكر", "primaryContactTitle": "عنوان جهة الاتصال الأساسية", "requestPaymentOfAnyKindFromProspectiveInvestor": "طلب الدفع من أي نوع من المستثمر المحتمل", "requestReason4": "سب<PERSON> الطلب *", "emai": "إيماي", "showMore": "عرض المزيد...", "grouped": "تجميع", "orderManagement": "إدارة الطلبات", "currentMonth": "الشهر الحالي", "orderValueByTop3BrandCategoryUsd2": "قيمة الطلب حسب أعلى 3\n فئة العلامة التجارية (بالدولار الأمريكي)", "user": "مستخدم", "createdBy": "تم إنشاؤه بواسطة", "applyFilter": "تطبيق الفلتر", "ubscripionIdr": "معر<PERSON> ubscripion", "designation": "تعيين", "hexalyticsUae1000123597": "Hexalytics, الإمارات العربية المتحدة (1000123597)", "dynamicsSubscriptionUser": "اشتراك المستخدم الذي يتضمن Dynamics 365 Customer Service، إصدار Enterprise", "reportType2": "نوع التقرير *", "department": "قسم*", "getSupported": "احصل على الدعم", "subscriptionEndDate": "تاريخ انتهاء الاكتتاب", "theOnlySelfServicePlatformWithManagedServicesCapabilities": "منصة الخدمة الذاتية الوحيدة المزودة بإمكانيات الخدمات المدارة.", "signingAuthorityName": "اسم سلطة التوقيع", "productName": "اسم المنتج", "rejectQuote": "رفض الاقتباس", "couponId1101": "معرف القسيمة:1101", "manageCompanyProfile": "إدارة ملف الشركة", "discount": "خصم", "expiring": "انتهاء الصلاحية", "openCartsSelect": "عربات التسوق المفتوحة - حدد", "consumeCloudquarksApis": "استهلاك واجهات برمجة تطبيقات CloudQuarks", "primaryContactInformation": "معلومات الاتصال الأساسية", "supportHubDocumentAndHowTos1": "مستند مركز الدعم وكيفية القيام بذلك (1)", "unbilled": "غير مفوترة", "subscriptionUpdateRequestHasBeenSubmitted": "تم إرسال طلب تحديث الاشتراك.", "azureEntitlements": "استحقاقات Azure", "addNewEndCustomer": "إضافة عميل نهائي جديد", "metricScoreCards": "بطاقات نقاط متري", "back": "<PERSON><PERSON><PERSON>", "fullTimestamp": "طابع زمني كامل", "enterApnId": "أدخل معرف APN", "due": "سبب:", "rejected": "نبيذ", "addToCart": "<PERSON><PERSON><PERSON> السلة", "totalUnbilled": "إجمالي غير المفوترة", "priority": "أولوية", "agreementName": "اسم الاتفاقية", "audioDocument": "مستند صوتي", "openingBalance": "الرصيد الافتتاحي", "firstName2": "الاسم الأول*", "status": "حالة", "invoiceDate2": "تاريخ الفاتورة:", "selectBrand": "اختر العلامة التجارية", "minutesAgo": "منذ 3 دقائق", "billingScheduleUsd": "جدول الفوترة (بالدولار الأمريكي)", "lastName": "اسم العائلة", "resendMail": "إعادة إرسال البريد", "aws": "AWS", "enterLinkedinUrl": "أدخل عنوان URL ل Linkedin", "unauthorizedMonetaryDeal": "تفويض أي شخص بتحصيل الأموال أو الوصول إلى أي ترتيب نقدي مقابل أي فرصة عمل في ريدنجتون", "selectCustomerVertical": "<PERSON><PERSON><PERSON> العميل الرأسي", "serviceDesk": "مكتب الخدمة", "oracle": "اوراكل", "lastDays4": "آخر 60 يوما", "skuid": "سكويد", "optionsTerm": "الخيارات : المدة (2)", "redington": "ريدنجتون", "yourQuoteIdqhskw123IsSubmittedSuccessfully": "معرف عرض الأسعار الخاص بك:QHSKW123 تم إرساله بنجاح.", "listOfCustomer": "قائمة العملاء", "addNew": "<PERSON><PERSON><PERSON> ج<PERSON>", "productDetailedView": "عرض تفصيلي للمنتج", "convertCartToQuote": "تحويل السلة إلى اقتباس", "marketplaceProfile": "ملف تعريف السوق", "enterLastName": "أد<PERSON>ل اسم العائلة", "activityLog": "سجل النشاط", "fakeSolicitationWarning": "توصي ريدنجتون بشدة بأن المستثمرين المحتملين شركاء الأعمال يجب ألا يستجيبوا لمثل هذه الطلبات المزيفة بأي شكل من الأشكال", "googleWorkspaceCommitment": "التزام Google Workspace", "invoicesValueByBillType": "قيمة الفواتير حسب نوع الفاتورة", "vatCostChangeDisclaimer": "صافي قيمة الطلب المذكورة أعلاه شامل ضرائب القيمة المضافة (VAT) كما هو معمول به حاليا. سيؤدي أي تأثير إضافي على التكلفة ناتج عن التغيير في هيكل الضرائب المحلية إلى زيادة\n الأسعار لهذا الغرض في وقت الفوترة وسيتحمل الشريك ذلك على حساب الشريك", "doItYourselfOrGetItDoneByAnExpert": "افعلها بنفسك أو أنجزها خبير!", "address1": "العنوان1", "description": "وصف", "selectSector": "اختر القطاع", "agreement": "اتفاق", "outstanding": "المستحقات", "reportType": "نوع التقرير", "attachments": "المرفقات", "finopsCostManagement": "FinOps / إدارة التكاليف", "no": "لا", "financeOutstanding": "التمويل / المستحقات", "cartId3": "معرف السلة: CT100001", "serviceRequest": "طل<PERSON> الخدمة", "selectTheLanguageYouPreferForBrowsingThePlatform": "حد<PERSON> اللغة التي تفضلها لتصفح المنصة", "new": "الجديد", "purchaseInfo": "معلومات الشراء:", "authorizedSignatory": "المفوض بالتوقيع", "softwareUpgradeRequestHasBeenSubmitted": "تم إرسال طلب ترقية البرنامج.", "vat": "ضريبة القيمة المضافة ٪", "delphiConsultingLlc": "دلفي للاستشارات ذ.م.م", "uaeSharjah": "الإمارات العربية المتحدة - الشارقة", "redingtonOfferClarification": "تستفيد ريدنجتون من خدمات معينة من خلال وكالات الاستثمار المحترفة. حتى في\n في هذه الحالات ، يتم تقديم العروض دائما مباشرة من قبل Redington وليس من قبل أي أطراف ثالثة.", "complete": "كامل", "validTillDate": "صال<PERSON> حتى الآن", "countryOfBusiness": "<PERSON><PERSON><PERSON> العمل", "supportingDocument": "الوثيقة الداعمة", "endCustomerName": "اسم العميل النهائي", "invoiceDate": "تاريخ الفاتورة", "newPurchase": "عملية شراء جديدة", "addBrands": "إضافة العلامات التجارية", "requestType": "نوع الطلب", "innovateLimitedOrder***********": "ابتكا<PERSON> محدود / طلب # ***********", "dayAgo": "1 اليوم منذ", "expiringSubscriptionDays2": "انتهاء صلاحية الاشتراك 15 يوما", "format": "تنسيق", "pleaseWaitForTheRedingtonResponse": "يرجى انتظار ر<PERSON>", "itemDetails": "تفاصيل العنصر", "olderNotifications": "الإشعارات القديمة", "validUntil": "صا<PERSON><PERSON>تى", "turkey": "تركيا", "alli": "الكل أنا", "firstName": "الاسم الأول", "fileType": "نوع الملف", "primaryContactName": "اسم جهة الاتصال الأساسية", "enterNumber": "أ<PERSON><PERSON>ل الرقم", "currentWeek": "الأسبوع الحالي", "cloneTheOrder": "استنساخ الطلب", "enterCompanyRegvattaxId": "أدخل سجل الشركة / ضريبة القيمة المضافة / معرف الضريبة", "blockThisUser": "حظر هذا المستخدم؟", "addUser": "إضافة مستخدم", "writeTextHere": "اكتب نصا هنا", "orderPlacedOn": "تم تقديم الطلب على", "roductNam": "رودكت نام", "domainId": "معرف النطاق", "totalOrders": "إجمالي الطلبات", "fileDescription": "وصف الملف", "unbilledUsd": "غير المفوترة : USD 150,000.00", "monthlyAnnual": "• شهري سنوي", "azureRi": "أزور RI", "requestAQuote": "اطلب اقتباس", "usageBased": "على أساس الاستخدام", "saveChanges": "حفظ التغييرات", "expiringSubscriptionsDays": "انتهاء صلاحية الاشتراكات 7 أيام", "regionstateprovince": "المنطقة / الولاية / المقاطعة", "acceptedOn": "مقبول في", "paymentTerms": "شروط الدفع", "markAsRead": "وضع علامة كمقروءة", "favourites": "المفضلة", "availableUsd": "متاح: USD 250,000.00", "newCart": "عربة جديدة", "delete": "<PERSON><PERSON><PERSON>", "prev": "السابق", "downloadAsPdf": "تنزيل بصيغة PDF", "hourAgo": "منذ 1 ساعة", "lastNam": "لاست نام", "hexalyticsLlcUae": "HEXALYTICS ذ.م.م - الإمارات العربية المتحدة", "currency": "عملة", "items": "العناصر", "invoiceBillDate2": "تاريخ فاتورة الفاتورة *", "fieldsAreMandatory": "الحقول إلزامية", "englishAed": "الإنجليزية - درهم إماراتي", "allTime": "كل الوقت", "contractId": "معر<PERSON> العقد", "dateAndTime": "التاريخ والوقت", "chargeDescription": "وصف التهمة", "completeMatch": "المباراة الكاملة", "success": "نجاح", "enterCity": "أد<PERSON>ل المدينة", "detailedInformation": "معلومات مفصلة", "yesSuspend": "نعم، تعليق", "video": "فيديو", "currentPassword": "كلمة المرور الحالية", "expiringSubscriptionDays": "انتهاء صلاحية الاشتراك 7 أيام", "next": "م<PERSON><PERSON><PERSON>", "anyoneMakingAnyInvestmentBusinessAssociationOfferInReturnForMoneyOrWhoIsNotAuthorizedByRedington": "أي شخص يقدم أي عرض للاستثمار / جمعية الأعمال مقابل المال أو من هو\n غير مصرح به من قبل ريدنجتون", "uaeDubai": "الإمارات العربية المتحدة - دبي", "dateCreated100520244": "تاريخ الإنشاء: 10/05/2024|", "info4": "معلومات.", "brandCategory": "فئة العلامة التجارية", "paymentTerm": "مصطلح الدفع :", "off": "خصم", "accountManagement": "إدارة الحسابات", "billingDetails": "تفاصيل الفواتير", "contracts": "<PERSON><PERSON>و<PERSON>", "creditAvailable": "الرصيد متاح", "customerIdorganizationId": "معرف العميل/معرف المؤسسة", "resetToDefaultSettings": "إعادة التعيين إلى الإعدادات الافتراضية", "doNotRenew": "لا تجدد", "newPassword": "كلمة مرور جديدة", "integrateOwnApls": "دمج ملفات APls الخاصة", "signOut": "تسجيل الخروج", "alphaSystems": "أنظمة ألفا", "flexible": "مرن", "dueDate": "تاريخ الاستحقاق", "renewalManagementPending": "إدارة التجديد > معلقة", "email": "البريد الإلكتروني*", "goToExpiringList": "انتقل إلى قائمة انتهاء الصلاحية", "listPriceAfterPromo": "قائمة الأسعار بعد العرض الترويجي", "enterTheValue": "أد<PERSON>ل القيمة", "category05": "الفئة 05", "inReview": "قيد المراجعة", "linkedinSocialHandle": "لينكد إن / التعامل الاجتماعي", "search": "ب<PERSON><PERSON>", "enterCompanyName": "أد<PERSON>ل اسم الشركة", "resolutionRemarksFromTheSolutionArchitectServiceDeskAgent": "ملاحظات القرار من مهندس الحل / وكيل مكتب الخدمة", "selectTheCheckboxToViewTheEstimatedCharges": "ح<PERSON><PERSON> خانة الاختيار لعرض الرسوم المقدرة", "thefirstgrouponmicrosoftcom": "thefirstgroup.onmicrosoft.com", "totalUnbilledByBrandCategoryUsd": "إجمالي غير المفوترة حسب فئة العلامة التجارية (بالدولار الأمريكي)", "catalog": "كتالوج", "pleaseNote": "ير<PERSON>ى الملاحظة", "noLater": "لا ، لاحقا", "quotes": "# اقتباسات", "customerReadiness": "جاهزية العملاء", "enterMobileNumber": "أدخل رقم الجوال", "brand": "وسم", "yesTerminate": "نعم ، إنهاء", "orderPlacedBy": "الطلب المقدم من قبل", "helpCenter": "مركز المساعدة", "productDetails2": "تفاصيل المنتج", "approxSkus": "وحدات SKU تقريبا", "getOffOnOrdersAboveQuantity": "احصل على خصم 10٪ على الطلبات التي تزيد عن 5 كمية.", "basicInformation": "معلومات اساسية", "endCustomer": "العميل النهائي", "others": "الاخرين", "selectTimeZone": "ح<PERSON><PERSON> المنطقة الزمنية", "marketplaceSearch": "البحث في السوق", "supportSlaTiersOffered": "دعم مستويات اتفاقية مستوى الخدمة المقدمة", "customerManagementCustomers": "إدارة العملاء > العملاء", "testAudioFile0069": "اختبار ملف الصوت 0069", "softwareUpgradeRequest": "طلب ترقية البرنامج", "department2": "قسم", "weRequestYouToKindlyVisitOurOfficialCareersWebsiteHttpsRedingtongroupComForAuthenticInformationPertainingToBusinessAssociationOpportunityAtRedingtonAndEnquireWithTheCompanyToConfirmIfTheOffersOrPromotionsAreGenuine": "نطلب منك التكرم بزيارة موقعنا الرسمي للوظائف https://redingtongroup.com/for\n معلومات أصلية تتعلق بفرصة جمعية الأعمال في ريدنجتون و\n استفسر من الشركة للتأكد مما إذا كانت العروض أو العروض الترويجية حقيقية.", "financeCreditLimitUtilization": "التمويل / الحد الائتماني والاستخدام", "commercialMarketPlace": "السوق التجاري", "generateNewReport": "إنشاء تقرير جديد", "targetMarketsstores": "الأسواق / المتاجر المستهدفة", "tags2": "العلامات:", "link": "رابط", "redingtonDoesNot": "لا يقوم Redington بما يلي:", "block": "<PERSON><PERSON><PERSON>", "product": "حاصل الضرب", "microsoft365BusinessBasicCfq7ttcolh180001": "Microsoft 365 Business Basic | CFQ7TTCOLH18:0001", "totalContractsValue": "إجمالي قيمة العقود", "salePriceUnitTu": "سعر البيع / وحدة TU", "billFrequency": "تردد الفاتورة", "finance": "الشؤون المالية", "category06": "الفئة 06", "currencyTu": "العملة Tu", "outstandingUsd": "المبلغ القائم: 50,000.00 دولار أمريكي", "trainingCertification": "التدريب والشهادات", "microsoft365F1": "مايكروسوفت 365 F1", "amazon": "الامازون", "productDetails": "تفاصيل المنتج :", "decrease": "نقصان", "listOfContracts": "قائمة العقود", "wouldYouLikeToCloneThisOrderWithAllItsContentsPleaseConfirm": "هل ترغب في استنساخ هذا الطلب بجميع محتوياته ، يرجى التأكيد", "netAmount": "المبلغ الصافي", "quickBrands": "العلامات التجارية السريعة", "buyingOptions": "خيارات الشراء", "couponDetails": "تفاصيل الكوبون", "reportTile": "لوحة التقرير", "oderPlacedBy": "أودر وضعت من قبل", "unbilledUsage": "الاستخدام غير المفوترة", "aboutCompany": "عن الشركة", "totalOrdersUsd": "إجمالي الطلبات (بالدولار الأمريكي)", "monthly": "شهري", "qty": "الكميه", "default": "افتراضي", "subscriptionId": "معرف الاشتراك", "tenantName": "اسم المستأجر", "eventSpecificDataFields": "حقول البيانات الخاصة بالحدث", "testDescription": "وصف الاختبار ..", "aedUnitedArabEmiratesDirhamDefault": "AED - الدرهم الإماراتي (افتراضي)", "technicalEnablement": "التمكين الفني", "address2": "العنوان2", "promotionType": "نوع العرض الترويجي", "domair": "دومير", "orders": "اوامر", "subscriptions": "الاشتراكات", "pleaseNoteThatTheSubscriptionUpgradeCannotBeReversedOnceRequested": "يرجى ملاحظة أنه لا يمكن التراجع عن ترقية الاشتراك بمجرد الطلب.", "legalEntityName": "اسم الكيان القانوني", "orderPlacedBy2": "الطلب الذي تم تقديمه بواسطة", "home": "الصفحة الرئيسية", "createANewCart": "إنشاء عربة جديدة", "brandManagement": "إدارة العلامة التجارية", "redemptionDetails": "تفاصيل الاسترداد", "promotionId": "رقم الترويج", "hexalyticsLlcKsa": "هيكساليتيكس ذ.م.م-السعودية", "billingProfile": "ملف تعريف الفوترة (1/6)", "fromDate": "من التاريخ", "financeStatementOfAccounts": "المالية / كشف الحسابات", "renewalPrice": "سعر التجديد", "expiringSubscriptions": "انتهاء صلاحية الاشتراكات", "mobileNumber": "رقم الجوال", "enterWebsiteUrl": "أدخل عنوان الموقع الإلكتروني", "hexalyticsPvtLtd": "Hexalytics Pvt المحدودة", "myAccount": "حسابي", "acceptanceOfTerms": "قبول الشروط", "oneDescriptionAboutTheProductsAddedInTheCartAndOffers": "وصف وا<PERSON><PERSON> عن المنتجات المضافة في السلة والعروض", "terminate": "أنهى", "endDate": "تاريخ الانتهاء", "nA": "<PERSON>ير متاح", "education": "تعليم", "amount": "م<PERSON><PERSON><PERSON>", "ticketsInReview": "التذاكر قيد المراجعة", "lastUpdatedDate280520242": "تاريخ آخر تحديث : 28/05/2024", "chargeDetails": "تفاصيل الشحن", "read": "قرأ", "oldCreditLimitS": "الحد الائتماني القديم (S)", "platform": "رصيف", "listOfQuestions": "قائمة الأسئلة", "selectEndCustomer": "<PERSON><PERSON><PERSON> العميل النهائي", "increaseBy": "زيادة بمقدار", "destinationSubscription": "الاشتراك في الوجهة", "whatWillHappenToMyExistingTransactionsOrdersSubscriptionsAndEndUsers": "ماذا سيحدث لمعاملاتي الحالية،\n الطلبات والاشتراكات والمستخدمين النهائيين؟", "phoneNumber": "رقم الهاتف", "listOfReportsShowing": "قائمة التقارير التي تظهر", "skuId2": "رقم الصنف : DZH318Z009PGJ:0007", "sector": "قطاع", "personalInformation": "المعلومات الشخصية", "submittedOn": "تاريخ التقديم:", "microsoft365BusinessBasic": "Microsoft 365 Business Basic", "grandTotal": "المجموع الكلي", "selectRequestReason": "-- ح<PERSON><PERSON> سب<PERSON> الطلب -", "active": "نشط", "renewalManagementApproved": "إدارة التجديد / معتمد", "enterAccountId": "أد<PERSON>ل معرف الحساب", "partnerReadiness": "جاهزية الشركاء", "brands": "العلامات التجاريه", "agreementType": "نوع الاتفاقية", "innovateLimitedOrganizationTenants": "ابتكار محدود / منظمة ومستأجرين", "onboardNewCompany": "شركة جديدة على متن الطائرة", "close": "غلق", "upgrade": "ترقيه", "theEstimatedProrateCostIs224025Usd": "التكلفة التناسبية التقديرية هي 2,240.25 دولار أمريكي |", "services": "خدمات", "termsAndConditions": "الشروط والأحكام", "accountManagementBrandSetUp": "إدارة الحساب > إعد<PERSON> العلامة التجارية", "create": "خلق", "endDateAlignment": "محاذاة تاريخ الانتهاء", "enterMiddleName": "أد<PERSON>ل الاسم الأوسط", "invoicesByTop5Customers": "الفواتير من قبل أفضل 5 عملاء", "revisedCreditLimit": "الحد الائتماني المنقح ($)", "qualityTu": "جودة TU", "totalUnbilledUsd": "إجمالي غير المفوترة (بالدولار الأمريكي)", "reports": "التقارير", "invoiceValue": "قيمة الفاتورة", "totalReceivablesUsd": "إجمالي الذمم المدينة (بالدولار الأمريكي)", "createANewCartFromCoupon": "إنشاء عربة جديدة من الكوبون", "increase": "أكثر", "redingtonSalesContacts": "جهات اتصال مبيعات Redington", "creditManagement": "إدارة الائتمان", "requestCreditLimit": "<PERSON><PERSON><PERSON> <PERSON>د ائتماني", "companyName2": "اسم الشركة *", "viewRequest": "عر<PERSON> الطلب", "totalTickets": "إجمالي التذاكر", "suspend": "تعليق", "noOfProduc": "لا. من إنتاج", "offerDetails": "تفاصيل العرض", "invoiceDueDate": "تاريخ استحقاق الفاتورة", "vendorDisplayNameShownToBuyers": "اسم عرض المورد (يظهر للمشترين) *", "totalAssigned": "المجموع المخصص", "resolutionRemarks": "ملاحظات القرار", "redingtonAccountId": "معر<PERSON> ح<PERSON><PERSON><PERSON> *", "expiringSubscriptionsDays2": "انتهاء صلاحية الاشتراكات 15 يوما", "enterCartTitle": "أدخل عنوان سلة التسوق", "discountCoupons": "كوبونات الخصم", "permission": "إذن", "selectAny3": "<PERSON><PERSON><PERSON> 1", "getToKnowUs": "تعرف علينا", "regionStateOrProvince": "المنطقة أو الولاية أو المقاطعة", "subscriptionName": "اسم الاشتراك", "documentLastEmailedOn": "آخر بريد إلكتروني للمستند في", "use": "استخدام", "overdueAgeingUsd": "التأخر - الشيخوخة (دولار أمريكي)*", "searchByCartIdCartNameCreatedByCustomer": "البحث عن طريق معرف سلة التسوق / اسم عربة التسوق / تم إنشاؤه بواسطة / العميل", "requestedAssignedCreditLimit": "الح<PERSON> الائتماني المطلوب - المخصص", "enterRemarks": "أد<PERSON>ل ملاحظات", "lastDays3": "آخر 30 يوما", "ticket": "تذكرة #", "subTotal": "المجموع الفرعي", "faqs": "الأسئلة الشائعة", "billTypc": "بيل تيبيك", "wouldYouLikeToConvertThisCartToQuote": "هل ترغب في تحويل هذه العربة إلى عرض أسعار؟", "change": "تغيير", "descriptior": "واصف", "billTypeMonthlyYearly": "نوع الفاتورة : شهري ، سنوي", "targetPrice": "السعر المستهدف", "reject": "<PERSON><PERSON><PERSON>", "other": "آخر", "enterEmailAddress2": "أدخل عنوان البريد الإلكتروني", "approved": "معت<PERSON>د", "alerts": "التنبيهات", "robertFox": "روبرت فوكس", "renewalManagementExpired": "إدارة التجديد / منتهية الصلاحية", "pleaseConfirmToProceed": "يرجى التأكيد للمتابعة.", "announcements": "اعلانات", "dell": "ديل", "allFiles": "جميع الملفات", "zipPostalCode": "الرمز البريدي / الرمز البريدي", "enterFirstName": "أد<PERSON>ل الاسم الأول", "reportRequestDate": "تاريخ طلب التقرير", "r032AdvancePayment": "R032 الدفعة المقدمة", "requestedE": "المطلوب هاء", "uaeAbuDhabi": "الإمارات العربية المتحدة - أبوظبي", "category01": "الفئة 01", "totalValueUsd2": "القيمة الإجمالية: USD 500.00", "totalValueUsd": "القيمة الإجمالية: USD 50,000.00", "category": "باب", "signingAuthorityTitle": "عنوان سلطة التوقيع", "permittedCount": "العدد المسموح به", "solicitAnyInvestmentInSchemesFromFreeEmailServicesLikeGmailRediffMailYahooMailEtc": "اطلب أي استثمار في المخططات من خدمات البريد الإلكتروني المجانية مثل Gmail وبريد Rediff وبريد Yahoo وما إلى ذلك", "fileCategory": "فئة الملف", "edit": "<PERSON><PERSON><PERSON>", "endDate2": "تاريخ الانتهاء", "selectStatus": "<PERSON><PERSON><PERSON> الحالة", "onboarding": "الإعداد", "netOrderValueQuotedAboveIsInclusiveOfValueAddedTaxesVatAsCurrentlyApplicableAnyAdditionalCostImplicationArisingFromChangeInLocalTaxesStructureWillLeadToIncreaseInPricesToThatEffectAtTheTimeOfBillingAndThisWillBeBorneByThePartnerAndOnPartnersAccount": "صافي قيمة الطلب المذكورة أعلاه شامل ضرائب القيمة المضافة (VAT) كما هو معمول به حاليا. سيؤدي أي تأثير إضافي على التكلفة ناتج عن التغيير في هيكل الضرائب المحلية إلى زيادة الأسعار لهذا الغرض في وقت الفوترة وسيتحمل ذلك الشريك وعلى حساب الشريك", "offerId": "معر<PERSON> العرض", "segmentCommercial": "الشريحة : تجاري", "dynamicsCustomerServiceEnterpriseDevice": "جهاز Dynamics 365 Customer Service Enterprise", "renew": "ج<PERSON><PERSON>", "orderDetails": "تفاصيل الطلب", "startDate": "تاريخ البدء", "faq": "الأسئلة المتداولة", "specify": "تحديد", "upcomingExpiration": "انتهاء الصلاحية القادم", "tagsProduct": "العلامات: المنتج", "paynowAzure": "Paynow Azure", "yesConfirm": "نعم، أكد", "rfq1001": "RFQ1001", "all": "كل", "listPriceUnit": "قائمة الأسعار / الوحدة", "consumed": "استهلاك", "declined": "انخفض", "contractValueTotal": "إجمالي قيمة العقد", "renewalManagementExpiring": "إدارة التجديد / انتهاء الصلاحية", "theServicesThatRedingtonProvidesToYouAreSubjectToTheFollowingTermsOfUseTouRedingtonReservesTheRightToUpdateAndModifyTheTouAtAnyTimeWithoutNoticeToYouTheMostCurrentVersionOfTheTouCanBeReviewedByClickingOnTheTermsOfUseHyperlinkLocatedAtTheBottomOfOurWebpagesWhenWeMakeUpdatesToTheTouRedingtonWillUpdateTheDateAtTheTopOfThisPageByUsingTheWebsiteAfterANewVersionOfTheTouHasBeenPostedYouAgreeToTheTermsOfSuchNewVersion": "تخضع الخدمات التي تقدمها لك Redington لشروط الاستخدام التالية\n (\"TOU\"). تحتفظ Redington بالحق في تحديث وتعديل شروط الاستخدام في أي وقت دون\n إشعار لك. يمكن مراجعة أحدث إصدار من شروط الاستخدام بالنقر فوق الرمز\n الارتباط التشعبي \"شروط الاستخدام\" الموجود أسفل صفحات الويب الخاصة بنا. عندما نجري التحديثات\n إلى شروط الاستخدام ، ستقوم Redington بتحديث التاريخ الموجود أعلى هذه الصفحة. باستخدام الموقع\n بعد نشر إصدار جديد من شروط الاستخدام، فإنك توافق على شروط هذه الشروط الجديدة\n الإصدار.", "settings": "الإعدادات", "redingtonAccountNumberIsADigitNumericValueExampleWhichIsTaggedWithSegmentCloudServices": "رقم حسا<PERSON>ington هو قيمة رقمية مكونة من 10 أرقام\n (مثال **********) الذي تم وضع علامة عليه بشريحة\n \"الخدمات السحابية", "productType": "نوع المنتج", "companyProfile": "ملف الشركة", "albertFloresDeliveredTheOrder": "قام ألبرت فلوريس بتسليم الطلب", "myCompanyS": "شركتي", "term": "الفصل الدراسي", "segment": "الجزء", "closingBalanceOutstanding": "الرصيد الختامي المستحق", "tax": "الضريبة (5.00٪) :", "usd500000000": "5,000,000.00 دولار أمريكي |", "temporarilyDisableTheSubscriptionWithoutTerminatingItAccessIsBlockedButDataIsRetainedOftenUsedForSeasonalNeeds": "قم بتعطيل الاشتراك مؤقتا دون إنهائه. تم حظر الوصول ولكن يتم الاحتفاظ بالبيانات. غالبا ما تستخدم للاحتياجات الموسمية.", "showingRecords": "عرض 10/100 سجل", "addMoreUsersStorageFeaturesOrOtherResourcesUnderTheCurrentSubscriptionOftenInvolvesAdditionalCost": "أضف المزيد من المستخدمين أو التخزين أو الميزات أو الموارد الأخرى ضمن الاشتراك الحالي. غالبا ما ينطوي على تكلفة إضافية.", "myAccessAndPermissions": "وصولي وأذوناتي", "decreaseBy": "انخفاض بمقدار", "pending": "المعلقه", "commercial": "تجاري", "comment": "التعليق", "activityType": "نوع النشاط", "renewing": "التجديد", "selectTheCurrencyYouWantToShopWith": "حد<PERSON> العملة التي تريد التسوق بها", "monthlyYearly": "شهري ، سنوي", "newNotifications": "إشعارات جديدة", "requestForRenewalPrice": "طل<PERSON> تجديد السعر", "requestedAssignedCreditLimit2": "مطلوب - حد ائتماني مخصص *", "view": "منظر", "provisioningId": "معرف التوفير:", "kuwait": "الكويت", "enterNickName": "أد<PERSON>ل الاسم المستعار", "action": "فعل", "totalCoupons": "إجمالي الكوبونات", "ndaExecutedWithRedington": "تم تنفيذ اتفاقية عدم الإفشاء مع ريدنجتون؟", "minQuality": "الحد الأدنى للجودة", "unitPrice": "سعر الوحدة", "perpetualLicence": "رخصة دائمة", "orderReview": "مراجعة الطلب", "companyName": "اسم الشركة", "notificationType": "نوع الإخطار", "skuId": "رقم الصنف : DZH318Z09PGJ:0007", "1Month": "• 1 شهر", "ifYouAreRegisteredPartnerWithRedingtonPleaseEnterYourRedingtonAccountNumberAndSelectTheCountry": "إذا كنت شريكا مسجلا في Redington ، فيرجى إدخال حساب Redington الخاص بك\n رقم البلد وتحديده", "subscriptionTop5Customers": "اكتتاب-\n أف<PERSON><PERSON> 5 عملاء *", "cameronWilliamsonDeliveredTheOrder": "سلم كاميرون ويليامسون الطلب", "revisionRequestPleaseConfirm": "طلب المراجعة ، يرجى التأكيد", "showBookmarkedFile": "إظهار ملف المرجعية", "admin": "المشرف", "forSelectedPeriod": "للفترة المحددة", "creditManagementUsd": "إدارة الائتمان (بالدولار الأمريكي)", "hexalyticsLtd": "Hexalytics المحدودة", "financeUnbilledCharges": "التمويل / الرسوم غير المفوترة", "backToNotification": "العودة إلى الإشعار", "reportRequestId": "معرف طلب الإبلاغ", "cartDetails": "تفاصيل سلة التسوق", "type": "نوع", "orderPlaced": "تم تقديم الطلب", "renewalStatus": "حالة التجديد :", "viewPdf": "عرض ملف PDF", "announcement": "إعلان", "reduceTheNumberOfUsersFeaturesOrResourceUsageMayLowerCostsButOftenRestrictedToRenewalPeriods": "تقليل عدد المستخدمين أو الميزات أو استخدام الموارد. قد يخفض التكاليف ، ولكنه غالبا ما يقتصر على فترات التجديد.", "registeredBusinessAddress": "عنوان العمل المسجل", "enterPhoneNumber": "أدخل رقم الهاتف", "profilePicture": "صورة الملف الشخصي", "ipAddress": "عنوان IP", "select": "-ا<PERSON>تار-", "quoteSummary": "ملخص الاقتباس", "tag123": "العلامة 123", "customerId": "معر<PERSON> العميل", "billing": "الفواتير", "streetAddress2": "عنوان الشارع 2", "unredeemedCoupons": "كوبونات غير مستردة", "companyInformation": "معلومات الشركة", "moreInfo": "المزيد", "couponId": "معرف القسيمة", "amazonWebServices": "خدمات أمازون ويب", "requestedCreditLimitS": "الحد الائتماني المطلوب (S)", "base": "قاعدة", "subscriptionTop5Customers2": "الاشتراك - أفضل 5 عملاء*", "ticketCategory": "فئة التذكرة", "balance": "توازن", "accountManagementFinance": "إدارة الحسابات > المالية", "enterName": "أ<PERSON><PERSON><PERSON> الاسم", "willSmithOrderedFourItemsWithATotalOrderValueOf": "طلب ويل سميث أربعة عناصر ، بقيمة إجمالية للطلب تبلغ 1,210 دولارات", "update": "تحديث", "options": "خيارات", "addToExistingCart": "أ<PERSON><PERSON> <PERSON><PERSON> السلة الحالية", "customerDetails": "تفاصيل العميل:", "additionalInformation": "معلومات إضافية", "globalOther": "عالمي / أخرى", "details": "التفاصيل", "invoiceValueUsd": "قيمة الفاتورة (بالدولار الأمريكي)", "mostSearchedQuestions": "الأسئلة الأكثر بحثا", "renewalManagement": "إدارة التجديد", "creditCheck": "التحقق من الائتمان", "groupedProducts": "المنتجات المجمعة", "dueDate3": "تاريخ الاستحقاق:", "chooseAnEligibleUpgradeForYourSubscription": "اختيار ترقية مؤهلة لاشتراكك", "enterDisplayNumber": "أدخل رقم العرض", "chargeTyp": "نوع الشحن", "standardCreditTermsDays": "شروط الرصيد القياسية (أيام)", "enterTaxIdentificationNumber": "أدخل رقم التعريف الضريبي", "value": "قيمة", "enterAddress": "أ<PERSON><PERSON><PERSON> العنوان", "manager": "مدير", "orderValueByTop5EndCustomerUsd": "قيمة الطلب حسب أفضل 5 عملاء نهائيين (بالدولار الأمريكي)", "folderOne4": "المج<PERSON>د الأول (4)", "discountedPriceUnit": "سعر مخفض / وحدة", "submit": "إرسال", "selectDepartment": "<PERSON><PERSON><PERSON> القسم", "doYouWantToMakeChangesInThisSubscriptionDuringRenewal": "هل تريد إجراء تغييرات في هذا الاشتراك أثناء التجديد؟", "helpSupport": "المساعدة والدعم", "generateRenort": "تو<PERSON><PERSON><PERSON>", "totalUnbilledByChargeTypeUsd": "إجمالي الفوترة غير المفوترة حسب نوع الرسوم (بالدولار الأمريكي)*", "privacyPolicy": "سياسة الخصوصية", "thePremiumOfficeSuiteForOrganizationsIncludingWordExcelPowerpointOutlookOnenoteAccessAndSkypeForBusiness": "مجموعة Office المميزة للمؤسسات\n  - بما في ذلك Word وExcel وPowerPoint وOutlook وOneNote \n وAccess وSkype for Business", "continueShopping": "استمر في التسوق", "currencySettings": "إعدادات العملة", "subscriptionInformation": "معلومات الاشتراك", "dubai": "دبي", "asPerTheOrderDateRangeSelected": "وفقا لنطاق تاريخ الطلب المحدد", "manualFulfilment": "الوفاء اليدوي", "chargeEndDate": "تاريخ انتهاء الخصم", "price": "ثمن", "selectSegment": "<PERSON><PERSON><PERSON> الشريحة", "transactionDate": "تاريخ المعاملة", "dynamicsCustomerServiceEnterpriseEducationFacultyPricing": "Dynamics 365 Customer Service Enterprise (تسعير أعضاء هيئة التدريس في التعليم)", "tags": "العلامات", "myProfile": "ملفي الشخصي", "emailAddress": "عنوان البريد الإلكتروني", "editProfile": "تحرير الملف الشخصي", "remove": "<PERSON><PERSON><PERSON><PERSON>", "clickToUploadOrDragAndDrop": "انقر للتحميل أو السحب والإفلات", "editProfileDetails": "تعديل تفاصيل الملف الشخصي", "company": "الشركة", "address": "عنوان", "accountName": "اسم الحساب", "contact": "الاتصال", "RedingtonAccountNumberisa10digitnumericvalue(example**********)whichistaggedwithsegmentCloudServices": "رقم حسا<PERSON>ington هو قيمة رقمية مكونة من 10 أرقام (مثال **********) والتي يتم تمييزها بمقطع الخدمات السحابية", "PleasereachouttoyourRedingtonAccountManagerwhowillassistyouwithyourRedingtonAccountNumber": "يرجى التواصل مع مدير حساب <PERSON>ington الخاص بك والذي سيساعدك في رقم حساب Redington الخاص بك", "accountInformation": "معلومات الحساب", "guestAccountInformation": "معلومات الحساب المكتبي", "ReviewyourguestregistrationdetailsbelowanddownloadthePDFofyourregistrationdata": "راجع تفاصيل تسجيل الضيف أدناه وقم بتنزيل ملف PDF الخاص ببيانات التسجيل الخاصة بك.", "redingtonACNo": "رقم مكيف ريدنجتون", "companyCode": "ر<PERSON>ز الشركة", "itemPerPage": "العنصر لكل صفحة", "invoices": "الفواتير", "myCompany": "شركتي", "statementOfAccounts": "كشف الحسابات", "asOndate": "كما هو الحال في التاريخ", "notDue": "غير مستحق", "requestId": "معر<PERSON> الطلب", "requestedDate": "التاريخ المطلوب", "filter": "راووق", "billed": " وصفت", "creditLimitRevisionNewRequest": "مراجعة الحد الائتماني - طل<PERSON> جديد", "debt": "دين", "credit": "تسليف", "documentNo": "رقم الوثيقة", "referenceNo": "رقم المرجع", "customers": "الزبائن", "documentDate": "تاريخ المستند", "vendor": "بائع", "google": "جوجل", "microsoft": " مايكروسوفت", "information": "معلومات", "seatBased": "م<PERSON><PERSON><PERSON> قائم", "sku": "سكو", "chargeType": "نوع الشحن", "acceptedBy": "مق<PERSON>و<PERSON> من قبل", "usageCost": "الاستخدام والتكلفة", "brandSetUp": "إعداد العلامة التجارية", "enterAPNID": "أدخل معرف APN", "apnText": "معرف APN هو معرف شبكة شركاء AWS يتم إنشاؤه بواسطة كل شريك للتعامل مع AWS. هذا نشاط لمرة واحدة ولا توجد فترة انتهاء صلاحية. سيوفر معرف APN الوصول إلى تحميل الفرص والتدريب والاعتمادZA", "allBrands": "جميع الماركات", "couponCode": "ر<PERSON>ز القسيمة", "dateCreated": "تاريخ الإنشاء", "noOfProducts": "لا. من إنتاج", "usageType": "نوع الاستخدام", "redeemedCount": "الع<PERSON> المسترد", "asOnDate": "كما هو الحال في التاريخ", "unRedeemedCoupons": "كوبونات غير مستردة", "clearAll": "<PERSON><PERSON><PERSON> ال<PERSON>", "SKU": "سكو", "dateRedeemed": "تاريخ استرداد ", "addNewCart": "إضافة عربة جديدة", "googleCloud": "سحابة جوجل", "microsoftCSP": "مايكروسوفت CSP", "awaitingAction": "في انتظار اتخاذ إجراء", "quotesByStatus": "اقتباسات حسب الحالة", "valid": "<PERSON><PERSON>", "countOfQuotes": "<PERSON><PERSON><PERSON> الاقتباسات", "canceled": "قيد المراجعة", "in-review": "قيد المراجعة", "createNewQuote": "إنشاء عرض أسعار جديد", "STEPONE": "الخطوة الأولى", "STEPTWO": "الخطوة الثانية", "quoteInstructions1": "من السوق، حدد المنتجات وأضفها إلى عربة التسوق الحالية أو أنشئ عربة جديدة", "quoteInstructions2": "قم بتحويل عربة التسوق التي تم إنشاؤها مع منتجاتها إلى عرض أسعار وإرسال عرض الأسعار للموافقة عليها", "quoteID": " معرف الاقتباس", "cartID": "معرف سلة التسوق", "initiatedBy": "بمبادرة من", "totalValue": "القيمة الإجمالية", "selectFromDate": "اختر من التاريخ", "selectToDate": "<PERSON><PERSON><PERSON><PERSON> التاريخ", "selectInitiatedby": "حدد بدأه بواسطة", "selectCreatedBy": "حدد تم إنشاؤه بواسطة", "requestForQuotation": "طلب عرض سعر", "endCustomerInformation": "معلومات العميل النهائي", "USD": "(دولار)", "reSubmit": "إعادة تقديم", "remarkByPartner": "ملاحظة من الشريك", "approvedQuantity": "الكمية المعتمدة", "approvedPrice": "السعر المعتمد", "cancelQuote": "إلغاء الاقتباس", "max50Characters": "ح<PERSON> 50 حرفًا", "cancelInstructions": "هل ترغب في إلغاء هذا الاقتباس؟ لا يمكن عكس هذا الإجراء، يرجى التأكيد للمتابعة.", "count": "العدد", "fulfilled": "تم الوفاء", "failed": "فشل", "orderValueByTop3": "قيمة الطلب من أعلى 3", "bandCategory(USD)": "فئة العلامة التجارية (بالدولار الأمريكي)", "orderValueByTop5": "قيمة الطلب من أعلى 5", "endCustomer(USD)": "العميل النهائي (دولار)", "csp": "CSP", "googlePer": "جوجل لكل..", "orderValue": "قيمة الطلب", "orderType": "نوع الطلب", "orderDate": "تاريخ الطلب", "printOrder": "طلب طباعة", "created": "انشاء", "billFrom": "فاتورة من", "VATTRN": "ضريبة القيمة المضافة", "billTo": "فاتورة إلى", "TEL": "تل", "redingtonAccountNumber": "رق<PERSON> ح<PERSON><PERSON><PERSON>", "serviceRecipient": "متلقي الخدمة", "organizationTenant": "التنظيم والمستأجرين", "tenantID": "معرف المستأجر", "reference": "مرجع", "disclaimer": "اخلاء المسؤوليه", "subtotal": " المجموع الفرعي", "estimatedVAT": "ضريبة القيمة المضافة المقدرة", "listOfOrders": "قائمة الطلبات", "QTY": "الكميه", "VAT": "ضريبة القيمة المضافة ٪", "doc": "دكتور.", "itemsOrdered": "العناصر المطلوبة", "lpoReference": "مرجع LPO", "attachedDocument": "المستند المرفق", "languageCurrency": "اللغة والعملة", "selectThelanguage": "حد<PERSON> اللغة التي تفضلها لتصفح المدخل", "announcementsType": "نوع الإعلانات", "general": "عام", "microsoftAIInnovation": "الذكاء الاصطناعي من Microsoft يشعل ابتكارات الاتصالات", "andGrowth": "والنمو", "allNotifications": "جميع الإشعارات", "selectLanguage": "", "deleteCart": "Delete Cart (s)", "wouldYouLikeToDeleteTheelectedCartYourActionCannotBeReversed": "Would you like to delete the selected cart(s), your action cannot be reversed, please confirm?", "manageCart": "إدارة عربة التسوق", "checkOut": "Check Out", "cartSummary": "ملخص سلة التسوق", "discounts": "خصم", "continuePurchase": "استمر في الشراء", "optional": "اختياري", "itemCovered": "العنصر مغطى", "expiringOn": "ينتهي في", "organizationTenants": "التنظيم والمستأجرين", "endCustomerDetails": "تفاصيل العميل النهائي", "sampleCartName": "Sample Cart Name", "more": "المزيد", "secondaryValidations": "Secondary Validations", "generalT&Cs": "الشروط والأحكام العامة", "vendorT&C": "شروط وأحكام البائعين", "deleteAll": "<PERSON><PERSON><PERSON> ال", "selectEligibleOffers": "اختر العروض المؤهلة", "selectAlternateOffers": "اختر العروض البديلة", "selectAvailablePromotions": "اختر العروض المتاحة", "eligibleOffers": "العروض المؤهلة", "alternateOffers": "العروض البديلة", "appsheetEnterprisePlus(Additional Service)": "Appsheet Enterprise Plus (Additional Service)", "viewMoreDetails": "عرض المزيد من التفاصيل", "offerEndson": "تنتهي عروض", "applyOffer": "تطبيق العرض", "annualPlan(MonthlyPayment)": "Annual Plan (Monthly Payment)", "AnnualPlan(YearlyPayment)": "Annual Plan (Yearly Payment)", "discountIncluded": "الخصم مدرج", "relatedProducts": "المنتجات المرتبطة", "crosssellProducts": "منتجات التحويل", "upsellProducts": "منتجات الترقية", "creationDate": "تاريخ الإنشاء", "commitmentEnd": "نهاية الالتزام", "commitmentStart": "بداية الالتزام", "cancellationUntil": "الإلغاء حتى", "bysubmittinganorderororderrevisionYourCompanyrepresentsthatanyCustomerPurchaseCommitmentprovidediscompleteandaccurateinallrespectsandagreestopayRedingtonforallordersitsubmitsforProducts": "من خلال تقديم طلب أو مراجعة طلب ، تقر شركتك بأن أي عميل التزام الشراء المقدم كامل ودقيق من جميع النواحي ويوافق على ادفع لشركة Redington مقابل جميع الطلبات التي تقدمها للمنتجات", "purchaseCommitmentCheck": "نؤكد بموجب هذا أننا تلقينا التزام الشراء للعميل النهائي لهذا الأمر أو مراجعة الطلب ويجب تقديمه عند الطلب.", "subscriptionTerminate": "الاشتراك - إنهاء", "generaltncs": "General T&Cs", "viewMore": "View More", "vendortncs": "Vendor T&Cs", "youCanCoterminateYourSubscriptionWithAnExistingNonTrialCspNceSubscriptionOrAlignTheEndDateWithTheCalendarMonthByChoosingAnAppropriateEndDateDependingOnTheTermDuration": "يمكنك إنهاء اشتراكك مع اشتراك CSP NCE موجود غير تجريبي أو محاذاة تاريخ الانتهاء مع الشهر التقويمي عن طريق اختيار تاريخ انتهاء مناسب اعتمادا على مدة المدة.", "renevalSuccessMsg": "Your Request for Renewal Price Successfully Submitted!", "headline": "تحويل عملك باستخدام CloudQuarks", "subheadline": "هناك العديد من الطرق التي يمكن أن يتشكل بها النمو. Redington هي موزع رائد للتكنولوجيا في طليعة التطورات السحابية وAl.", "signInTitle": "قم بتسجيل الدخول للمتابعة باستخدام CloudQuarks Portal", "emailLabel": "عنوان البريد الإلكتروني", "enterEmailPlaceHolder": "أدخل عنوان بريدك الإلكتروني", "rememberMe": "تذكرني", "forgotPassword": "هل نسيت كلمة المرور؟", "signInButton": "تسجيل الدخول", "orContinueWith": "أو تابع مع", "copyright": " مجموعة ريدنجتون 2025©", "designedBy": " تصميم وتطوير Hexalytics", "allRightsReserved": " كل الحقوق محفوظة.", "customerManagement": "إدارة العملاء", "manageQuickWidgets": "إدارة الأدوات السريعة", "languageEnglish": "English - EN", "languageArabic": "العربية - AR", "mea": "الشرق الأوسط وأفريقيا", "nonRegisteredPartners": "Non Registered Partners", "registeredPartnerDescription": "Would you like to register your company and become a part of Redington's Partner Ecosystem?", "proceedRegistrationButton": "Proceed to Registration", "getOnboarded": "احصل على الإعداد", "apply": "طبق", "reset": "اعاده تعيين", "goodDay": "يوم سعيد!", "welcomeTitle1": "مرحبا بكم في ", "welcomeTitle2": "Redington Cloud Quarks", "platformMessage": "منصة واحدة لتحويل أعمالك", "callToAction": "+3,213 شريكا تم إعدادهم بالفعل. لماذا ما زلت تنتظر.", "callToActionLine2": "احصل على الإعداد الآن ، لتجربة ...", "watchVideo": "شا<PERSON><PERSON> الفيديو", "getOnboardedNow": "احصل على الإعداد الآن", "nextStep": "بعد ذلك ، دعنا نجعل شركتك في CloudQuarks", "stepOne": "الخطوة الأولى", "accountActiveMessage1": "حسابك نشط.", "accountActiveMessage2": "مرحبًا بك!", "stepTwo": "الخطوة الثانية", "businessInformation": "معلومات العمل", "shareholdingDetails": "تقديم نمط حيازة الأسهم وتفاصيل الاتصال الرئيسية - المدير / المالك والمالية والمفوض بالتوقيع", "stepThree": "الخطوة الثالثة", "shareDocuments": "مشاركة المستندات", "shareDocumentsDescription": "تحميل مستندات الرخصة التجارية / الموزع ، والإقرارات الضريبية كشوف الحسابات المصرفية للمراجعة", "stepFour": "الخطوة الرابعة", "checkCompliance": "التحقق من الامتثال", "checkComplianceDescription": "توفير سنة التأسيس والقبول في شروط وأحكام Redington", "stepFive": "الخطوة الخامسة", "brandCategoryDescription": "ح<PERSON><PERSON> علامتك التجارية المفضلة وفئات علامتك التجارية", "majorVendors": "Major <PERSON><PERSON><PERSON>", "totalCustomers": "إجمالي العملاء", "openCarts": "العربات المفتوحة", "overdueInvoices": "الفواتير المتأخرة", "quickLinks": "روابط سريعة", "selectAny5": "(ح<PERSON><PERSON> أي 5)", "redAlIntro": "التعريف Red.Al مستقبل الابتكار مع التميز التوليدي", "knowMore": "اعرف المزيد", "cartId": "معرف سلة التسوق", "simmonsOn": "سيمونز في", "quoteId": "إجمالي العناصر", "expiresAt": "تنتهي الصلاحية في ", "recentPurchases": "المشتريات الأخيرة", "last7Days": "آخر 7 أيام", "last15Days": "آخر 15 يوما", "last30Days": "آخر 30 يوما", "last60Days": "آخر 60 يوما", "quality": "جودة ", "salePricePerUnit": "سعر البيع / وحدة", "dateOfPurchase": "تاريخ الشراء", "myTop5Products": "كل الوقت", "ytd": "YTD", "units": "Units", "productsWithDiscountCoupon": "المنتجات مع كوبون خصم", "minQuantity": "الحد الأدنى للجودة", "assigned": "تعيين", "quoteManagement": "إدارة الاقتباس", "skuIdLabel": "رقم الصنف", "discountCoupon": "كوبونات الخصم", "promotions": "الترقيات", "selectTheCurrencyWith": "حد<PERSON> العملة التي تريد الشراء بها", "contactAddress": "1 ، شارع الشيخ زايد ، فندق H ، برج الأعمال - دبي ، الإمارات العربية المتحدة", "EligibleBaseOffers": "العروض الأساسية المؤهلة", "annual": "سنوي", "unitedArabEmirates": "الإمارات العربية المتحدة", "singapore": "سنغافورة", "organizationAndTenants": "التنظيم والمستأجرين", "innovateFirstInnovateLast": "(ابتكر أولا ابتكر أخيرا)", "subscriptionDetails": "تفاصيل الاشتراك", "innovateItd": "Innovate ITD", "startsOn": "تاريخ البدء", "expiresOn": "تاريخ الانتهاء", "orderStatus": "حالة الطلب", "deleteLineItem": "Delete Line Item", "listofSubscriptions": "قائمة الاشتراكات", "BasedOnTheCreateDateSelectedDateRange": "استنادا إلى النطاق الزمني المحدد لإنشاء التاريخ", "subscriptionValue": "Subscription Value", "createDate": "إنشاء تاريخ", "productId": "معر<PERSON> المنتج", "direct": "مبا<PERSON>ر", "productListing": "قائمة المنتجات", "searchProductNameSkuID": "Product Search by Product Name/SKU ID", "appliedFilters": "المرشحات المطبقة", "categories": "Categories", "pricing": "pricing", "exploreProductsbyCategories": "Explore Products by Categories", "infrastructureServices": "Infrastructure Services", "businessApplications": "Business Applications", "software": "Software", "businessContinuity": "Business Continuity", "security": "Security", "solutions": "Solutions", "page": "صفحة", "yourRecentPurchases": "مشترياتك الأخيرة", "expand": "ستوسع", "collapse": "تقوض", "productsBanner": "لافتة المنتجات", "myRecentPurchases": "مشترياتي الأخيرة", "topSellingProductsbyStore": "المنتجات الأكثر مبيعا حسب المتجر", "topSellingProductsbyAWS": "المنتجات الأكثر مبيعا من AWS", "topSellingProductsbyMicroSoft": "المنتجات الأكثر مبيعا من Microsoft", "topSellingProductsbyGoogle": "المنتجات الأكثر مبيعا من Google", "addtoQuote": "<PERSON><PERSON><PERSON> الاقتباس", "addtoFavourite": "أض<PERSON> <PERSON><PERSON> المفضلة", "activityDateRange": "Activity Date Range", "selectEndDate": "Select End Date", "performedBy(User, System, API)": "من قبل (المستخدم ، النظام ، واجهة برمجة التطبيقات)", "help": "Help", "audio": "Audio", "document": "Document", "lastUpdatedDate": "تاريخ آخر تحديث ", "noFrameworkFound": "No framework found", "date": "Date", "records": "السجلات", "countofTickets": "Count Of Tickets", "tickets": "Tickets", "fileSize": "File Size", "max250Characters": "Max 250 Characters", "fileTypeSize": "SVG, PNG, JPG or GIF (MAX. 800x400px)", "typeDescription": "وصف النوع...", "mostViewed": "الأسئلة الأكثر مشاهدة", "whatIsCQ2": "ما هو Redington Cloud Quarks 2.0؟", "whatIsNewInCQ2": "ما الجديد في Redington Cloud Quarks 2.0؟", "isCQ1Replaced": "هل يتم إعادة Cloud Market Place Cloud Quarks 1.0 بواسطة CQ 2.0؟", "needNewLogin": "هل سأحتاج إلى عنوان URL جديد وتسجيل الدخول للوصول إلى CQ 2.0؟", "existingTransactions": "ماذا سيحدث لمعاملاتي الحالية وطلباتي واشتراكاتي والمستخدمين النهائيين؟", "issuesContact": "إذا واجهت أي مشكلات في Cloud Quarks 2.0 ، فمن الذي يجب أن أتواصل؟", "stillUseCQ1": "هل لا يزال بإمكاني استخدام منصة Cloud Quarks 1.0 ، بعد أن نهاجر إلى Cloud Quarks 2.0؟", "noData": "No Data", "redingtonAccountNumberisauniqueidentifierprovidedbyRedingtonforyourcompany": "رقم حسا<PERSON> هو معرف فريد توفره Redington لشركتك", "sameAsDirector/Owner": "مثل المدير / المالك", "keyContacts": "معلومات الاتصال الرئيسية", "addAnotherContact": "إضافة معلومات اتصال إضافية", "city": "مدينة", "customerVertical": "عمودي للعملاء", "middleName": "الاسم الأوسط", "documentInformation": "Document Information", "brandInformation": "Brand Information", "totalActiveContractsCount": "إجمالي عدد العقود النشطة", "countOfContracts": "<PERSON><PERSON><PERSON> العقود", "contractStatus": "حالة العقد", "contractEndDate": "تاريخ انتهاء العقد", "nonActive": "غير نشط", "contractInformation": "معر<PERSON> العقد", "valueBilledUnbilled": "القيمة - فاتورة / غير مفوترة", "scheduleTotal": "الجدول الزمني - الإجمالي", "schedulesBilledUnbilled": "الجداول - المفوترة / غير المفوترة", "organisationAndTenantId": "المؤسسة ومعرف المستأجر", "productNameSku": "اسم المنتج وSKU", "requestsIn": "الطلبات في", "selectProductName": "<PERSON><PERSON><PERSON> اسم المنتج", "renewalStartDate": "Renewal Start Date", "renewalEndDate": "<PERSON>wal End Date", "comments": "Comments", "partnerComment": "تعليق الشريك", "areYouSureYouWantToRejectThisQuote": "هل أنت متأكد من أنك تريد رفض هذا الاقتباس؟", "adminRemark": "ملاحظة المسؤول", "acceptRenewQuote": "قبول وتجديد عرض الأسعار", "acceptRenew": "قبول وتجديد", "areYouSureYouWantToRenewThisQuote": "هل أنت متأكد من أنك تريد تجديد هذا الاقتباس؟", "yesCancel": "نعم، إلغاء", "gstRegistrationNumber": "GST Registration Number", "panNumber": "PAN Number", "isCompanyPartOfSEZ": "Is Company Part Of SEZ", "cinNumber": "CIN Number", "streetAddress1": "Street Address1", "state": "State", "postalCode": "الر<PERSON>ز البريدي", "legalStatus": "Legal Status", "companyWebsite": "Company Website", "numberOfOfficesInRegion": "Number Of Offices In Region", "otherCountriesWithOffices": "Other Countries With Offices", "numberOfWarehousesInRegion": "Number Of Warehouses In Region", "numberOfEmployees": "Number Of Employees", "numberOfSalesStaff": "Number Of Sales Staff", "numberOfTechnicalStaff": "Number Of TechnicalStaff", "twitterAccount": "Twitter Account", "facebookAccount": "Facebook Account", "linkedInAccount": "LinkedIn Account", "instagramAccount": "Instagram Account", "enterGSTNumber": "Enter GST Number", "entervalue": "Enter value", "multiSelectDropdownForSelectingCountry": "Multi select dropdown for selecting country", "enterAccountURL": "Enter Account URL", "partnerProfile": "Partner Profile", "redingtonTermsConditions": "Redington - Terms & Conditions", "iAcceptRedingtonTermsandConditions": "I Accept Redington Terms and Conditions", "failedToFetchQuestionnaire": "Failed to fetch questionnaire", "oadingQuestionnaire": "Loading questionnaire", "weRequestvisitwebsite": "We request you to kindly visit our official careers website", "authenticInformation": "for authentic information pertaining to business association opportunity at Redington and enquire with the company to confirm if the offers or promotions are genuine.", "requestfornewRelationship": "Request for new Relationship", "terminateRelationship": "Terminate Relationship", "invitationLink": "Invitation Link", "microsoftEntraroles": "Microsoft Entra roles", "durationinDays": "Duration In Days", "name": "Name", "autoExtend": "Auto Extend", "autoExtendBy": "Auto Extend By", "securityGroups": "Security Groups", "addSecurityGroup": "Add Security Group", "removeSecuritygroups": "Remove Security groups", "adminRelationshipName": "Admin Relationship Name", "gdap": "GDAP", "createanAdminRelationshipRequest": "إنشاء طلب علاقة إدارية", "customerList": "Customer List", "organizationTenantConfiguration": "تكوين المنظمة والم", "selectaBrandCategory": "حدد فئة العلامة التجارية", "googleCloudPlatform": "خدمات الغوغل السحابية", "selectCustomerTenantType": "حدد نوع العميل / المستأجر", "domainCustomer(recommended)": "العميل المستأجر (موصى به)", "teamCustomer": "عميل الفريق", "linkorTransferExistingCustomerTenant/Domain": "ربط أو نقل العميل المستأجر / المجال الموجود", "entertheDomainName": "أد<PERSON>ل اسم المجال", "checkAvailability": "تحقق من الاستعداد", "existingCustomerTenantDetails": "تفاصيل العميل المستأجر الموجود", "belowAreTheCustomerDetails": "تلخص التفاصيل التالية للعميل وفقًا لسجلات Google ، والمطابقة لاسم المجال الذي قدمته. يرجى التحقق من التفاصيل والضغط على 'حفظ' لإكمال عملية الربط", "channelPartnerID": "رقم المحترف التجاري", "customerCloudIdentityID": "معرف الهوية السحابية للعميل", "domain": "المجال", "customerType": "نوع العميل", "customerCompanyName": "اسم الشركة للعميل", "primaryEmail": "البريد الإلكتروني الرئيسي", "alternativeEmail": "البريد الإلكتروني البديل", "regionCode": "<PERSON><PERSON><PERSON> المنطقة", "backtoOrg&TenantList": "العودة إلى قائمة المنظمات والمستأجرين", "yourRequestforLinkingaGoogleend": "Your request for linking a Google end", "customerTenantHasBeenSuccessfully": "Customer tenant has been successfully", "processed": "processed", "successfully!": "successfully!", "changePassword": "تغيير كلمة المرور", "createNewDomainCustomer": "إنشاء عميل مجال جديد", "selectTheCustomerSegmentType": "حدد نوع قطاع العميل", "educationalInstitution": "مؤسسة تعليمية", "thecompanyInformationIsUsedToCreate": "The company information is used to create the initial administrator account  for Google Workspace and Google Cloud", "organizationName": "اسم المنظمة", "CRMID": "CRM ID", "addressInformation": "معلومات العنوان", "pincode": "الر<PERSON>ز البريدي", "contactInformation": "معلومات الاتصال", "theNameAndEmailAddress": "The name and email address are used to create the initial administrator account for Google Workspace and Google Cloud.", "educationalInstituteType": "نوع المؤسسة التعليمية", "pleaseSelectInstituteType": "الرجاء تحديد نوع المؤسسة التعليمية", "instituteSize": "حجم المؤسسة", "pleaseSelectInstituteSize": "الرجاء تحديد حجم المؤسسة", "add": "يضيف", "pleaseselecttheBrandsandBrandCategories": "الرجاء تحديد العلامات التجارية وفئات العلامات التجارية", "enterAWSAccountID": "أدخل معرف حساب AWS", "createNewTenant": "إنشاء مستأجر جديد", "linkExistingTenant": "ربط المستأجر الحالي", "accountCreationInstructions": "To get started, you must create an account using your customer's company name followed by .onmicrosoft.com. Choose a name similar to the company name, with no spaces or punctuation. The primary domain name can’t be changed later. If your customer has its own custom domain that it wants to use without .onmicrosoft.com, you can change this later in Office 365 Admin Portal.", "tenantCreationMessage": "“redingtongulfdxbtest.onmicrosoft.com” is available. Please click Save to continue and create a new tenant for this customer.", "awsAccountIdInfo": "معرف حساب AWS هو معرف فريد مخصص لمستأجر AWS الخاص بك. يمكنك العثور على معرف حسابك في وحدة تحكم إدارة AWS. سجّل الدخول إلى وحدة تحكم إدارة AWS وافتح وحدة تحكم IAM.", "signedInAccountInfo": "يظهر الحساب الذي قمت بتسجيل الدخول إليه في الجزء العلوي من لوحة التنقل.", "findTenantId": "البحث عن معرف المستأجر", "check": "Check", "findTenantIdUsingCustomer": "Find Tenant ID using Customer's", "clickLinkButton": "الرجاء الضغط على زر الرابط", "tenantIdInfo": "Tenant ID, also referred to as Directory ID or Customer ID, is a globally unique identifier (GUID) for your customer's Microsoft O365 or Azure Organization account. This ID is a 36-character alphanumeric value and is different from your tenant name or domain. For example: 824ddc74-1210-470a-8f4a-d0be4769346d. Please", "clickHere": "انقر هنا", "confirmCustomerTaxId": "تأكيد معرف تسجيل ضريبة العميل", "taxRegistrationRequirement": "يُشترط على هذا العميل تقديم رقم تعريف/تسجيل ضريبي. وقد فرضت مايكروسوفت تقديمه عند تسجيل عميل جديد.", "microsoftAnnouncements": "إعلانات مايكروسوفت", "taxIdValidationMessage": "يجب أن يتكون رقم التسجيل الضريبي من 5 إلى 22 حرفًا أبجديًا رقميًا، دون أي أحرف خاصة مثل النقاط والشرطات المائلة وما إلى ذلك. (مثال: abc123)", "verify": "يؤكد", "requestSubmitted": "لقد تم تقديم طلبك", "successfully": "Successfully", "enterSubscriptionNickName": "أدخل اسم الاشتراك", "confirmCompanyTaxId": "يرجى العمل مع جهة اتصال العملاء الخاصة بك لتأكيد رقم تسجيل ضريبة الشركة", "confirmMcaAcceptance": "Confirm Microsoft Customer Agreement (MCA) Acceptance", "microsoftCustomerAgreement": "Microsoft Customer Agreement (MCA)", "documentAcceptanceInstruction": "Please also document this acceptance in a sound and secure manner.", "viewDownloadAgreement": "View & Download Agreement", "endCustomerFirstName": "First Name of the end customer representative", "endCustomerLastName": "Last Name of the end customer representative", "endCustomerEmail": "Email Address of the end customer representative", "phoneFormatInfo": "Phone should be in international format starting with “+”", "dateOfAcceptance": "Date of Acceptance", "dateFormatInfo": "Date format in mm/dd/yyyy", "tenantDetails": "Tenant Details", "customerDomain": "Customer Domain", "username": "Username", "temporaryPassword": "Temporary Password", "addSubscription": "Add Subscription", "provideInfoAndConfirmAcceptance": "Please provide us with the following information and confirm that the end customer has accepted the", "displayInvitationUrl": "Display Invitation URL", "customerLinkingInvitationUrl": "Customer Linking Invitation URL", "invitationUrlDescription": "Here is the URL for inviting end customers to link their tenant. If you prefer to send the invitation from your own email address, you can copy the link and forward it to your end customer.", "copyLink": "Copy Link", "sendEmail": "Send Email", "sentEmail": "<PERSON><PERSON>", "cspInvitationSentMessage": "CSP invitation email has been <NAME_EMAIL>. The customer has to accept this invitation for you to add and manage Microsoft cloud licenses in their tenant.", "reverifyAfterCustomerAcceptance": "Once the customer accepts the request, please revisit this menu to reverify.", "saveTenantInstruction": "and save the Tenant", "resendInvitationInfo": "If required, you can also resend the invitation email using Resend.", "attention": "Attention", "shareMcaConfirmation": "Please share the Microsoft Customer Agreement (MCA) with your customer and confirm their acceptance. Use of cloud services through the CSP program is subject to the Microsoft Customer Agreement (MCA).", "viewAgreementLinkText": "Click here to view the agreement", "findId": "Find ID", "products": "المنتجات", "period": "الفترة", "actions": "الإجراءات", "becomeVendor": "كن بائعًا"}