{"10Off": "%10 İndirim", "15Discount": "%15 İndirim", "1DayAgo": "1 gün ago", "1HourAgo": "1 saat önce", "1Month": "• 1 Ay", "1Year": "1 Yıl", "31122025": "31/12/2025", "3MinutesAgo": "3 Dakika önce", "50WordCompanyBlurb": "50 Kelimelik Şirket Tanıtım Yazısı", "96OfOurTier1TelecomCustomersAreAlreadyAdoptingMicrosoftAlSolutionsOurEcosystemOfCustomersAndPartnersAreHarnessingThePowerOfAlToReimagineCustomerExperiencesModernizeNetworksAutomateBusinessOperationsAndDriveArowth": "1. katman telekom müşterilerimizin %96'sı şimdiden Microsoft'u benimsiyor\n Al çözümleri. Müşterilerimiz ve iş ortaklarımızdan oluşan ekosistemimiz\n Müşteri deneyimlerini yeniden hayal etmek için Al'ın gücünden yararlanmak,\n Ağları modernleştirin, iş operasyonlarını otomatikleştirin ve yönlendirin\n Öğr.", "_quotation": "_<PERSON><PERSON>t tek<PERSON>", "aBriefDescriptionAboutTheProductsSelectedByTheCustomerAndDetailsOfTheProducts": "Müşteri tarafından seçilen ürünler hakkında kısa bir açıklama ve ürünlerin detayları", "aboutCompany": "Şirket hakkında", "aboutThisItem": "<PERSON>u öğe hakkında", "accept": "Almak", "acceptRenew": "Kabul Et ve Yenile", "acceptRenewQuote": "Teklifi Kabul Et ve Yenile", "acceptanceOfTerms": "Şartların <PERSON>", "accepted": "Kabul", "acceptedBy": "Kabul eden", "acceptedOn": "", "acceptedOn1152025": "Kabul tarihi 11/5/2025", "accessPermissions": "<PERSON><PERSON><PERSON><PERSON>", "accountManagement": "<PERSON><PERSON><PERSON>", "accountName": "<PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON><PERSON>", "activity": "E<PERSON>kin<PERSON>", "activityLog": "Etkinlik Günlüğü", "activityType": "Etkinlik Türü", "addBrands": "<PERSON><PERSON>", "addContinuePurchase": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>am E<PERSON>", "addGoToCart": "Sepete Ekle ve Git", "addMoreUsersStorageFeaturesOrOtherResourcesUnderTheCurrentSubscriptionOftenInvolvesAdditionalCost": "Geçerli abonelik altında daha fazla kullanıcı, de<PERSON><PERSON> alan<PERSON>, özellik veya başka kaynak ekleyin. Genellikle ek maliyet içerir.", "addNew": "<PERSON><PERSON>", "addNewEndCustomer": "<PERSON><PERSON>teri <PERSON>", "addNewTicket": "<PERSON><PERSON>", "addNewUser": "<PERSON><PERSON>", "addToCart": "Sepete ekle", "addToExistingCart": "Mevcut Sepete Ekle", "addToNewCart": "<PERSON><PERSON>", "addUser": "Kullanıcı Ekle", "additionalInformation": "ek bilgi", "address": "<PERSON><PERSON>", "address1": "Adres1", "address2": "Adres2", "admin": "Admin", "advancedSearch": "<PERSON><PERSON>ş<PERSON>ş <PERSON>", "aedUnitedArabEmiratesDirhamDefault": "AED - Birleşik Arap Emirlikleri Dirhemi (Varsayılan)", "agreement": "<PERSON><PERSON><PERSON><PERSON>", "agreementName": "Sözleşme Adı", "agreementType": "Sözleşme Türü", "agreements": "<PERSON><PERSON><PERSON><PERSON>", "aheadOfMobileWorldCongress2025MwcWereSharingNewCapabilitiesAndCustomerMomentumThatShowHowTelecomsAreAdoptingTheMicrosoftCloudAndAlCapabilitiesToSupportTheirAlJourneyAndEmpowerTheNextGenerationOfTelecomSolutionsWeInviteYouToJoinUsNextWeekAtMwcToLearnMoreAboutOurNewAnnouncementsAndSeeFirsthandHowMicrosoftAlIslTransformingTheTelecomIndustryExperienceLiveDemosAttendInsightfulSessionsAndMeetOurExpertsToLearnHowYouCanDriveInnovationAndGrowthWithMicrosoftAlTechnologies": "Mobil Dünya Kongresi 2025 (MWC) öncesinde, yeni bilgiler paylaşıyoruz\n Telekomünikasyonun nasıl olduğunu gösteren yetenekler ve müşteri momentumu\n Al'lerini desteklemek için Microsoft Cloud ve Al özelliklerini benimsemek\n yeni nesil telekom çözümlerine yolculuk yapın ve onları güçlendirin.\n Hakkımızda daha fazla bilgi edinmek için sizi önümüzdeki hafta MWC'de bize katılmaya davet ediyoruz.\n yeni duyurular ve Microsoft Al isl'nin nasıl olduğunu ilk elden görün\n Telekom endüstrisini dönüştürmek. Canlı demoları deneyimleyin, katılın\n bilgilendirici oturumlar ve nasıl araba kullanabileceğinizi öğrenmek için uzmanlarımızla tanışın\n Microsoft Al teknolojileri ile yenilik ve büyüme.", "aiMl": "<PERSON><PERSON><PERSON> / Makine <PERSON>", "albertFloresDeliveredTheOrder": "<PERSON> siparişi teslim etti", "alerts": "Uyarı", "alertsAnnouncements": "Uyarılar / Duyurular", "alertsNotifications": "Uyarılar / Bildirimler", "alex": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "allAnnouncements": "<PERSON><PERSON><PERSON>", "allBrands": "<PERSON><PERSON><PERSON>", "allFiles": "<PERSON><PERSON><PERSON>", "allMultiSelect": "Tümü (Çoklu Seçim)", "allRecords": "<PERSON><PERSON><PERSON>", "allTime": "<PERSON><PERSON><PERSON>", "alli": "Tümü", "alphaSystems": "Alfa Sistemleri", "amazon": "Amazon", "amazonWebServices": "Amazon Web Hizmetleri", "amount": "<PERSON><PERSON><PERSON>", "analytics": "Analytics", "announcement": "<PERSON><PERSON><PERSON>", "announcementType": "<PERSON><PERSON><PERSON>", "announcements": "<PERSON><PERSON><PERSON><PERSON>", "annual": "Yıllık", "antiRacistPratices": "Irkçılık Karşıtı Uygulamalar", "anyoneMakingAnyInvestmentbusinessAssociationOfferInReturnForMoneyOrWhoIsNotAuthorizedByRedington": "Para karşılığında herhangi bir yatırım/iş birliği teklifinde bulunan veya olan herkes\n Redington tarafından yetkilendirilmemiş", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyFilter": "Filtre Uygula", "approved": "Onaylı", "approvedOn": "Onaylandığı tarih:", "approverRemarks": "Onaylayan Açıklamaları", "approxSkus": "Yaklaşık SKU'lar", "april12025InnovateFirstInnovateLast": "1 Nisan 2025 (<PERSON><PERSON> Ya<PERSON> Son Yenilik Ya<PERSON>)", "arabicAr": "Arapça - AR", "areYouSureYouDoNotWantToRenewThisQuote": "Yenilemek istemediğinizden emin misiniz?\n bu Alıntı?", "areYouSureYouWantToRejectThisQuote": "<PERSON><PERSON>u reddetmek istediğinden emin misin?\n alıntı?", "areYouSureYouWantToSuspendThisSubscription": "İstediğinden emin misin\n Bu abonelik askıya alınsın mı?", "areYouSureYouWantToTerminateThisSubscription": "İstediğinden emin misin\n Bu abonelik sonlandırılsın mı?", "asOnDate": "<PERSON><PERSON><PERSON>", "asPerTheOrderDateRangeSelected": "Seçilen Sipariş Tarihi Aralığına göre", "assigned": "<PERSON><PERSON><PERSON>", "assignedCredit": "<PERSON><PERSON><PERSON>", "assignedCreditLimitExisting": "<PERSON><PERSON><PERSON> (Mevcut)", "attachedDocument": "Ekli Belge:", "attachments": "<PERSON><PERSON><PERSON>", "audioDocument": "<PERSON><PERSON><PERSON>", "authoriseAnyoneToCollectMoneyOrArriveAtAnyMonetaryArrangementInReturnForAnyBusinessOpportunityAtRedington": "Redington'daki herhangi bir iş fırsatı karşılığında herhangi birine para toplama veya herhangi bir parasal düzenlemeye varma yetkisi vermek", "authorizedSignatory": "<PERSON><PERSON><PERSON>", "available": "Mevcut", "availableCredit": "Kullanılabilir Kredi", "availableCreditLimit": "Kullanılabilir Kredi Limiti", "availableUsd25000000": "Mevcut: USD 250,000.00", "awaitingAction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aws": "HAYIR", "azure": "Gök mavisi", "azureEntitlements": "Azure Yetkilendirmeleri", "azurePlan": "Azure Planı", "azurePlanUsageAe3": "Azure Planı Kullanımı AE3", "azureRi": "Azure RI", "back": "<PERSON><PERSON>", "backToHome": "<PERSON><PERSON>", "backToNotification": "Bildirime Geri <PERSON>", "balance": "<PERSON><PERSON>", "base": "Taban", "basedOnTheSelectedDateRange": "Seçilen tarih a<PERSON>ığına gö<PERSON>", "basicInformation": "<PERSON><PERSON> Bilgiler", "billFrequency": "Fatura Sıklığı", "billFrom": "Fatura ba<PERSON><PERSON><PERSON>ç fiyatı", "billTo": "Fatura Alıcısı", "billTypc": "<PERSON><PERSON>", "billType": "<PERSON><PERSON>", "billTypeMonthlyYearly": "Fatura Tipi : <PERSON><PERSON><PERSON><PERSON>, Yıllık", "billed": "<PERSON><PERSON>", "billedUsd10000000": "Fatura: 100.000,00 USD", "billing": "<PERSON><PERSON>", "billingAddress12": "<PERSON><PERSON> (1/2)", "billingDetails": "<PERSON><PERSON>", "billingProfile16": "<PERSON><PERSON> (1/6)", "billingScheduleUsd": "Faturaland<PERSON><PERSON> (USD)", "billingSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block": "Blok", "blockThisUser": "Bu kullanıcı engellensin mi?", "bookmark": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON>", "brandCategor": "<PERSON><PERSON>", "brandCategory": "<PERSON><PERSON>", "brandManagement": "<PERSON><PERSON>", "brandName": "Markası", "brandSetUp": "<PERSON><PERSON>", "brandSetup": "<PERSON><PERSON>", "brands": "<PERSON><PERSON>", "buyingOptions": "<PERSON><PERSON><PERSON><PERSON>", "bySubmittingAnOrderOrOrderRevisionYourCompanyRepresentsThatAnyCustomerPurchaseCommitmentProvidedIsCompleteAndAccurateInAllRespectsAndAgreesToPayRedingtonForAllOrdersItSubmitsForProducts": "Bir sipariş veya sipariş revizyonu göndererek, Şirketiniz herhangi bir Müşterinin\n Sağlanan Satın Alma Taahhüdü her bakımdan eksiksiz ve doğrudur ve aşağıdakileri kabul eder\n Ürünler için gönderdiği tüm siparişler için Redington'a ödeme yapın", "cameronWilliamsonDeliveredTheOrder": "<PERSON> Williamson siparişi teslim etti", "canIStillUseTheCloudQuarks10PlatformAfterWeHaveMigratedToCloudQuarks20": "Cloud Quarks 2.0'a geçiş yaptıktan sonra Cloud Quarks 1.0 platformunu kullanmaya devam edebilir miyim?", "cancel": "İptal", "cancelled": "Iptal", "cartDetails": "Sepet Detayları", "cartId": "<PERSON><PERSON>", "cartIdCt100001": "Alışveriş Sepeti No: CT100001", "cartInformation": "<PERSON>et Bilgileri", "cartName": "Sepet Adı", "cartNameOne": "Sepet Adı Bir", "cartSummary": "Alışveriş Sepeti Özeti", "cartTitle": "Alışveriş Sepeti Başlığı", "catalog": "Katalog", "category": "<PERSON><PERSON><PERSON>", "category01": "Kategori 01", "category04": "Kategori 04", "category05": "Kategori 05", "category06": "Kategori 06", "category1": "Kategori 1", "cdpId": "CDP Kimliği", "change": "Değiştirmek", "changePassword": "<PERSON><PERSON><PERSON>", "changeProductOrTermLengthOrBillFrequency": "Ürün veya dönem uzunluğunu ya da fatura sıklığını değiştirin.", "characterLimit50": "<PERSON><PERSON><PERSON>: 50", "chargeDescription": "Ücret Açıklaması", "chargeDetails": "Ücret Detayları", "chargeEnd": "<PERSON><PERSON><PERSON>", "chargeEndDate": "Ücret Bitiş Tarihi", "chargeStart": "Şarj <PERSON>langıcı", "chargeStartDate": "Ücretlendirme Başlangıç Tarihi", "chargeTyp": "<PERSON><PERSON><PERSON>", "chargeType": "Ücret Türü", "checkEligibleBaseOffer": "<PERSON>ygun Temel Teklifi Kont<PERSON>", "checkOut": "<PERSON><PERSON>", "chooseAnEligibleUpgradeForYourSubscription": "Aboneliğiniz için uygun bir yükseltme seçin", "city": "Şehir", "clone": "Klon", "cloneTheOrder": "Siparişi Klonlayın", "close": "Kapatmak", "closingBalanceOutstanding": "Ödenmemiş Kapanış Bakiyesi", "cloudLaasPaas": "Bulut laas / PaaS", "cloudquarksOnboarding": "CloudQuarks - <PERSON><PERSON><PERSON>", "comment": "<PERSON><PERSON>", "commercial": "<PERSON><PERSON><PERSON>", "commercialMarketPlace": "<PERSON><PERSON><PERSON>", "commercialMarketplaceTest": "Ticari Market Testi", "companyHexalytics": "Şirket: Hexalytics", "companyInformation": "Şirket bilgileri", "companyName": "Şirket Adı", "companyProfile": "Şirket Profili", "companyProfiles": "Şirket Profilleri", "companyRegVatTaxId": "Şirket Kaydı / KDV / Vergi Numarası", "companyTaxId": "Şirket Vergi Numarası", "complete": "<PERSON><PERSON><PERSON><PERSON>", "completeMatch": "<PERSON>", "complianceCertifications": "Uyumluluk ve <PERSON>ar", "confirmToBlockTheUserAccountAndContentUsersOrdersBeVisibleToTheAdmin": "Kullanıcı hesabını engellemek için onaylayın ve\n içerik. Kullanıcının emirleri herkes tarafından görülebilir\n Admin.", "confirmToProceed": "<PERSON><PERSON> etmek i<PERSON>", "consumeCloudquarksApis": "CloudQuarks API'<PERSON><PERSON> k<PERSON>", "consumed": "Tüketilen:", "consumedUsd25000000": "Tüketilen : 250.000,00 USD", "contact": "<PERSON><PERSON>", "contactNumber": "<PERSON>rt<PERSON><PERSON>", "continueShopping": "Alışverişe Devam Et", "contractId": "Sözleşme <PERSON>", "contractInfo": "Sözleşme Bilgileri", "contractInformation": "Sözleşme Bilgileri", "contractSchedule": "Sözleşme <PERSON>", "contractValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contractValueTotal": "Sözleşme Bedeli Toplamı", "contracts": "S<PERSON>zleşme", "convertCartToQuote": "Sepeti Teklife Dönüştür", "country": "<PERSON><PERSON><PERSON>", "countryOfBusiness": "<PERSON><PERSON><PERSON>ı Ülke", "couponCode": "<PERSON><PERSON><PERSON>", "couponDetails": "<PERSON><PERSON><PERSON>", "couponId": "<PERSON><PERSON><PERSON>", "couponId1101": "Kupon No:1101", "create": "<PERSON><PERSON><PERSON><PERSON>", "createANewCart": "Yeni Bir Sepet Oluştur", "createANewCartFromCoupon": "Kupondan Yeni Bir Sepet Oluşturun", "createNewSubscription": "Yeni Abonelik Oluştur", "created": "Oluşturulan:", "createdBy": "Oluşturan", "createdDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createdOn": "Oluşturulma tarihi:", "credit": "<PERSON><PERSON><PERSON>", "creditAvailable": "<PERSON><PERSON><PERSON>", "creditCheck": "<PERSON><PERSON><PERSON>", "creditLimitRevisionNewRequest": "Kredi Limiti Revizyonu - Yeni İstek", "creditLimitUtilization": "Kredi Limiti ve Kullanımı", "creditManagement": "<PERSON><PERSON><PERSON>", "creditManagementUsd": "<PERSON><PERSON><PERSON> (USD)", "currency": "Para birimi", "currencyFu": "Para birimi fu", "currencyLanguage": "Para Birimi ve <PERSON>l", "currencySettings": "Para Birimi <PERSON>ı", "currencyTu": "Para birimi Tu", "currentMonth": "Geçerli Ay", "currentPassword": "Mevcut <PERSON>", "currentWeek": "<PERSON>v<PERSON> Ha<PERSON>a", "customerDetails": "Müşteri Detayları:", "customerId": "Müşteri Kimliği", "customerIdorganizationId": "Müşteri Kimliği/Kuruluş <PERSON>ği", "customerManagement": "Müşteri Yönetimi", "customerName": "Müşteri Adı", "customerReadiness": "Müşteri Hazırlığı", "customerVertical": "Müşteri Sektörü", "customers": "Müş<PERSON>i", "cyberSecurity": "Siber Güvenlik", "dashboard": "<PERSON><PERSON><PERSON>", "dataAnalytics": "<PERSON><PERSON>", "dateAccepted": "Kabul Tarihi", "dateAndTime": "<PERSON><PERSON><PERSON> ve <PERSON>", "dateCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dateCreated10052024": "Oluşturulma Tarihi: 10/05/2024", "dateOfPurchaseTu": "Satın alma tarihi TU", "dateRedeemedTi": "Kullanıldığı tarih TI", "debt": "<PERSON><PERSON><PERSON>", "declined": "<PERSON><PERSON><PERSON>", "decrease": "Azaltmak", "decreaseBy": "Azaltma Oranı", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "dell": "Dell", "delphiConsultingLlc": "Delphi Danışmanlık LLC", "department": "Bölüm", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "descriptionDetails": "Açıklama / Ayrıntılar", "descriptionOfIssueRequest": "Sorunun/<PERSON><PERSON><PERSON><PERSON>ı", "descriptior": "Tanımlayıcı", "designation": "<PERSON><PERSON>", "destinationSubscription": "<PERSON><PERSON><PERSON>", "detailedInformation": "Detaylı Bilgi", "details": "<PERSON><PERSON>", "devopsCiCd": "DevOps / CI-CD", "directPhone": "Direkt Telefon", "disclaimer": "Feragatname:", "discount": "İskonto", "discountCoupon": "<PERSON><PERSON><PERSON>", "discountCoupons": "İndirim <PERSON>ı", "discountDetails": "<PERSON><PERSON><PERSON>ı", "discountValue": "<PERSON><PERSON><PERSON>", "discountedPriceunit": "İndirimli Fiyat/Birim", "doItYourselfOrGetItDoneByAnExpert": "<PERSON><PERSON><PERSON> yapın veya bir Uzmana yaptırın!", "doMoreWithMicrosoft365E3Promo": "Microsoft 365 E3 Promosyonu ile daha fazlasını yapın", "doNotRenew": "<PERSON><PERSON><PERSON><PERSON>", "doYouWantToMakeChangesInThisSubscriptionDuringRenewal": "<PERSON><PERSON><PERSON><PERSON> sı<PERSON>ı<PERSON> bu abonelikte değişiklik yapmak istiyor musunuz?", "doc": "Doktor.", "document001pdf": "Belge 001.pdf", "documentDate": "Belge <PERSON>", "documentLastEmailedOn": "Belgenin En Son E-postayla Gönderildiği Ta<PERSON>h", "documentNo": "Doküman No.", "domainId": "Etki Alanı Kimliği", "domair": "<PERSON><PERSON>", "download": "İndirmek", "downloadAsPdf": "PDF olarak indir", "downloadCsv": "CSV'yi indirin", "dubai": "Dubai", "due": "<PERSON><PERSON> <PERSON><PERSON><PERSON>:", "dueDate": "<PERSON>", "dueDays": "<PERSON>", "duplicate": "<PERSON><PERSON><PERSON>", "dynamics365CustomerServiceEnterprise": "Dynamics 365 Müşteri Hizmetleri Kuruluşu", "dynamics365CustomerServiceEnterpriseDevice": "Dynamics 365 Customer Service Kurumsal Cihazı", "dynamics365CustomerServiceEnterpriseEducationFacultyPricing": "Dynamics 365 Customer Service Enterprise (Eğitim Fakültesi Fiyatlandırması)", "dynamics365CustomerServiceEnterpriseNonProfitPricing": "Dynamics 365 Customer Service Enterprise (Kar Amacı Gütmeyen Kuruluş Fiyatlandırması)", "edit": "D<PERSON><PERSON>lemek", "editProfile": "<PERSON><PERSON>", "education": "Eğitim", "eligibleBaseOffers": "<PERSON><PERSON><PERSON>lifle<PERSON>", "emai": "<PERSON><PERSON>", "email": "E-posta", "emailAddress": "E-posta Adresi", "endCustome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endCustomer": "<PERSON>", "endCustomerInformation": "Son Mü<PERSON>teri Bilgileri", "endCustomerName": "Son Müşteri <PERSON>", "endCustomerPurchaseCommitmentConfirmation": "Son Müşteri Satın Alma Taahhüdü Onayı", "endCustomerT1": "Son Müşteri T1", "endDate": "Bitiş Tarihi", "endDate19052025": "Bitiş Tarihi: 19/05/2025", "endDateAlignment": "Bitiş ta<PERSON>", "endTheSubscriptionPermanentlyAccessIsRevokedAndDataMayBeDeletedAfterARetentionPeriod": "Aboneliği kalıcı olarak sonlandırın. <PERSON><PERSON><PERSON>im iptal edilir ve veriler bir saklama süresinden sonra silinebilir.", "englishAed": "İngilizce - AED", "englishEn": "İngilizce - TR", "enterAccountId": "<PERSON><PERSON><PERSON>", "enterAddress": "<PERSON><PERSON>", "enterApnId": "APN Kimliğini Girin", "enterCartTitle": "Sepet Başlığını Girin", "enterCity": "<PERSON><PERSON><PERSON>", "enterCompanyName": "Şirket Adını Girin", "enterCompanyRegvattaxId": "Şirket Kaydı/KDV/Vergi Numarasını Girin", "enterDisplayNumber": "Ekran Numarasını Girin", "enterEmail": "E-post<PERSON>", "enterEmailAddress": "E-posta Adresini <PERSON>-", "enterFirstName": "Adınızı Girin", "enterLastName": "Soyadını Gir", "enterLegalEntityName": "Tüzel kişilik adını girin", "enterLinkedinUrl": "Linkedin URL'sini girin", "enterMiddleName": "İkinci Adı Girin", "enterMobileNumber": "Cep Telefonu Numarasını Girin", "enterName": "<PERSON><PERSON><PERSON>", "enterNickName": "<PERSON>k<PERSON>", "enterNumber": "<PERSON><PERSON><PERSON>", "enterPhoneNumber": "Telefon Numarasını Girin", "enterRemarks": "Açıklamaları Girin", "enterTaxIdentificationNumber": "Vergi Kimlik Numarasını Girin", "enterTheValue": "<PERSON><PERSON><PERSON> girin", "enterTitle": "Başlık Girin", "enterWebsiteUrl": "Web Sitesi URL'sini girin", "enterYearFound": "Bulunma Yılını Girin", "enterZippostalCode": "<PERSON><PERSON>", "estimatedVat": "<PERSON><PERSON><PERSON> KDV", "eventSpecificDataFields": "Etkinliğe Özel Veri Alanları", "existing": "Varolan", "expired": "S<PERSON><PERSON>i <PERSON>ş", "expiresAt1030PmMarch222025": "<PERSON> kullanma tarihi 22 Mart 2025, 22:30", "expiring": "<PERSON><PERSON><PERSON><PERSON>lan", "expiringSubscription15Days": "Süresi Doluyor Abonelik 15 Gün", "expiringSubscription7Days": "Süresi Doluyor Abonelik 7 Gün", "expiringSubscriptions": "<PERSON><PERSON><PERSON><PERSON>", "expiringSubscriptions15Days": "Süresi Dolan Abonelikler 15 Gün", "expiringSubscriptions7Days": "Süresi Dolan Abonelikler 7 Gün", "expiryDate": "<PERSON> kullanma tarihi", "f6e191f85aed467086d4616277ebe4co": "f6e191f8-5aed-4670-86d4-616277ebe4cO", "faq": "SSS", "faqs": "Sıkça Sorulan Sorular", "favourites": "<PERSON><PERSON><PERSON><PERSON>", "fieldsAreMandatory": "Alanların Doldurulması Zorunludur", "fileCategory": "<PERSON><PERSON><PERSON>", "fileDescription": "<PERSON><PERSON>a <PERSON>", "fileDetails": "<PERSON><PERSON><PERSON>", "fileName": "<PERSON><PERSON><PERSON> adı", "fileType": "<PERSON><PERSON><PERSON>", "filter": "Filtre", "finance": "<PERSON><PERSON>", "financeCreditLimitUtilization": "Finans / Kredi Limiti ve Kullanımı", "financeOutstanding": "Finans / Ödenmemiş", "financePaymentTerms": "Finans / Ödeme Ko<PERSON>ulları", "financeStatementOfAccounts": "Finans / Hesap <PERSON>", "financeUnbilledCharges": "Finans / Faturalandırılmamış Masraflar", "finopsCostManagement": "FinOps / Maliyet Yönetimi", "firstName": "Ad", "flexible": "Esnek", "folderOne4": "<PERSON><PERSON><PERSON><PERSON>ör (4)", "forSelectedPeriod": "Seçilen dönem için", "format": "<PERSON><PERSON><PERSON><PERSON>", "frequentlyAskedQuestionsFaqs": "Sıkça Sorulan Sorular (SSS)", "fromDate": "Başlangıç <PERSON>", "fullTimestamp": "Tam <PERSON>", "furtherRedingtonDoesNotAskSolicitAndAcceptAnyMoniesForAnySchemesInAnyFormFromTheInvestorsAndBusinessAssociatesWhetherOnlineOrOtherwiseRedingtonBearsNoResponsibilityForAmountsBeingDepositedWithdrawnTherefromInResponseToSuchFakeOffers": "<PERSON><PERSON><PERSON><PERSON>, Redington herhangi bir plandaki herhangi bir plan için herhangi bir para istemez, talep etmez ve kabul etmez.\n çevrimiçi veya başka bir şekilde yatırımcılardan ve iş ortaklarından form. Kızıl Ertuğrul\n bunlara yanıt olarak yatırılan / çekilen tutarlar için hiçbir sorumluluk kabul etmez.\n böyle sahte teklifler.", "generalTcs": "<PERSON><PERSON>", "generalTermsConditions": "<PERSON><PERSON>", "generateNewReport": "<PERSON><PERSON>", "generateRenort": "<PERSON><PERSON>", "get10OffOnOrdersAbove5Quantity": "10 Miktarın üzerindeki siparişlerde %5 indirim ka<PERSON>.", "getSupported": "Destek Alın", "getToKnowUs": "Bizi Tanıyın", "globalOther": "Global / Diğer", "goToExpiringList": "Süresi Doluyor Listesine Git", "google": "Google'da Arayın", "googleWorkspaceBusinessPlus": "Google Workspace Business Plus", "googleWorkspaceCommitment": "Google Workspace Taahhüdü", "grandTotal": "<PERSON><PERSON>", "grouped": "Gruplandırılmış", "groupedProducts": "Gruplandırılmış Ürünler", "guestAccount": "<PERSON><PERSON><PERSON><PERSON>", "guyHawkinsDeliveredTheOrder": "<PERSON> siparişi teslim etti", "hello": "<PERSON><PERSON><PERSON><PERSON>!", "helpCenter": "<PERSON><PERSON><PERSON>", "helpSupport": "Yardım ve Destek", "hexalytics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexalyticsLlcKsa": "HEXALYTICS LLC-KSA", "hexalyticsLlcUae": "HEXALYTICS LLC-BAE", "hexalyticsLtd": "Hexalytics Ltd", "hexalyticsPvtLtd": "Hexalytics Pvt Ltd", "hexalyticsUae**********": "Hexalytics, BAE (**********)", "homeAccountManagementAgreements": "<PERSON> say<PERSON> › Hesap Yönetimi › S<PERSON><PERSON><PERSON>meler", "homeAccountManagementBrandSetUp": "Ev › <PERSON><PERSON> > <PERSON><PERSON><PERSON>", "homeAccountManagementFinanceCreditLimitUtilization": "Home › Hesap Yönetimi › Finans › Kredi Limiti ve Kullanımı", "homeAccountManagementFinanceOutstanding": "Anasayfa › <PERSON><PERSON><PERSON> > Finans › Ödenmemiş", "homeAccountManagementFinancePaymentTerms": "Home › Hesap Yönetimi › Finans › Ödeme Koşulları", "homeAccountManagementFinanceStatementOfAccounts": "Anasayfa › <PERSON><PERSON><PERSON> > Finans › <PERSON><PERSON><PERSON>", "homeAccountManagementFinanceUnbilledCharges": "<PERSON> sayfa › Hesap Yönetimi › Finans › Faturalandır<PERSON><PERSON>mış Masraflar", "homeAccountManagementMyCompanys": "Home › <PERSON>sa<PERSON> > Ş<PERSON><PERSON><PERSON>", "homeAccountManagementMyProfile": "<PERSON> › <PERSON><PERSON><PERSON> > Profilim", "homeBecomeAVendor": "Home › Satıcı Olun", "homeBillingInvoices": "Home › Faturalandırma › Faturalar", "homeBillingReports": "Home › Faturalandırma › Raporlar", "homeCustomerManagementCustomers": "Home › Müşteri Yönetimi › Müşteriler", "homeCustomerManagementCustomersDetails": "Home › Müşteri Yönetimi › Müşteriler › Ayrıntılar", "homeCustomerManagementCustomersOrders": "Home › Müşteri Yönetimi › Müşteriler › <PERSON><PERSON><PERSON><PERSON>ler", "homeCustomerManagementCustomersOrganizationTenants": "Home › Müşteri Yönetimi > Müşteriler › Organizasyon ve Kiracılar", "homeCustomerManagementCustomersSubscriptionLicenses": "Home › Müşteri Yönetimi › Müşteriler › Abonelik ve Lisanslar", "homeHelpSupportKnowledgeHub": "Ana Sayfa > <PERSON><PERSON><PERSON> ve Destek › Bilgi Merkezi", "homeHelpSupportServiceDesk": "Home › Yardım ve Destek › Hizmet Masası", "homePage": "<PERSON>", "homePurchaseCart": "Ana Sayfa > Satın Alma Sepeti", "homeRequestForQuotation": "Ev › <PERSON><PERSON><PERSON><PERSON>", "homeSalesDiscountCoupons": "Ev > Satışları > İndirim Kuponları", "homeSalesOrders": "Home › Satış > Siparişler", "homeSettingsActivityLog": "Ev › Ayarlar > Etkinlik Günlüğü", "homeSettingsAlertsNotifications": "<PERSON> sayfa › Ayarlar › Uyarılar › Bil<PERSON><PERSON><PERSON>", "homeSettingsLanguageCurrency": "ev › Ayarlar › Dil ve <PERSON> Biri<PERSON>", "homeSettingsUsers": "Home › Ayarlar › Kullanıcılar", "homeSubscriptionLicensesContracts": "Home › Abonelik ve Lisanslar › Sözleşmeler", "homeSubscriptionLicensesRenewalManagementApproved": "Home › Abonelik ve Lisanslar › Yenileme Yönetimi › Onaylandı", "homeSubscriptionLicensesRenewalManagementExpired": "Home › Abonelik ve Lisanslar › <PERSON><PERSON><PERSON><PERSON> Yönetimi › <PERSON><PERSON><PERSON><PERSON> Doldu", "homeSubscriptionLicensesRenewalManagementExpiring": "Home › Abonelik ve Lisanslar › <PERSON><PERSON><PERSON><PERSON> Yönetimi › <PERSON><PERSON><PERSON><PERSON> Doluyor", "homeSubscriptionLicensesRenewalManagementPending": "Home › Abonelik ve Lisanslar › <PERSON><PERSON><PERSON><PERSON> > Beklemede", "homeSubscriptionLicensesRenewalManagementRenewing": "Home › Abonelik ve Lisanslar › <PERSON><PERSON><PERSON><PERSON> Yönetimi › Ye<PERSON><PERSON>e", "homeSubscriptionLicensesSubscriptionsSubscriptionDetail": "Home › Abonelik ve Lisanslar › Abonelikler › Abonelik Detayı", "hp": "HP", "ifIEncounterAnyIssuesInCloudQuarks20WhoShouldIContact": "Cloud Quarks 2.0'da herhangi bir sorunla karşılaşırsam kiminle iletişime geçmeliyim?", "ifYouAreRegisteredPartnerWithRedingtonPleaseEnterYourRedingtonAccountNumberAndSelectTheCountry": "Redington'da Kayıtlı İş Ortağıysanız, lütfen Redington Hesabınızı girin\n Numara tıklayın ve ülkeyi seçin", "image": "Resim", "inActive": "<PERSON><PERSON><PERSON>", "inReview": "İncelemede", "increase": "Artmak", "increaseBy": "Artırma Oranı", "india": "Hindistan", "info": "<PERSON><PERSON><PERSON>", "information": "<PERSON><PERSON><PERSON>", "informationWidget": "<PERSON><PERSON><PERSON>", "initiate": "Başlatmak", "innovateLimitedDetails": "Innovate Limited / Ayrıntılar", "innovateLimitedOrder***********": "Sınırlı Yenilik / Sipariş # ***********", "innovateLimitedOrders": "Yenilik Sınırlı", "innovateLimitedOrganizationTenants": "Innovate Limited / Organizasyon ve Kiracılar", "insights": "Anlayış", "integrateOwnApls": "<PERSON><PERSON><PERSON>", "invoice": "<PERSON><PERSON>", "invoiceBillDate": "<PERSON>ura fatura tarihi", "invoiceDate": "<PERSON><PERSON>", "invoiceDescription": "Fatura Açıklaması", "invoiceDetails": "<PERSON><PERSON>", "invoiceDueDate": "<PERSON><PERSON>", "invoiceManagement": "<PERSON><PERSON>", "invoiceNumber": "<PERSON><PERSON>", "invoiceValue": "<PERSON><PERSON>", "invoiceValueUsd": "<PERSON><PERSON> (USD)", "invoices": "<PERSON><PERSON>", "invoicesByTop5Customers": "İlk 5 Müşteriye Göre Faturalar", "invoicesOpen": "Faturalar - Açık", "invoicesValueByBillType": "<PERSON>ura Türüne Göre Fatura <PERSON>ğ<PERSON>", "ipAddress": "IP Adresi", "isTheCloudMarketPlaceCloudQuarks10BeingRepalcedByCq20": "Bulut Pazar yeri Bulut Kuarkları 1.0 mı?\n CQ 2.0 tarafından yeniden ele geçiriliyor mu?", "isTheExistingCloudMarketPlaceCloudQuarksCq10BeingReplacedByCq20": "Mevcut bulut pazar yeri Cloud Quarks CQ 1.0, CQ 2.0 ile değiştiriliyor mu?", "itemDetails": "<PERSON><PERSON><PERSON><PERSON>", "items": "Eşya", "itemsOrdered": "Sipariş edilen <PERSON>", "knowledgeBase": "Bilgi bankası", "knowledgeHub": "<PERSON><PERSON><PERSON>", "ksa": "Suudi Arabistan", "kuwait": "<PERSON><PERSON><PERSON>", "languageCurrencySettings": "<PERSON>l ve Para Biri<PERSON>", "languageSettings": "<PERSON><PERSON>ı", "last15Days": "Son 15 gün", "last30Days": "Son 30 gün", "last60Days": "Son 60 gün", "last7Days": "Son 7 gün", "lastNam": "<PERSON>", "lastName": "Soyadı", "lastUpdated": "<PERSON>", "lastUpdatedDate28052024": "<PERSON><PERSON><PERSON><PERSON><PERSON> : 28/05/2024", "leadOnlyListings": "Yalnızca Potansiyel Müşteri Listeleri", "legalContractual": "Yasal ve Sözleşmeye Dayalı", "legalEntityName": "Tüzel Kişilik Adı", "lifeCycleManagement": "<PERSON><PERSON><PERSON>", "link": "Bağlantı", "linkedinSocialHandle": "Linkedin / Sosyal Tanıtıcı", "listCertificationsEgIso27001Soc2GdprHipaaPciDss": "Liste Sertifikaları (e.g. ISO 27001, SOC 2, GDPR, HIPAA, PCI-DSS)", "listOfContracts": "Sözleşmel<PERSON><PERSON>", "listOfCustomer": "Müşteri <PERSON>esi", "listOfDiscountCoupons": "İndirim Kuponları Listesi", "listOfOrders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listOfQuestions": "<PERSON><PERSON>", "listOfRecords": "<PERSON><PERSON><PERSON>", "listOfReportsShowing": "G<PERSON><PERSON>ilen <PERSON>", "listOfUsers": "Kullanıcı Listesi", "listPrice": "Liste Fiyatı", "listPriceAfterPromo": "Promosyondan Sonra Liste Fiyatı", "listPriceunit": "Liste Fiyatı/Birim", "localSubsidiariesEntityCountry": "<PERSON><PERSON> (Kuruluş ve Ülke)", "lpoReference11223344": "LPO referansı: #11223344", "manageCart": "Sepeti Yönet", "manageCompanyProfile": "Şirket Profilini Yönet", "manager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manualFulfilment": "<PERSON>", "markAsRead": "<PERSON><PERSON><PERSON>ak <PERSON>aretle", "markAsUnread": "Okunmadı Olarak İşaretle", "markaKurulumu": "<PERSON><PERSON>", "marketingCollateralLinksPreferredGoLiveWindowOrSpecialRequirements": "Pazarlama Teminat Bağlantıları, Tercih Edilen Canlı Geçiş Penceresi veya Özel Gereksinimler", "marketplace": "Pazar", "marketplaceProfile": "Market Profili", "marketplaceSearch": "Pazar <PERSON>", "maxCharacters50Words": "<PERSON><PERSON><PERSON><PERSON> karakter: 50 kelime", "medium": "Orta", "mergeWithExistingSubscription": "Mevcut Abonelikle Birleştir", "metricScoreCards": "<PERSON><PERSON>", "microsoft": "Microsoft Bilişim <PERSON>i", "microsoft365AppsForEnterprise": "Kurumlar için Microsoft 365 Uygulamaları", "microsoft365AppsForEnterpriseIsASubscriptionBasedProductivitySuiteDesignedForLargeOrganizationsOfferingTheLatestVersionsOfMicrosoftOfficeApplicationsEnhancedSecurityAndCloudBasedCollaborationFeatures": "Kurumlar için Microsoft 365 Uygulamaları, Microsoft Office uygulamalarının en son <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gelişmiş güvenlik ve bulut tabanlı işbirliği özelliklerini sunan, büyük kuruluşlar için tasarlanmış abonelik tabanlı bir üretkenlik paketidir.", "microsoft365BusinessBasic": "Microsoft 365 İş Temel", "microsoft365BusinessBasicCfq7ttcolh180001": "Microsoft 365 İş Temel | CFQ7TTCOLH18:0001", "microsoft365F1": "Microsoft 365 F1", "microsoftAlIgnitesTelecomInnovationAndGrowth": "Microsoft Al, telekomünikasyon inovasyonunun fitilini ateşliyor\n ve büyüme", "microsoftCsp": "Microsoft CSP", "middleEastAfrica": "<PERSON><PERSON> ve Afrika", "middleName": "İkinci İsim", "minQty": "Minimum Adet", "minQuality": "<PERSON>", "miniShop": "Mini Dükkan", "mobileNumber": "Cep Telefonu Numarası", "monthly": "Aylık", "monthlyAnnual": "• Aylık, Yıllık", "monthlyYearly": "Aylık, Yıllık", "moreInfo": "Detaylı Bilgi", "mostSearchedQuestions": "En Çok Aranan Sorular", "moveToAHigherPlanTierEgFromBasicToProWithMoreFeaturesSupportOrCapacity": "<PERSON><PERSON>, destek veya kapasite içeren daha yüksek bir plana/katmana (ör. Basic'ten Pro'ya) geçin", "multiple": "Çoklu", "myAccessAndPermissions": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve <PERSON>lerim", "myAccount": "He<PERSON>b<PERSON>m", "myCompanys": "<PERSON><PERSON><PERSON><PERSON>", "myOrders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myProfile": "<PERSON><PERSON> profilim", "myTop5Customer": "İlk 5 Müşterim", "myTop5Products": "En İyi 5 Ürünüm", "na": "YOK", "ndaExecutedWithRedington": "NDA Redington ile mi Yürütüldü?", "netAmount": "Net Tutar", "netOrderValueQuotedAboveIsInclusiveOfValueAddedTaxesVatAsCurrentlyApplicableAnyAdditionalCostImplicationArisingFromChangeInLocalTaxesStructureWillLeadToIncreaseInPricesToThatEffectAtTheTimeOfBillingAndThisWillBeBorneByThePartnerAndOnPartnersAccount": "Yukarıda belirtilen Net Sipariş Değeri, şu anda geçerli olan Katma Değer Vergileri (KDV) dahildir. Yerel vergi yapısındaki değişiklikten kaynaklanan herhangi bir ek maliyet etkisi, vergi yapısında artışa yol açacaktır.\n Faturalandırma sırasında bu etkiye sahip fiyatlar ve bu, İş Ortağı tarafından ve İş Ortağının hesabından karşılanacaktır.", "netPrice": "Net Fiyat", "netValue": "<PERSON>", "networkingConnectivity": "Ağ / Bağlantı", "new": "<PERSON><PERSON>", "newCart": "<PERSON><PERSON>", "newNotifications2": "<PERSON><PERSON> (2)", "newPassword": "<PERSON><PERSON>", "newPurchase": "<PERSON><PERSON>", "newRequest": "Yeni <PERSON>k", "newSubscriptionQtyWillBe6": "Yeni Abonelik Miktarı  olacak", "next": "Önümüzdeki", "nextInvoiceDate": "<PERSON><PERSON><PERSON>", "nickName": "Takma Ad*", "no": "Hay<PERSON><PERSON>", "noExistingSubscriptionsFoundPleaseSelectNewSubscriptionFromDestinationSubscriptionAndProceed": "Mevcut abonelik bulunamadı. Lütfen hedef abonelikten \"Yeni Abonelik\"i seçin\n ve devam edin.", "noLater": "<PERSON><PERSON><PERSON>, daha sonra", "noOfProduc": "Hayır. Ü<PERSON>n", "nonOrderableLeadGenerationOnly": "Sipariş Edilemez - Yalnızca Müşteri Adayı Oluşturma", "notDue": "<PERSON><PERSON><PERSON>", "notDueUsd5000000": "<PERSON><PERSON><PERSON>: 50.000,00 USD", "noteSuspendingASubscriptionImmediatelyCausesDisruptionOfServiceToCustomerButDoesNotStopYourBillingYouWillContinueToBeChargedForThisSubscription": "Not: Aboneliği hemen askıya alma\n müşteriye verilen hizmetin aksamasına neden olur, ancak\n faturalandırmanızı durdurmaz. <PERSON><PERSON> edecek<PERSON>\n bu abonelik için ücretlendirilirsiniz.", "noteTerminatedSubscriptionsCannotBeReactivatedThisSubscriptionTerminationIsSubjectToTheVendorsCancellationPolicyYouWillBeNotifiedThroughEmailOnceYourCancellationRequestIsAcceptedByTheVendor": "Not: Sonlandırılan abonelikler\n Kütüğe. Bu Abonelik Sonlandırma\n Satıcının iptal politikasına tabidir. Sen\n bir kez e-posta yoluyla bilgilendirilecektir.\n iptal talebi Satıcı tarafından kabul edilir.", "notificationDate": "<PERSON><PERSON><PERSON><PERSON>", "notificationType": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON>", "oderPlacedBy": "Oder Yerleştiren", "offerDetails": "<PERSON><PERSON><PERSON><PERSON>", "offerId": "<PERSON><PERSON><PERSON><PERSON>", "oldCreditLimitS": "Eski Kredi <PERSON> (S)", "olderNotifications": "<PERSON><PERSON>", "onboardNewCompany": "Yeni Şirket Katılımı", "onboarding": "İşe Alım", "oneDescriptionAboutTheProductsAddedInTheCartAndOffers": "Sepete eklenen ürünler ve teklifler hakkında bir açıklama", "onePlatformToManageYourMultiCloudInfrastructureForAllPartnersVendorsAndCustomers": "Tüm iş ortakları, satıcılar ve müşteriler için çoklu bulut altyapınızı yönetmek için Tek Platform", "open": "Açık", "openCart": "Sepeti Aç", "openCarts": "Açık <PERSON>", "openCartsSelect": "Sepetleri Aç - Seç", "openTickets": "Açık Biletler", "openingBalance": "Açılış Bakiyesi", "operationalCommercialDetails": "Operasyonel ve <PERSON>", "options": "Seçenekler", "optionsTerm2": "Seçenekler : <PERSON><PERSON> (2)", "or": "veya", "oracle": "Oracle", "order": "Sipariş #", "order41400000429": "Sipariş # 41400000429", "orderDate": "Sipariş Tarihi", "orderDelivery": "Sipariş Teslimatı", "orderDetails": "Sipariş Detayları", "orderId": "Sipariş Numarası", "orderManagement": "Sipariş Yönetimi", "orderPlaced": "Sipariş Verildi", "orderPlacedBy": "Sipariş Veren", "orderPlacedOn": "Sipariş Verildi", "orderPlacement": "Sipariş Verme", "orderReview": "Sipariş İncelemesi", "orderType": "Sipariş Türü", "orderValueByTop3BrandCategoryUsd": "İlk 3'e Göre Sipariş Değeri\n Marka Kategorisi (USD)", "orderValueByTop5EndCustomerUsd": "İlk 5 Son Müşteriye Göre Sipariş Değeri (USD)", "orders": "Sipariş", "organisationTenantId": "Kuruluş ve Kiracı kimliği", "organizationTenant": "Kuruluş ve Kiracı", "organizationTenants": "Organizasyon ve <PERSON>ılar", "other": "<PERSON><PERSON><PERSON>", "others": "<PERSON><PERSON><PERSON>", "outstanding": "Üstün", "outstandingInformation": "<PERSON>ne <PERSON>ıkan Bilgiler", "outstandingUsd5000000": "Ödenmemiş: USD 50,000.00", "overallStatus": "<PERSON><PERSON>", "overdue": "Gecik -miş", "overdueAgeingUsd": "Vadesi Geçmiş - <PERSON>ma (USD)*", "overdueInvoices": "Vadesi Geçmiş Faturalar", "owenSmith": "<PERSON>", "partner": "Ortak", "partnerReadiness": "İş Ortağı Hazırlığı", "partnerRemarks": "İş Ortağı Açıklamaları", "pass": "Geçmek", "password": "Pa<PERSON><PERSON>", "paymentTerm": "Ödeme koşulu :", "paymentTerms": "Ödeme koşulları", "paynowAzure": "Paynow Azure", "pending": "Beklemede", "percentDiscount": "İndirim Yüzdesi", "performedByUserSystemApi": "Gerçekleştiren: (Kullanıcı, Sistem, API)", "permission": "İzin", "permittedCount": "İzin Verilen Sayı", "perpetualLicence": "Kalıcı Lisans", "personalInformation": "<PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Telefon numarası", "platform": "<PERSON><PERSON>", "pleaseConfirmToProceed": "<PERSON>am etmek için lü<PERSON>fen <PERSON>aylayın.", "pleaseNote": "Lütfen aklınızda bulundurun", "pleaseNoteThatTheSubscriptionUpgradeCannotBeReversedOnceRequested": "Abonelik yükseltmesinin talep edildikten sonra geri alınamayacağını lütfen unutmayın.", "pleaseReachOutToYourRedingtonAccountManagerWhoWillAssistYouWithYourRedingtonAccountNumber": "Lütfen Redington Hesap Yöneticinizle iletişime geçin\n Redington Hesap Numaranız konusunda size kim yardımcı olacak", "pleaseSelectTheDropDownToUpgrade": "Lütfen Yükseltmek için açılır menüyü seçin", "pleaseWaitForTheRedingtonResponse": "Lütfen Redington'ın yanıtını bekleyin", "pointOfContact": "İletişim Noktası", "preTaxTotal": "<PERSON><PERSON><PERSON>", "preferredBillingModelPrePaidPostPaid": "<PERSON>rcih Edilen <PERSON> (Ön <PERSON> / Sonradan <PERSON>i)", "preliminaryValidations": "<PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "primaryContact": "Birincil İrtibat Kişisi", "primaryContactInformation": "Birincil İletişim Bilgileri", "primaryContactName": "Birincil İlgili Kişi Adı", "primaryContactTitle": "Birincil İletişim Başlığı", "printOrder": "Baskı Sırası", "priorToSubmittingAnOrderOrOrderRevisionYourCompanyMustObtainACustomerPurchaseCommitmentAndProvideTheSameToAndRedingtonUponItsRequest": "Bir sipariş veya sipariş revizyonu göndermeden önce, Şirketiniz bir Müşteri Satın Alma Taahhüdü almalı ve talebi üzerine bunu Redington'a sağlamalıdır.", "priority": "Öncelik", "privacyPolicy": "Gizlilik Politikası", "product": "<PERSON><PERSON><PERSON><PERSON>", "productDetailedView": "Ürün Detaylı Görünümü", "productDetails": "<PERSON><PERSON><PERSON><PERSON>", "productName": "<PERSON><PERSON><PERSON><PERSON> adı", "productNameSku": "Ürün Adı ve SKU", "productOfferingType": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>", "productType": "<PERSON><PERSON><PERSON><PERSON>", "productivityCollaboration": "Üretkenlik ve İşbirliği", "productsWithDiscountCoupon": "<PERSON><PERSON><PERSON>", "profile": "Profil", "profilePicture": "<PERSON><PERSON>", "promotionId39nfjqt206qx000239nfjqt1q5kk": "Promosyon Kimliği: 39NFJQT206QX-0002-39NFJQT1Q5KK", "promotionType": "Promosyon Türü", "promotions": "Promosyon", "provisioningId": "Sağlama <PERSON>ği:", "provisioningIntegrationMethod": "Sağlama ve Entegrasyon Yöntemi", "purchaseCommitmentObligations": "Satın <PERSON>üdü <PERSON>ükümlülükleri", "purchaseInfo": "Satın Alma Bilgileri:", "purchaseInformation": "Satın Alma Bilgileri", "qty": "MİKTAR", "qualityTu": "Kalite TU", "quantity": "<PERSON><PERSON><PERSON>", "quickBrands": "Hızlı Markalar", "quickLinks": "Hızlı Linkler", "quickOrder": "Hızlı Sipariş", "quotations": "<PERSON><PERSON><PERSON><PERSON>", "quoteCreated": "<PERSON><PERSON><PERSON><PERSON>", "quoteId": "<PERSON><PERSON><PERSON><PERSON>", "quoteIdRfq1001": "Alıntı Kimliği: RFQ1001", "quoteInformation": "<PERSON><PERSON><PERSON><PERSON>", "quoteManagement": "<PERSON><PERSON><PERSON><PERSON>", "quoteSummary": "Alıntı Özeti", "quotes": "# Alınt<PERSON>lar", "r032AdvancePayment": "R032 <PERSON><PERSON><PERSON><PERSON>", "reEnterPassword": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON>", "recentPurchases": "<PERSON>", "redeemed": "Itfa", "redeemedCount": "Kullanılmış Sayım", "redeemedCoupons": "Kullanılan Ku<PERSON>nlar", "redemptionDetails": "Kullanım Detayları", "redington": "Kızıl Ertuğrul", "redingtonAcNo": "Redington AC Hayır", "redingtonAccountId": "Redington Hesap <PERSON>*", "redingtonAccountNumberIsA10DigitNumericValueExample**********WhichIsTaggedWithSegmentCloudServices": "Redington Hesap Numarası 10 haneli sayısal bir değerdir\n (örnek **********) segment ile etiketlenmiş\n \"Bulut Hizmetleri", "redingtonAccountNumberIsAUniqueIdentifierProvidedByRedingtonForYourCompany": "Redington Hesap Numarası benzersiz bir tanımlayıcıdır\n Redington tarafından şirketiniz için sağlanır", "redingtonAvailsCertainServicesThroughProfessionalInvestmentAgenciesEvenInThoseCasesOffersAreAlwaysMadeDirectlyByRedingtonAndNotByAnyThirdParties": "Redington, profesyonel yatırım ajansları aracılığıyla belirli hizmetlerden yararlanmaktadır. <PERSON><PERSON>\n bu <PERSON>, tekli<PERSON>r her zaman doğrudan Redington tarafından yapılır ve herhangi bir üçüncü tarafça yapılmaz.", "redingtonCloudquarksBecomeAVendor": "Redington CloudQuarks - Satıcı Olun", "redingtonCloudquarksPlatformUserAccountCreationTcs": "Redington CloudQuarks - Platform Kullanıcı Hesabı Oluşturma Şart ve Koşulları", "redingtonDoesNot": "Redington şunları yapmaz:", "redingtonGroupAllRightsReserved": "Redington Grubu | Tüm Hakları Saklıdır.", "redingtonGulfFze": "Redington Körfezi FZE", "redingtonReservesTheRightToTakeLegalActionIncludingCriminalActionAgainstSuchIndividualsentities": "Redington, bu tür işlemlere karşı cezai işlem de dahil olmak üzere yasal işlem başlatma hakkını saklı tutar\n Bireyler/Kuruluşlar", "redingtonSalesContacts": "Redington Satış İletişim Bilgileri", "redingtonStronglyRecommendsThatThePotentialInvestorsBusinessAssociatesShouldNotRespondToSuchFakeSolicitationsInAnyManner": "Redington, potansiyel yatırımcı iş ortaklarının bu tür sahte taleplere hiçbir şekilde yanıt vermemelerini şiddetle tavsiye eder", "redingtonWillNotBeResponsibleToAnyoneActingOnAnOfferNotDirectlyMadeByRedington": "Redington, doğrudan kendisi tarafından yapılmayan bir teklife göre hareket eden hiç kimseye karşı sorumlu olmayacaktır.\n Kızıl Ertuğrul", "reduceTheNumberOfUsersFeaturesOrResourceUsageMayLowerCostsButOftenRestrictedToRenewalPeriods": "<PERSON><PERSON><PERSON><PERSON><PERSON>, özellik veya kaynak kullanımı sayısını azaltın. Maliyetleri düşürebilir, ancak genellikle yenileme dönemleriyle sınırlıdır.", "referenceNo": "Referans No.", "regionStateOrProvince": "<PERSON><PERSON><PERSON>, Eyalet veya İl", "regionstateprovince": "Bölge/Eyalet/İl", "registeredBusinessAddress": "Kayıtlı İş Adresi", "reject": "Reddetmek", "rejectQuote": "<PERSON><PERSON><PERSON><PERSON>", "rejected": "Reddedilmiş", "renew": "<PERSON><PERSON><PERSON><PERSON>", "renewal": "<PERSON><PERSON><PERSON><PERSON>", "renewalEndDate20260503": "<PERSON><PERSON><PERSON><PERSON>hi : 2026-05-03", "renewalManagement": "<PERSON><PERSON><PERSON><PERSON>", "renewalManagementApproved": "Yenileme <PERSON> / Onaylandı", "renewalManagementExpired": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>", "renewalManagementExpiring": "<PERSON><PERSON><PERSON><PERSON> / S<PERSON><PERSON><PERSON>", "renewalManagementRenewing": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>", "renewalPrice": "<PERSON><PERSON><PERSON><PERSON>", "renewalStartDate20250503": "Yeni<PERSON><PERSON> Başlangıç Tarihi : 2025-05-03", "renewalStatus": "<PERSON><PERSON><PERSON><PERSON> :", "renewing": "<PERSON><PERSON><PERSON><PERSON>", "reportRequestDate": "<PERSON><PERSON>", "reportRequestId": "<PERSON><PERSON>ğ<PERSON>", "reportTile": "<PERSON><PERSON>", "reportTitle": "<PERSON><PERSON>şlığı", "reportType": "<PERSON><PERSON>", "reports": "<PERSON><PERSON>", "requestAQuote": "<PERSON><PERSON><PERSON>", "requestCreditLimit": "<PERSON><PERSON><PERSON>", "requestForQuotation": "<PERSON><PERSON><PERSON><PERSON>", "requestForRenewalPrice": "<PERSON><PERSON><PERSON><PERSON>", "requestId": "İstek Kimliği", "requestPaymentOfAnyKindFromProspectiveInvestor": "<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> her türlü ödemeyi talep edin", "requestReason": "İstek Nedeni *", "requestType": "İstek Türü", "requestedAssignedCreditLimit": "İstenen - <PERSON><PERSON><PERSON> *", "requestedBy": "<PERSON><PERSON> eden", "requestedCreditLimitS": "İstenen Kredi <PERSON> (S)", "requestedDate": "<PERSON><PERSON><PERSON>", "requestedE": "talep edilen <PERSON>", "requestsInPending": "İstekler\n Beklemede*", "resellerId": "<PERSON><PERSON>", "resendMail": "Postaları yeniden gönder", "reservedVmInstanceCool_grs_data_stored_10": "Ayrılmış VM Örneği, Cool_GRS_Data_Stored_10", "resetToDefaultSettings": "Varsayılan ayarlara sıfırla", "resolutionRemarks": "<PERSON><PERSON>", "resolutionRemarksFromTheSolutionArchitectServiceDeskAgent": "Çözüm Mimarı/Hizmet Masası Temsilcisinden çözüm açıklamaları", "revisedAssignedCreditLimit": "Gözden Geçirilmiş - Tahsis Edilen Kredi Limiti", "revisedCreditLimit": "Gözden Geçirilmiş Kredi Limiti($)", "revisionRequestPleaseConfirm": "Revizyon Talebi, lütfen onaylayın", "rfq1001": "RFQ1001", "robertFox": "<PERSON>", "roductNam": "<PERSON><PERSON><PERSON><PERSON>", "role": "Rol", "salePriceunitTu": "Satış Fiyatı/Birim TU", "sales": "Satış", "salesContactName": "Satış İrtibat Kişisi Adı", "save": "<PERSON><PERSON><PERSON>", "saveChanges": "Değişiklikleri Kaydet", "scheduleTotal": "Çizelge - Toplam", "schedulesBilledunbilled": "Tarifeler - Faturalı/Faturasız|", "search": "Aramak", "searchByCartIdcartNamecreatedBycustomer": "Sepet Kimliği/<PERSON><PERSON> Adı/Oluşturan/Müşteri O<PERSON>ak Ara", "seatBased": "Koltuk Tabanlı", "sector": "Se<PERSON><PERSON><PERSON>", "seeAllNotifications": "<PERSON><PERSON><PERSON> bi<PERSON> gör", "segment": "Parça", "segmentCommercial": "Segment : <PERSON><PERSON><PERSON>", "select": "Seçmek", "selectAny1": "Herhangi birini seçin 1", "selectAny5": "(<PERSON><PERSON><PERSON> bir 5'i seçin)", "selectBrand": "<PERSON><PERSON>", "selectBrandCategory": "<PERSON><PERSON>", "selectCustomerVertical": "Müşteri Sektörünü Seçin", "selectDepartment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectEndCustomer": "<PERSON><PERSON><PERSON>", "selectExistingSubscription": "<PERSON><PERSON><PERSON> a<PERSON>", "selectFromDate": "<PERSON><PERSON><PERSON><PERSON>", "selectOrderPlacedBy": "Tarafından Verilen Siparişi Seçin", "selectOrderType": "Sipariş Türünü Seçin", "selectRegionStateOrProvince": "<PERSON><PERSON><PERSON>, Eyalet veya İl Seçin", "selectRequestReason": "İstek Nedenini Seçin", "selectRequestedBy": "<PERSON><PERSON>", "selectSector": "<PERSON><PERSON><PERSON><PERSON>", "selectSegment": "Segment Seç", "selectStatus": "<PERSON>rum <PERSON>", "selectTheCheckboxToViewTheEstimatedCharges": "<PERSON><PERSON>ini ücretleri görüntülemek için onay kutusunu seçin", "selectTheCurrencyYouWantToPurchaseWith": "Satın almak için kullanmak istediğiniz para birimini seçin", "selectTheCurrencyYouWantToShopWith": "Alışveriş yapmak istediğiniz para birimini seçin", "selectTheLanguageYouPreferForBrowsingThePlatform": "Platforma göz atmak için tercih ettiğiniz dili seçin", "selectTimeZone": "Saat Dilimini <PERSON>", "selectTimezone": "Saat Dilimi'ni seçin", "selectToDate": "<PERSON><PERSON><PERSON>", "serviceDesk": "Hizmet Masası", "serviceOneTimeprojectBased": "Hizmet - Tek Seferlik/Proje Bazlı", "serviceRecipient": "Hizmet Alıcısı", "serviceRecurring": "Hizmet - Yinelenen", "serviceRequest": "<PERSON><PERSON>", "services": "Hizmetleri", "settings": "<PERSON><PERSON><PERSON>", "showBookmarkedFile": "Yer İşaretli Dosyayı Göster", "showMore": "<PERSON><PERSON>...", "showing10100Records": "10/100 Kayıtlar Gösteriliyor", "signOut": "<PERSON><PERSON><PERSON><PERSON> kapat", "signingAuthorityName": "İmza Yetkilisi Adı", "signingAuthorityTitle": "İmza Yetkilisi Unvanı", "simmonsOn11032025": "<PERSON> 11/03/2025 tarihinde", "sku": "SKU", "skuIdDzh318z009pgj0007": "Ürün Ko<PERSON>: DZH318Z009PGJ:0007", "skuIdDzh318z09pgj0007": "Ürün Kodu: DZH318Z09PGJ:0007", "skuid": "SKUID (Ürün Kodu)", "smith": "<PERSON><PERSON><PERSON>", "softwareUpgradeRequest": "Yazılım Yükseltme İsteği", "softwareUpgradeRequestHasBeenSubmitted": "Ya<PERSON><PERSON><PERSON>ım Yükseltme talebi gönderildi.", "softwaredigitalProducts": "Yazılım/Dijital Ürünler", "solicitAnyInvestmentInSchemesFromFreeEmailServicesLikeGmailRediffMailYahooMailEtc": "Gmail, Rediff mail, Yahoo mail vb. gibi ücretsiz e-posta hizmetlerinden planlara herhangi bir yatırım talep edin", "solutionTechnologyAreas": "Çözüm ve Teknoloji Alanları", "specify": "Belirtmek", "standardCreditTermsDays": "Standart <PERSON> (Gün)", "startDate": "Başlangıç <PERSON>", "startDatel": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "statementOfAccounts": "<PERSON><PERSON><PERSON>", "status": "Durum", "storageBackupDr": "Depolama / Yedekleme / DR", "store": "Mağaza:", "streetAddress": "Açık Adres", "streetAddress2": "Sokak Adresi 2", "subStatus": "Alt Du<PERSON>", "subTotal": "<PERSON>", "submit": "<PERSON><PERSON><PERSON>", "submitRequest": "İstek Gönder", "submittedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>:", "subscriptionCount": "Abonelik Sayısı*", "subscriptionDecrease": "Abonelik - Azalt", "subscriptionEndDate": "Abonelik Bitiş Tarihi", "subscriptionId": "Abonelik Kimliği", "subscriptionIncrease": "Abonelik - Artış", "subscriptionInformation": "Abonelik Bilgileri", "subscriptionLicenses": "Abonelik ve Lisanslar", "subscriptionName": "Abonelik Adı", "subscriptionOneTimeBilling": "Abonelik - Tek Seferlik Faturalandırma", "subscriptionRecurringBilling": "Abonelik - Yinelenen Faturalandırma", "subscriptionStartDate": "Abonelik Başlangıç Tarihi", "subscriptionTerminate": "Abonelik - Sonlandır", "subscriptionTop5Customers": "Abonelik - İlk 5 Müşteri*", "subscriptionUpdateRequestHasBeenSubmitted": "Abonelik Güncelleştirme isteği gönderildi.", "subscriptionUpdateRequestHasBeenSubmittedTheChangesWillTake15MinutesToReflect": "Abonelik Güncelleştirme İsteği gönderildi. bu\n Değişikliklerin yansıtılması 15 dakika sürecektir", "subscriptions": "Abonelik", "subtotal": "<PERSON>", "success": "Başarı", "supportEmailForEndCustomers": "Son müşteriler için destek e-postası", "supportHubDocumentAndHowTos1": "Destek Merkezi Belgesi ve Nasıl Yapılır (1)", "supportHubVideoAndHowTos1": "Destek Merkezi Videosu ve Nasıl Yapılır (1)", "supportPhoneForEndCustomers": "Son mü<PERSON><PERSON>iler i<PERSON>in destek telefonu", "supportSlaTiersOffered": "Sunulan SLA Katmanlarını Destekleyin", "supportedPlatformsHyperscalers": "Desteklenen Platformlar / Hiper Ölçekleyiciler", "supportingDocument": "<PERSON><PERSON><PERSON><PERSON>", "suspend": "<PERSON>ma<PERSON>", "tag123": "Etiket 123", "tag236": "Etiket 236", "tags": "Etiketler:", "tagsProduct": "Etiketler: <PERSON><PERSON><PERSON><PERSON>", "tagsProductAwsPlatform": "Etiketler: Ürün AWS Platformu", "targetMarketsstores": "<PERSON><PERSON><PERSON>/Mağazalar", "targetPrice": "<PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "tax500": "Vergi (5.00%) :", "taxIdentificationNumber": "<PERSON><PERSON>gi <PERSON>", "taxInvoicingJurisdictionss": "Vergi ve Faturalandırma Yargı Bölge(ler)i", "teamReactive": "<PERSON><PERSON><PERSON><PERSON>", "technicalEnablement": "Teknik Enablement", "temporarilyDisableTheSubscriptionWithoutTerminatingItAccessIsBlockedButDataIsRetainedOftenUsedForSeasonalNeeds": "Aboneliği sonlandırmadan geçici olarak devre dışı bırakın. <PERSON><PERSON><PERSON><PERSON>, ancak veriler korunur. Genellikle mevsimsel ihtiyaçlar için kullanılır.", "tenantName": "Kiracı Adı", "term": "Terim", "term2": "Dönem (2)", "termBillType": "<PERSON><PERSON> ve Fatura Türü:", "terminate": "Bitirmek", "termsAndConditions": "Şartlar ve koşullar", "termsOfSale": "Satış Şartları", "termsOfUse": "Kullanım Şartları", "testAudioFile0069": "Ses Dosyasını Test Et 0069", "testAudioFile3250120251": "Test Ses Dosyası 3 (25-01-2025) (1)", "testDescription": "Test açıklaması..", "testReason": "Test nedeni...", "testRemarks": "Test açıklamaları...", "testTile": "Test Kutucuğu", "thankYouYourFormHasBeenSubmittedBaşarılı bir şekilde": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Formunuz\n gönderildi\n Başarı -yla!", "theEstimatedProrateCostIs224025Usd": "Ta<PERSON>ini orantılı maliyet 2.240,25 USD|", "theEstimatedProrateRefundIs349Usd": "<PERSON><PERSON><PERSON> oranlı geri ödeme -3,49 USD'dir", "theFirstGroup": "<PERSON><PERSON><PERSON><PERSON>", "theOnlySelfServicePlatformWithManagedServicesCapabilities": "Yönetilen hizmetler özelliklerine sahip tek Self Servis platformu.", "thePremiumOfficeSuiteForOrganizationsIncludingWordExcelPowerpointOutlookOnenoteAccessAndSkypeForBusiness": "Kuruluşlar için Premium Office paketi - \n Word, Excel, PowerPoint, Outlook, OneNote,\n Access ve Skype Kurumsal dahil", "theServicesThatRedingtonProvidesToYouAreSubjectToTheFollowingTermsOfUseTouRedingtonReservesTheRightToUpdateAndModifyTheTouAtAnyTimeWithoutNoticeToYouTheMostCurrentVersionOfTheTouCanBeReviewedByClickingOnTheTermsOfUseHyperlinkLocatedAtTheBottomOfOurWebpagesWhenWeMakeUpdatesToTheTouRedingtonWillUpdateTheDateAtTheTopOfThisPageByUsingTheWebsiteAfterANewVersionOfTheTouHasBeenPostedYouAgreeToTheTermsOfSuchNewVersion": "Redington'ın size sağladığı hizmetler aşağıdaki Kullanım Koşullarına tabidir\n (\"Kullanım Koşulları\"). Redington, Kullanım Koşullarını herhangi bir zamanda güncelleme ve değiştirme hakkını saklı tutar.\n size bildirim. TOU'nun en güncel sürümü, üzerine tıklanarak incelenebilir.\n Web sayfalarımızın alt kısmında yer alan \"Kullanım Koşulları\" köprüsü. Güncellemeler yaptığımızda\n Kullanım Koşulları'na göre, Redington bu sayfanın üst kısmındaki tarihi güncelleyecektir. Web sitesini kullanarak\n Kullanım Koşulları'nın yeni bir sürümü yayınlandıktan sonra, bu yeni sürümün koşullarını kabul etmiş olursunuz.\n Sürüm.", "theTelecommunicationsIndustryIsExperiencingSignificantAlAdvancementsEmergingAsTheLeadingAdopterOfGenerativeAndAgenticAlToDriveAutomationPersonalizationAndDataDrivenDecisionsAccordingToARecentIdcWhitePaperTelecomAndMediaCompaniesAreSeeingNearlyFourTimesTheReturnOnInvestmentRolOnEveryDollarInvestedInAlAdditionallyBy2027Almost90OfTelecomProvidersAreExpectedToUseGenerativeAlToImproveCustomerExperiencesUpFrom62Today": "Telekomünikasyon endüstrisi önemli bir Al yaşıyor\n Üretken ve üretken teknolojilerin önde gelen uygulayıcısı olarak ortaya çıkan ilerlemeler\n Otomasyonu, kişiselleştirmeyi ve veriye dayalı çalışmayı desteklemek için agentic Al\n Karar. Yakın tarihli bir IDC teknik incelemesine göre, telekom ve medya\n Şirketler yaklaşık dört kat daha fazla yatırım getirisi elde ediyor\n (ROl) Al'a yatırılan her dolarda. Ayrıca, 2027 yılına kadar neredeyse %90\n Telekom sağlayıcılarının oranının iyileştirilmesi için üretken Al'yi kullanması bekleniyor.\n Müşteri deneyimleri, bugün %62'den yükseldi.", "thefirstgrouponmicrosoftcom": "thefirstgroup.onmicrosoft.com", "thisIsToNotifyToTheGeneralPublicThatSomeUnscrupulousPersonsAreUnauthorizedlyUsingTheNameOfRedingtonLimitedRedingtonGroupAffiliatesRedingtonAndPosingThemselvesAsEmployeesRepresentativesAgentsOfRedingtonAndItsAssociatedgroupCompaniesWithUlteriorMotiveToEarnWrongfulGainAndorCheatTheProspectiveInvestorsAndBusinessAssociatesAndAreFraudulentlyOfferingInvestmentOpportunityOnlineThroughCertainWebsitesOrThroughTelephoneCallsOrByIssuingFakeOfferLettersAndAlsoSolicitingThemToDepositSomeAmountInCertainBankAccountsThesePeopleAreAlsoUnauthorizedlyUsingTheNameTrademarkDomainNameAndLogoOfRedingtonWithAViewToTarnishTheImageAndReputationOfRedington": "Bu, bazı vicdansız kişilerin yetkisiz olduğunu kamuoyuna bildirmek içindir.\n Redington Limited adını kullanarak; Redington Group/ İştirakleri (\"Redington\") ve poz verme\n Redington ve bağlı şirketinin/gru<PERSON><PERSON><PERSON>alışanları, temsil<PERSON>leri, acenteleri olarak kendileri\n haksız kazanç elde etmek ve/veya potansiyel müşteriyi aldatmak için art niyetli şirketler\n yatırımcılar ve iş ortakları ve hileli bir şekilde yatırım fırsatı sunuyorlar\n belirli web siteleri aracılığıyla veya telefon görüşmeleri yoluyla veya sahte teklif mektupları düzenleyerek çevrimiçi\n ve ayrıca belirli banka hesaplarına bir miktar para yatırmalarını istemek. Bu insanlar\n ayrıca Redington'ın adını, ticari mark<PERSON>, alan adını ve logosunu yetkisiz olarak kullanıyor\n Redington'ın imajını ve itibarını zedelemek amacıyla.", "ticket": "Bilet #", "ticketCategory": "<PERSON><PERSON><PERSON>", "ticketSr112233": "Bilet: #SR-112233", "ticketsInReview": "B<PERSON><PERSON>", "timeStamp": "Zaman Damgası", "toDate": "<PERSON><PERSON><PERSON><PERSON>", "topProductsForUae": "BAE İçin En İyi Ürünler", "total": "Toplam", "totalActiveContractsCount": "Toplam Aktif Sözleşme Sayısı", "totalAssigned": "<PERSON><PERSON><PERSON>", "totalBilled": "Toplam Faturalandırılan", "totalConsumed": "Toplam Tüketilen", "totalContractsValue": "Toplam Sözleşme <PERSON>", "totalCoupons": "Toplam Kupon Sayısı", "totalCustomers": "Toplam Müşteri Sayısı", "totalInvoiceValueUsd": "Toplam Fatura <PERSON> (USD)", "totalItems": "Toplam Öğe Sayı<PERSON>ı", "totalItems5": "Toplam Ürün: 5", "totalOrder": "Toplam Sipariş", "totalOrderValue": "Toplam Sipariş Değeri", "totalOrders": "Toplam Siparişler", "totalOrdersUsd": "<PERSON><PERSON> (USD)", "totalOutstanding": "Toplam Ödenmemiş", "totalOverdueByBrandUsd": "Markaya Göre Toplam Gecikme <PERSON> (USD)", "totalQuoteValue": "<PERSON>lam <PERSON>", "totalQuotes": "Toplam Alıntı", "totalReceivablesUsd": "<PERSON>lam <PERSON> (USD)", "totalTickets": "Toplam Bilet Sayısı", "totalUnbilled": "Toplam Faturalandırılmamış Tutar", "totalUnbilledByBrandCategoryUsd": "Marka Kategorisine Göre Faturalandırılmamış Toplam (USD)", "totalUnbilledByChargeTypeUsd": "Ücret Türüne Göre Faturalandırılmamış Toplam (USD)*", "totalUnbilledUsd": "Toplam Faturalandırı<PERSON>ış (USD)", "totalValueUsd50000": "Toplam Değer: USD 500,00", "totalValueUsd5000000": "Toplam Değer: 50.000,00 USD", "trainingCertification": "Eğitim ve Sertifikasyon", "transactionAmount": "İşlem Tutarı", "transactionCurrencycurrencies": "İşlem Para Birimi/Birimleri *", "transactionDate": "İşlem Tarihi", "turkey": "Türkiye", "type": "<PERSON><PERSON><PERSON>", "type1": "1 yazın", "uae": "UÇAN", "uaeAbuDhabi": "BAE - <PERSON>", "uaeDubai": "BAE - Dubai", "uaeSharjah": "BAE - Sharjah", "ubscripionIdr": "ubscripion IDr", "unbilled": "Faturalandırılmamış", "unbilledCharges": "Faturalandırılmamış Ücretler", "unbilledUsage": "Faturalandır<PERSON><PERSON><PERSON><PERSON>ş Kullanım", "unbilledUsageValue": "Faturalanmamış <PERSON>ı<PERSON>", "unbilledUsd15000000": "Faturalandırılmamış : USD 150.000,00", "unitPrice": "<PERSON><PERSON><PERSON>", "unredeemedCoupons": "Kullanılmamış Kuponlar", "upcomingExpiration": "<PERSON><PERSON><PERSON><PERSON>", "update": "G<PERSON><PERSON><PERSON>ş<PERSON>rmek", "updateNickName": "<PERSON>", "upgrade": "Yükseltmek", "usageBased": "Kullanım Bazlı", "usageCost": "Kullanım ve Maliyet", "usageType": "Kullanım Şekli", "usd": "USD", "usd20000000": "2.00.000,00 ABD Doları", "usd500000000": "5.000.000,00 ABD Doları|", "use": "Kullanmak", "user": "Kullanıcı", "userAccessAndPermission": "Kullanıcı Erişimi ve İzni", "userManagement": "Kullanıcı Yönetimi", "userSubscriptionThatIncludesDynamics365CustomerServiceEnterpriseEdition": "Dynamics 365 Customer Service, Enterprise sürümünü içeren kullanıcı aboneliği", "users": "Kullanıcı", "validTil": "<PERSON><PERSON>", "validTill": "Geçerlilik Süresi", "validTill18052025": "Geç<PERSON><PERSON>: 18/05/2025", "validTillDate": "Geçerlilik Tarihine Kadar", "validUntil": "<PERSON><PERSON> tarihe kadar geçerlidir:", "validate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validation": "Doğrulama", "value": "<PERSON><PERSON><PERSON>", "valueBilledUnbilled": "Değer - Faturalandırılmış/ Faturalandırılmamış", "vat": "KDV %", "vendor": "Satıcı", "vendorDisplayNameShownToBuyers": "Satıcı görünen adı (alıcılara gösterilir) *", "vendorSkuId": "Satıcı SKU Kimliği", "vendorSkuIdDzh318z09pgj0007": "Satıcı SKU Kimliği: DZH318Z09PGJ:0007", "vendorTcs": "Satıcı Şartlar ve Koşullar", "verified": "Doğrulandı!", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "video": "Video", "view": "G<PERSON>rü<PERSON><PERSON><PERSON>", "viewPdf": "PDF'ı Görüntüle", "viewRequest": "İsteği Görü<PERSON>üle", "weHerebyConfirmThatWeHaveReceivedTheEndCustomerPurchaseCommitmentForThisOrderOrOrderRevisionAndShallProvideTheSameOnRequest": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, son mü<PERSON><PERSON><PERSON> satın alma taahhüdünü aldığımızı onaylıyoruz\n bu sipariş veya sipariş revizyonu için ve talep üzerine aynısını sağlayacaktır.", "weRequestYouToKindlyVisitOurOfficialCareersWebsiteHttpsredingtongroupcomforAuthenticInformationPertainingToBusinessAssociationOpportunityAtRedingtonAndEnquireWithTheCompanyToConfirmIfTheOffersOrPromotionsAreGenuine": "<PERSON><PERSON><PERSON> web sitemizi ziyaret etmenizi rica ederiz https://redingtongroup.com/for\n Redington'daki iş birliği fırsatına ilişkin gerçek bilgiler ve\n Tekliflerin veya promosyonların gerçek olup olmadığını teyit etmek için şirketle görüşün.", "website": "İnternet sitesi", "whatIsNewWithRedingtonCloudQuarks20": "Redington Cloud Quarks 2.0'daki yeni<PERSON> neler<PERSON>?", "whatIsRedingtonCloudQuarks20": "Redington Bulut Kuarkları 2.0 nedir?", "whatSRedingtonCloudQuarks20": "Redington Cloud Quarks 2.0 nedir?", "whatWillHappenToMyExistingTransactionsOrdersSubscriptionsAndEndUsers": "Mevcut işlemlerime, siparişlerime, aboneliklerime ve son kullanıcılarıma ne olacak?", "willINeedANewUrlAndLoginToAccessCq20": "CQ 2.0'a erişmek için yeni bir URL'ye ve oturum açmaya ihtiyacım olacak mı?", "willSmithOrderedFourItemsWithATotalOrderValueOf1210": "<PERSON>, <PERSON><PERSON> sipariş değeri 1,210 dolar olan dört ür<PERSON><PERSON> sipariş etti", "wouldYouLikeToCloneThisOrderWithAllItsContentsPleaseConfirm": "Bu siparişi tüm içeriğiyle klonlamak ister misiniz, lütfen onaylayın", "wouldYouLikeToConvertThisCartToQuote": "Bu Sepeti Teklife dönüştürmek ister misiniz?", "wouldYouLikeToSubmitYourCreditLimit": "Kredi Limitinizi Göndermek İster misiniz?", "writeTextHere": "<PERSON><PERSON><PERSON> metin yazın", "yearFounded": "Kuruluş Yılı", "yes": "<PERSON><PERSON>", "yesConfirm": "Evet, Onayla", "yesDelete": "Evet, Sil", "yesReject": "Eve<PERSON>, Reddet", "yesSuspend": "Evet, Askıya Al", "yesTerminate": "<PERSON><PERSON>, sonlandır", "youCanCoterminateYourSubscriptionWithAnExistingNonTrialCspNceSubscriptionOrAlignTheEndDateWithTheCalendarMonthByChoosingAnAppropriateEndDateDependingOnTheTermDuration": "Aboneliğinizi mevcut bir deneme dışı CSP NCE aboneliğiyle sonlandırabilir veya dönem süresine bağlı olarak uygun bir bitiş tarihi seçerek bitiş tarihini takvim ayıyla uyumlu hale getirebilirsiniz.", "yourQuoteIdqhskw123IsSubmittedBaşarılı bir şekilde": "<PERSON>k<PERSON><PERSON>: QHSKW123 Başarıyla Gönderildi.", "yourRequestForRenewalPriceBaşarılı bir şekildeSubmitted": "Yenileme Talebiniz\n Fiyat başarıyla gönderildi!", "zipPostalCode": "Posta Kodu", "asOndate": "<PERSON><PERSON><PERSON>", "creationDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commitmentEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commitmentStart": "Taahhüt Başlangıcı", "cancellationUntil": "İptal Tarihine <PERSON>", "bysubmittinganorderororderrevisionYourCompanyrepresentsthatanyCustomerPurchaseCommitmentprovidediscompleteandaccurateinallrespectsandagreestopayRedingtonforallordersitsubmitsforProducts": "Bir sipariş veya sipariş revizyonu göndererek, Şirketiniz herhangi bir Müşterinin Sağlanan Satın Alma Taahhüdü her bakımdan eksiksiz ve doğrudur ve aşağıdakileri kabul eder Ürünler için gönderdiği tüm siparişler için Redington'a ödeme yapın", "purchaseCommitmentCheck": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, son mü<PERSON><PERSON><PERSON> satın alma taahhüdünü aldığımızı onaylıyoruz bu sipariş veya sipariş revizyonu için ve talep üzerine aynısını sağlayacaktır.", "generaltncs": "General T&Cs", "viewMore": "View More", "vendortncs": "Vendor T&Cs", "renevalSuccessMsg": "Your Request for Renewal Price Successfully Submitted!", "headline": "CloudQuarks ile işletmenizi dönüştürün", "subheadline": "Büyümenin şekillenmesinin birçok yolu vardır. Redington, bulut ve Al ilerlemelerinin ön saflarında yer alan lider bir teknoloji distribütörüdür.", "signInTitle": "CloudQuarks Portal ile devam etmek için oturum açın", "emailLabel": "E-posta Adresi", "enterEmailPlaceHolder": "E-posta adresinizi girin", "rememberMe": "<PERSON><PERSON>", "forgotPassword": "Şifrenizi mi unuttunuz?", "signInButton": "Oturum Aç", "orContinueWith": "veya <PERSON>", "copyright": "©2025 Redington Grubu", "designedBy": "Hexalytics tarafından tasarlandı ve geliştirildi ", "allRightsReserved": "Tüm Hakları Saklıdır.", "manageQuickWidgets": "Hızlı Widget'ları Yönetin", "languageEnglish": "İngilizce - TR", "languageArabic": "Arapça - AR", "mea": "MEA", "accountNumberDefinition": "Redington Hesap <PERSON>, şirketiniz için Redington tarafından sağlanan benzersiz bir tanımlayıcıdır.", "redingtonAccountNumberIsADigitNumericValueExampleWhichIsTaggedWithSegmentCloudServices": "Redington Hesap <PERSON>, \"Bulut Hizmetleri\" segmentiyle etiketlenmiş 10 basamaklı bir sayısal <PERSON>ğerdir (örnek **********)", "contactRedingtonForAccount": "Lütfen Redington Hesap Numaranız konusunda size yardımcı olacak Redington Hesap Yöneticinizle iletişime geçin.", "nonRegisteredPartners": "Non-Registered Partners", "registeredPartnerDescription": "Would you like to register your company and become a part of Redington's Partner Ecosystem?", "proceedRegistrationButton": "Proceed to Registration", "newNotifications": "<PERSON><PERSON>", "getOnboarded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "home": "Ev", "goodDay": "<PERSON><PERSON> günler!", "welcomeTitle1": "na Hoş Geldiniz", "welcomeTitle2": "Redington Bulut Kuarkları", "platformMessage": "İŞİNİZİ DÖNÜŞTÜRMEK İÇİN TEK PLATFORM", "callToAction": "+3.213 <PERSON>ş Ortağı zaten katıldı. <PERSON><PERSON> hala bekliyorsun?", "callToActionLine2": "Deneyimlemek için ş<PERSON>di Üye Olun...", "watchVideo": "Videoyu İzleyin", "getOnboardedNow": "<PERSON><PERSON>", "nextStep": "<PERSON><PERSON><PERSON><PERSON>, şirketinizi CloudQuarks'a dahil edelim", "stepOne": "BİRİNCİ ADIM", "accountActiveMessage1": "Hesabınız aktif.", "accountActiveMessage2": "Gemiye hoş geldiniz!", "stepTwo": "İKİNCİ ADIM", "businessInformation": "İşletme Bilgileri", "shareholdingDetails": "Hisse tutma modelini ve önemli iletişim bilgilerini sağlayın - Yönetici/Sahip, Finans ve İmza Yet<PERSON>lisi", "stepThree": "ÜÇÜNCÜ ADIM", "shareDocuments": "<PERSON><PERSON><PERSON>", "shareDocumentsDescription": "<PERSON><PERSON><PERSON> r<PERSON>/bayi be<PERSON>, vergi be<PERSON>, banka hesap özetlerini incelenmek üzere yükleyin", "stepFour": "DÖRDÜNCÜ ADIM", "checkCompliance": "Uygunluğu Kontrol Edin", "checkComplianceDescription": "Kuruluş yılını, Redington T & C'lerine kabul sağlayın", "stepFive": "BEŞİNCİ ADIM", "brandCategoryDescription": "<PERSON><PERSON><PERSON> marka ve marka kategorilerini seçin", "majorVendors": "Major <PERSON><PERSON><PERSON>", "redAlIntro": "Generative Al Excellence ile Red.Al İnovasyonun Geleceği ile Tanışın", "knowMore": "<PERSON><PERSON>", "expiringSubscriptionsDays": "Süresi Dolan Abonelikler 7 Gün", "expiringSubscriptionsDays2": "Süresi Dolan Abonelikler 15 Gün", "simmonsOn": "<PERSON> ta<PERSON>", "totalValue": "<PERSON><PERSON>", "expiresAt": "<PERSON> kullanma tarihi", "quality": "<PERSON><PERSON>", "salePricePerUnit": "Satış Fiyatı/Birim", "dateOfPurchase": "<PERSON><PERSON>ın alma tarihi", "ytd": "YTD", "skuId": "SKU ID", "units": "Units", "minQuantity": "<PERSON>", "listPriceUnit": "Liste Fiyatı/Birim", "discountedPriceUnit": "İndirimli Fiyat/Birim", "skuIdLabel": "<PERSON><PERSON><PERSON><PERSON>", "contactAddress": "1, <PERSON><PERSON><PERSON>, H Hotel, İş Kulesi - Dubai, BAE", "EligibleBaseOffers": "<PERSON><PERSON><PERSON>lifle<PERSON>", "vendorSkuId2": "Satıcı SKU Kimliği", "promotionId": "Promosyon Kimliği", "off": "İndirim", "unitedArabEmirates": "Birleşik Arap Emirlikleri", "singapore": "Singapur", "select2": "<PERSON><PERSON><PERSON>", "select3": "Seçildi", "organizationAndTenants": "Organizasyon ve <PERSON>ılar", "count": "Say", "fulfilled": "Tamamlandı", "failed": "Başarısız", "orderValue": "Sipariş <PERSON>ğeri", "googlePer": "Google Per", "showingRecords": "<PERSON><PERSON><PERSON><PERSON><PERSON>: 10/100", "innovateFirstInnovateLast": "(<PERSON><PERSON> Son <PERSON>)", "subscriptionDetails": "Abonelik Detayları", "innovateItd": "Innovate ITD", "startsOn": "Başlangıç <PERSON>", "expiresOn": "Bitiş Tarihi", "orderStatus": "Sipariş Durumu", "deleteLineItem": "Delete Line Item", "listofSubscriptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BasedOnTheCreateDateSelectedDateRange": "Oluşturulan Tarih'e göre seçilen tarih aralığı", "subscriptionValue": "Subscription Value", "createDate": "<PERSON><PERSON><PERSON>", "productId": "<PERSON><PERSON><PERSON><PERSON>", "googleCloud": "Google Bulut", "direct": "Dolaysız", "productListing": "<PERSON><PERSON><PERSON><PERSON>", "searchProductNameSkuID": "Product Search by Product Name/SKU ID", "appliedFilters": "Uygulanan Filtreler", "categories": "Categories", "pricing": "pricing", "exploreProductsbyCategories": "Explore Products by Categories", "infrastructureServices": "Infrastructure Services", "businessApplications": "Business Applications", "software": "Software", "businessContinuity": "Business Continuity", "security": "Security", "solutions": "Solutions", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "page": "Say<PERSON>", "yourRecentPurchases": "Son <PERSON>", "expand": "Genişletmek", "collapse": "çökmek", "productsBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myRecentPurchases": "<PERSON>", "topSellingProductsbyStore": "Mağazaya Göre En Çok Satan Ürünler", "topSellingProductsbyAWS": "AWS'den En Çok Satan Ürünler", "topSellingProductsbyMicroSoft": "Microsoft'un En Çok Satan Ürünleri", "topSellingProductsbyGoogle": "Google'dan <PERSON>", "addtoQuote": "Teklife ekle", "addtoFavourite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterAPNID": "APN Kimliğini Girin", "apnText": "APN Kimliği, AWS ile işlem yapmak için her çözüm ortağı tarafından oluşturulacak AWS Çözüm Ortağı Ağı Kimliğidir. Bu tek seferlik bir faaliyettir ve sona erme süresi yoktur. APN ID, fırsatları, Eğitim ve Akreditasyonu yüklemek için erişim sağlayacaktır.", "noOfProducts": "Hayır. Ü<PERSON>n", "unRedeemedCoupons": "Kullanılmamış Kuponlar", "SKU": "SKU", "dateRedeemed": "Kullanıldığı tarih", "addNewCart": "<PERSON><PERSON>", "microsoftCSP": "Microsoft CSP", "quotesByStatus": "<PERSON><PERSON><PERSON> Gö<PERSON>", "valid": "<PERSON><PERSON>", "countOfQuotes": "<PERSON><PERSON><PERSON><PERSON>", "canceled": "İptal edilmiş", "in-review": "İncelemede", "createNewQuote": "<PERSON><PERSON>", "STEPONE": "BİRİNCİ ADIM", "STEPTWO": "İKİNCİ ADIM", "quoteInstructions1": "Pazar yerinden ürünleri seçin ve mevcut bir sepete ekleyin veya yeni bir alışveriş sepeti oluşturun", "quoteInstructions2": "Oluşturulan alışveriş sepetini ürünleriyle birlikte bir teklife dönüştürün ve teklifi onay için gönderin", "quoteID": "<PERSON><PERSON><PERSON><PERSON>", "cartID": "<PERSON><PERSON>", "initiatedBy": "<PERSON><PERSON><PERSON><PERSON>", "selectInitiatedby": "Başlatan'ı seçin", "selectCreatedBy": "Başlatan'ı seçin", "USD": "(USD)", "reSubmit": "<PERSON><PERSON><PERSON>", "remarkByPartner": "İş Ortağı Tarafından Yapılan Açıklama", "approvedQuantity": "<PERSON><PERSON><PERSON><PERSON>", "approvedPrice": "Onaylı Fiyat", "cancelQuote": "Teklifi <PERSON> Et", "max50Characters": "En fazla 50 karakter", "cancelInstructions": "Bu teklifi iptal etmek ister misiniz? Bu işlem geri alı<PERSON>, daha fazla ilerlemek için lütfen onaylayın..", "orderValueByTop3": "İlk 3'e Göre Sipariş Değeri", "bandCategory(USD)": "<PERSON><PERSON> (USD)", "orderValueByTop5": "Oİlk 5'e Göre Sipariş Değeri", "endCustomer(USD)": "<PERSON> (USD)", "csp": "CSP", "VATTRN": "KDV TRN", "TEL": "TEL", "redingtonAccountNumber": "Redington Hesap <PERSON>", "tenantID": "Kiracı Kimliği", "reference": "Referans", "estimatedVAT": "<PERSON><PERSON><PERSON> KDV", "QTY": "MİKTAR", "VAT": "KDV %", "lpoReference": "LPO referansı", "languageCurrency": "<PERSON><PERSON> ve <PERSON> Biri<PERSON>", "selectThelanguage": "Portala göz atmak için tercih ettiğiniz dili seçin", "announcementsType": "<PERSON><PERSON><PERSON>", "general": "<PERSON><PERSON>", "microsoftAIInnovation": "Microsoft yapay zeka, telekomünikasyon inovasyonunun fitilini ateşliyor", "andGrowth": "<PERSON>", "allNotifications": "<PERSON><PERSON><PERSON> bi<PERSON> gör", "activityDateRange": "Etkinlik Tarih <PERSON>ı", "selectEndDate": "Select End Date", "performedBy(User, System, API)": "Performed by (User, System, API)", "help": "Help", "audio": "Audio", "document": "Document", "lastUpdatedDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noFrameworkFound": "No framework found", "date": "Date", "records": "Records", "countofTickets": "Count Of Tickets", "tickets": "Tickets", "fileSize": "File Size", "max250Characters": "Max 250 Characters", "fileTypeSize": "SVG, PNG, JPG or GIF (MAX. 800x400px)", "typeDescription": "Tip Açıklama...", "mostViewed": "En Çok İzlenen Sorular", "whatIsCQ2": "Redington Bulut Kuarkları 2.0 nedir?", "whatIsNewInCQ2": "Redington Cloud Quarks 2.0'daki yeni<PERSON> neler<PERSON>?", "isCQ1Replaced": "Bulut Pazarı yeri Cloud Quarks 1.0, CQ 2.0 tarafından yeniden mi ele geçiriliyor?", "needNewLogin": "CQ 2.0'a erişmek için yeni bir URL'ye ve oturum açmaya ihtiyacım olacak mı?", "existingTransactions": "Mevcut işlemlerime, siparişlerime, aboneliklerime ve son kullanıcılarıma ne olacak?", "issuesContact": "Cloud Quarks 2.0'da herhangi bir sorunla karşılaşırsam kiminle iletişime geçmeliyim?", "stillUseCQ1": "Cloud Quarks 2.0'a geçiş yaptıktan sonra Cloud Quarks 1.0 platformunu kullanmaya devam edebilir miyim?", "gstRegistrationNumber": "GST Registration Number", "panNumber": "PAN Number", "isCompanyPartOfSEZ": "Is Company Part Of SEZ", "cinNumber": "CIN Number", "streetAddress1": "Street Address1", "state": "State", "postalCode": "Posta Kodu", "legalStatus": "Legal Status", "companyWebsite": "Company Website", "numberOfOfficesInRegion": "Number Of Offices In Region", "otherCountriesWithOffices": "Other Countries With Offices", "numberOfWarehousesInRegion": "Number Of Warehouses In Region", "numberOfEmployees": "Number Of Employees", "numberOfSalesStaff": "Number Of Sales Staff", "numberOfTechnicalStaff": "Number Of TechnicalStaff", "twitterAccount": "Twitter Account", "facebookAccount": "Facebook Account", "linkedInAccount": "LinkedIn Account", "instagramAccount": "Instagram Account", "enterGSTNumber": "Enter GST Number", "entervalue": "Enter value", "multiSelectDropdownForSelectingCountry": "Multi select dropdown for selecting country", "enterAccountURL": "Enter Account URL", "partnerProfile": "Partner Profile", "redingtonTermsConditions": "Redington - Terms & Conditions", "iAcceptRedingtonTermsandConditions": "I Accept Redington Terms and Conditions", "failedToFetchQuestionnaire": "Failed to fetch questionnaire", "oadingQuestionnaire": "Loading questionnaire", "weRequestvisitwebsite": "We request you to kindly visit our official careers website", "authenticInformation": "for authentic information pertaining to business association opportunity at Redington and enquire with the company to confirm if the offers or promotions are genuine.", "remove": "Kaldırmak", "clickToUploadOrDragAndDrop": "Yüklemek veya sürükleyip bırakmak için tıklayın", "editProfileDetails": "Profil <PERSON>larını Düzenle", "company": "Şirket", "accountInformation": "<PERSON><PERSON><PERSON>", "guestAccountInformation": "<PERSON><PERSON><PERSON><PERSON>", "ReviewyourguestregistrationdetailsbelowanddownloadthePDFofyourregistrationdata": "Aşağ<PERSON><PERSON><PERSON> misafir kayıt bilgilerinizi inceleyin ve kayıt verilerinizin PDF'sini indirin.", "companyCode": "Şirket Kodu", "itemPerPage": "Sayfa Başına <PERSON>", "selectLanguage": "<PERSON><PERSON>", "deleteCart": "Sepeti Sil", "wouldYouLikeToDeleteTheelectedCartYourActionCannotBeReversed": "<PERSON><PERSON><PERSON> sepeti silmek istiyor musunuz? Bu eylem geri alınamaz.", "discounts": "<PERSON><PERSON><PERSON><PERSON>", "continuePurchase": "Sa<PERSON>ın Almaya Devam Et", "optional": "İsteğe Bağlı", "itemCovered": "Ört<PERSON>lü", "expiringOn": "<PERSON><PERSON>", "sampleCartName": "Örnek Sepet Adı", "endCustomerDetails": "Son Müşteri <PERSON>", "more": "<PERSON><PERSON>", "secondaryValidations": "İkinci Doğrulamalar", "generalT&Cs": "Genel T&C'ler", "vendorT&C": "Satıcı T&C", "deleteAll": "Tümünü <PERSON>", "selectEligibleOffers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> te<PERSON>", "selectAlternateOffers": "Alternatif <PERSON>", "selectAvailablePromotions": "Mevcut promosyonları seçin", "eligibleOffers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alternateOffers": "<PERSON><PERSON><PERSON><PERSON>", "appsheetEnterprisePlus(Additional Service)": "Appsheet Enterprise Plus (Ek Hizmet)", "viewMoreDetails": "Daha Fazla Ayrıntı Görüntüle", "offerEndson": "<PERSON><PERSON><PERSON><PERSON>", "applyOffer": "<PERSON><PERSON><PERSON><PERSON>", "annualPlan(MonthlyPayment)": "Yıllık Plan (Aylık Ödeme)", "AnnualPlan(YearlyPayment)": "Yıllık Plan (Yıllık Ödeme)", "discountIncluded": "<PERSON><PERSON><PERSON>", "relatedProducts": "<PERSON>lgi<PERSON>", "crosssellProducts": "Kesiştirme Ürünleri", "upsellProducts": "Upsell Ürünleri", "sameAsDirector/Owner": "Yönetici/<PERSON><PERSON>", "keyContacts": "<PERSON><PERSON><PERSON>", "addAnotherContact": "Başka Bir Kişi Ekle", "customerStatus": "Customer Status", "requestfornewRelationship": "Request for new Relationship", "terminateRelationship": "Terminate Relationship", "invitationLink": "Invitation Link", "microsoftEntraroles": "Microsoft Entra roles", "durationinDays": "Duration In Days", "name": "Name", "autoExtend": "Auto Extend", "autoExtendBy": "Auto Extend By", "securityGroups": "Security Groups", "addSecurityGroup": "Add Security Group", "removeSecuritygroups": "Remove Security groups", "adminRelationshipName": "Admin Relationship Name", "gdap": "GDAP", "createanAdminRelationshipRequest": "Yönetici İlişkisi Talebi Oluşturun", "customerList": "Customer List", "organizationTenantConfiguration": "Organizasyon Tenant Yapılandırması", "selectaBrandCategory": "Bir Marka Kategor<PERSON>", "googleCloudPlatform": "Google Cloud Platform", "createNewCustomerTenant/Domain": "Yeni Müşteri Tenant/Etki Alanı Oluştur", "selectCustomerTenantType": "Müşteri Tenant Türü <PERSON>", "domainCustomer(recommended)": "Etki Alanı Müşterisi (Önerilir)", "teamCustomer": "Takım Müşterisi", "linkorTransferExistingCustomerTenant/Domain": "Mevcut Müşteri Tenant/Etki Alanını Bağlayın veya Aktarın", "entertheDomainName": "Etki Alanı Adını Girin", "checkAvailability": "Kullanılabilirlik Kontrolü", "existingCustomerTenantDetails": "Mevcut Müşteri Tenant Ayrıntıları", "belowAreTheCustomerDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ğ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z etki alanı adına karşılık gelen Google kayıtlarına göre müşteri ayrıntıları verilmiştir. Ayrıntıları kontrol edin ve bağlantı işlemini tamamlamak için “Kaydet”'e tıklayın.", "channelPartnerID": "Kanal İş Ortağı Kimliği", "customerCloudIdentityID": "Müşteri Bulut Kimlik Kimliği", "domain": "E<PERSON><PERSON>", "customerType": "Müşteri Türü", "customerCompanyName": "Müşteri Şirket Adı", "primaryEmail": "Birincil E-posta", "alternativeEmail": "Alternatif E-posta", "regionCode": "<PERSON><PERSON><PERSON>", "backtoOrg&TenantList": "Organizasyon ve Tenant <PERSON>", "yourRequestforLinkingaGoogleend": "Your request for linking a Google end", "customerTenantHasBeenBaşarılı bir şekilde": "Customer tenant has been Başarılı bir şekilde", "processed": "processed", "customerTenantLinked": "Customer tenant linked", "Başarılı bir şekilde!": "Başarılı bir şekilde!", "createNewDomainCustomer": "Yeni Etki Alanı Müşterisi Oluştur", "selectTheCustomerSegmentType": "Müşteri Segmenti Türünü Seçin", "educationalInstitution": "<PERSON>ğ<PERSON><PERSON>", "thecompanyInformationIsUsedToCreate": "The company information is used to create the initial administrator account  for Google Workspace and Google Cloud", "organizationName": "Organizasyon Adı", "CRMID": "CRM ID", "addressInformation": "<PERSON><PERSON>", "pincode": "Pincode", "contactInformation": "İletişim Bilgileri", "theNameAndEmailAddress": "The name and email address are used to create the initial administrator account for Google Workspace and Google Cloud.", "educationalInstituteType": "Eğitim Kurumu Türü", "pleaseSelectInstituteType": "Lütfen Eğitim Kurumu Türünü Seçin", "instituteSize": "Eğiti<PERSON> Ku<PERSON>", "pleaseSelectInstituteSize": "Lütfen Eğitim Kurumu Boy<PERSON>u <PERSON>", "add": "Eklemek", "pleaseselecttheBrandsandBrandCategories": "Lütfen Markaları ve Marka Kategorilerini seçin", "enterAWSAccountID": "AWS Hesap <PERSON> girin", "createNewTenant": "Yeni Kiracı Oluştur", "linkExistingTenant": "Mevcut Kiracıyı Bağla", "accountCreationInstructions": "To get started, you must create an account using your customer's company name followed by .onmicrosoft.com. Choose a name similar to the company name, with no spaces or punctuation. The primary domain name can’t be changed later. If your customer has its own custom domain that it wants to use without .onmicrosoft.com, you can change this later in Office 365 Admin Portal.", "tenantCreationMessage": "“redingtongulfdxbtest.onmicrosoft.com” is available. Please click Save to continue and create a new tenant for this customer.", "awsAccountIdInfo": "AWS He<PERSON><PERSON>, AWS kiracınıza atanan benzersiz bir tanımlayıcıdır. Hesap kimliğinizi AWS Yönetim Konsolu'nda bulabilirsiniz. AWS Yönetim Konsolu'nda oturum açın ve IAM konsolunu açın.", "signedInAccountInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gezinme panelinin en üstünde görünür.", "findTenantId": "Find Tenant ID", "check": "Check", "findTenantIdUsingCustomer": "Find Tenant ID using Customer's", "clickLinkButton": "Lütfen bağlantı butonuna tıklayın", "tenantIdInfo": "Tenant ID, also referred to as Directory ID or Customer ID, is a globally unique identifier (GUID) for your customer's Microsoft O365 or Azure Organization account. This ID is a 36-character alphanumeric value and is different from your tenant name or domain. For example: 824ddc74-1210-470a-8f4a-d0be4769346d. Please", "clickHere": "buraya tıklayın", "confirmCustomerTaxId": "Müşteri Vergi Kayıt Kimliğini Onaylayın", "taxRegistrationRequirement": "Bu müşteri için Vergi <PERSON>/<PERSON><PERSON> gereklidir. Microsoft, yeni müşteri kaydı için müşterinin Vergi Kay<PERSON>ği/<PERSON><PERSON>ı<PERSON>ın sağlanmasını zorunlu hale getirmiştir.", "microsoftAnnouncements": "Microsoft Duyuruları", "taxIdValidationMessage": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, eğik çizgi vb. gibi özel karakterler içermeden 5-22 alfanümerik karakterden oluşmalıdır. (Örnek: abc123)", "requestSubmitted": "<PERSON><PERSON><PERSON>", "Başarılı bir şekilde": "Başarılı bir şekilde", "enterSubscriptionNickName": "Abonelik Takma Adını Girin", "confirmCompanyTaxId": "Şirket Vergi Kayıt Kimliğini doğrulamak için lütfen müşteri temsilcinizle çalışın", "confirmMcaAcceptance": "Confirm Microsoft Customer Agreement (MCA) Acceptance", "microsoftCustomerAgreement": "Microsoft Customer Agreement (MCA)", "documentAcceptanceInstruction": "Please also document this acceptance in a sound and secure manner.", "viewDownloadAgreement": "View & Download Agreement", "endCustomerFirstName": "First Name of the end customer representative", "endCustomerLastName": "Last Name of the end customer representative", "endCustomerEmail": "Email Address of the end customer representative", "phoneFormatInfo": "Phone should be in international format starting with “+”", "dateOfAcceptance": "Date of Acceptance", "dateFormatInfo": "Date format in mm/dd/yyyy", "tenantDetails": "Tenant Details", "customerDomain": "Customer Domain", "username": "Username", "temporaryPassword": "Temporary Password", "addSubscription": "Add Subscription", "provideInfoAndConfirmAcceptance": "Please provide us with the following information and confirm that the end customer has accepted the", "displayInvitationUrl": "Display Invitation URL", "customerLinkingInvitationUrl": "Customer Linking Invitation URL", "invitationUrlDescription": "Here is the URL for inviting end customers to link their tenant. If you prefer to send the invitation from your own email address, you can copy the link and forward it to your end customer.", "copyLink": "Copy Link", "sendEmail": "Send Email", "sentEmail": "<PERSON><PERSON>", "cspInvitationSentMessage": "CSP invitation email has been <NAME_EMAIL>. The customer has to accept this invitation for you to add and manage Microsoft cloud licenses in their tenant.", "reverifyAfterCustomerAcceptance": "Once the customer accepts the request, please revisit this menu to reverify.", "saveTenantInstruction": "and save the Tenant", "resendInvitationInfo": "If required, you can also resend the invitation email using Resend.", "attention": "Attention", "shareMcaConfirmation": "Please share the Microsoft Customer Agreement (MCA) with your customer and confirm their acceptance. Use of cloud services through the CSP program is subject to the Microsoft Customer Agreement (MCA).", "viewAgreementLinkText": "Click here to view the agreement", "findId": "Find ID", "noData": "No Data", "documentInformation": "Document Information", "brandInformation": "Brand Information", "countOfContracts": "Count of Contracts", "contractStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contractEndDate": "Sözleşme Bitiş Tarihi", "nonActive": "<PERSON><PERSON><PERSON>", "areYouSureYouWantToRenewThisQuote": "Are you sure you want to renew this quote?", "schedulesBilledUnbilled": "Tarifeler - Faturalı/Faturasız", "organisationAndTenantId": "Kuruluş ve Kiracı kimliği", "requestsIn": "İstekler", "selectProductName": "Select Product Name", "renewalStartDate": "Renewal Start Date", "renewalEndDate": "<PERSON>wal End Date", "comments": "Comments", "partnerComment": "Partner Comment", "adminRemark": "Admin Remark", "yesCancel": "Yes, <PERSON>cel", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "period": "Dönem", "becomeVendor": "Satıcı Ol"}