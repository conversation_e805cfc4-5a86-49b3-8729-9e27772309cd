{"endSubscriptionNotice": "End the subscription permanently. Access is revoked, and data may be deleted after a retention period.", "requestedBy": "Requested By", "quickOrder": "Quick Order", "lastUpdatedDate28052024": "Last Updated Date: 28/05/2024", "users": "Users", "bookmark": "Bookmark", "quoteInformation": "Quote Information", "contractValue": "Contract Value", "transactionCurrencycurrencies": "Transaction Currency/Currencies *", "smith": "<PERSON>", "total": "Total", "selectRegionStateOrProvince": "Select Region, State, or Province", "allAnnouncements": "All Announcements", "companyRegVatTaxId": "Company Reg. / VAT / Tax ID", "enterZippostalCode": "Enter Zip/Postal Code", "markAsUnread": "<PERSON>read", "totalOrder": "Total Order", "selectOrderPlacedBy": "Select Order Placed by", "clone": "<PERSON><PERSON>", "outstandingInformation": "Outstanding Information", "totalOutstanding": "Total Outstanding", "purchaseInformation": "Purchase Information", "informationWidget": "Information Widget", "contractInfo": "Contract Info", "renewal": "Renewal", "availableCreditLimit": "Available Credit Limit", "companyTaxId": "Company Tax ID", "streetAddress": "Street Address", "redingtonDisclaimer1": "Further, Redington does not ask, solicit and accept any monies for any schemes in any\n form from the investors and business associates, whether online or otherwise. Redington\n bears no responsibility for amounts being deposited / withdrawn therefrom in response to\n such fake offers.", "notificationDate": "Notification Date", "invoice2": "Invoice #", "lastDays2": "Last 15 days", "brandSetup": "Brand Setup", "downloadCsv": "Download CSV", "supportedPlatformsHyperscalers": "Supported Platforms / Hyperscalers", "vendorSkuId2": "Vendor SKU ID", "networkingConnectivity": "Networking / Connectivity", "category1": "Category 1", "open": "Open", "pointOfContact": "Point of Contact", "languageCurrencySettings": "Language & Currency Settings", "advancedSearch": "Advanced Search", "enterYearFound": "Enter Year Found", "usd20000000": "USD 2,00,000.00", "selectExistingSubscription": "Select Existing Subscription", "cartName": "Cart Name", "toDate": "To Date", "lastName2": "Last Name *", "upgradePlanNote": "Move to a higher plan/ tier (e.g., from Basic to Pro) with more features, support, or capacity", "subscriptionStartDate": "Subscription Start Date", "listOfUsers": "List of Users", "productivityCollaboration": "Productivity & Collaboration", "totalItems": "Total Items", "info": "Info", "subscriptionLicenses": "Subscription & Licenses", "notDueUsd": "Not Due: USD 50,000.00", "addNewUser": "Add New User", "yesDelete": "Yes, Delete", "subscriptionDetail": "Subscription Detail", "yearFounded": "Year Founded", "contactRedingtonForAccount": "Please reach out to your Redington Account Manager\n who will assist you with your Redington Account Number", "or": "or", "dateRedeemedTi": "Date Redeemed TI", "allMultiSelect": "All (Multi select)", "descriptionOfIssueRequest": "Description of Issue/ Request", "invoice": "Invoice", "quotations": "Quotations", "invoiceNumber": "Invoice Number", "submitRequest": "Submit Request", "myTopCustomer": "My Top 5 Customer", "azurePlan": "Azure Plan", "validTill": "<PERSON><PERSON>", "invoiceBillDate": "Invoice Bill Date", "fileDetails": "File Details", "fileName": "File Name", "sales": "Sales", "accept": "Accept", "termsOfSale": "Terms of Sale", "redingtonCloudquarksPlatformUserAccountCreationTCs": "Redington CloudQuarks - Platform User Account Creation T&Cs", "password": "Password", "invoiceDetails": "Invoice Details", "accepted": "Accepted", "lastDays": "Last 7 days", "billType": "<PERSON>", "orderPlacement": "Order Placement", "agreements": "Agreements", "languageSettings": "Language Settings", "brandName": "Brand Name", "cartInformation": "Cart Information", "checkEligibleBaseOffer": "Check Eligible Base Offer", "endCustomerT1": "End Customer T1", "tag236": "Tag 236", "netValue": "Net Value", "verified": "Verified!", "nextInvoiceDate": "Next Invoice Date", "companyProfiles": "Company Profiles", "notifications": "Notifications", "totalBilled": "Total Billed", "primaryContact": "Primary Contact", "preTaxTotal": "Pre-Tax Total", "cartTitle": "Cart Title", "allRecords": "All Records", "quantity7": "Quantity:", "doMoreWithMicrosoftPromo": "Do more with Microsoft 365 E3 Promo", "order": "Order #", "yes": "Yes", "azurePlanUsageAe3": "Azure Plan Usage AE3", "profile": "Profile", "storageBackupDr": "Storage / Backup / DR", "totalQuotes": "Total Quotes", "existing": "Existing", "billedUsd": "Billed: USD 100,000.00", "mergeWithExistingSubscription": "Merge with Existing Subscription", "miniShop": "Mini Shop", "billingAddress": "Billing Address (1/2)", "lastUpdated": "Last Updated", "orderPrerequisiteNotice": "Prior to submitting an order or order revision, Your Company must obtain a Customer Purchase Commitment and provide the same to and Redington upon its request.", "selectTheCurrencyWith": "Select the currency you want to purchase with", "testReason": "Test reason...", "redeemed": "Redeemed", "addContinuePurchase": "Add & Continue Purchase", "teamReactive": "Team Reactive", "homePurchaseCart": "Home > Purchase Cart", "fraudulentOfferAlert": "This is to notify to the general public that some unscrupulous persons are unauthorizedly\n using the name of Redington Limited; Redington Group/ Affiliates (\"Redington\") and posing\n themselves as employees, representatives, agents of Redington and its associated/group\n companies, with ulterior motive to earn wrongful gain and/or cheat the prospective\n investors and business associates and are fraudulently offering investment opportunity\n online through certain websites or through telephone calls or by issuing fake offer letters\n and also soliciting them to deposit some amount in certain bank accounts. These people\n are also unauthorizedly using the name, trademark, domain name and logo of Redington\n with a view to tarnish the image and reputation of Redington.", "companyHexalytics": "Company: Hexalytics", "innovateLimitedDetails": "Innovate Limited / Details", "salesContactName": "Sales Contact Name", "download": "Download", "type1": "Type 1", "india": "India", "azure": "Azure", "dateOfPurchaseTu": "Date of Purchase TU", "reEnterPassword": "Re-Enter Password", "duplicate": "Duplicate", "directPhone": "Direct Phone", "dynamicsCustomerServiceEnterpriseNonProfitPricing": "Dynamics 365 Customer Service Enterprise (Non-Profit Pricing)", "cloudLaasPaas": "Cloud laas / PaaS", "expired": "Expired", "knowledgeBase": "Knowledge Base", "knowledgeHub": "Knowledge Hub", "invoiceManagement": "Invoice Management", "newRequest": "New Request", "commercialMarketplaceTest": "Commercial Marketplace Test", "basedOnTheSelectedDateRange": "Based on the selected date range", "transactionAmount": "Transaction Amount", "legalActionWarning": "Redington reserves the right to take legal action, including criminal action, against such\n individuals/entities", "innovateLimitedOrders": "Innovate Limited", "totalInvoiceValueUsd": "Total Invoice Value (USD)", "overallStatus": "Overall Status", "requestReason": "Request Reason", "nickName2": "<PERSON>*", "cancel": "Cancel", "creditLimitUtilization": "Credit Limit & Utilization", "redeemedCoupons": "Redeemed Coupons", "consumed2": "Consumed :", "dateCreated10052024": "Date Created: 10/05/2024", "consumedUsd": "Consumed : USD 250,000.00", "cancelled": "Cancelled", "addNewTicket": "Add New Ticket", "overdue": "Overdue", "availableCredit": "Available Credit", "createdOn": "Created On:", "approvedOn": "Approved On:", "available": "Available", "financePaymentTerms": "Finance / Payment Terms", "listOfDiscountCoupons": "List of Discount Coupons", "frequentlyAskedQuestionsFaqs": "Frequently Asked Questions (FAQs)", "customerName": "Customer Name", "pleaseSelectTheDropDownToUpgrade": "Please select the drop down to Upgrade", "uae": "UAE", "chargeStartDate": "Charge Start Date", "partner": "Partner", "currencyFu": "Currency fu", "enterLegalEntityName": "Enter Legal Entity Name", "taxIdentificationNumber": "Tax Identification Number", "marketplace": "Marketplace", "reportTitle": "Report Title", "contactNumber": "Contact Number", "backToHome": "Back to Home", "googleWorkspaceBusinessPlus": "Google Workspace Business Plus", "assignedCreditLimitExisting": "Assigned Credit Limit (Existing)", "insights": "Insights", "select2": "Select", "select3": "Selected", "pass": "Pass", "quantity": "Quantity", "partnerRemarks": "Partner Remarks", "timeStamp": "Time Stamp", "addGoToCart": "Add & Go to Cart", "unbilledCharges": "Unbilled Charges", "image": "Image", "vendorSkuId": "Vendor SKU ID : DZH318Z09PGJ:0007", "resellerId": "Reseller ID", "guestAccount": "Guest Account", "guyHawkinsDeliveredTheOrder": "<PERSON> delivered the order", "cloudquarksOnboarding": "CloudQuarks - Onboarding", "currencyLanguage": "Currency & Language", "changeProductOrTermLengthOrBillFrequency": "Change product or term length or bill frequency.", "accountManagementMyProfile": "Account Management > My Profile", "redingtonGulfFze": "Redington Gulf FZE", "nonAffiliatedOfferNotice": "<PERSON><PERSON> will not be responsible to anyone acting on an offer not directly made by\n <PERSON><PERSON>", "country": "Country", "enterEmail": "<PERSON><PERSON>", "termsOfUse": "Terms of Use", "subscriptionCount": "Subscription Count", "billingSummary": "Billing Summary", "preliminaryValidations": "Preliminary Validations", "********": "31/12/2025", "dueDays": "Due Days", "cdpId": "CDP ID", "website": "Website", "totalOrderValue": "Total Order Value", "quoteCreated": "Quote Created", "supportHubVideoAndHowTos1": "Support Hub Video and How Tos (1)", "openCart": "Open Cart", "alertsAnnouncements": "Alerts / Announcements", "yesReject": "Yes, Reject", "descriptionDetails": "Description/ Details", "orderId": "Order ID", "store": "Store:", "netPrice": "Net Price", "renewalManagementRenewing": "Renewal Management / Renewing", "lifeCycleManagement": "Life Cycle Management", "validation": "Validation", "userAccessAndPermission": "User Access and Permission", "nickName": "<PERSON>", "category04": "Category 04", "performedByUserSystemApi": "Performed by (User, System, API)", "hexalytics": "Hexalytics", "selectBrandCategory": "Select Brand Category", "listPrice": "List Price", "theFirstGroup": "The First Group", "homePage": "Home Page", "contractSchedule": "Contract Schedule", "revisedAssignedCreditLimit": "Revised - Assigned Credit Limit", "createNewSubscription": "Create New Subscription", "email2": "Email", "characterLimit50": "Character Limit: 50", "redingtonGroupAllRightsReserved": "Redington Group | All Rights Reserved.", "1Year3": "• 1 Year", "confirmToProceed": "Confirm to proceed", "role": "Role", "chargeStart": "Charge Start", "reservedVmInstance": "Reserved Vm Instance, Cool_GRS_Data_Stored_10", "invoiceDescription": "Invoice Description", "newSubscriptionQtyWillBe6": "New Subscription Qty will be ", "totalQuoteValue": "Total Quote Value", "purchaseCommitmentObligations": "Purchase Commitment Obligations", "usd": "USD", "invoicesOpen": "Invoices - Open", "addToNewCart": "Add to New Cart", "endCustomerPurchaseCommitmentConfirmation": "End Customer Purchase Commitment Confirmation", "wouldYouLikeToSubmitYourCreditLimit": "Would you like to Submit your Credit Limit", "ksa": "KSA", "order41400000429": "Order # 41400000429", "alex": "<PERSON>", "save": "Save", "hp": "HP", "discountValue": "Discount Value", "orderValueByTop3BrandCategoryUsd": "Order Value By Top 3\nBrand Category (USD)", "brandCategor": "Brand Categor", "testTile": "Test Tile", "assignedCredit": "Assigned Credit", "totalOverdueByBrandUsd": "Total Overdue By Brand (USD)", "tax2": "Tax", "percentDiscount": "Percent Discount", "maxCharacters50Words": "Max Characters: 50 Words", "subStatus": "Sub Status", "hello": "Hello!", "selectOrderType": "Select Order Type", "msAppsForEnterpriseDesc": "Microsoft 365 Apps for Enterprise is a subscription-based productivity suite designed for large organizations, offering the latest versions of Microsoft Office applications, enhanced security, and cloud-based collaboration features.", "startDatel": "Start datel", "markAsUnread2": "<PERSON> as Unread", "chargeEnd": "Charge End", "salesOrders": "Sales > Orders", "initiate": "Initiate", "medium": "Medium", "accountNumberDefinition": "Redington Account Number is a unique identifier\n provided by Redington for your company", "seeAllNotifications": "See All Notifications", "activity": "Activity", "softwaredigitalProducts": "Software/Digital Products", "listOfRecords": "List of Reports", "discountDetails": "Discount Details", "analytics": "Analytics", "1Year": "1 Year", "topProductsForUae": "Top Products For UAE", "unbilledUsageValue": "Unbilled Usage Value", "multiple": "Multiple", "enterEmailAddress": "Enter Email Address-", "alertsNotifications": "Alerts / Notifications", "selectRequestedBy": "Select Requested by", "minQty": "<PERSON>", "approverRemarks": "Approver Remarks", "announcementType": "Announcement Type", "totalConsumed": "Total Consumed", "aboutThisItem": "About This Item", "homeSalesDiscountCoupons": "Home > Sales > Discount Coupons", "createdDate": "Created Date", "expiryDate": "Expiry Date", "dateAccepted": "Date Accepted", "openTickets": "Open Tickets", "primaryContactTitle": "Primary Contact Title", "requestPaymentOfAnyKindFromProspectiveInvestor": "Request payment of any kind from prospective investor", "requestReason4": "Request Reason *", "emai": "<PERSON><PERSON>", "showMore": "Show More...", "grouped": "Grouped", "orderManagement": "Order Management", "currentMonth": "Current Month", "orderValueByTop3BrandCategoryUsd2": "Order Value By Top 3\n Brand Category (USD)", "user": "User", "createdBy": "Created By", "applyFilter": "Apply Filter", "ubscripionIdr": "ubscripion IDr", "designation": "Designation", "hexalyticsUae1000123597": "Hexalytics, UAE (1000123597)", "dynamicsSubscriptionUser": "User subscription that includes Dynamics 365 Customer Service, Enterprise edition", "reportType2": "Report Type *", "department": "Department*", "getSupported": "Get Supported", "subscriptionEndDate": "Subscription End Date", "theOnlySelfServicePlatformWithManagedServicesCapabilities": "The only Self-Service platform with managed services capabilities.", "signingAuthorityName": "Signing Authority Name", "productName": "Product Name", "rejectQuote": "Reject Quote", "couponId1101": "Coupon ID:1101", "manageCompanyProfile": "Manage Company Profile", "discount": "Discount", "expiring": "Expiring", "openCartsSelect": "Open Carts - Select", "consumeCloudquarksApis": "Consume CloudQuarks APIs", "primaryContactInformation": "Primary Contact Information", "supportHubDocumentAndHowTos1": "Support Hub Document and How Tos (1)", "unbilled": "Unbilled", "subscriptionUpdateRequestHasBeenSubmitted": "Subscription Update request has been submitted.", "azureEntitlements": "Azure Entitlements", "addNewEndCustomer": "Add New End Customer", "metricScoreCards": "Metric Score Cards", "back": "Back", "fullTimestamp": "Full Timestamp", "enterApnId": "Enter APN ID", "due": "Due:", "rejected": "Rejected", "addToCart": "Add to Cart", "totalUnbilled": "Total Unbilled", "priority": "Priority", "agreementName": "Agreement Name", "audioDocument": "Audio Document", "openingBalance": "Opening Balance", "firstName2": "First Name*", "status": "Status", "invoiceDate2": "Invoice Date:", "selectBrand": "Select Brand", "minutesAgo": "3 Minutes ago", "billingScheduleUsd": "Billing Schedule (USD)", "lastName": "Last Name", "resendMail": "Resend mail", "aws": "AWS", "enterLinkedinUrl": "Enter Linkedin URL", "unauthorizedMonetaryDeal": "Authorise anyone to collect money or arrive at any monetary arrangement in return for any business opportunity at Redington", "selectCustomerVertical": "Select Customer Vertical", "serviceDesk": "Service Desk", "oracle": "Oracle", "lastDays4": "Last 60 days", "skuid": "SKUID", "optionsTerm": "Options : Term (2)", "redington": "<PERSON><PERSON>", "yourQuoteIdqhskw123IsSubmittedSuccessfully": "Your Quote ID:QHSKW123 is Submitted Successfully.", "listOfCustomer": "List of Customer", "addNew": "Add New", "productDetailedView": "Product Detailed View", "convertCartToQuote": "Convert <PERSON><PERSON> to Quote", "marketplaceProfile": "Marketplace Profile", "enterLastName": "Enter Last Name", "activityLog": "Activity Log", "fakeSolicitationWarning": "<PERSON><PERSON> strongly recommends that the potential investors business associates should not respond to such fake solicitations, in any manner", "googleWorkspaceCommitment": "Google Workspace Commitment", "invoicesValueByBillType": "Invoices Value by Bill Type", "vatCostChangeDisclaimer": "Net Order Value quoted above is inclusive of Value Added Taxes (VAT) as currently applicable. Any additional cost implication arising from change in local taxes structure will lead to increase in\n prices to that effect at the time of billing and this will be borne by the partner and on partner's account", "doItYourselfOrGetItDoneByAnExpert": "Do it yourself or get it done by an Expert!", "address1": "Address1", "description": "Description", "selectSector": "Select Sector", "agreement": "Agreement", "outstanding": "Outstanding", "reportType": "Report Type", "attachments": "Attachments", "finopsCostManagement": "FinOps / Cost Management", "no": "No", "financeOutstanding": "Finance / Outstanding", "cartId3": "Cart ID: CT100001", "serviceRequest": "Service Request", "selectTheLanguageYouPreferForBrowsingThePlatform": "Select the language you prefer for browsing the platform", "new": "New", "purchaseInfo": "Purchase Info:", "authorizedSignatory": "Authorized Signatory", "softwareUpgradeRequestHasBeenSubmitted": "Software Upgrade request has been submitted.", "vat": "VAT", "delphiConsultingLlc": "Delphi Consulting LLC", "uaeSharjah": "UAE - Sharjah", "redingtonOfferClarification": "Redington avails certain services through professional investment agencies. Even in\n those cases, offers are always made directly by Redington and not by any third parties.", "complete": "Complete", "validTillDate": "<PERSON><PERSON>", "countryOfBusiness": "Country of Business", "supportingDocument": "Supporting Document", "endCustomerName": "End Customer Name", "invoiceDate": "Invoice Date", "newPurchase": "New Purchase", "addBrands": "Add Brands", "requestType": "Request Type", "innovateLimitedOrder***********": "Innovate Limited / Order # ***********", "dayAgo": "1 day ago", "expiringSubscriptionDays2": "Expiring Subscription 15 Days", "format": "Format", "pleaseWaitForTheRedingtonResponse": "Please wait for the Redington response", "itemDetails": "<PERSON><PERSON>", "olderNotifications": "Older Notifications", "validUntil": "<PERSON>id <PERSON>", "turkey": "Turkey", "alli": "AllI", "firstName": "First Name", "fileType": "File Type", "primaryContactName": "Primary Contact Name", "enterNumber": "Enter Number", "currentWeek": "Current Week", "cloneTheOrder": "Clone the order", "enterCompanyRegvattaxId": "Enter Company Reg./VAT/Tax ID", "blockThisUser": "Block This User?", "addUser": "Add User", "writeTextHere": "Write text here", "orderPlacedOn": "Order Placed on", "roductNam": "roduct Nam", "domainId": "Domain ID", "totalOrders": "Total Orders", "fileDescription": "File Description", "unbilledUsd": "Unbilled : USD 150,000.00", "monthlyAnnual": "• Monthly Annual", "azureRi": "Azure RI", "requestAQuote": "Request a Quote", "usageBased": "Usage Based", "saveChanges": "Save Changes", "expiringSubscriptionsDays": "Expiring Subscriptions 7 Days", "regionstateprovince": "Region/State/Province", "acceptedOn": "Accepted on", "paymentTerms": "Payment Terms", "markAsRead": "<PERSON>", "favourites": "Favourites", "availableUsd": "Available: USD 250,000.00", "newCart": "New Cart", "delete": "Delete", "prev": "Prev", "downloadAsPdf": "Download as PDF", "hourAgo": "1 hour ago", "lastNam": "Last Nam", "hexalyticsLlcUae": "HEXALYTICS LLC-UAE", "currency": "<PERSON><PERSON><PERSON><PERSON>", "items": "Items", "invoiceBillDate2": "Invoice Bill Date *", "fieldsAreMandatory": "Fields are Mandatory", "englishAed": "English - AED", "allTime": "All Time", "contractId": "Contract ID", "dateAndTime": "Date and Time", "chargeDescription": "Charge Description", "completeMatch": "Complete Match", "success": "Success", "enterCity": "Enter City", "detailedInformation": "Detailed Information", "yesSuspend": "Yes, Suspend", "video": "Video", "currentPassword": "Current Password", "expiringSubscriptionDays": "Expiring Subscription 7 Days", "next": "Next", "anyoneMakingAnyInvestmentBusinessAssociationOfferInReturnForMoneyOrWhoIsNotAuthorizedByRedington": "Anyone making any investment/business association offer in return for money or who is\n not authorized by Redington", "uaeDubai": "UAE - Dubai", "dateCreated100520244": "Date Created: 10/05/2024|", "info4": "Info.", "brandCategory": "Brand Category", "paymentTerm": "Payment Term :", "off": "Off", "accountManagement": "Account Management", "billingDetails": "Billing Details", "contracts": "Contracts", "creditAvailable": "Credit Available", "customerIdorganizationId": "Customer ID/Organization ID", "resetToDefaultSettings": "Reset to default settings", "doNotRenew": "Do Not Renew", "newPassword": "New Password", "integrateOwnApls": "Integrate Own APls", "signOut": "Sign out", "alphaSystems": "Alpha Systems", "flexible": "Flexible", "dueDate": "Due Date", "renewalManagementPending": "Renewal Management > Pending", "email": "Email", "goToExpiringList": "Go to Expiring List", "listPriceAfterPromo": "List Price After Promo", "enterTheValue": "Enter the value", "category05": "Category 05", "inReview": "In Review", "linkedinSocialHandle": "Linkedin / Social Handle", "search": "Search", "enterCompanyName": "Enter Company Name", "resolutionRemarksFromTheSolutionArchitectServiceDeskAgent": "Resolution remarks from the solution Architect/ Service Desk Agent", "selectTheCheckboxToViewTheEstimatedCharges": "Select the checkbox to view the estimated charges", "thefirstgrouponmicrosoftcom": "thefirstgroup.onmicrosoft.com", "totalUnbilledByBrandCategoryUsd": "Total Unbilled by Brand Category (USD)", "catalog": "Catalog", "pleaseNote": "Please Note", "noLater": "No, Later", "quotes": "# Quotes", "customerReadiness": "Customer Readiness", "enterMobileNumber": "Enter Mobile Number", "brand": "Brand", "yesTerminate": "Yes, Terminate", "orderPlacedBy": "Order placed by", "helpCenter": "Help Center", "productDetails2": "Product Details", "approxSkus": "Approx. SKUs", "getOffOnOrdersAboveQuantity": "Get 10% off on orders above 5 Quantity.", "basicInformation": "Basic Information", "endCustomer": "End Customer", "others": "Others", "selectTimeZone": "Select Time Zone", "marketplaceSearch": "Marketplace Search", "supportSlaTiersOffered": "Support SLA Tiers Offered", "customerManagementCustomers": "Customer Management > Customers", "testAudioFile0069": "Test Audio File 0069", "softwareUpgradeRequest": "Software Upgrade Request", "department2": "Department", "weRequestYouToKindlyVisitOurOfficialCareersWebsiteHttpsRedingtongroupComForAuthenticInformationPertainingToBusinessAssociationOpportunityAtRedingtonAndEnquireWithTheCompanyToConfirmIfTheOffersOrPromotionsAreGenuine": "We request you to kindly visit our official careers website https://redingtongroup.com/for\n authentic information pertaining to business association opportunity at Redington and\n enquire with the company to confirm if the offers or promotions are genuine.", "financeCreditLimitUtilization": "Finance / Credit Limit & Utilization", "commercialMarketPlace": "Commercial Market Place", "generateNewReport": "Generate New Report", "targetMarketsstores": "Target Markets/Stores", "tags2": "Tags:", "link": "Link", "redingtonDoesNot": "<PERSON><PERSON> does not:", "block": "Block", "product": "Product", "microsoft365BusinessBasicCfq7ttcolh180001": "Microsoft 365 Business Basic | CFQ7TTCOLH18:0001", "totalContractsValue": "Total Contracts Value", "salePriceUnitTu": "Sale Price/Unit TU", "billFrequency": "<PERSON>", "finance": "Finance", "category06": "Category 06", "currencyTu": "<PERSON><PERSON><PERSON><PERSON>", "outstandingUsd": "Outstanding: USD 50,000.00", "trainingCertification": "Training & Certification", "microsoft365F1": "Microsoft 365 F1", "amazon": "Amazon", "productDetails": "Product Details", "decrease": "Decrease", "listOfContracts": "List of Contracts", "cloneContent": "Would you like to clone this order with all its contents, please confirm", "netAmount": "Net Amount", "quickBrands": "Quick Brands", "buyingOptions": "Buying Options", "couponDetails": "Coupon Details", "reportTile": "Report Tile", "oderPlacedBy": "Oder Placed by", "unbilledUsage": "Unbilled Usage", "aboutCompany": "About Company", "totalOrdersUsd": "Total Orders (USD)", "monthly": "Monthly", "qty": "Qty", "default": "<PERSON><PERSON><PERSON>", "subscriptionId": "Subscription ID", "tenantName": "Tenant Name", "eventSpecificDataFields": "Event Specific Data Fields", "testDescription": "Test description..", "aedUnitedArabEmiratesDirhamDefault": "AED - United Arab Emirates Dirham (Default)", "technicalEnablement": "Technical Enablement", "address2": "Address2", "promotionType": "Promotion Type", "domair": "<PERSON><PERSON>", "orders": "Orders", "subscriptions": "Subscriptions", "pleaseNoteThatTheSubscriptionUpgradeCannotBeReversedOnceRequested": "Please note that the subscription upgrade cannot be reversed once requested.", "legalEntityName": "Legal Entity Name", "orderPlacedBy2": "Order Placed by", "home": "Home", "createANewCart": "Create a New Cart", "brandManagement": "Brand Management", "redemptionDetails": "Redemption Details", "promotionId": "Promotion ID", "hexalyticsLlcKsa": "HEXALYTICS LLC-KSA", "billingProfile": "Billing Profile (1/6)", "fromDate": "From Date", "financeStatementOfAccounts": "Finance / Statement of Accounts", "renewalPrice": "Renewal Price", "expiringSubscriptions": "Expiring Subscriptions", "mobileNumber": "Mobile Number", "enterWebsiteUrl": "Enter Website URL", "hexalyticsPvtLtd": "Hexalytics Pvt Ltd", "myAccount": "My Account", "acceptanceOfTerms": "Acceptance of Terms", "oneDescriptionAboutTheProductsAddedInTheCartAndOffers": "One description about the products added in the cart and offers", "terminate": "Terminate", "endDate": "End Date", "nA": "N/A", "education": "Education", "amount": "Amount", "ticketsInReview": "Tickets in Review", "lastUpdatedDate280520242": "Last Updated Date : 28/05/2024", "chargeDetails": "Charge Details", "read": "Read", "oldCreditLimitS": "Old Credit Limit (S)", "platform": "Platform", "listOfQuestions": "List of Questions", "selectEndCustomer": "Select End Customer", "increaseBy": "Increase By", "destinationSubscription": "Destination Subscription", "phoneNumber": "Phone Number", "listOfReportsShowing": "List of Reports Showing", "skuId2": "SKU ID : DZH318Z009PGJ:0007", "sector": "Sector", "personalInformation": "Personal Information", "submittedOn": "Submitted On:", "microsoft365BusinessBasic": "Microsoft 365 Business Basic", "grandTotal": "Grand Total", "selectRequestReason": "Select Request Reason", "active": "Active", "renewalManagementApproved": "Renewal Management / Approved", "enterAccountId": "Enter Account ID", "partnerReadiness": "Partner Readiness", "brands": "Brands", "agreementType": "Agreement Type", "innovateLimitedOrganizationTenants": "Innovate Limited / Organization & Tenants", "onboardNewCompany": "Onboard New Company", "close": "Close", "upgrade": "Upgrade", "theEstimatedProrateCostIs224025Usd": "The estimated prorate cost is 2,240.25 USD|", "services": "Services", "termsAndConditions": "Terms and Conditions", "accountManagementBrandSetUp": "Account Management > Brand Set Up", "create": "Create", "endDateAlignment": "End date Alignment", "enterMiddleName": "Enter Middle Name", "invoicesByTop5Customers": "Invoices by Top 5 Customers", "revisedCreditLimit": "Revised Credit Limit($)", "qualityTu": "Quality TU", "totalUnbilledUsd": "Total Unbilled (USD)", "reports": "Reports", "invoiceValue": "Invoice Value", "totalReceivablesUsd": "Total Receivables (USD)", "createANewCartFromCoupon": "Create a New Cart from Coupon", "increase": "Increase", "redingtonSalesContacts": "Redington Sales Contacts", "creditManagement": "Credit Management", "requestCreditLimit": "Request Credit Limit", "canIStillUseTheCloudQuarks10PlatformAfterWeHaveMigratedToCloudQuarks20": "Can I still use the Cloud Quarks 1.0 platform, after we have migrated to Cloud Quarks 2.0?", "companyName2": "Company Name *", "viewRequest": "View Request", "totalTickets": "Total Tickets", "suspend": "Suspend", "noOfProduc": "No. of Produc", "offerDetails": "Offer Details", "invoiceDueDate": "Invoice Due Date", "vendorDisplayNameShownToBuyers": "Vendor Display Name (Shown to Buyers) *", "totalAssigned": "Total Assigned", "resolutionRemarks": "Resolution Remarks", "redingtonAccountId": "Redington Account ID*", "expiringSubscriptionsDays2": "Expiring Subscriptions 15 Days", "enterCartTitle": "<PERSON><PERSON> Cart Title", "discountCoupons": "Discount Coupons", "permission": "Permission", "selectAny3": "Select any 1", "getToKnowUs": "Get to Know Us", "regionStateOrProvince": "Region, State or Province", "subscriptionName": "Subscription Name", "documentLastEmailedOn": "Document Last Emailed On", "use": "Use", "overdueAgeingUsd": "Overdue - Ageing (USD)*", "searchByCartIdCartNameCreatedByCustomer": "Search by Cart ID/Cart Name/Created by/Customer", "requestedAssignedCreditLimit": "Requested -Assigned Credit Limit", "enterRemarks": "Enter Remarks", "lastDays3": "Last 30 days", "ticket": "Ticket #", "subTotal": "Sub Total", "faqs": "FAQs", "billTypc": "<PERSON>", "wouldYouLikeToConvertThisCartToQuote": "Would you like to convert this Cart to Quote?", "change": "Change", "descriptior": "Descriptior", "billTypeMonthlyYearly": "Bill Type : Monthly, Yearly", "targetPrice": "Target Price", "reject": "Reject", "other": "Other", "enterEmailAddress2": "Enter Email Address", "approved": "Approved", "alerts": "<PERSON><PERSON><PERSON>", "robertFox": "<PERSON>", "renewalManagementExpired": "Renewal Management / Expired", "pleaseConfirmToProceed": "Please confirm to proceed.", "announcements": "Announcements", "dell": "Dell", "allFiles": "All Files", "zipPostalCode": "Zip / Postal Code", "enterFirstName": "Enter First Name", "reportRequestDate": "Report Request Date", "r032AdvancePayment": "R032 Advance Payment", "requestedE": "requested E", "uaeAbuDhabi": "UAE - Abu Dhabi", "category01": "Category 01", "totalValueUsd2": "Total Value: USD 500.00", "totalValueUsd": "Total Value: USD 50,000.00", "category": "Category", "signingAuthorityTitle": "Signing Authority Title", "permittedCount": "Permitted Count", "solicitAnyInvestmentInSchemesFromFreeEmailServicesLikeGmailRediffMailYahooMailEtc": "Solicit any investment in schemes from free email services like Gmail, Rediff mail, Yahoo mail, etc", "fileCategory": "File Category", "edit": "Edit", "endDate2": "End date", "selectStatus": "Select Status", "onboarding": "Onboarding", "netOrderValueQuotedAboveIsInclusiveOfValueAddedTaxesVatAsCurrentlyApplicableAnyAdditionalCostImplicationArisingFromChangeInLocalTaxesStructureWillLeadToIncreaseInPricesToThatEffectAtTheTimeOfBillingAndThisWillBeBorneByThePartnerAndOnPartnersAccount": "Net Order Value quoted above is inclusive of Value Added Taxes (VAT) as currently applicable. Any additional cost implication arising from change in local taxes structure will lead to increase in prices to that effect at the time of billing and this will be borne by the partner and on partner's account", "offerId": "Offer ID", "segmentCommercial": "Segment : Commercial", "dynamicsCustomerServiceEnterpriseDevice": "Dynamics 365 Customer Service Enterprise Device", "renew": "<PERSON>w", "orderDetails": "Order Details", "startDate": "Start date", "faq": "FAQ", "specify": "Specify", "upcomingExpiration": "Upcoming Expiration", "tagsProduct": "Tags: Product", "paynowAzure": "Paynow Azure", "yesConfirm": "Yes, Confirm", "rfq1001": "RFQ1001", "all": "All", "listPriceUnit": "List Price/Unit", "consumed": "Consumed", "declined": "Declined", "contractValueTotal": "Contract Value Total", "renewalManagementExpiring": "Renewal Management / Expiring", "theServicesThatRedingtonProvides": "The services that Redington provides to you are subject to the following Terms of use\n (\"TOU\"). Redington reserves the right to update and modify the TOU at any time without\n notice to you. The most current version of the TOU can be reviewed by clicking on the\n \"Terms of Use\" hyperlink located at the bottom of our webpages. When we make updates\n to the TOU, <PERSON><PERSON> will update the date at the top of this page. By using the website\n after a new version of the TOU has been posted, you agree to the terms of such new\n Version.", "settings": "Settings", "redingtonAccountNumberIsADigitNumericValueExampleWhichIsTaggedWithSegmentCloudServices": "Redington Account Number is a 10 digit numeric value\n (example **********) which is tagged with segment\n \"Cloud Services", "productType": "Product Type", "companyProfile": "Company Profile", "albertFloresDeliveredTheOrder": "<PERSON> delivered the order", "myCompanyS": "My Company's", "term": "Term", "segment": "Segment", "closingBalanceOutstanding": "Closing Balance Outstanding", "tax": "Tax (5.00%) :", "usd500000000": "USD 5,000,000.00|", "temporarilyDisableTheSubscriptionWithoutTerminatingItAccessIsBlockedButDataIsRetainedOftenUsedForSeasonalNeeds": "Temporarily disable the subscription without terminating it. Access is blocked but data is retained. Often used for seasonal needs.", "showingRecords": "Showing 10/100 Records", "addMoreUsersStorageFeaturesOrOtherResourcesUnderTheCurrentSubscriptionOftenInvolvesAdditionalCost": "Add more users, storage, features, or other resources under the current subscription. Often involves additional cost.", "myAccessAndPermissions": "My Access and Permissions", "decreaseBy": "Decrease By", "pending": "Pending", "commercial": "Commercial", "comment": "Comment", "activityType": "Activity Type", "renewing": "Renewing", "selectTheCurrencyYouWantToShopWith": "Select the currency you want to shop with", "monthlyYearly": "Monthly, Yearly", "newNotifications": "New Notifications", "requestForRenewalPrice": "Request for Renewal Price", "requestedAssignedCreditLimit2": "Requested - Assigned Credit Limit *", "view": "View", "provisioningId": "Provisioning ID:", "kuwait": "Kuwait", "enterNickName": "Enter Nick <PERSON>", "action": "Action", "totalCoupons": "Total Coupons", "ndaExecutedWithRedington": "NDA Executed With Redington?", "minQuality": "Min Quality", "unitPrice": "Unit Price", "perpetualLicence": "Perpetual Licence", "orderReview": "Order Review", "companyName": "Company Name", "notificationType": "Notification Type", "skuId": "SKU ID", "1Month": "1 Month", "ifYouAreRegisteredPartnerWithRedingtonPleaseEnterYourRedingtonAccountNumberAndSelectTheCountry": "If you are Registered Partner with Redington, Please enter your Redington Account\n Number and select the country", "subscriptionTop5Customers": "Subscription -\n Top 5 Customers*", "cameronWilliamsonDeliveredTheOrder": "<PERSON> delivered the order", "revisionRequestPleaseConfirm": "Revision Request, please confirm", "showBookmarkedFile": "Show Bookmarked File", "admin": "Admin", "forSelectedPeriod": "For selected period", "creditManagementUsd": "Credit Management (USD)", "hexalyticsLtd": "Hexalytics Ltd", "financeUnbilledCharges": "Finance / Unbilled Charges", "backToNotification": "Back to Notification", "reportRequestId": "Report Request ID", "cartDetails": "Cart Det<PERSON>", "type": "Type", "orderPlaced": "Order Placed", "renewalStatus": "Renewal Status :", "viewPdf": "View PDF", "announcement": "Announcement", "reduceTheNumberOfUsersFeaturesOrResourceUsageMayLowerCostsButOftenRestrictedToRenewalPeriods": "Reduce the number of users, features, or resource usage. May lower costs, but often restricted to renewal periods.", "registeredBusinessAddress": "Registered Business Address", "enterPhoneNumber": "Enter Phone Number", "profilePicture": "Profile Picture", "ipAddress": "IP Address", "select": "-Select-", "quoteSummary": "Quote Summary", "tag123": "Tag 123", "customerId": "Customer ID", "billing": "Billing", "streetAddress2": "Street Address 2", "unredeemedCoupons": "Unredeemed Coupons", "companyInformation": "Company Information", "moreInfo": "More Info", "couponId": "Coupon ID", "amazonWebServices": "Amazon Web Services", "requestedCreditLimitS": "Requested Credit Limit (S)", "base": "Base", "subscriptionTop5Customers2": "Subscription - Top 5 Customers*", "ticketCategory": "Ticket Category", "balance": "Balance", "accountManagementFinance": "Account Management > Finance", "enterName": "Enter Name", "willSmithOrderedFourItemsWithATotalOrderValueOf": "<PERSON> ordered four items, with a total order value of $1,210", "update": "Update", "options": "Options", "addToExistingCart": "Add to Existing Cart", "customerDetails": "Customer Details", "additionalInformation": "Additional Information", "globalOther": "Global / Other", "details": "Details", "invoiceValueUsd": "Invoice Value (USD)", "mostSearchedQuestions": "Most Searched Questions", "renewalManagement": "Renewal Management", "creditCheck": "Credit Check", "groupedProducts": "Grouped Products", "dueDate3": "Due Date:", "chooseAnEligibleUpgradeForYourSubscription": "Choose an eligible upgrade for your subscription", "enterDisplayNumber": "Enter Display Number", "chargeTyp": "Charge Typ", "standardCreditTermsDays": "Standard Credit Terms (Days)", "enterTaxIdentificationNumber": "Enter Tax Identification Number", "value": "Value", "enterAddress": "Enter Address", "manager": "Manager", "orderValueByTop5EndCustomerUsd": "Order Value By Top 5 End Customer (USD)", "folderOne4": "Folder one (4)", "discountedPriceUnit": "Discounted Price/Unit", "submit": "Submit", "selectDepartment": "Select Department", "doYouWantToMakeChangesInThisSubscriptionDuringRenewal": "Do you want to make changes in this subscription during renewal?", "helpSupport": "Help & Support", "generateRenort": "Generate Renort", "totalUnbilledByChargeTypeUsd": "Total Unbilled by Charge Type (USD)*", "privacyPolicy": "Privacy Policy", "thePremiumOfficeSuiteForOrganizationsIncludingWordExcelPowerpointOutlookOnenoteAccessAndSkypeForBusiness": "The Premium Office suite for organizations - \n including Word, Excel , PowerPoint, Outlook, \n OneNote, Access and Skype for Business", "continueShopping": "Continue Shopping", "currencySettings": "<PERSON><PERSON><PERSON><PERSON>", "subscriptionInformation": "Subscription Information", "dubai": "Dubai", "asPerTheOrderDateRangeSelected": "As per the Order Date Range selected", "manualFulfilment": "Manual Fulfilment", "chargeEndDate": "Charge End Date", "price": "Price", "selectSegment": "Select Segment", "transactionDate": "Transaction Date", "dynamicsCustomerServiceEnterpriseEducationFacultyPricing": "Dynamics 365 Customer Service Enterprise (Education Faculty Pricing)", "tags": "Tags", "changePassword": "Change Password", "myProfile": "My Profile", "emailAddress": "Email Address", "editProfile": "Edit Profile", "remove": "Remove", "clickToUploadOrDragAndDrop": "Click to upload or drag and drop", "editProfileDetails": "Edit Profile Details", "company": "Company", "address": "Address", "accountName": "Account Name", "contact": "Contact", "RedingtonAccountNumberisa10digitnumericvalue(example**********)whichistaggedwithsegmentCloudServices": "Redington Account Number is a 10 digit numeric value (example **********) which is tagged with segment Cloud Services", "PleasereachouttoyourRedingtonAccountManagerwhowillassistyouwithyourRedingtonAccountNumber": "Please reach out to your Redington Account Manager who will assist you with your Redington Account Number", "redingtonAccountNumberisauniqueidentifierprovidedbyRedingtonforyourcompany": "Redington Account Number is a unique identifier provided by Redington for your company", "accountInformation": "Account Information", "guestAccountInformation": "Guest Account Information", "ReviewyourguestregistrationdetailsbelowanddownloadthePDFofyourregistrationdata": "Review your guest registration details below and download the PDF of your registration data.", "redingtonACNo": "Redington AC No", "companyCode": "Company Code", "itemPerPage": "Item per Page", "invoices": "Invoices", "myCompany": "My Company", "statementOfAccounts": "Statement of Accounts", "asOndate": "As on date", "notDue": "Not Due", "requestId": "Request ID", "requestedDate": "Requested Date", "filter": "Filter", "billed": " Billed", "creditLimitRevisionNewRequest": "Credit Limit Revision - New Request", "debt": "Debt", "credit": "Credit", "documentNo": "Document No.", "referenceNo": "Reference No.", "customers": "Customers", "documentDate": "Document Date", "vendor": "<PERSON><PERSON><PERSON>", "google": "Google", "microsoft": " Microsoft", "information": "Information", "seatBased": "Seat Based", "sku": "SKU", "chargeType": "Charge Type", "acceptedBy": "Accepted By", "usageCost": "Usage & Cost", "brandSetUp": "Brand Set Up", "enterAPNID": "Enter APN ID", "apnText": "APN ID is AWS Partner Network ID to be created by every partner to transact with AWS. This is a one-time activity and there is no expiry period. APN ID will provide the access to upload the opportunities ,Training & Accreditation", "allBrands": "All Brands", "couponCode": "Coupon Code", "dateCreated": "Date Created", "noOfProducts": "No. of Products", "usageType": "Usage Type", "redeemedCount": "Redeemed Count", "asOnDate": "As on date", "unRedeemedCoupons": "Unredeemed Coupons", "clearAll": "Clear All", "SKU": "SKU", "dateRedeemed": "Date Redeemed", "addNewCart": "Add New Cart", "googleCloud": "Google Cloud", "microsoftCSP": "Microsoft CSP", "awaitingAction": "Awaiting Action", "quotesByStatus": "Quotes by Status", "valid": "<PERSON><PERSON>", "countOfQuotes": "Count of Quotes", "canceled": "Canceled", "in-review": "In-review", "createNewQuote": "Create New Quote", "STEPONE": "STEP ONE", "STEPTWO": "STEP TWO", "quoteInstructions1": "From the marketplace, select and add the products to an existing cart or create a new cart", "quoteInstructions2": "Convert the created cart along with its products in to a quote and submit the quote for a approval", "quoteID": " Quote ID", "cartID": "Cart ID", "initiatedBy": "Initiated By", "totalValue": "Total Value", "selectFromDate": "Select From date", "selectToDate": "Select To date", "selectInitiatedby": "Select Initiated by", "selectCreatedBy": "Select created by", "requestForQuotation": "Request for Quotation", "endCustomerInformation": "End Customer Information", "USD": "(USD)", "reSubmit": "Re-Submit", "remarkByPartner": " Remark by Partner", "approvedQuantity": "Approved Quantity", "approvedPrice": "Approved Price", "cancelQuote": "<PERSON><PERSON> Quote", "max50Characters": "Max 50 characters", "cancelInstructions": "Would you like to cancel this quote? This action cannot be reversed, please confirm to proceed further.", "count": "Count", "fulfilled": "Fulfilled", "failed": "Failed", "orderValueByTop3": "Order Value By Top 3", "bandCategory(USD)": "Brand Category (USD)*", "orderValueByTop5": "Order Value By Top 5", "endCustomer(USD)": "End Customer (USD)", "csp": "CSP", "googlePer": "Google Per..", "orderValue": "Order Value", "orderType": "Order Type", "orderDate": "Order Date", "printOrder": "Print Order", "created": "Created", "billFrom": "<PERSON>", "VATTRN": "VAT TRN", "billTo": " Bill <PERSON>", "TEL": "TEL", "redingtonAccountNumber": "Redington Account Number", "serviceRecipient": "Service Recipient", "organizationTenant": "Organization & Tenant", "tenantID": "Tenant ID", "reference": "Reference", "disclaimer": "Disclaimer", "subtotal": " Subtotal", "estimatedVAT": "Estimated VAT", "listOfOrders": "List of Orders", "QTY": "QTY", "VAT": "VAT %", "doc": "Doc.", "itemsOrdered": "Items Ordered", "lpoReference": "LPO reference", "attachedDocument": "Attached Document", "languageCurrency": "Language & Currency", "selectThelanguage": "Select the language you prefer for browsing the Portal", "announcementsType": "Announcements Type", "general": "General", "microsoftAIInnovation": "Microsoft AI ignites telecom innovation", "andGrowth": "and growth", "allNotifications": "All Notifications", "selectLanguage": "Select Language", "deleteCart": "Delete Cart (s)", "wouldYouLikeToDeleteTheelectedCartYourActionCannotBeReversed": "Would you like to delete the selected cart(s), your action cannot be reversed, please confirm?", "manageCart": "Manage Cart", "checkOut": "Check Out", "cartSummary": "<PERSON>t <PERSON>mma<PERSON>", "discounts": "Discount", "continuePurchase": "Continue Purchase", "optional": "Optional", "itemCovered": "Item Covered", "expiringOn": "Expiring On", "sampleCartName": "Sample Cart Name", "endCustomerDetails": "End Customer Details", "organizationTenants": "Organization Tenants", "more": "More", "secondaryValidations": "Secondary Validations", "generalT&Cs": "General T&C's", "vendorT&C": "Vendor T&C's", "deleteAll": "Delete All", "selectEligibleOffers": "Select Eligible Offers", "selectAlternateOffers": "Select Alternate Offers", "selectAvailablePromotions": "Select Available Promotions", "eligibleOffers": "Eligible Offers", "alternateOffers": "Alternate Offers", "appsheetEnterprisePlus(Additional Service)": "Appsheet Enterprise Plus (Additional Service)", "viewMoreDetails": "View More Details", "offerEndson": "Offer Ends on", "applyOffer": "Apply Offer", "annualPlan(MonthlyPayment)": "Annual Plan (Monthly Payment)", "AnnualPlan(YearlyPayment)": "Annual Plan (Yearly Payment)", "discountIncluded": "Discount Included", "relatedProducts": "Related Products", "crosssellProducts": "Cross sell Products", "upsellProducts": "Upsell Products", "creationDate": "Creation Date", "commitmentEnd": "Commitment End", "commitmentStart": "Commitment Start", "cancellationUntil": "Cancellation Until", "bysubmittinganorderororderrevisionYourCompanyrepresentsthatanyCustomerPurchaseCommitmentprovidediscompleteandaccurateinallrespectsandagreestopayRedingtonforallordersitsubmitsforProducts": "By submitting an order or order revision, Your Company represents that any Customer Purchase Commitment provided is complete and accurate in all respects and agrees to pay Redington for all orders it submits for Products", "purchaseCommitmentCheck": "We hereby confirm that we have received the end customer purchase commitment for this order or order revision and shall provide the same on request.", "subscriptionTerminate": "Subscription - Terminate", "generaltncs": "General T&Cs", "viewMore": "View More", "vendortncs": "Vendor T&Cs", "youCanCoterminateYourSubscriptionWithAnExistingNonTrialCspNceSubscriptionOrAlignTheEndDateWithTheCalendarMonthByChoosingAnAppropriateEndDateDependingOnTheTermDuration": "You can coterminate your subscription with an existing non-trial CSP NCE subscription or align the end date with the calendar month by choosing an appropriate end date depending on the term duration.", "renevalSuccessMsg": "Your Request for Renewal Price Successfully Submitted!", "headline": "Transform your business with CloudQuarks", "subheadline": "There are many ways growth can take shape. Redington is a leading technology distributor at the forefront of cloud and Al advancements.", "signInTitle": "Sign In to continue with CloudQuarks Portal", "emailLabel": "Email Address", "enterEmailPlaceHolder": "Enter your email address", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "signInButton": "Sign In", "orContinueWith": "or Continue With", "copyright": "©2025 Redington Group", "designedBy": "Designed and Developed by Hexalytics", "allRightsReserved": "All Rights Reserved.", "customerManagement": "Customer Management", "manageQuickWidgets": "Manage Quick Widgets", "languageEnglish": "English - EN", "languageArabic": "العربية - AR", "mea": "MEA", "nonRegisteredPartners": "Non-Registered Partners", "registeredPartnerDescription": "Would you like to register your company and become a part of Redington's Partner Ecosystem?", "proceedRegistrationButton": "Proceed to Registration", "getOnboarded": "Get Onboarded", "apply": "Apply", "goodDay": "Good day!", "welcomeTitle1": "Welcome to", "welcomeTitle2": "Redington CloudQuarks", "platformMessage": "One Platform to Transform your Business", "callToAction": "+3,213 Partners already onboarded. Why are you still waiting.", "callToActionLine2": "Get Onboarded now, to experience...", "watchVideo": "Watch Video", "getOnboardedNow": "Get Onboarded Now", "nextStep": "Next, let's get your company onboarded in CloudQuarks", "stepOne": "Step One", "accountActiveMessage1": "Your account is active.", "accountActiveMessage2": "Welcome aboard!", "stepTwo": "Step Two", "businessInformation": "Business Information", "shareholdingDetails": "Provide share holding pattern and key contact details - Director/Owner, Finance and Authorized Signatory", "stepThree": "Step Three", "shareDocuments": "Share Documents", "shareDocumentsDescription": "Upload trade license/reseller documents, tax returns bank statements for review", "stepFour": "Step Four", "checkCompliance": "Check Compliance", "checkComplianceDescription": "Provide year of incorporation, acceptance to Redington T&Cs", "stepFive": "Step Five", "brandCategoryDescription": "Select Your Preferred brand and brand categories", "majorVendors": "Major <PERSON><PERSON><PERSON>", "totalCustomers": "Total Customers", "openCarts": "Open Carts", "overdueInvoices": "Overdue Invoices", "quickLinks": "Quick Links", "selectAny5": "(Select any 5)", "redAlIntro": "Introducing Red.Al The Future of Innovation With Generative AI Excellence", "knowMore": "Know More", "cartId": "Cart ID", "simmonsOn": "<PERSON> on", "quoteId": "Quote ID", "expiresAt": "Expires at", "recentPurchases": "Recent Purchases", "last7Days": "Last 7 days", "last15Days": "Last 15 days", "last30Days": "Last 30 days", "last60Days": "Last 60 days", "quality": "Quality", "salePricePerUnit": "Sale Price/Unit", "dateOfPurchase": "Date of Purchase", "myTop5Products": "My Top 5 Products", "ytd": "YTD", "units": "Units", "productsWithDiscountCoupon": "Products with Discount Coupon", "assigned": "Assigned", "quoteManagement": "Quote Management", "skuIdLabel": "SKU ID", "discountCoupon": "Discount Coupon", "promotions": "Promotions", "reset": "Reset", "contactAddress": "1, <PERSON><PERSON><PERSON>, H Hotel, Business Tower - Dubai, UAE", "EligibleBaseOffers": "Eligible Base Offers", "annual": "Annual", "unitedArabEmirates": "United Arab Emirates", "singapore": "Singapore", "organizationAndTenants": "Organization & Tenants", "innovateFirstInnovateLast": "Innovate First Innovate Last", "subscriptionDetails": "Subscription Details", "innovateItd": "Innovate ITD", "startsOn": "Starts On", "expiresOn": "Expires On", "orderStatus": "Order Status", "deleteLineItem": "Delete Line Item", "listofSubscriptions": "List of Subscriptions", "BasedOnTheCreateDateSelectedDateRange": "Based on the Create Date selected date range", "subscriptionValue": "Subscription Value", "createDate": "Create Date", "productId": "Product ID", "direct": "Direct", "productListing": "Product Listing", "searchProductNameSkuID": "Product Search by Product Name/SKU ID", "appliedFilters": "Applied Filters", "categories": "Categories", "pricing": "pricing", "exploreProductsbyCategories": "Explore Products by Categories", "infrastructureServices": "Infrastructure Services", "businessApplications": "Business Applications", "software": "Software", "businessContinuity": "Business Continuity", "security": "Security", "solutions": "Solutions", "page": "Page", "yourRecentPurchases": "Your Recent Purchases", "expand": "Expand", "collapse": "Collapse", "productsBanner": "Products Banner", "myRecentPurchases": "My Recent Purchases", "topSellingProductsbyStore": "Top Selling Products by Store", "topSellingProductsbyAWS": "Top Selling Products by AWS", "topSellingProductsbyMicroSoft": "Top Selling Products by Microsoft", "topSellingProductsbyGoogle": "Top Selling Products by Google", "addtoQuote": "Add to Quote", "addtoFavourite": "Add to Favourite", "activityDateRange": "Activity Date Range", "selectEndDate": "Select End Date", "performedBy(User, System, API)": " Performed by (User, System, API)", "help": "Help", "audio": "Audio", "document": "Document", "lastUpdatedDate": "Last Updated Date", "noFrameworkFound": "No framework found", "date": "Date", "records": "Records", "countofTickets": "Count Of Tickets", "tickets": "Tickets", "fileSize": "File Size", "max250Characters": "Max 250 Characters", "fileTypeSize": "SVG, PNG, JPG or GIF (MAX. 800x400px)", "typeDescription": "Type Description", "mostViewed": "Most Searched Questions", "whatIsCQ2": "What is Redington Cloud Quarks 2.0?", "whatIsNewInCQ2": "What is new with Redington Cloud Quarks 2.0?", "isCQ1Replaced": "Is the existing cloud market place Cloud Quarks CQ 1.0 being replaced by CQ 2.0?", "needNewLogin": "Will I need a new URL and login to access CQ 2.0?", "existingTransactions": "What will happen to my existing transactions, orders, subscriptions and end users?", "issuesContact": "If I encounter any issues in Cloud Quarks 2.0, who should I contact?", "stillUseCQ1": "Can I still use the Cloud Quarks 1.0 platform after we have migrated to Cloud Quarks 2.0?", "noData": "No Data", "sameAsDirector/Owner": "Same as Director/Owner", "keyContacts": "Key Contacts", "addAnotherContact": "Add Another Contact", "city": "City", "customerStatus": "Customer Status", "customerVertical": "Customer Vertical", "documentInformation": "Document Information", "partnerProfile": "Partner Profile", "brandInformation": "Brand Information", "totalActiveContractsCount": "Total Active\n Contracts-Count", "countOfContracts": "Count of Contracts", "contractStatus": "Contract Status", "contractEndDate": "Contract End Date", "nonActive": "Non Active", "contractInformation": "Contract Information", "valueBilledUnbilled": "Value - Billed/ Unbilled", "scheduleTotal": "Schedule - Total", "schedulesBilledUnbilled": "Schedules - Billed/Unbilled", "organisationAndTenantId": "Organisation & Tenant ID", "productNameSku": "Product Name & SKU", "requestsIn": "Requests in", "selectProductName": "Select Product Name", "renewalStartDate": "Renewal Start Date", "renewalEndDate": "<PERSON>wal End Date", "comments": "Comments", "partnerComment": "Partner Comment", "areYouSureYouWantToRejectThisQuote": "Are you sure you want to reject this quote?", "areYouSureYouWantToRenewThisQuote": "Are you sure you want to renew this quote?", "adminRemark": "Admin Remark", "acceptRenewQuote": "Accept & Renew Quote", "acceptRenew": "Accept & Renew", "yesCancel": "Yes, <PERSON>cel", "middleName": "Middle Name", "gstRegistrationNumber": "GST Registration Number", "panNumber": "PAN Number", "isCompanyPartOfSEZ": "Is Company Part Of SEZ", "cinNumber": "CIN Number", "streetAddress1": "Street Address1", "state": "State", "postalCode": "Postal Code", "legalStatus": "Legal Status", "companyWebsite": "Company Website", "numberOfOfficesInRegion": "Number Of Offices In Region", "otherCountriesWithOffices": "Other Countries With Offices", "numberOfWarehousesInRegion": "Number Of Warehouses In Region", "numberOfEmployees": "Number Of Employees", "numberOfSalesStaff": "Number Of Sales Staff", "numberOfTechnicalStaff": "Number Of TechnicalStaff", "twitterAccount": "Twitter Account", "facebookAccount": "Facebook Account", "linkedInAccount": "LinkedIn Account", "instagramAccount": "Instagram Account", "enterGSTNumber": "Enter GST Number", "entervalue": "Enter value", "multiSelectDropdownForSelectingCountry": "Multi select dropdown for selecting country", "enterAccountURL": "Enter Account URL", "redingtonTermsConditions": "Redington - Terms & Conditions", "iAcceptRedingtonTermsandConditions": "I Accept Redington Terms and Conditions", "failedToFetchQuestionnaire": "Failed to fetch questionnaire", "oadingQuestionnaire": "Loading questionnaire", "weRequestvisitwebsite": "We request you to kindly visit our official careers website", "authenticInformation": "for authentic information pertaining to business association opportunity at Redington and enquire with the company to confirm if the offers or promotions are genuine.", "companyTaxRegistrationID": "Company Tax/Registration ID", "requestfornewRelationship": "Request for new Relationship", "terminateRelationship": "Terminate Relationship", "invitationLink": "Invitation Link", "microsoftEntraroles": "Microsoft Entra roles", "durationinDays": "Duration In Days", "name": "Name", "autoExtend": "Auto Extend", "autoExtendBy": "Auto Extend By", "securityGroups": "Security Groups", "addSecurityGroup": "Add Security Group", "removeSecuritygroups": "Remove Security groups", "adminRelationshipName": "Admin Relationship Name", "gdap": "GDAP", "createanAdminRelationshipRequest": "Create an Admin Relationship Request", "customerList": "Customer List", "organizationTenantConfiguration": "Organization & Tenant Configuration", "selectaBrandCategory": "Select a Brand Category", "googleCloudPlatform": "Google Cloud Platform", "createNewCustomerTenant/Domain": "Create New Customer Tenant/Domain", "selectCustomerTenantType": "Select Customer Tenant Type", "domainCustomer(recommended)": "Domain Customer (recommended)", "teamCustomer": "Team Customer", "linkorTransferExistingCustomerTenant/Domain": "Link or Transfer Existing Customer Tenant/Domain", "entertheDomainName": "Enter the Domain Name", "checkAvailability": "Check Availability", "existingCustomerTenantDetails": "Existing Customer Tenant Details", "belowAreTheCustomerDetails": "Below are the Customer details according to Google records, corresponding to the domain name you provided. Please verify the details and click ‘Save’ to complete the linking process", "channelPartnerID": "Channel Partner ID", "customerCloudIdentityID": "Customer Cloud Identity ID", "domain": "Domain", "customerType": "Customer Type", "customerCompanyName": "Customer Company Name", "primaryEmail": "Primary Email", "alternativeEmail": "Alternative Email", "regionCode": "Region Code", "backtoOrg&TenantList": "Back to Org & Tenant List", "yourRequestforLinkingaGoogleend": "Your request for linking a Google end", "customerTenantHasBeenSuccessfully": " customer tenant has been successfully", "processed": "processed", "customerTenantLinked": "Customer Tenant Linked", "successfully!": "successfully!", "createNewDomainCustomer": "Create New Domain Customer", "selectTheCustomerSegmentType": "Select the Customer Segment Type", "educationalInstitution": "Educational Institution", "thecompanyInformationIsUsedToCreate": "The company information is used to create the initial administrator account  for Google Workspace and Google Cloud", "organizationName": "Organization Name", "CRMID": "CRM ID", "addressInformation": "Address Information", "pincode": "Pincode", "contactInformation": "Contact Information", "theNameAndEmailAddress": "The name and email address are used to create the initial administrator account for Google Workspace and Google Cloud.", "educationalInstituteType": "Educational Institute Type", "pleaseSelectInstituteType": "Please Select Institute Type", "instituteSize": "Institute Size", "pleaseSelectInstituteSize": "Please Select Institute Size", "add": "Add", "pleaseselecttheBrandsandBrandCategories": "Please select the Brands and Brand Categories", "enterAWSAccountID": "Enter AWS Account ID", "createNewTenant": "Create New Tenant", "linkExistingTenant": "Link Existing Tenant", "accountCreationInstructions": "To get started, you must create an account using your customer's company name followed by .onmicrosoft.com. Choose a name similar to the company name, with no spaces or punctuation. The primary domain name can’t be changed later. If your customer has its own custom domain that it wants to use without .onmicrosoft.com, you can change this later in Office 365 Admin Portal.", "tenantCreationMessage": "“redingtongulfdxbtest.onmicrosoft.com” is available. Please click Save to continue and create a new tenant for this customer.", "awsAccountIdInfo": "AWS Account ID is a unique identifier assigned to your AWS tenant. You can find your account ID in the AWS Management Console. Sign in to the AWS Management Console and open the IAM console.", "signedInAccountInfo": "The account to which you are signed in appears at the top of the navigation panel.", "findTenantId": "Find Tenant ID", "check": "Check", "findTenantIdUsingCustomer": "Find Tenant ID using Customer's", "clickLinkButton": "Please click on the link button", "tenantIdInfo": "Tenant ID, also referred to as Directory ID or Customer ID, is a globally unique identifier (GUID) for your customer's Microsoft O365 or Azure Organization account. This ID is a 36-character alphanumeric value and is different from your tenant name or domain. For example: 824ddc74-1210-470a-8f4a-d0be4769346d. Please", "clickHere": "Click here", "confirmCustomerTaxId": "Confirm Customer Tax Registration ID", "taxRegistrationRequirement": "Tax Registration ID/Identification Number is required for this customer. Microsoft has made it mandatory to provide the customer’s Tax Registration ID/Identification Number for new customer onboarding.", "microsoftAnnouncements": "Microsoft Announcements", "taxIdValidationMessage": "The Tax Registration ID should be 5–22 alphanumeric characters, with no special characters such as periods, slashes, etc. (Example: abc123)", "verify": "Verify", "requestSubmitted": "Your request has been submitted", "successfully": "Successfully", "enterSubscriptionNickName": "Enter Subscription Nick Name", "confirmCompanyTaxId": "Please work with your customer contact to confirm the Company Tax Registration ID", "confirmMcaAcceptance": "Confirm Microsoft Customer Agreement (MCA) Acceptance", "provideInfoAndConfirmAcceptance": "Please provide us with the following information and confirm that the end customer has accepted the", "microsoftCustomerAgreement": "Microsoft Customer Agreement (MCA)", "documentAcceptanceInstruction": "Please also document this acceptance in a sound and secure manner.", "viewDownloadAgreement": "View & Download Agreement", "endCustomerFirstName": "First Name of the end customer representative", "endCustomerLastName": "Last Name of the end customer representative", "endCustomerEmail": "Email Address of the end customer representative", "phoneFormatInfo": "Phone should be in international format starting with “+”", "dateOfAcceptance": "Date of Acceptance", "dateFormatInfo": "Date format in mm/dd/yyyy", "tenantDetails": "Tenant Details", "customerDomain": "Customer Domain", "username": "Username", "temporaryPassword": "Temporary Password", "addSubscription": "Add Subscription", "displayInvitationUrl": "Display Invitation URL", "customerLinkingInvitationUrl": "Customer Linking Invitation URL", "invitationUrlDescription": "Here is the URL for inviting end customers to link their tenant. If you prefer to send the invitation from your own email address, you can copy the link and forward it to your end customer.", "copyLink": "Copy Link", "sendEmail": "Send Email", "sentEmail": "<PERSON><PERSON>", "cspInvitationSentMessage": "CSP invitation email has been <NAME_EMAIL>. The customer has to accept this invitation for you to add and manage Microsoft cloud licenses in their tenant.", "reverifyAfterCustomerAcceptance": "Once the customer accepts the request, please revisit this menu to reverify.", "saveTenantInstruction": "and save the Tenant", "resendInvitationInfo": "If required, you can also resend the invitation email using Resend.", "attention": "Attention", "shareMcaConfirmation": "Please share the Microsoft Customer Agreement (MCA) with your customer and confirm their acceptance. Use of cloud services through the CSP program is subject to the Microsoft Customer Agreement (MCA).", "viewAgreementLinkText": "Click here to view the agreement", "findId": "Find ID", "products": "Products", "period": "Period", "actions": "Actions", "becomeVendor": "Become a Vendor"}