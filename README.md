# Next.js Enterprise Boilerplate

A production-ready Next.js boilerplate with enterprise-level architecture and best practices. This template includes Redux Toolkit for state management, React Query for server state management, TypeScript for type safety, and a comprehensive setup for code quality and consistency.

## Table of Contents

- [Dependencies](#dependencies)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Code Quality Tools](#code-quality-tools)
- [Git Workflow](#git-workflow)
- [Sentry Setup for Team Members](#sentry-setup-for-team-members)

## Dependencies

### Core Dependencies

- **Next.js (v15.2)**: React framework for production-grade applications
- **React (v19)**: UI library for building user interfaces
- **TypeScript (v5)**: Adds static typing to JavaScript

### State Management

- **@reduxjs/toolkit**: Official Redux toolkit for efficient Redux development
- **react-redux**: React bindings for Redux
- **@tanstack/react-query**: Powerful data synchronization for React

### API & Data Validation

- **axios**: Promise-based HTTP client
- **zod**: TypeScript-first schema validation

### Development Dependencies

- **eslint**: Linting utility for JavaScript and TypeScript
- **prettier**: Code formatter
- **husky**: Git hooks made easy
- **lint-staged**: Run linters on git staged files
- **@commitlint**: Lint commit messages
- **tailwindcss**: Utility-first CSS framework

## Project Structure

```
src/
├── app/                    # Next.js App Router pages and layouts
├── components/            # Reusable UI components
├── lib/                   # Core utilities and configurations
│   ├── config/           # App configurations
│   ├── constants/        # Constants and enums
│   ├── helpers/          # Business logic helpers
│   ├── hooks/            # Custom React hooks
│   ├── services/         # API services
│   ├── types/            # TypeScript types
│   └── utils/            # Pure utility functions
├── modules/              # Feature-based modules
├── store/                # Redux store configuration
│   ├── index.ts         # Store setup
│   └── slices/          # Redux slices
├── styles/               # Global styles
└── types/               # Global TypeScript types
```

### Directory Purpose

- **app/**: Contains all the Next.js pages and layouts using the App Router
- **components/**: Houses all reusable UI components
- **lib/**: Core utilities and configurations
  - **config/**: Application configurations (env, react-query, etc.)
  - **constants/**: Constants and enums
  - **helpers/**: Domain-specific helper functions with business logic
  - **hooks/**: Reusable React hooks
  - **services/**: API services and external integrations
  - **types/**: Shared TypeScript types
  - **utils/**: Pure, stateless utility functions
    - String manipulation
    - Date formatting
    - Number calculations
    - Data transformation
    - Type checking
    - etc.
- **modules/**: Feature-based modules (each module can have its own components, hooks, etc.)
- **store/**: Redux store setup and slices
- **styles/**: Global styles and theme configurations
- **types/**: Global TypeScript type declarations

### Utils vs Helpers

The project makes a clear distinction between utils and helpers:

#### Utils (src/lib/utils/)

Pure utility functions that:

- Are stateless and have no side effects
- Perform single, specific operations
- Are context-independent
- Can be used across different features
- Focus on data transformation

Example utils:

```typescript
// src/lib/utils/string.ts
export const capitalize = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1);

// src/lib/utils/date.ts
export const formatDate = (date: Date): string => new Intl.DateTimeFormat('en-US').format(date);
```

#### Helpers (src/lib/helpers/)

Domain-specific helper functions that:

- May contain business logic
- Can have side effects
- Often combine multiple utils
- Are specific to features or domains
- May interact with services or state

Example helpers:

```typescript
// src/lib/helpers/auth.ts
export const validateUserSession = async () => {
  const token = getStoredToken(); // util function
  const decoded = decodeJwt(token); // util function
  await api.post('/auth/verify'); // service call
  // ... business logic
};
```

## Getting Started

1. Clone the repository:

```bash
git clone [repository-url]
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file:

```env
NEXT_PUBLIC_API_URL=your_api_url_here
```

4. Run the development server:

```bash
npm run dev
```

## Code Quality Tools

[Rest of the sections remain the same...]

## Sentry Setup for Team Members

### 1. Create a Sentry Account

1. Go to [Sentry.io](https://sentry.io) and sign up for a new account
2. After signing up, you'll be prompted to create a new organization or join an existing one
3. If you're joining the existing team:
   - Ask the admin for an invitation to the organization
   - Check your email for the invitation and accept it

### 2. Project Access

1. Once you're part of the organization, you'll have access to the project
2. Navigate to the project settings to view your access level
3. You can customize your notification settings under User Settings > Notifications

### 3. Local Development Setup

1. Create a `.env.local` file in the root directory if it doesn't exist
2. Add your Sentry DSN:
   ```env
   NEXT_PUBLIC_SENTRY_DSN=your-dsn-here
   ```
3. To get your DSN:
   - Go to Project Settings > Client Keys (DSN)
   - Copy the DSN value
   - Replace the existing DSN in `sentry.client.config.ts` with your DSN

### 4. Verify Installation

1. Run the development server:
   ```bash
   npm run dev
   ```
2. Test Sentry by triggering an error (e.g., accessing an undefined variable)
3. Check your Sentry dashboard - the error should appear within a few minutes

### 5. Best Practices

- Always include relevant context when capturing errors
- Use appropriate logging levels
- Add breadcrumbs for better debugging
- Create meaningful error messages

Example usage:

```typescript
try {
  // Your code here
} catch (error) {
  Sentry.captureException(error, {
    level: 'error',
    tags: {
      feature: 'authentication',
      environment: process.env.NODE_ENV,
    },
    extra: {
      userId: user.id,
      action: 'login',
    },
  });
}
```

### 6. Performance Monitoring

- Transaction traces are automatically captured for requests
- Custom spans can be added for specific operations:

```typescript
const result = Sentry.startSpan(
  {
    name: 'data-processing',
    op: 'task',
  },
  () => {
    // Your code here
    return processData();
  }
);
```

### 7. Environment Variables

Required environment variables for Sentry:

```env
NEXT_PUBLIC_SENTRY_DSN=your-dsn-here
SENTRY_AUTH_TOKEN=your-auth-token # For source maps upload (optional)
SENTRY_ORG=your-org-name
SENTRY_PROJECT=your-project-name
```

For any questions or issues with Sentry setup, please contact the team lead or refer to the [Sentry Documentation](https://docs.sentry.io/platforms/javascript/guides/nextjs/).
