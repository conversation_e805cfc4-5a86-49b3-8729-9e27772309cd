// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;
const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';

// Disable Sentry in development mode
if (IS_DEVELOPMENT) {
  // eslint-disable-next-line no-console
  console.info('Sentry is disabled in development mode.');
} else if (!SENTRY_DSN) {
  // eslint-disable-next-line no-console
  console.warn('NEXT_PUBLIC_SENTRY_DSN is not set. Error tracking will be disabled.');
}

// Only initialize Sentry in production
if (!IS_DEVELOPMENT && SENTRY_DSN && SENTRY_DSN?.length > 0) {
  Sentry.init({
    dsn: SENTRY_DSN,

    // Define how likely traces are sampled. Adjust this value in production
    tracesSampleRate: 0.1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
  });
}
