{"name": "cloudquarks-partner-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env WATCHPACK_POLLING=true next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "docker:dev": "docker-compose up", "docker:dev:build": "docker-compose up --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up", "docker:prod:build": "docker-compose -f docker-compose.prod.yml up --build", "prepare": "npx husky install", "prebuild": "next lint && npm run format"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@redington-gulf-fze/cloudquarks-component-library": "github:RedingtonGulfFZE/CloudQuarksComponentLibrary#UIChanges", "@reduxjs/toolkit": "^2.0.0", "@sentry/nextjs": "^9.5.0", "@sentry/tracing": "^7.120.3", "@tanstack/react-query": "^5.0.0", "@tanstack/react-table": "^8.21.3", "@types/simplebar": "^5.3.3", "axios": "^1.6.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.2.1", "next-intl": "^4.1.0", "next-s3-upload": "^0.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-phone-number-input": "^3.4.12", "react-redux": "^9.0.0", "react-tabs": "^6.1.0", "recharts": "^2.15.3", "simplebar-react": "^3.3.1", "tailwind-merge": "^3.3.0", "tslib": "^2.8.1", "zod": "^3.25.67"}, "devDependencies": {"@commitlint/cli": "^19.0.0", "@commitlint/config-conventional": "^19.0.0", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.1", "eslint-config-prettier": "^9.0.0", "husky": "^9.0.0", "lint-staged": "^15.0.0", "postcss": "^8.5.3", "prettier": "^3.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.6.1"}, "packageManager": "yarn@1.22.22"}